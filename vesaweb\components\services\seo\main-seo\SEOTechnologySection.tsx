import React from 'react';
import { Monitor, Smartphone, Settings } from 'lucide-react';

const SEOTechnologySection: React.FC = () => {
  const seoTools = [
    {
      name: 'Google Analytics 4',
      description: 'Comprehensive website traffic and user behavior analysis',
      purpose: 'Track conversions, user journeys, and ROI measurement'
    },
    {
      name: 'Google Search Console',
      description: 'Monitor search performance and technical issues',
      purpose: 'Keyword tracking, indexing status, and search appearance'
    },
    {
      name: 'SEMrush Enterprise',
      description: 'Advanced keyword research and competitor analysis',
      purpose: 'Market research, content planning, and competitive insights'
    },
    {
      name: 'Ahrefs Professional',
      description: 'Backlink analysis and content gap identification',
      purpose: 'Link building opportunities and content strategy development'
    },
    {
      name: 'Screaming Frog',
      description: 'Technical SEO auditing and site crawling',
      purpose: 'Identify technical issues and optimization opportunities'
    },
    {
      name: 'PageSpeed Insights',
      description: 'Core Web Vitals and site speed optimization',
      purpose: 'Improve user experience and search rankings'
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6">
        {/* SEO Tools Section */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Professional SEO Tools & Technology
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Vesa Solutions utilizes industry-leading SEO tools and technologies to deliver comprehensive analysis, tracking, and optimization for maximum results.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {seoTools.map((tool, index) => (
            <div key={index} className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="bg-blue-100 w-12 h-12 rounded-xl flex items-center justify-center mb-4">
                <Monitor className="text-blue-600" size={24} />
              </div>
              <h4 className="text-lg font-bold text-gray-800 mb-3">{tool.name}</h4>
              <p className="text-gray-600 mb-3">{tool.description}</p>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-700 font-medium">Purpose: {tool.purpose}</p>
              </div>
            </div>
          ))}
        </div>

        {/* AI & Future Technology Section */}
        <div className="border-t border-gray-200 pt-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-8">
              Future-Proof Your Business: AI & Voice Search Optimization
            </h3>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              SEO is no longer just about traditional search. Vesa Solutions ensures your business is discoverable across all search platforms including Google AI Overviews, ChatGPT, voice assistants, and emerging AI technologies.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl">
                <Smartphone className="text-green-600 mb-4" size={48} />
                <h4 className="text-xl font-bold text-gray-800 mb-4">Voice Search Optimization</h4>
                <p className="text-gray-600">Optimize for conversational queries and voice search patterns used by Siri, Alexa, and Google Assistant.</p>
              </div>
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl">
                <Monitor className="text-green-600 mb-4" size={48} />
                <h4 className="text-xl font-bold text-gray-800 mb-4">AI Platform Visibility</h4>
                <p className="text-gray-600">Ensure your content appears in ChatGPT responses, Google AI Overviews, and other AI-powered search results.</p>
              </div>
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl">
                <Settings className="text-green-600 mb-4" size={48} />
                <h4 className="text-xl font-bold text-gray-800 mb-4">Structured Data Implementation</h4>
                <p className="text-gray-600">Advanced schema markup to help AI systems understand and surface your content effectively.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SEOTechnologySection;