// lib/sanity-case-studies.ts - Sanity client utilities for case studies
import { createClient } from '@sanity/client'
import imageUrlBuilder from '@sanity/image-url'

export const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: '2023-05-03',
  useCdn: false,
})

// Sanity image type
export interface SanityImageObject {
  _type: 'image';
  asset: {
    _ref: string;
    _type: 'reference';
  };
  alt?: string;
  caption?: string;
}

// Image URL builder
const builder = imageUrlBuilder(client)

export function urlFor(source: SanityImageObject) {
  return builder.image(source)
}

// TypeScript interfaces for case studies
export interface CaseStudyMetric {
  label: string;
  beforeValue?: string;
  afterValue: string;
  improvement?: string;
  icon?: string;
}

export interface CaseStudyClient {
  name: string;
  industry: string;
  location?: string;
  website?: string;
  logo?: SanityImageObject;
  companySize?: string;
}

export interface CaseStudyProject {
  serviceType: string;
  duration?: string;
  challenge: Array<{
    _key: string;
    _type: string;
    children: Array<{
      _key: string;
      _type: 'span';
      marks: string[];
      text: string;
    }>;
    markDefs: unknown[];
    style: string;
  }>;
  goals: string[];
}

export interface CaseStudySolution {
  approach: Array<{
    _key: string;
    _type: string;
    children: Array<{
      _key: string;
      _type: 'span';
      marks: string[];
      text: string;
    }>;
    markDefs: unknown[];
    style: string;
  }>;
  implementation?: Array<{
    title: string;
    description: string;
    timeline?: string;
  }>;
  tools?: string[];
}

export interface CaseStudyResults {
  overview: string;
  metrics: CaseStudyMetric[];
  timeline?: string;
}

export interface CaseStudyTestimonial {
  quote: string;
  author: string;
  position: string;
  authorImage?: SanityImageObject;
  rating?: number;
}

export interface CaseStudy {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
  shortDescription: string;
  featured: boolean;
  publishedAt: string;
  client: CaseStudyClient;
  project: CaseStudyProject;
  solution: CaseStudySolution;
  results: CaseStudyResults;
  testimonial: CaseStudyTestimonial;
  featuredImage: SanityImageObject;
  beforeAfterImages?: {
    before?: SanityImageObject;
    after?: SanityImageObject;
  };
  gallery?: SanityImageObject[];
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
    ogImage?: SanityImageObject;
  };
}

// GROQ queries for case studies
export const caseStudyQueries = {
  // Get all case studies with basic info
  all: `*[_type == "caseStudy"] | order(publishedAt desc) {
    _id,
    title,
    slug,
    shortDescription,
    featured,
    publishedAt,
    client,
    project {
      serviceType,
      duration
    },
    results {
      overview,
      metrics[] {
        label,
        afterValue,
        improvement,
        icon
      }
    },
    testimonial {
      quote,
      author,
      position,
      rating
    },
    featuredImage,
    seo
  }`,

  // Get featured case studies
  featured: `*[_type == "caseStudy" && featured == true] | order(publishedAt desc) {
    _id,
    title,
    slug,
    shortDescription,
    client,
    project {
      serviceType,
      duration
    },
    results {
      overview,
      metrics[] {
        label,
        afterValue,
        improvement,
        icon
      }
    },
    testimonial {
      quote,
      author,
      position,
      rating
    },
    featuredImage
  }`,

  // Get case studies by industry
  byIndustry: (industry: string) => `*[_type == "caseStudy" && client.industry == "${industry}"] | order(publishedAt desc) {
    _id,
    title,
    slug,
    shortDescription,
    client,
    project {
      serviceType,
      duration
    },
    results {
      metrics[] {
        label,
        afterValue,
        improvement
      }
    },
    featuredImage
  }`,

  // Get single case study by slug
  bySlug: (slug: string) => `*[_type == "caseStudy" && slug.current == "${slug}"][0] {
    _id,
    title,
    slug,
    shortDescription,
    featured,
    publishedAt,
    client,
    project,
    solution,
    results,
    testimonial,
    featuredImage,
    beforeAfterImages,
    gallery,
    seo
  }`,

  // Get case studies for sitemap
  sitemap: `*[_type == "caseStudy"] {
    slug,
    publishedAt
  }`
};

// API functions
export async function getAllCaseStudies(): Promise<CaseStudy[]> {
  try {
    return await client.fetch(caseStudyQueries.all);
  } catch (error) {
    console.error('Error fetching case studies:', error);
    return [];
  }
}

export async function getFeaturedCaseStudies(): Promise<CaseStudy[]> {
  try {
    return await client.fetch(caseStudyQueries.featured);
  } catch (error) {
    console.error('Error fetching featured case studies:', error);
    return [];
  }
}

export async function getCaseStudiesByIndustry(industry: string): Promise<CaseStudy[]> {
  try {
    return await client.fetch(caseStudyQueries.byIndustry(industry));
  } catch (error) {
    console.error('Error fetching case studies by industry:', error);
    return [];
  }
}

export async function getCaseStudyBySlug(slug: string): Promise<CaseStudy | null> {
  try {
    return await client.fetch(caseStudyQueries.bySlug(slug));
  } catch (error) {
    console.error('Error fetching case study by slug:', error);
    return null;
  }
}

export async function getCaseStudiesForSitemap() {
  try {
    return await client.fetch(caseStudyQueries.sitemap);
  } catch (error) {
    console.error('Error fetching case studies for sitemap:', error);
    return [];
  }
}

// Helper functions
export function getIndustryColor(industry: string): string {
  const industryColors: { [key: string]: string } = {
    'ecommerce': 'blue',
    'healthcare': 'green',
    'professional-services': 'purple',
    'education': 'indigo',
    'real-estate': 'orange',
    'automotive': 'red',
    'financial': 'yellow',
    'non-profit': 'pink',
    'technology': 'cyan',
    'manufacturing': 'gray',
    'food-beverage': 'lime',
    'other': 'slate'
  };
  
  return industryColors[industry] || 'blue';
}

export function formatMetricValue(value: string): string {
  // Format metric values for display
  if (value.includes('%')) {
    return value;
  }
  if (value.includes('+')) {
    return value;
  }
  if (value.includes('-')) {
    return value;
  }
  if (!isNaN(Number(value))) {
    return `+${value}%`;
  }
  return value;
}

export function getServiceTypeColor(serviceType: string): string {
  const serviceColors: { [key: string]: string } = {
    'seo': 'blue',
    'web-development': 'purple',
    'ppc': 'green',
    'social-media': 'pink',
    'content-marketing': 'orange',
    'email-marketing': 'yellow',
    'reputation': 'red',
    'full-strategy': 'indigo'
  };
  
  return serviceColors[serviceType] || 'blue';
}
