// FooterCTA.tsx - Fixed with IconRenderer
import React from 'react'
import Link from 'next/link'
import { IconRenderer } from '@/components/global/IconRender'
import { FooterCTA as FooterCTAType } from '@/types/subService'

interface FooterCTAProps {
  data?: FooterCTAType
}

export const FooterCTA: React.FC<FooterCTAProps> = ({ data }) => {
  if (!data) return null

  return (
    <>
      {/* Final CTA Section */}
      <section className={`py-24 bg-gradient-to-r ${data.backgroundGradient || 'from-blue-700 to-blue-900'}`}>
        <div className="max-w-4xl mx-auto px-6 text-center">
          {data.title && (
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              {data.title}
            </h2>
          )}
          
          {data.description && (
            <p className="text-xl text-blue-100 mb-8 leading-relaxed">
              {data.description}
            </p>
          )}
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {data.primaryButton && (
              <Link
                href="/free-estimate"
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg inline-flex items-center justify-center"
              >
                <IconRenderer iconName={data.primaryButton.icon} size={20} className="mr-2" />
                {data.primaryButton.text}
              </Link>
            )}
            
            {data.secondaryButton && (
              <a
                href="tel:+14166283793"
                className="bg-white text-blue-900 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 inline-flex items-center justify-center"
              >
                <IconRenderer iconName={data.secondaryButton.icon} size={20} className="mr-2" />
                {data.secondaryButton.text} ****** 628 3793
              </a>
            )}
          </div>
        </div>
      </section>


    </>
  )
}