import React from 'react';
import Image from 'next/image';

interface Partner {
  id: number;
  name: string;
  logo: string;
}

interface PartnersProps {
  className?: string;
  speed?: number;
}

const Partners: React.FC<PartnersProps> = ({ 
  className = '', 
  speed = 30
}) => {
  // Partner logos data
  const partners: Partner[] = [
    { id: 1, name: "<PERSON><PERSON>", logo: "/logo/AIClogo.png" },
    { id: 2, name: "After Hours", logo: "/logo/afterhourslogo.png" },
    { id: 3, name: "Argon", logo: "/logo/argon logo.png" },
    { id: 4, name: "Dorchester", logo: "/logo/dorchester logo.png" },
    { id: 5, name: "Harbour", logo: "/logo/harbour logo.png" },
    { id: 6, name: "Ice Cream Truck", logo: "/logo/ice cream truck logo.png" },
    { id: 7, name: "JN<PERSON>", logo: "/logo/jnj logo.png" },
    { id: 8, name: "Kiddie Couch", logo: "/logo/kiddie couch logo.png" },
    { id: 9, name: "Liquor To Go", logo: "/logo/liquor to go logo.png" },
    { id: 10, name: "Miami", logo: "/logo/miami logo.png" }
  ];

  return (
    <div className={`relative bg-blue-600 py-8 overflow-hidden ${className}`}>
      <style jsx>{`
        @keyframes scroll {
          0% { transform: translateX(0); }
          100% { transform: translateX(calc(-200px * ${partners.length})); }
        }
        
        @media (min-width: 768px) {
          @keyframes scroll {
            0% { transform: translateX(0); }
            100% { transform: translateX(calc(-250px * ${partners.length})); }
          }
        }
        
        .slider {
          position: relative;
        }
        
        .slider::before,
        .slider::after {
          content: "";
          position: absolute;
          top: 0;
          width: 60px;
          height: 100%;
          z-index: 10;
          pointer-events: none;
        }
        
        .slider::before {
          left: 0;
          background: linear-gradient(to right, rgb(37, 99, 235) 0%, rgba(37, 99, 235, 0) 100%);
        }
        
        .slider::after {
          right: 0;
          background: linear-gradient(to left, rgb(37, 99, 235) 0%, rgba(37, 99, 235, 0) 100%);
        }
        
        .slide-track {
          animation: scroll ${speed}s linear infinite;
          display: flex;
          width: calc(200px * ${partners.length * 2});
        }
        
        @media (min-width: 768px) {
          .slide-track {
            width: calc(250px * ${partners.length * 2});
          }
        }
        
        .slide {
          height: 100px;
          width: 200px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          padding: 0 20px;
        }
        
        @media (min-width: 768px) {
          .slide {
            width: 250px;
          }
        }
      `}</style>
      
      <div className="slider">
        <div className="slide-track">
          {/* First set */}
          {partners.map((partner) => (
            <div key={partner.id} className="slide">
              <Image
                src={partner.logo}
                alt={partner.name}
                width={160}
                height={80}
                className="h-12 md:h-16 object-contain opacity-90 w-auto"
              />
            </div>
          ))}
          {/* Duplicate for seamless loop */}
          {partners.map((partner) => (
            <div key={`dup-${partner.id}`} className="slide">
              <Image
                src={partner.logo}
                alt={partner.name}
                width={160}
                height={80}
                className="h-12 md:h-16 object-contain opacity-90 w-auto"
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Partners;