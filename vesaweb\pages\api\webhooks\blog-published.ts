// pages/api/webhooks/blog-published.ts - Webhook for Sanity blog post publication
import { NextApiRequest, NextApiResponse } from 'next';
import { sendBlogNotificationToSubscribers, BlogNotificationData } from '@/lib/newsletter';

interface SanityWebhookPayload {
  _type: string;
  _id: string;
  title?: string;
  slug?: {
    current: string;
  };
  excerpt?: string;
  featuredImage?: {
    asset?: {
      _ref: string;
      _type: string;
    };
    alt?: string;
  };
  publishedAt?: string;
  // Add other fields as needed
}

// Helper function to get error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) return error.message;
  return String(error);
}

// Helper function to validate webhook signature (optional security measure)
function validateWebhookSignature(req: NextApiRequest): boolean {
  // If you set up a webhook secret in Sanity, validate it here
  const webhookSecret = process.env.SANITY_WEBHOOK_SECRET;
  
  if (!webhookSecret) {
    // If no secret is configured, allow all requests (less secure)
    return true;
  }

  // For now, we'll just check if the secret matches a simple header
  const providedSecret = req.headers['x-webhook-secret'] as string;
  
  return providedSecret === webhookSecret;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Validate webhook signature for security
    if (!validateWebhookSignature(req)) {
      console.warn('Invalid webhook signature');
      return res.status(401).json({ 
        message: 'Unauthorized webhook request',
        success: false 
      });
    }

    const payload: SanityWebhookPayload = req.body;

    // Validate that this is a blog post
    if (payload._type !== 'blogPost') {
      return res.status(400).json({ 
        message: 'Webhook is only for blog posts',
        success: false 
      });
    }

    // Validate required fields
    if (!payload.title || !payload.slug?.current || !payload.excerpt || !payload.publishedAt) {
      console.warn('Blog post missing required fields for notification:', payload);
      return res.status(400).json({ 
        message: 'Blog post missing required fields for notification',
        success: false 
      });
    }

    // Prepare notification data
    const notificationData: BlogNotificationData = {
      title: payload.title,
      excerpt: payload.excerpt,
      slug: payload.slug.current,
      publishedAt: payload.publishedAt,
      // Note: Featured image URL would need to be processed from Sanity asset
      // For now, we'll leave it undefined and handle it in the email template
      featuredImage: undefined
    };

    console.log(`Processing blog notification for: ${payload.title}`);

    // Send notifications to all subscribers
    const result = await sendBlogNotificationToSubscribers(notificationData);

    if (result.success || result.sentCount > 0) {
      console.log(`Blog notification webhook processed successfully: ${result.sentCount} emails sent, ${result.failedCount} failed`);
      
      res.status(200).json({
        message: `Blog notification sent to ${result.sentCount} subscribers`,
        success: true,
        details: {
          blogTitle: payload.title,
          blogSlug: payload.slug.current,
          sentCount: result.sentCount,
          failedCount: result.failedCount,
          totalSubscribers: result.sentCount + result.failedCount
        }
      });
    } else {
      console.error('Blog notification webhook failed:', result.errors);
      
      // Return 200 to prevent Sanity from retrying, but log the failure
      res.status(200).json({
        message: 'Webhook received but notifications failed',
        success: false,
        details: {
          blogTitle: payload.title,
          blogSlug: payload.slug.current,
          sentCount: result.sentCount,
          failedCount: result.failedCount,
          errors: result.errors.slice(0, 5) // Limit error details
        }
      });
    }

  } catch (error) {
    console.error('Blog notification webhook error:', getErrorMessage(error));
    
    // Return 200 to prevent Sanity from retrying on server errors
    res.status(200).json({ 
      message: 'Webhook received but processing failed',
      success: false,
      error: process.env.NODE_ENV === 'development' ? getErrorMessage(error) : 'Internal server error'
    });
  }
}

// Disable body parsing to handle raw webhook data if needed
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
