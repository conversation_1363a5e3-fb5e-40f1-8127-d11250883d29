// pages/[slug].tsx - Updated to match LocalSEO structure
import React from 'react'
import { GetStaticProps, GetStaticPaths } from 'next'
import Head from 'next/head'
import Link from 'next/link'
import Header from '@/components/global/Header'
import Footer from '@/components/global/Footer'
import { getSubService, getSubServiceSlugs, urlForImage } from '@/lib/sanity'
import { SubServiceData } from '@/types/subService'
import {
  SubServiceHero,
  ServiceDetails,
  ServiceProcess,
  //CaseStudy,
  ServiceCTA,
  //Testimonials,
  FAQ,
  FooterCTA
} from '@/components/services/sub-services'

interface SubServicePageProps {
  subService: SubServiceData
}

const SubServicePage: React.FC<SubServicePageProps> = ({ subService }) => {
  const seo = subService.seo || {}
  const pageTitle = seo.metaTitle || `${subService.title} | Vesa Solutions`
  const pageDescription = seo.metaDescription || `Professional ${subService.title} services to grow your business.`
  const canonicalUrl = `https://vesasolutions.com/${subService.slug?.current || ''}`
  const ogImageUrl = seo.ogImage ? urlForImage(seo.ogImage).width(1200).height(630).url() : null

  return (
    <>
      <Head>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        {seo.keywords && seo.keywords.length > 0 && (
          <meta name="keywords" content={seo.keywords.join(', ')} />
        )}
        
        {/* Open Graph */}
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={canonicalUrl} />
        {ogImageUrl && <meta property="og:image" content={ogImageUrl} />}
        
        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={pageTitle} />
        <meta name="twitter:description" content={pageDescription} />
        {ogImageUrl && <meta name="twitter:image" content={ogImageUrl} />}
        
        {/* Canonical URL */}
        <link rel="canonical" href={canonicalUrl} />
        
        {/* Additional SEO */}
        <meta name="robots" content="index, follow" />
        <meta name="author" content="Vesa Solutions" />
      </Head>

      <Header isVisible={true} />

      {/* Breadcrumbs */}
      <div className="bg-gray-50 py-4">
        <div className="max-w-7xl mx-auto px-6">
          <nav className="text-sm" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              <li>
                <Link href="/" className="text-gray-500 hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li>
                <Link href="/services" className="text-gray-500 hover:text-blue-600 transition-colors">
                  Services
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              {subService.parentService && (
                <li>
                  <Link
                    href={`/${subService.parentService === 'seo' ? 'seo-search-engine-optimization' :
                             subService.parentService === 'web-development' ? 'web-development' :
                             subService.parentService === 'digital-marketing' ? 'digital-marketing' :
                             subService.parentService}`}
                    className="text-gray-500 hover:text-blue-600 transition-colors"
                  >
                    {subService.parentService === 'seo' ? 'SEO Search Engine Optimization' :
                     subService.parentService === 'web-development' ? 'Website Development' :
                     subService.parentService === 'digital-marketing' ? 'Digital Marketing' :
                     subService.parentService}
                  </Link>
                </li>
              )}
              {subService.parentService && <li className="text-gray-400">/</li>}
              <li className="text-gray-400">/</li>
              <li className="text-gray-900 font-medium" aria-current="page">
                {subService.title}
              </li>
            </ol>
          </nav>
        </div>
      </div>

      <main className="min-h-screen bg-white">
        {/* 1. Hero Section */}
        {subService.hero ? (
          <SubServiceHero data={subService.hero} />
        ) : (
          <DefaultHero title={subService.title} />
        )}

        {/* 2. CTA Section */}
        {subService.cta && (
          <ServiceCTA
            data={subService.cta}
            serviceName={subService.title}
          />
        )}

        {/* 3. ServiceDetails - Combined 4 Sections */}
        <ServiceDetails
          whyMatters={subService.whyMatters}
          services={subService.services}
          strategicImplementation={subService.strategicImplementation}
          marketIntelligence={subService.marketIntelligence}
        />

        {/* 4. FooterCTA - Moved above ServiceProcess */}
        {subService.footerCta && (
          <FooterCTA data={subService.footerCta} />
        )}

        {/* 5. ServiceProcess - Combined 2 Sections */}
        <>
          {subService.process && (
            <ServiceProcess data={subService.process} />
          )}
          {/* {subService.caseStudy && (
            <CaseStudy data={subService.caseStudy} />
          )} */}
        </>

        {/* 6. Testimonials - Combined 2 Sections */}
        <>
          {/* {subService.testimonials && subService.testimonials.length > 0 && (
            <Testimonials data={subService.testimonials} />
          )} */}
          {subService.faqs && subService.faqs.length > 0 && (
            <FAQ data={subService.faqs} />
          )}
        </>
      </main>

      <Footer />
    </>
  )
}

// Default hero component for fallback
const DefaultHero: React.FC<{ title: string }> = ({ title }) => (
  <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20">
    <div className="container mx-auto px-4 text-center text-white">
      <h1 className="text-4xl md:text-5xl font-bold mb-6">{title}</h1>
      <p className="text-xl opacity-90 max-w-2xl mx-auto">
        Professional {title.toLowerCase()} services to help grow your business.
      </p>
    </div>
  </section>
)

export const getStaticPaths: GetStaticPaths = async () => {
  try {
    const slugs = await getSubServiceSlugs()
    
    if (!slugs || slugs.length === 0) {
      console.warn('⚠️ No service slugs found in getStaticPaths')
      return { 
        paths: [], 
        fallback: 'blocking' 
      }
    }
    
    // Filter out any invalid slugs
    const validSlugs = slugs.filter((item: { slug: string }) => 
      item.slug && typeof item.slug === 'string' && item.slug.length > 0
    )
    
    const paths = validSlugs.map((item: { slug: string }) => ({
      params: { slug: item.slug }
    }))

    console.log(`✅ Generated ${paths.length} service paths`)
    
    return {
      paths,
      fallback: 'blocking'
    }
  } catch (error) {
    console.error('❌ Error in getStaticPaths:', error)
    return {
      paths: [],
      fallback: 'blocking'
    }
  }
}

export const getStaticProps: GetStaticProps = async (context) => {
  const { params, preview = false } = context
  const slug = params?.slug as string
  
  console.log(`🔍 getStaticProps called for slug: ${slug}`)
  
  if (!slug || typeof slug !== 'string') {
    console.log('❌ Invalid or missing slug')
    return { notFound: true }
  }

  try {
    const subService = await getSubService(slug)
    
    if (!subService) {
      console.log(`❌ SubService not found for slug: ${slug}`)
      return { notFound: true }
    }

    // Validate essential data structure
    if (!subService.title || !subService.slug?.current) {
      console.error(`❌ Invalid subService data structure for slug: ${slug}`)
      return { notFound: true }
    }

    console.log(`✅ Successfully loaded service: ${subService.title}`)

    return {
      props: { 
        subService 
      },
      // Only use revalidate in production to prevent ISR manifest HMR errors
      ...(process.env.NODE_ENV === 'production' && { 
        revalidate: preview ? 1 : 60 
      }),
    }
  } catch (error) {
    console.error(`❌ Error fetching subService for slug ${slug}:`, error)
    return { notFound: true }
  }
}

export default SubServicePage