# CTA and Trust Signals Cleanup Migration

This migration removes the following fields from sub-service documents:

## Changes Made

### 1. ServiceCTA Component
- **Removed**: Phone number button from the CTA section
- **Removed**: `phoneNumber` field from the CTA schema

### 2. ServiceDetails Component  
- **Removed**: "Learn About Our On-Page Strategy" button (ctaButton in WhyServiceMatters)
- **Updated**: "Analyze Your On-Page Opportunities" button now links to `/contact` page (ctaButton in MarketIntelligence)

### 3. FooterCTA Component
- **Removed**: Trust signals section completely (ratings, logos, etc.)
- **Removed**: `trustSignals` field from the FooterCTA schema

### 4. Page Layout
- **Moved**: FooterCTA section now appears above ServiceProcess section instead of at the bottom

## Files Updated

### Components
- `vesaweb/components/services/sub-services/ServiceCTA.tsx`
- `vesaweb/components/services/sub-services/ServiceDetails.tsx`
- `vesaweb/components/services/sub-services/FooterCTA.tsx`
- `vesaweb/pages/[slug].tsx`

### Schema & Types
- `vesasanity/schemaTypes/subservice.ts`
- `vesaweb/types/subService.ts`
- `vesaweb/lib/sanity.ts`

## Running the Migration

To clean up existing data in Sanity:

1. Update the project ID in `cleanup-cta-trust-signals.js`
2. Set your SANITY_API_TOKEN environment variable
3. Run: `node cleanup-cta-trust-signals.js`

This will remove the `phoneNumber` from CTA and `trustSignals` from FooterCTA in all existing sub-service documents.

## Result

After this migration:
- CTA sections will only show the contact form (no phone button)
- WhyServiceMatters sections will not have CTA buttons
- MarketIntelligence CTA buttons will link to the contact page
- FooterCTA sections will not show trust signals/ratings
- FooterCTA appears before the process section instead of at the end
