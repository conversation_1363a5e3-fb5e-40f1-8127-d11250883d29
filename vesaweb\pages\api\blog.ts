// pages/api/blog.ts - API route for blog data fetching
import { NextApiRequest, NextApiResponse } from 'next'
import { getBlogPosts } from '@/lib/sanity-blog'
import { BlogFilters } from '@/types/blog'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { category, tag, search, page = '1', limit = '12' } = req.query

    const filters: BlogFilters = {
      category: category as string,
      tag: tag as string,
      search: search as string,
      page: parseInt(page as string),
      limit: parseInt(limit as string)
    }

    // Remove undefined values
    Object.keys(filters).forEach(key => {
      if (filters[key as keyof BlogFilters] === undefined) {
        delete filters[key as keyof BlogFilters]
      }
    })

    const data = await getBlogPosts(filters)

    res.status(200).json(data)
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
}
