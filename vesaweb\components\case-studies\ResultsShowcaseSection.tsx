import React from 'react';
import { TrendingUp, Users, DollarSign, MousePointerClick, Globe, ShoppingCart } from 'lucide-react';

const ResultsShowcaseSection: React.FC = () => {
  const resultCategories = [
    {
      title: 'Revenue & Sales Growth',
      icon: DollarSign,
      color: 'green',
      metrics: [
        { value: '450%', label: 'Revenue Increase', description: 'E-commerce Fashion Client' },
        { value: '380%', label: 'Sales Growth', description: 'Real Estate Agency' },
        { value: '320%', label: 'Online Orders', description: 'Restaurant Chain' },
        { value: '290%', label: 'Conversion Rate', description: 'SaaS Platform' }
      ]
    },
    {
      title: 'Traffic & Visibility',
      icon: TrendingUp,
      color: 'blue',
      metrics: [
        { value: '340%', label: 'Organic Traffic', description: 'Technology Startup' },
        { value: '280%', label: 'Search Visibility', description: 'Healthcare Practice' },
        { value: '350%', label: 'Local Rankings', description: 'Professional Services' },
        { value: '250%', label: 'Brand Awareness', description: 'Manufacturing Company' }
      ]
    },
    {
      title: 'Lead Generation',
      icon: Users,
      color: 'purple',
      metrics: [
        { value: '400%', label: 'Qualified Leads', description: 'Law Firm' },
        { value: '340%', label: 'Lead Quality', description: 'B2B Software' },
        { value: '380%', label: 'Contact Forms', description: 'Consulting Firm' },
        { value: '290%', label: 'Phone Inquiries', description: 'Home Services' }
      ]
    },
    {
      title: 'Cost Efficiency',
      icon: MousePointerClick,
      color: 'orange',
      metrics: [
        { value: '-85%', label: 'Cost Per Lead', description: 'Technology Company' },
        { value: '-70%', label: 'Customer Acquisition Cost', description: 'E-commerce Store' },
        { value: '-60%', label: 'Marketing Spend', description: 'Professional Services' },
        { value: '-75%', label: 'PPC Costs', description: 'Healthcare Provider' }
      ]
    }
  ];

  const overallStats = [
    { icon: TrendingUp, value: '300%', label: 'Average ROI', description: 'Across all clients' },
    { icon: Users, value: '500+', label: 'Successful Projects', description: 'Completed to date' },
    { icon: Globe, value: '15+', label: 'Industries', description: 'Served worldwide' },
    { icon: ShoppingCart, value: '98%', label: 'Client Satisfaction', description: 'Retention rate' }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      green: { bg: 'bg-green-50', text: 'text-green-600', border: 'border-green-200' },
      blue: { bg: 'bg-blue-50', text: 'text-blue-600', border: 'border-blue-200' },
      purple: { bg: 'bg-purple-50', text: 'text-purple-600', border: 'border-purple-200' },
      orange: { bg: 'bg-orange-50', text: 'text-orange-600', border: 'border-orange-200' }
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Results That Speak for Themselves
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Numbers don&apos;t lie. Our data-driven approach consistently delivers exceptional results across all key performance indicators that matter to your business.
          </p>
        </div>

        {/* Results Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20">
          {resultCategories.map((category, index) => {
            const IconComponent = category.icon;
            const colors = getColorClasses(category.color);
            
            return (
              <div key={index} className="bg-white rounded-3xl p-8 shadow-lg">
                <div className="flex items-center mb-8">
                  <div className={`${colors.bg} p-4 rounded-2xl mr-4`}>
                    <IconComponent className={colors.text} size={32} />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800">{category.title}</h3>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {category.metrics.map((metric, idx) => (
                    <div key={idx} className={`${colors.bg} ${colors.border} border rounded-2xl p-6 text-center`}>
                      <div className={`text-3xl font-bold ${colors.text} mb-2`}>
                        {metric.value}
                      </div>
                      <div className="font-semibold text-gray-800 mb-1">
                        {metric.label}
                      </div>
                      <div className="text-sm text-gray-600">
                        {metric.description}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>

        {/* Overall Performance Stats */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-12 text-white mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold mb-4">
              Overall Performance Metrics
            </h3>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Our track record of success spans across industries, company sizes, and marketing objectives.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {overallStats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div key={index} className="text-center">
                  <div className="bg-blue-500 p-4 rounded-2xl inline-block mb-4">
                    <IconComponent className="text-white" size={32} />
                  </div>
                  <div className="text-4xl md:text-5xl font-bold text-blue-200 mb-2">
                    {stat.value}
                  </div>
                  <div className="text-xl font-semibold text-white mb-2">
                    {stat.label}
                  </div>
                  <div className="text-blue-100">
                    {stat.description}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Results Timeline */}
        <div className="bg-white rounded-3xl p-12 shadow-lg">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Typical Results Timeline
            </h3>
            <p className="text-xl text-gray-600">
              See how our strategies deliver progressive improvements over time
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">1</span>
              </div>
              <h4 className="text-xl font-bold text-gray-800 mb-2">Month 1-3</h4>
              <p className="text-gray-600 mb-4">Foundation & Setup</p>
              <div className="bg-blue-50 p-4 rounded-xl">
                <div className="text-2xl font-bold text-blue-600">25-50%</div>
                <div className="text-sm text-gray-600">Initial Improvements</div>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">2</span>
              </div>
              <h4 className="text-xl font-bold text-gray-800 mb-2">Month 4-6</h4>
              <p className="text-gray-600 mb-4">Momentum Building</p>
              <div className="bg-green-50 p-4 rounded-xl">
                <div className="text-2xl font-bold text-green-600">100-150%</div>
                <div className="text-sm text-gray-600">Significant Growth</div>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">3</span>
              </div>
              <h4 className="text-xl font-bold text-gray-800 mb-2">Month 7-12</h4>
              <p className="text-gray-600 mb-4">Acceleration Phase</p>
              <div className="bg-purple-50 p-4 rounded-xl">
                <div className="text-2xl font-bold text-purple-600">200-300%</div>
                <div className="text-sm text-gray-600">Exponential Results</div>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-orange-600">4</span>
              </div>
              <h4 className="text-xl font-bold text-gray-800 mb-2">Year 2+</h4>
              <p className="text-gray-600 mb-4">Sustained Excellence</p>
              <div className="bg-orange-50 p-4 rounded-xl">
                <div className="text-2xl font-bold text-orange-600">400%+</div>
                <div className="text-sm text-gray-600">Market Leadership</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ResultsShowcaseSection;
