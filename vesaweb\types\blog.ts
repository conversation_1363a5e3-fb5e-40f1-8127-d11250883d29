// types/blog.ts - TypeScript types for VESA blog system
export interface SanityImageObject {
  _type: 'image';
  asset: {
    _ref: string;
    _type: 'reference';
  };
  alt?: string;
  caption?: string;
}

export interface BlogCategory {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
  description?: string;
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'teal';
}

export interface BlogTag {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
  description?: string;
}



export interface BlogPost {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
  excerpt: string;
  featuredImage?: SanityImageObject;
  content: any[]; // Portable Text content
  publishedAt: string;
  categories: BlogCategory[];
  tags?: BlogTag[];
  featured: boolean;
  readingTime?: number;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
    ogImage?: SanityImageObject;
  };
}

export interface BlogListItem {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
  excerpt: string;
  featuredImage?: SanityImageObject;
  publishedAt: string;
  categories: BlogCategory[];
  tags?: BlogTag[];
  featured: boolean;
  readingTime?: number;
}

export interface BlogArchiveData {
  posts: BlogListItem[];
  categories: BlogCategory[];
  tags: BlogTag[];
  totalPosts: number;
  hasMore: boolean;
}

export interface BlogFilters {
  category?: string;
  tag?: string;
  search?: string;
  page?: number;
  limit?: number;
}
