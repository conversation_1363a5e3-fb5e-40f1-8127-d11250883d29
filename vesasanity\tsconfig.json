{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "Preserve", "moduleDetection": "force", "isolatedModules": true, "jsx": "preserve", "incremental": true}, "include": ["**/*.ts", "**/*.tsx", "migrations/local-seo-migration.js", "schemaTypes/index.tss", "schemaTypes/index.tss"], "exclude": ["node_modules"]}