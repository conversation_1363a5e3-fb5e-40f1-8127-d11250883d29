// migrations/blog-migration.js - Blog system demo data migration
const { createClient } = require('@sanity/client')

const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

// Blog Categories Data
const blogCategoriesData = [
  {
    _type: 'blogCategory',
    title: 'SEO & Search Marketing',
    slug: {
      _type: 'slug',
      current: 'seo-search-marketing'
    },
    description: 'Search engine optimization strategies, tips, and industry updates.',
    color: 'blue'
  },
  {
    _type: 'blogCategory',
    title: 'Web Development',
    slug: {
      _type: 'slug',
      current: 'web-development'
    },
    description: 'Website development, design trends, and technical insights.',
    color: 'green'
  },
  {
    _type: 'blogCategory',
    title: 'Digital Marketing',
    slug: {
      _type: 'slug',
      current: 'digital-marketing'
    },
    description: 'Digital marketing strategies, campaigns, and best practices.',
    color: 'purple'
  },
  {
    _type: 'blogCategory',
    title: 'PPC & Advertising',
    slug: {
      _type: 'slug',
      current: 'ppc-advertising'
    },
    description: 'Pay-per-click advertising, Google Ads, and paid marketing strategies.',
    color: 'orange'
  },
  {
    _type: 'blogCategory',
    title: 'Social Media',
    slug: {
      _type: 'slug',
      current: 'social-media'
    },
    description: 'Social media marketing, content strategies, and platform updates.',
    color: 'teal'
  },
  {
    _type: 'blogCategory',
    title: 'Analytics & Data',
    slug: {
      _type: 'slug',
      current: 'analytics-data'
    },
    description: 'Web analytics, data analysis, and performance measurement.',
    color: 'red'
  }
]

// Blog Tags Data
const blogTagsData = [
  {
    _type: 'blogTag',
    title: 'Google Algorithm',
    slug: { _type: 'slug', current: 'google-algorithm' },
    description: 'Updates and changes to Google search algorithms'
  },
  {
    _type: 'blogTag',
    title: 'Local SEO',
    slug: { _type: 'slug', current: 'local-seo' },
    description: 'Local search optimization strategies'
  },
  {
    _type: 'blogTag',
    title: 'Content Marketing',
    slug: { _type: 'slug', current: 'content-marketing' },
    description: 'Content creation and marketing strategies'
  },
  {
    _type: 'blogTag',
    title: 'E-commerce',
    slug: { _type: 'slug', current: 'ecommerce' },
    description: 'E-commerce marketing and optimization'
  },
  {
    _type: 'blogTag',
    title: 'Mobile Optimization',
    slug: { _type: 'slug', current: 'mobile-optimization' },
    description: 'Mobile-first design and optimization'
  },
  {
    _type: 'blogTag',
    title: 'Conversion Rate',
    slug: { _type: 'slug', current: 'conversion-rate' },
    description: 'Conversion rate optimization techniques'
  },
  {
    _type: 'blogTag',
    title: 'Google Ads',
    slug: { _type: 'slug', current: 'google-ads' },
    description: 'Google Ads strategies and tips'
  },
  {
    _type: 'blogTag',
    title: 'Facebook Ads',
    slug: { _type: 'slug', current: 'facebook-ads' },
    description: 'Facebook advertising strategies'
  },
  {
    _type: 'blogTag',
    title: 'WordPress',
    slug: { _type: 'slug', current: 'wordpress' },
    description: 'WordPress development and optimization'
  },
  {
    _type: 'blogTag',
    title: 'Technical SEO',
    slug: { _type: 'slug', current: 'technical-seo' },
    description: 'Technical aspects of search optimization'
  }
]





// Helper function to create references
function createReference(id) {
  return {
    _type: 'reference',
    _ref: id
  }
}

// Sample blog posts data
const createBlogPostsData = (categories, tags) => [
  {
    _type: 'blogPost',
    title: 'The Complete Guide to Local SEO in 2024: Dominate Your Local Market',
    slug: {
      _type: 'slug',
      current: 'complete-guide-local-seo-2024'
    },
    excerpt: 'Master local SEO with our comprehensive 2024 guide. Learn proven strategies to rank higher in local search results and attract more customers to your business.',
    content: [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'Local SEO has become more crucial than ever for businesses looking to attract customers in their immediate vicinity. With over 46% of all Google searches having local intent, optimizing your online presence for local search is no longer optional—it\'s essential.'
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: 'Why Local SEO Matters More Than Ever'
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'The digital landscape has shifted dramatically, especially post-pandemic. Consumers are increasingly using their mobile devices to find local businesses, read reviews, and make purchasing decisions. This shift has made local SEO a critical component of any successful digital marketing strategy.'
          }
        ]
      }
    ],

    publishedAt: '2024-01-15T10:00:00Z',
    categories: [createReference(categories[0]._id)],
    tags: [createReference(tags[1]._id), createReference(tags[9]._id)],
    featured: true,
    readingTime: 12,
    seo: {
      metaTitle: 'Complete Local SEO Guide 2024 | VESA Solutions',
      metaDescription: 'Master local SEO with our comprehensive guide. Learn strategies to rank higher in local search and attract more customers.',
      keywords: ['local seo', 'local search optimization', 'google my business', 'local rankings']
    }
  },
  {
    _type: 'blogPost',
    title: 'Mobile-First Web Design: Best Practices for 2024',
    slug: {
      _type: 'slug',
      current: 'mobile-first-web-design-best-practices-2024'
    },
    excerpt: 'Discover the latest mobile-first design principles and best practices to create websites that perform exceptionally on all devices.',
    content: [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'With mobile traffic accounting for over 54% of global website traffic, mobile-first design isn\'t just a trend—it\'s a necessity. This approach prioritizes the mobile experience from the ground up, ensuring your website performs optimally on smaller screens before scaling up to desktop.'
          }
        ]
      }
    ],

    publishedAt: '2024-01-10T14:30:00Z',
    categories: [createReference(categories[1]._id)],
    tags: [createReference(tags[4]._id), createReference(tags[8]._id)],
    featured: true,
    readingTime: 8,
    seo: {
      metaTitle: 'Mobile-First Web Design Best Practices 2024',
      metaDescription: 'Learn mobile-first design principles and best practices for creating responsive websites that excel on all devices.',
      keywords: ['mobile-first design', 'responsive web design', 'mobile optimization', 'web development']
    }
  },
  {
    _type: 'blogPost',
    title: 'Google Ads vs Facebook Ads: Which Platform Delivers Better ROI?',
    slug: {
      _type: 'slug',
      current: 'google-ads-vs-facebook-ads-roi-comparison'
    },
    excerpt: 'Compare Google Ads and Facebook Ads to determine which platform offers better ROI for your business goals and target audience.',
    content: [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'Choosing between Google Ads and Facebook Ads can be challenging for businesses looking to maximize their advertising ROI. Both platforms offer unique advantages and cater to different stages of the customer journey.'
          }
        ]
      }
    ],

    publishedAt: '2024-01-08T09:15:00Z',
    categories: [createReference(categories[3]._id)],
    tags: [createReference(tags[6]._id), createReference(tags[7]._id)],
    featured: true,
    readingTime: 10,
    seo: {
      metaTitle: 'Google Ads vs Facebook Ads: ROI Comparison 2024',
      metaDescription: 'Compare Google Ads and Facebook Ads ROI. Learn which platform delivers better results for your business goals.',
      keywords: ['google ads', 'facebook ads', 'ppc advertising', 'advertising roi']
    }
  }
]

async function createBlogData() {
  console.log('🚀 Starting blog data migration...')

  try {
    // Create categories first
    console.log('📁 Creating blog categories...')
    const createdCategories = []
    for (const category of blogCategoriesData) {
      const result = await client.create(category)
      createdCategories.push(result)
      console.log(`✅ Created category: ${category.title}`)
    }

    // Create tags
    console.log('🏷️ Creating blog tags...')
    const createdTags = []
    for (const tag of blogTagsData) {
      const result = await client.create(tag)
      createdTags.push(result)
      console.log(`✅ Created tag: ${tag.title}`)
    }

    // Create blog posts
    console.log('📝 Creating blog posts...')
    const blogPostsData = createBlogPostsData(createdCategories, createdTags)
    const createdPosts = []

    for (const post of blogPostsData) {
      const result = await client.create(post)
      createdPosts.push(result)
      console.log(`✅ Created blog post: ${post.title}`)
    }

    console.log('✅ Blog data migration completed successfully!')
    console.log(`📊 Created: ${createdCategories.length} categories, ${createdTags.length} tags, ${createdPosts.length} blog posts`)

    return {
      categories: createdCategories,
      tags: createdTags,
      posts: createdPosts
    }

  } catch (error) {
    console.error('❌ Error during blog data migration:', error)
    throw error
  }
}

// Run migration if called directly
if (require.main === module) {
  createBlogData()
    .then(() => {
      console.log('🎉 Blog data migration completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error)
      process.exit(1)
    })
}

module.exports = { createBlogData }
