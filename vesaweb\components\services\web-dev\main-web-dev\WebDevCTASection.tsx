import React from 'react';
import { Phone, Mail } from 'lucide-react';

const WebDevCTASection: React.FC = () => {
  return (
    <section className="py-24 bg-gradient-to-br from-blue-50 to-blue-100">
      <div className="max-w-4xl mx-auto px-6 text-center">
        <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
          Ready to Build Your Dream Website?
        </h2>
        <p className="text-xl text-gray-600 mb-12 leading-relaxed">
          Let&apos;s discuss your vision and create a website that not only looks amazing but drives real business results. Our team is ready to bring your ideas to life.
        </p>

        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
          <a
            href="tel:+***********"
            className="group bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-xl shadow-blue-500/25 inline-flex items-center justify-center text-lg"
          >
            <Phone className="mr-3" size={24} />
            Get Started Today
          </a>

          <a
            href="mailto:<EMAIL>"
            className="group bg-white hover:bg-gray-50 text-blue-600 font-bold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-xl border-2 border-blue-500 hover:border-blue-600 inline-flex items-center justify-center text-lg"
          >
            <Mail className="mr-3" size={24} />
            Discuss Your Project
          </a>
        </div>
        
        <p className="text-gray-500 mt-8 text-sm">
          Free consultation • Custom solutions • Professional results guaranteed
        </p>
      </div>
    </section>
  );
};

export default WebDevCTASection;
