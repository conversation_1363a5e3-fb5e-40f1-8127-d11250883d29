import React, { useState } from 'react';
import { ChevronRight } from 'lucide-react';
import { Slide } from '@/types';

interface DigitalStrategySliderSectionProps {
  className?: string;
}

const DigitalStrategySliderSection: React.FC<DigitalStrategySliderSectionProps> = ({ className = '' }) => {
  const [activeSlide, setActiveSlide] = useState<number>(0);

  const getSlideImage = (index: number): string => {
    const images = [
      '/homepage/understanding your business.jpg',
      '/homepage/developing custom digital marketing plan.png',
      '/homepage/placing digital presence with precision.png',
      '/homepage/optimizing based off search results.png'
    ];
    return images[index] || '/api/placeholder/800/600';
  };

  const slides: Slide[] = [
    {
      number: "01",
      topTitle: "THE FOUNDATION",
      title: "Understanding Your Business to Build a Tailored Strategy",
      content: [
        "To lay the groundwork for success, we need to understand the intricacies of your business. This step allows us to understand the challenges so we can pave a clear path forward. From that point on, you can consider us your most devoted groupie (but please don't sing to us).",
        "Then comes the idea; the piece that will turn heads and spark conversation. We embrace the notion that not every idea will work, and that's okay!",
        "Even though it may be a stellar concept, if it doesn't align with your brand, we will put our egos aside and find an idea that's a perfect match."
      ],
      imageDesc: "Business team discussing strategy in a modern meeting room, with hands raised in agreement"
    },
    {
      number: "02",
      topTitle: "THE STRATEGY",
      title: "Developing Your Custom Digital Marketing Plan",
      content: [
        "With a deep understanding of your business objectives, we craft a comprehensive digital strategy that aligns with your goals and resonates with your target audience.",
        "Our approach combines data-driven insights with creative excellence to identify the most effective channels, messaging, and tactics for your brand.",
        "We establish clear KPIs and success metrics that will guide our efforts and allow us to measure the impact of our strategic initiatives."
      ],
      imageDesc: "Digital marketing specialists collaborating at whiteboard with diagrams and flowcharts"
    },
    {
      number: "03",
      topTitle: "THE EXECUTION",
      title: "Implementing Your Digital Presence with Precision",
      content: [
        "This is where plans transform into action. Our team of specialists deploys your custom strategy across all relevant digital platforms with meticulous attention to detail.",
        "From content creation and website optimization to paid campaigns and social media management, we execute each element with precision and care.",
        "Throughout the implementation phase, we maintain transparent communication and provide regular updates on progress and initial results."
      ],
      imageDesc: "Team working on implementation with multiple screens showing website designs and analytics"
    },
    {
      number: "04",
      topTitle: "THE REFINEMENT",
      title: "Optimizing Your Strategy Based on Real Results",
      content: [
        "Digital marketing is never static—and neither are we. We continuously analyze performance data to identify opportunities for optimization and growth.",
        "Through A/B testing, user feedback, and performance analytics, we refine your strategy to enhance results and maximize your return on investment.",
        "Our iterative approach ensures that your digital presence evolves with market trends, consumer behavior, and your business objectives."
      ],
      imageDesc: "Data analysis meeting with growth charts and performance metrics on screens"
    }
  ];

  const handleNext = (): void => {
    setActiveSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
  };

  const handleDotClick = (index: number): void => {
    setActiveSlide(index); 
  };

  return (
    <section className={`w-full bg-white min-h-screen flex flex-col ${className}`}>
      {/* Section Header - Centered */}
      <div className="container mx-auto px-4 sm:px-6 text-center pt-8 sm:pt-16 pb-6 sm:pb-8">
        <p className="text-blue-700 font-medium text-base sm:text-lg mb-3">TAILORED DIGITAL STRATEGY FOR SUCCESS</p>
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 max-w-4xl mx-auto">
          BUILDING A DIGITAL PRESENCE STRATEGY TAILORED TO YOUR BUSINESS
        </h2>
      </div>
      
      {/* Slider Content */}
      <div className="flex-grow container mx-auto px-4 sm:px-6 lg:px-12 relative flex items-center">
        {slides.map((slide, index) => (
          <div 
            key={index} 
            className={`w-full transition-opacity duration-500 ${activeSlide === index ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}
            style={{ display: activeSlide === index ? 'block' : 'none' }}
          >
            <div className="relative flex flex-col md:flex-row gap-6 sm:gap-10 lg:gap-16 items-center">
              {/* Left Column - Image */}
              <div className="relative w-full md:w-1/2 order-2 md:order-1">
                {/* Main Image Container */}
                <div className="relative rounded-lg overflow-hidden shadow-xl">
                  {/* Business Meeting Image */}
                  <div className="relative pt-[75%]"> {/* 4:3 aspect ratio */}
                    <div
                      className="absolute inset-0 bg-cover bg-center"
                      style={{
                        backgroundImage: `url('${getSlideImage(index)}')`,
                      }}
                      role="img"
                      aria-label={slide.imageDesc}
                    >
                    </div>
                  </div>
                  
                  {/* World Map Background */}
                  <div className="absolute bottom-0 left-0 right-0 h-32 sm:h-48 overflow-hidden">
                    <div className="absolute inset-0 bg-blue-50 opacity-30">
                      <svg viewBox="0 0 1200 600" width="100%" height="100%" preserveAspectRatio="none" className="opacity-50">
                        <path d="M300,150 Q400,100 500,150 T700,120 T900,150" fill="none" stroke="#d0d0d0" strokeWidth="1"/>
                        <path d="M200,200 Q300,180 400,200 T600,190 T800,210" fill="none" stroke="#d0d0d0" strokeWidth="1"/>
                        <path d="M250,250 Q350,230 450,250 T650,240 T850,260" fill="none" stroke="#d0d0d0" strokeWidth="1"/>
                        <path d="M150,120 L250,120 L300,170 L200,190 Z" fill="#e6e6e6" stroke="#d0d0d0" strokeWidth="0.5"/>
                        <path d="M400,140 L500,130 L550,180 L430,200 Z" fill="#e6e6e6" stroke="#d0d0d0" strokeWidth="0.5"/>
                        <path d="M700,130 L800,120 L820,160 L730,190 Z" fill="#e6e6e6" stroke="#d0d0d0" strokeWidth="0.5"/>
                        <path d="M300,220 L370,210 L390,240 L320,260 Z" fill="#e6e6e6" stroke="#d0d0d0" strokeWidth="0.5"/>
                        <path d="M600,220 L670,210 L690,250 L620,270 Z" fill="#e6e6e6" stroke="#d0d0d0" strokeWidth="0.5"/>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Large Number - Responsive positioning */}
              <div className="absolute md:right-1/2 top-0 md:top-0 z-10 md:transform md:translate-x-12 md:-translate-y-24 right-4 sm:right-8">
                <span
                  className="text-[120px] sm:text-[200px] md:text-[280px] font-black leading-none"
                  style={{
                    color: 'rgba(59, 130, 246, 0.15)',
                    WebkitTextStroke: '2px rgba(37, 99, 235, 0.3)',
                    WebkitTextFillColor: 'rgba(59, 130, 246, 0.08)',
                    textShadow: '0 0 20px rgba(59, 130, 246, 0.1)'
                  }}
                >
                  {slide.number}
                </span>
              </div>
              
              {/* Right Column - Content */}
              <div className="w-full md:w-1/2 order-1 md:order-2">
                <p className="text-blue-700 font-medium text-lg sm:text-xl mb-3">{slide.topTitle}</p>
                <h3 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-4 sm:mb-6">{slide.title}</h3>
                
                <div className="space-y-4 sm:space-y-5">
                  {slide.content.map((paragraph, i) => (
                    <p key={i} className="text-gray-700 text-base sm:text-lg">{paragraph}</p>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {/* Navigation Dots - Responsive positioning */}
        <div className="absolute left-2 sm:left-0 top-1/2 transform -translate-y-1/2 flex flex-col space-y-4 sm:space-y-6 z-30">
          {slides.map((_, index) => (
            <button 
              key={index} 
              onClick={() => handleDotClick(index)}
              className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-colors ${activeSlide === index ? 'bg-blue-500' : 'bg-gray-300'}`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
        
        {/* Right Arrow - Responsive positioning */}
        <div className="absolute right-2 sm:-right-4 top-1/2 transform -translate-y-1/2 z-30">
          <button 
            onClick={handleNext}
            className="w-8 h-8 sm:w-10 sm:h-10 bg-blue-500 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-blue-600 transition-colors"
            aria-label="Next slide"
          >
            <ChevronRight size={16} className="sm:w-5 sm:h-5" />
          </button>
        </div>
      </div>
      
      {/* Bottom padding area */}
      <div className="py-4 sm:py-8"></div>
    </section>
  );
};
export default DigitalStrategySliderSection;