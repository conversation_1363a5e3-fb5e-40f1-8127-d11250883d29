import React from 'react';
import Image from 'next/image';
import { Star, Quote } from 'lucide-react';

const CaseStudiesTestimonialsSection: React.FC = () => {
  const testimonials = [
    {
      id: 1,
      quote: "VESA Solutions completely transformed our online presence. The 450% revenue growth we achieved in just 12 months exceeded our wildest expectations. Their strategic approach and attention to detail is unmatched.",
      author: "<PERSON>",
      position: "CEO",
      company: "StyleHub Fashion",
      industry: "E-commerce",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=80&h=80&fit=crop&crop=face",
      rating: 5,
      result: "450% Revenue Growth",
      logo: "https://via.placeholder.com/120x40/4285f4/ffffff?text=StyleHub"
    },
    {
      id: 2,
      quote: "The ROI from our SEO investment has been incredible. We went from struggling to get leads to becoming the go-to solution in our niche. VESA's expertise in B2B marketing is exceptional.",
      author: "<PERSON>",
      position: "Founder & CTO",
      company: "TechFlow Solutions",
      industry: "Technology",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face",
      rating: 5,
      result: "340% Lead Increase",
      logo: "https://via.placeholder.com/120x40/10b981/ffffff?text=TechFlow"
    },
    {
      id: 3,
      quote: "Our online presence went from virtually non-existent to dominating local search results. The 250% increase in online orders has revolutionized our business model.",
      author: "David Rodriguez",
      position: "Owner",
      company: "Bella Vista Restaurants",
      industry: "Food & Beverage",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face",
      rating: 5,
      result: "250% Online Orders",
      logo: "https://via.placeholder.com/120x40/f59e0b/ffffff?text=Bella+Vista"
    },
    {
      id: 4,
      quote: "The team's understanding of healthcare marketing regulations while delivering outstanding results is impressive. We've seen a 280% increase in new patient inquiries.",
      author: "Dr. Emily Watson",
      position: "Medical Director",
      company: "Wellness Medical Center",
      industry: "Healthcare",
      image: "https://images.unsplash.com/photo-**********-2b71ea197ec2?w=80&h=80&fit=crop&crop=face",
      rating: 5,
      result: "280% New Patients",
      logo: "https://via.placeholder.com/120x40/ef4444/ffffff?text=Wellness"
    },
    {
      id: 5,
      quote: "VESA helped us generate 400% more qualified leads while reducing our cost per acquisition by 70%. Their data-driven approach delivers real business value.",
      author: "Robert Johnson",
      position: "Managing Partner",
      company: "Johnson & Associates Law",
      industry: "Legal Services",
      image: "https://images.unsplash.com/photo-**********-0b93528c311a?w=80&h=80&fit=crop&crop=face",
      rating: 5,
      result: "400% Qualified Leads",
      logo: "https://via.placeholder.com/120x40/8b5cf6/ffffff?text=Johnson+Law"
    },
    {
      id: 6,
      quote: "The comprehensive digital strategy transformed our real estate business. We now generate 380% more property inquiries and have become the market leader in our area.",
      author: "Lisa Thompson",
      position: "Broker/Owner",
      company: "Prime Properties Group",
      industry: "Real Estate",
      image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=80&h=80&fit=crop&crop=face",
      rating: 5,
      result: "380% Property Inquiries",
      logo: "https://via.placeholder.com/120x40/f97316/ffffff?text=Prime+Properties"
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            What Our Clients Say About Their Success
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Don&apos;t just take our word for it. Hear directly from business owners and executives who have experienced transformational growth with VESA Solutions.
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial) => (
            <div key={testimonial.id} className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              {/* Quote Icon */}
              <div className="flex justify-between items-start mb-6">
                <Quote className="text-blue-600" size={32} />
                <div className="bg-green-100 px-3 py-1 rounded-full">
                  <span className="text-green-600 font-bold text-sm">{testimonial.result}</span>
                </div>
              </div>

              {/* Rating */}
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} size={20} className="text-yellow-400 fill-current" />
                ))}
              </div>

              {/* Quote */}
              <p className="text-gray-700 mb-6 leading-relaxed italic">
                &quot;{testimonial.quote}&quot;
              </p>

              {/* Author Info */}
              <div className="flex items-center mb-4">
                <Image
                  src={testimonial.image}
                  alt={testimonial.author}
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full mr-4"
                />
                <div>
                  <div className="font-semibold text-gray-800">{testimonial.author}</div>
                  <div className="text-sm text-gray-600">{testimonial.position}</div>
                </div>
              </div>

              {/* Company Info */}
              <div className="border-t border-gray-200 pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-semibold text-gray-800">{testimonial.company}</div>
                    <div className="text-sm text-gray-600">{testimonial.industry}</div>
                  </div>
                  <Image
                    src={testimonial.logo}
                    alt={testimonial.company}
                    width={80}
                    height={32}
                    className="h-8"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-12 text-white">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold mb-4">
              Trusted by Industry Leaders
            </h3>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Our client testimonials reflect our commitment to delivering exceptional results and building long-term partnerships.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl md:text-5xl font-bold text-blue-200 mb-2">98%</div>
              <div className="text-blue-100">Client Satisfaction Rate</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-blue-200 mb-2">95%</div>
              <div className="text-blue-100">Client Retention Rate</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-blue-200 mb-2">4.9</div>
              <div className="text-blue-100">Average Rating</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-blue-200 mb-2">500+</div>
              <div className="text-blue-100">Success Stories</div>
            </div>
          </div>

          {/* Review Platforms */}
          <div className="mt-12 pt-8 border-t border-blue-500">
            <div className="text-center mb-8">
              <h4 className="text-xl font-semibold text-white mb-2">
                Verified Reviews Across Platforms
              </h4>
              <p className="text-blue-100">
                Our reputation speaks for itself across all major review platforms
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <Image src="https://via.placeholder.com/120x40/ffffff/4285f4?text=Google" alt="Google Reviews" width={120} height={40} className="mx-auto mb-2" />
                <div className="flex justify-center mb-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} size={16} className="text-yellow-400 fill-current" />
                  ))}
                </div>
                <div className="text-white font-semibold">4.9/5</div>
                <div className="text-blue-100 text-sm">150+ reviews</div>
              </div>
              
              <div className="text-center">
                <Image src="https://via.placeholder.com/120x40/ffffff/ff6154?text=Clutch" alt="Clutch Reviews" width={120} height={40} className="mx-auto mb-2" />
                <div className="flex justify-center mb-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} size={16} className="text-yellow-400 fill-current" />
                  ))}
                </div>
                <div className="text-white font-semibold">4.8/5</div>
                <div className="text-blue-100 text-sm">85+ reviews</div>
              </div>
              
              <div className="text-center">
                <Image src="https://via.placeholder.com/120x40/ffffff/0077b5?text=LinkedIn" alt="LinkedIn Reviews" width={120} height={40} className="mx-auto mb-2" />
                <div className="flex justify-center mb-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} size={16} className="text-yellow-400 fill-current" />
                  ))}
                </div>
                <div className="text-white font-semibold">4.9/5</div>
                <div className="text-blue-100 text-sm">60+ reviews</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CaseStudiesTestimonialsSection;
