{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/style.css"], "sourcesContent": ["/* CSS variables. */\r\n:root {\r\n\t--PhoneInput-color--focus: #03b2cb;\r\n\t--PhoneInputInternationalIconPhone-opacity: 0.8;\r\n\t--PhoneInputInternationalIconGlobe-opacity: 0.65;\r\n\t--PhoneInputCountrySelect-marginRight: 0.35em;\r\n\t--PhoneInputCountrySelectArrow-width: 0.3em;\r\n\t--PhoneInputCountrySelectArrow-marginLeft: var(--PhoneInputCountrySelect-marginRight);\r\n\t--PhoneInputCountrySelectArrow-borderWidth: 1px;\r\n\t--PhoneInputCountrySelectArrow-opacity: 0.45;\r\n\t--PhoneInputCountrySelectArrow-color: currentColor;\r\n\t--PhoneInputCountrySelectArrow-color--focus: var(--PhoneInput-color--focus);\r\n\t--PhoneInputCountrySelectArrow-transform: rotate(45deg);\r\n\t--PhoneInputCountryFlag-aspectRatio: 1.5;\r\n\t--PhoneInputCountryFlag-height: 1em;\r\n\t--PhoneInputCountryFlag-borderWidth: 1px;\r\n\t--PhoneInputCountryFlag-borderColor: rgba(0,0,0,0.5);\r\n\t--PhoneInputCountryFlag-borderColor--focus: var(--PhoneInput-color--focus);\r\n\t--PhoneInputCountryFlag-backgroundColor--loading: rgba(0,0,0,0.1);\r\n}\r\n\r\n.PhoneInput {\r\n\t/* This is done to stretch the contents of this component. */\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.PhoneInputInput {\r\n\t/* The phone number input stretches to fill all empty space */\r\n\tflex: 1;\r\n\t/* The phone number input should shrink\r\n\t   to make room for the extension input */\r\n\tmin-width: 0;\r\n}\r\n\r\n.PhoneInputCountryIcon {\r\n\twidth: calc(var(--PhoneInputCountryFlag-height) * var(--PhoneInputCountryFlag-aspectRatio));\r\n\theight: var(--PhoneInputCountryFlag-height);\r\n}\r\n\r\n.PhoneInputCountryIcon--square {\r\n\twidth: var(--PhoneInputCountryFlag-height);\r\n}\r\n\r\n.PhoneInputCountryIcon--border {\r\n\t/* Removed `background-color` because when an `<img/>` was still loading\r\n\t   it would show a dark gray rectangle. */\r\n\t/* For some reason the `<img/>` is not stretched to 100% width and height\r\n\t   and sometime there can be seen white pixels of the background at top and bottom. */\r\n\tbackground-color: var(--PhoneInputCountryFlag-backgroundColor--loading);\r\n\t/* Border is added via `box-shadow` because `border` interferes with `width`/`height`. */\r\n\t/* For some reason the `<img/>` is not stretched to 100% width and height\r\n\t   and sometime there can be seen white pixels of the background at top and bottom,\r\n\t   so an additional \"inset\" border is added. */\r\n\tbox-shadow: 0 0 0 var(--PhoneInputCountryFlag-borderWidth) var(--PhoneInputCountryFlag-borderColor),\r\n\t\tinset 0 0 0 var(--PhoneInputCountryFlag-borderWidth) var(--PhoneInputCountryFlag-borderColor);\r\n}\r\n\r\n.PhoneInputCountryIconImg {\r\n\t/* Fixes weird vertical space above the flag icon. */\r\n\t/* https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/7#note_348586559 */\r\n\tdisplay: block;\r\n\t/* 3rd party <SVG/> flag icons won't stretch if they have `width` and `height`.\r\n\t   Also, if an <SVG/> icon's aspect ratio was different, it wouldn't fit too. */\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.PhoneInputInternationalIconPhone {\r\n\topacity: var(--PhoneInputInternationalIconPhone-opacity);\r\n}\r\n\r\n.PhoneInputInternationalIconGlobe {\r\n\topacity: var(--PhoneInputInternationalIconGlobe-opacity);\r\n}\r\n\r\n/* Styling native country `<select/>`. */\r\n\r\n.PhoneInputCountry {\r\n\tposition: relative;\r\n\talign-self: stretch;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-right: var(--PhoneInputCountrySelect-marginRight);\r\n}\r\n\r\n.PhoneInputCountrySelect {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\theight: 100%;\r\n\twidth: 100%;\r\n\tz-index: 1;\r\n\tborder: 0;\r\n\topacity: 0;\r\n\tcursor: pointer;\r\n}\r\n\r\n.PhoneInputCountrySelect[disabled],\r\n.PhoneInputCountrySelect[readonly] {\r\n\tcursor: default;\r\n}\r\n\r\n.PhoneInputCountrySelectArrow {\r\n\tdisplay: block;\r\n\tcontent: '';\r\n\twidth: var(--PhoneInputCountrySelectArrow-width);\r\n\theight: var(--PhoneInputCountrySelectArrow-width);\r\n\tmargin-left: var(--PhoneInputCountrySelectArrow-marginLeft);\r\n\tborder-style: solid;\r\n\tborder-color: var(--PhoneInputCountrySelectArrow-color);\r\n\tborder-top-width: 0;\r\n\tborder-bottom-width: var(--PhoneInputCountrySelectArrow-borderWidth);\r\n\tborder-left-width: 0;\r\n\tborder-right-width: var(--PhoneInputCountrySelectArrow-borderWidth);\r\n\ttransform: var(--PhoneInputCountrySelectArrow-transform);\r\n\topacity: var(--PhoneInputCountrySelectArrow-opacity);\r\n}\r\n\r\n.PhoneInputCountrySelect:focus + .PhoneInputCountryIcon + .PhoneInputCountrySelectArrow {\r\n\topacity: 1;\r\n\tcolor: var(--PhoneInputCountrySelectArrow-color--focus);\r\n}\r\n\r\n.PhoneInputCountrySelect:focus + .PhoneInputCountryIcon--border {\r\n\tbox-shadow: 0 0 0 var(--PhoneInputCountryFlag-borderWidth) var(--PhoneInputCountryFlag-borderColor--focus),\r\n\t\tinset 0 0 0 var(--PhoneInputCountryFlag-borderWidth) var(--PhoneInputCountryFlag-borderColor--focus);\r\n}\r\n\r\n.PhoneInputCountrySelect:focus + .PhoneInputCountryIcon .PhoneInputInternationalIconGlobe {\r\n\topacity: 1;\r\n\tcolor: var(--PhoneInputCountrySelectArrow-color--focus);\r\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;;;;AAoBA;;;;;AAMA;;;;;AAQA;;;;;AAKA;;;;AAIA;;;;;AAcA;;;;;;AAUA;;;;AAIA;;;;AAMA;;;;;;;;AAQA;;;;;;;;;;;;AAYA;;;;AAKA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;AAKA", "ignoreList": [0], "debugId": null}}]}