{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\r\n\r\ntype SendMessage = (msg: any) => void;\r\nexport type WebSocketMessage =\r\n  | {\r\n      type: \"turbopack-connected\";\r\n    }\r\n  | {\r\n      type: \"turbopack-message\";\r\n      data: Record<string, any>;\r\n    };\r\n\r\n\r\nexport type ClientOptions = {\r\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void;\r\n  sendMessage: SendMessage;\r\n  onUpdateError: (err: unknown) => void;\r\n};\r\n\r\nexport function connect({\r\n  addMessageListener,\r\n  sendMessage,\r\n  onUpdateError = console.error,\r\n}: ClientOptions) {\r\n  addMessageListener((msg) => {\r\n    switch (msg.type) {\r\n      case \"turbopack-connected\":\r\n        handleSocketConnected(sendMessage);\r\n        break;\r\n      default:\r\n        try {\r\n          if (Array.isArray(msg.data)) {\r\n            for (let i = 0; i < msg.data.length; i++) {\r\n              handleSocketMessage(msg.data[i] as ServerMessage);\r\n            }\r\n          } else {\r\n            handleSocketMessage(msg.data as ServerMessage);\r\n          }\r\n          applyAggregatedUpdates();\r\n        } catch (e: unknown) {\r\n          console.warn(\r\n            \"[Fast Refresh] performing full reload\\n\\n\" +\r\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\r\n              \"You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n\" +\r\n              \"Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n\" +\r\n              \"It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n\" +\r\n              \"Fast Refresh requires at least one parent function component in your React tree.\"\r\n          );\r\n          onUpdateError(e);\r\n          location.reload();\r\n        }\r\n        break;\r\n    }\r\n  });\r\n\r\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS;\r\n  if (queued != null && !Array.isArray(queued)) {\r\n    throw new Error(\"A separate HMR handler was already registered\");\r\n  }\r\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\r\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    },\r\n  };\r\n\r\n  if (Array.isArray(queued)) {\r\n    for (const [chunkPath, callback] of queued) {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    }\r\n  }\r\n}\r\n\r\ntype UpdateCallbackSet = {\r\n  callbacks: Set<UpdateCallback>;\r\n  unsubscribe: () => void;\r\n};\r\n\r\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map();\r\n\r\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\r\n  sendMessage(JSON.stringify(message));\r\n}\r\n\r\ntype ResourceKey = string;\r\n\r\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\r\n  return JSON.stringify({\r\n    path: resource.path,\r\n    headers: resource.headers || null,\r\n  });\r\n}\r\n\r\nfunction subscribeToUpdates(\r\n  sendMessage: SendMessage,\r\n  resource: ResourceIdentifier\r\n): () => void {\r\n  sendJSON(sendMessage, {\r\n    type: \"turbopack-subscribe\",\r\n    ...resource,\r\n  });\r\n\r\n  return () => {\r\n    sendJSON(sendMessage, {\r\n      type: \"turbopack-unsubscribe\",\r\n      ...resource,\r\n    });\r\n  };\r\n}\r\n\r\nfunction handleSocketConnected(sendMessage: SendMessage) {\r\n  for (const key of updateCallbackSets.keys()) {\r\n    subscribeToUpdates(sendMessage, JSON.parse(key));\r\n  }\r\n}\r\n\r\n// we aggregate all pending updates until the issues are resolved\r\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\r\n  new Map();\r\n\r\nfunction aggregateUpdates(msg: PartialServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  let aggregated = chunkListsWithPendingUpdates.get(key);\r\n\r\n  if (aggregated) {\r\n    aggregated.instruction = mergeChunkListUpdates(\r\n      aggregated.instruction,\r\n      msg.instruction\r\n    );\r\n  } else {\r\n    chunkListsWithPendingUpdates.set(key, msg);\r\n  }\r\n}\r\n\r\nfunction applyAggregatedUpdates() {\r\n  if (chunkListsWithPendingUpdates.size === 0) return;\r\n  hooks.beforeRefresh();\r\n  for (const msg of chunkListsWithPendingUpdates.values()) {\r\n    triggerUpdate(msg);\r\n  }\r\n  chunkListsWithPendingUpdates.clear();\r\n  finalizeUpdate();\r\n}\r\n\r\nfunction mergeChunkListUpdates(\r\n  updateA: ChunkListUpdate,\r\n  updateB: ChunkListUpdate\r\n): ChunkListUpdate {\r\n  let chunks;\r\n  if (updateA.chunks != null) {\r\n    if (updateB.chunks == null) {\r\n      chunks = updateA.chunks;\r\n    } else {\r\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks);\r\n    }\r\n  } else if (updateB.chunks != null) {\r\n    chunks = updateB.chunks;\r\n  }\r\n\r\n  let merged;\r\n  if (updateA.merged != null) {\r\n    if (updateB.merged == null) {\r\n      merged = updateA.merged;\r\n    } else {\r\n      // Since `merged` is an array of updates, we need to merge them all into\r\n      // one, consistent update.\r\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\r\n      // no need to key on the `type` field.\r\n      let update = updateA.merged[0];\r\n      for (let i = 1; i < updateA.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateA.merged[i]\r\n        );\r\n      }\r\n\r\n      for (let i = 0; i < updateB.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateB.merged[i]\r\n        );\r\n      }\r\n\r\n      merged = [update];\r\n    }\r\n  } else if (updateB.merged != null) {\r\n    merged = updateB.merged;\r\n  }\r\n\r\n  return {\r\n    type: \"ChunkListUpdate\",\r\n    chunks,\r\n    merged,\r\n  };\r\n}\r\n\r\nfunction mergeChunkListChunks(\r\n  chunksA: Record<ChunkPath, ChunkUpdate>,\r\n  chunksB: Record<ChunkPath, ChunkUpdate>\r\n): Record<ChunkPath, ChunkUpdate> {\r\n  const chunks: Record<ChunkPath, ChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB);\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeChunkUpdates(\r\n  updateA: ChunkUpdate,\r\n  updateB: ChunkUpdate\r\n): ChunkUpdate | undefined {\r\n  if (\r\n    (updateA.type === \"added\" && updateB.type === \"deleted\") ||\r\n    (updateA.type === \"deleted\" && updateB.type === \"added\")\r\n  ) {\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"partial\") {\r\n    invariant(updateA.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  if (updateB.type === \"partial\") {\r\n    invariant(updateB.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction mergeChunkListEcmascriptMergedUpdates(\r\n  mergedA: EcmascriptMergedUpdate,\r\n  mergedB: EcmascriptMergedUpdate\r\n): EcmascriptMergedUpdate {\r\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries);\r\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks);\r\n\r\n  return {\r\n    type: \"EcmascriptMergedUpdate\",\r\n    entries,\r\n    chunks,\r\n  };\r\n}\r\n\r\nfunction mergeEcmascriptChunkEntries(\r\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\r\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\r\n): Record<ModuleId, EcmascriptModuleEntry> {\r\n  return { ...entriesA, ...entriesB };\r\n}\r\n\r\nfunction mergeEcmascriptChunksUpdates(\r\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\r\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\r\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\r\n  if (chunksA == null) {\r\n    return chunksB;\r\n  }\r\n\r\n  if (chunksB == null) {\r\n    return chunksA;\r\n  }\r\n\r\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\r\n        chunkUpdateA,\r\n        chunkUpdateB\r\n      );\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  if (Object.keys(chunks).length === 0) {\r\n    return undefined;\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeEcmascriptChunkUpdates(\r\n  updateA: EcmascriptMergedChunkUpdate,\r\n  updateB: EcmascriptMergedChunkUpdate\r\n): EcmascriptMergedChunkUpdate | undefined {\r\n  if (updateA.type === \"added\" && updateB.type === \"deleted\") {\r\n    // These two completely cancel each other out.\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"deleted\" && updateB.type === \"added\") {\r\n    const added = [];\r\n    const deleted = [];\r\n    const deletedModules = new Set(updateA.modules ?? []);\r\n    const addedModules = new Set(updateB.modules ?? []);\r\n\r\n    for (const moduleId of addedModules) {\r\n      if (!deletedModules.has(moduleId)) {\r\n        added.push(moduleId);\r\n      }\r\n    }\r\n\r\n    for (const moduleId of deletedModules) {\r\n      if (!addedModules.has(moduleId)) {\r\n        deleted.push(moduleId);\r\n      }\r\n    }\r\n\r\n    if (added.length === 0 && deleted.length === 0) {\r\n      return undefined;\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added,\r\n      deleted,\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"partial\") {\r\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])]);\r\n    const deleted = new Set([\r\n      ...(updateA.deleted ?? []),\r\n      ...(updateB.deleted ?? []),\r\n    ]);\r\n\r\n    if (updateB.added != null) {\r\n      for (const moduleId of updateB.added) {\r\n        deleted.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    if (updateB.deleted != null) {\r\n      for (const moduleId of updateB.deleted) {\r\n        added.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added: [...added],\r\n      deleted: [...deleted],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"added\" && updateB.type === \"partial\") {\r\n    const modules = new Set([\r\n      ...(updateA.modules ?? []),\r\n      ...(updateB.added ?? []),\r\n    ]);\r\n\r\n    for (const moduleId of updateB.deleted ?? []) {\r\n      modules.delete(moduleId);\r\n    }\r\n\r\n    return {\r\n      type: \"added\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"deleted\") {\r\n    // We could eagerly return `updateB` here, but this would potentially be\r\n    // incorrect if `updateA` has added modules.\r\n\r\n    const modules = new Set(updateB.modules ?? []);\r\n\r\n    if (updateA.added != null) {\r\n      for (const moduleId of updateA.added) {\r\n        modules.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"deleted\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  // Any other update combination is invalid.\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction invariant(_: never, message: string): never {\r\n  throw new Error(`Invariant: ${message}`);\r\n}\r\n\r\nconst CRITICAL = [\"bug\", \"error\", \"fatal\"];\r\n\r\nfunction compareByList(list: any[], a: any, b: any) {\r\n  const aI = list.indexOf(a) + 1 || list.length;\r\n  const bI = list.indexOf(b) + 1 || list.length;\r\n  return aI - bI;\r\n}\r\n\r\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map();\r\n\r\nfunction emitIssues() {\r\n  const issues = [];\r\n  const deduplicationSet = new Set();\r\n\r\n  for (const [_, chunkIssues] of chunksWithIssues) {\r\n    for (const chunkIssue of chunkIssues) {\r\n      if (deduplicationSet.has(chunkIssue.formatted)) continue;\r\n\r\n      issues.push(chunkIssue);\r\n      deduplicationSet.add(chunkIssue.formatted);\r\n    }\r\n  }\r\n\r\n  sortIssues(issues);\r\n\r\n  hooks.issues(issues);\r\n}\r\n\r\nfunction handleIssues(msg: ServerMessage): boolean {\r\n  const key = resourceKey(msg.resource);\r\n  let hasCriticalIssues = false;\r\n\r\n  for (const issue of msg.issues) {\r\n    if (CRITICAL.includes(issue.severity)) {\r\n      hasCriticalIssues = true;\r\n    }\r\n  }\r\n\r\n  if (msg.issues.length > 0) {\r\n    chunksWithIssues.set(key, msg.issues);\r\n  } else if (chunksWithIssues.has(key)) {\r\n    chunksWithIssues.delete(key);\r\n  }\r\n\r\n  emitIssues();\r\n\r\n  return hasCriticalIssues;\r\n}\r\n\r\nconst SEVERITY_ORDER = [\"bug\", \"fatal\", \"error\", \"warning\", \"info\", \"log\"];\r\nconst CATEGORY_ORDER = [\r\n  \"parse\",\r\n  \"resolve\",\r\n  \"code generation\",\r\n  \"rendering\",\r\n  \"typescript\",\r\n  \"other\",\r\n];\r\n\r\nfunction sortIssues(issues: Issue[]) {\r\n  issues.sort((a, b) => {\r\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity);\r\n    if (first !== 0) return first;\r\n    return compareByList(CATEGORY_ORDER, a.category, b.category);\r\n  });\r\n}\r\n\r\nconst hooks = {\r\n  beforeRefresh: () => {},\r\n  refresh: () => {},\r\n  buildOk: () => {},\r\n  issues: (_issues: Issue[]) => {},\r\n};\r\n\r\nexport function setHooks(newHooks: typeof hooks) {\r\n  Object.assign(hooks, newHooks);\r\n}\r\n\r\nfunction handleSocketMessage(msg: ServerMessage) {\r\n  sortIssues(msg.issues);\r\n\r\n  handleIssues(msg);\r\n\r\n  switch (msg.type) {\r\n    case \"issues\":\r\n      // issues are already handled\r\n      break;\r\n    case \"partial\":\r\n      // aggregate updates\r\n      aggregateUpdates(msg);\r\n      break;\r\n    default:\r\n      // run single update\r\n      const runHooks = chunkListsWithPendingUpdates.size === 0;\r\n      if (runHooks) hooks.beforeRefresh();\r\n      triggerUpdate(msg);\r\n      if (runHooks) finalizeUpdate();\r\n      break;\r\n  }\r\n}\r\n\r\nfunction finalizeUpdate() {\r\n  hooks.refresh();\r\n  hooks.buildOk();\r\n\r\n  // This is used by the Next.js integration test suite to notify it when HMR\r\n  // updates have been completed.\r\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\r\n  if (globalThis.__NEXT_HMR_CB) {\r\n    globalThis.__NEXT_HMR_CB();\r\n    globalThis.__NEXT_HMR_CB = null;\r\n  }\r\n}\r\n\r\nfunction subscribeToChunkUpdate(\r\n  chunkListPath: ChunkListPath,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n): () => void {\r\n  return subscribeToUpdate(\r\n    {\r\n      path: chunkListPath,\r\n    },\r\n    sendMessage,\r\n    callback\r\n  );\r\n}\r\n\r\nexport function subscribeToUpdate(\r\n  resource: ResourceIdentifier,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n) {\r\n  const key = resourceKey(resource);\r\n  let callbackSet: UpdateCallbackSet;\r\n  const existingCallbackSet = updateCallbackSets.get(key);\r\n  if (!existingCallbackSet) {\r\n    callbackSet = {\r\n      callbacks: new Set([callback]),\r\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\r\n    };\r\n    updateCallbackSets.set(key, callbackSet);\r\n  } else {\r\n    existingCallbackSet.callbacks.add(callback);\r\n    callbackSet = existingCallbackSet;\r\n  }\r\n\r\n  return () => {\r\n    callbackSet.callbacks.delete(callback);\r\n\r\n    if (callbackSet.callbacks.size === 0) {\r\n      callbackSet.unsubscribe();\r\n      updateCallbackSets.delete(key);\r\n    }\r\n  };\r\n}\r\n\r\nfunction triggerUpdate(msg: ServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  const callbackSet = updateCallbackSets.get(key);\r\n  if (!callbackSet) {\r\n    return;\r\n  }\r\n\r\n  for (const callback of callbackSet.callbacks) {\r\n    callback(msg);\r\n  }\r\n\r\n  if (msg.type === \"notFound\") {\r\n    // This indicates that the resource which we subscribed to either does not exist or\r\n    // has been deleted. In either case, we should clear all update callbacks, so if a\r\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\r\n    // message to the server.\r\n    // No need to send an \"unsubscribe\" message to the server, it will have already\r\n    // dropped the update stream before sending the \"notFound\" message.\r\n    updateCallbackSets.delete(key);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAmBtD,SAAS,QAAQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf;IACd,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM,CAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;QAClB,MAAM,iBAAiB,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QACpD,MAAM,eAAe,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,MAAM,QAAQ,IAAI,IAAI;eAAK,QAAQ,KAAK,IAAI,EAAE;eAAO,QAAQ,KAAK,IAAI,EAAE;SAAE;QAC1E,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,OAAO,IAAI,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,KAAK,IAAI,EAAE;SACxB;QAED,KAAK,MAAM,YAAY,QAAQ,OAAO,IAAI,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS;AACzC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { ChevronDown } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\n\r\ninterface HeaderProps {\r\n  isVisible?: boolean;\r\n}\r\n\r\nconst Header: React.FC<HeaderProps> = ({ isVisible = true }) => {\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);\r\n  const [seoDropdownOpen, setSeoDropdownOpen] = useState<boolean>(false);\r\n  const [webDevDropdownOpen, setWebDevDropdownOpen] = useState<boolean>(false);\r\n  const [digitalMarketingDropdownOpen, setDigitalMarketingDropdownOpen] = useState<boolean>(false);\r\n  const [aboutDropdownOpen, setAboutDropdownOpen] = useState<boolean>(false);\r\n\r\n  const [mounted, setMounted] = useState<boolean>(false);\r\n\r\n  // Ensure component is hydrated before rendering state-dependent UI\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Handle body scroll when mobile menu is open\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) {\r\n      document.body.style.overflow = 'hidden';\r\n    } else {\r\n      document.body.style.overflow = 'unset';\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [mobileMenuOpen]);\r\n\r\n  // Close mobile menu on escape key\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (e.key === 'Escape' && mobileMenuOpen) {\r\n        setMobileMenuOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleEscape);\r\n    return () => document.removeEventListener('keydown', handleEscape);\r\n  }, [mobileMenuOpen]);\r\n\r\n  // SEO Sub-services\r\n  const seoSubServices = [\r\n    { name: 'On-Page SEO Optimization', href: '/on-page-seo', description: 'Optimize content and structure' },\r\n    { name: 'Off-Page SEO & Link Building', href: '/off-page-seo', description: 'Build authority and backlinks' },\r\n    { name: 'Technical SEO Services', href: '/technical-seo', description: 'Optimize technical performance' },\r\n    { name: 'Local SEO Marketing', href: '/local-seo', description: 'Dominate local search results' },\r\n    { name: 'Content Writing', href: '/content-writing', description: 'SEO-optimized content creation' },\r\n    { name: 'SEO Analytics', href: '/seo-analytics', description: 'Comprehensive SEO analysis' }\r\n  ];\r\n\r\n  // Web Development Sub-services\r\n  const webDevSubServices = [\r\n    { name: 'Custom Website Development', href: '/custom-website-development', description: 'Unique, tailored websites' },\r\n    { name: 'E-commerce Development', href: '/ecommerce-development', description: 'Online store solutions' },\r\n    { name: 'Mobile App Development', href: '/mobile-app-development', description: 'iOS & Android apps' },\r\n    { name: 'Website Speed Optimization', href: '/website-speed-optimization', description: 'Lightning-fast websites' },\r\n    { name: 'Web Application Development', href: '/web-application-development', description: 'Custom web applications' },\r\n    { name: 'Website Maintenance & Support', href: '/website-maintenance-support', description: '24/7 website care' }\r\n  ];\r\n\r\n  // Digital Marketing Sub-services\r\n  const digitalMarketingSubServices = [\r\n    { name: 'PPC Advertising', href: '/ppc', description: 'Targeted pay-per-click campaigns' },\r\n    { name: 'Email Marketing', href: '/email-marketing', description: 'Automated campaigns that convert' },\r\n    { name: 'Social Media Marketing', href: '/social-media', description: 'Engage your audience effectively' },\r\n    { name: 'Branding Services', href: '/branding-services', description: 'Build powerful brand identity' },\r\n    { name: 'Conversion Optimization', href: '/conversion-optimization', description: 'Turn visitors into customers' },\r\n    { name: 'Reputation Management', href: '/reputation-management', description: 'Protect and enhance your brand' }\r\n  ];\r\n\r\n  // About Sub-services\r\n  const aboutSubServices = [\r\n    { name: 'Case Studies', href: '/case-studies', description: 'View our successful projects' },\r\n    { name: 'Blog', href: '/blog', description: 'Latest insights and industry news' }\r\n  ];\r\n\r\n  const toggleMobileMenu = () => {\r\n    setMobileMenuOpen(!mobileMenuOpen);\r\n  };\r\n\r\n  const closeMobileMenu = () => {\r\n    setMobileMenuOpen(false);\r\n    // Reset all dropdown states when closing mobile menu\r\n    setSeoDropdownOpen(false);\r\n    setWebDevDropdownOpen(false);\r\n    setDigitalMarketingDropdownOpen(false);\r\n    setAboutDropdownOpen(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <header className={`w-full bg-white/95 backdrop-blur-md border-b border-gray-100 z-50 transition-all duration-500 ease-in-out ${\r\n        isVisible \r\n          ? 'sticky top-0 translate-y-0 opacity-100' \r\n          : 'fixed top-0 -translate-y-full opacity-0 pointer-events-none'\r\n      }`}>\r\n        <div className=\"max-w-[1440px] mx-auto px-4 sm:px-6 py-3 sm:py-4\">\r\n          <div className=\"flex justify-between items-center\">\r\n            {/* Logo */}\r\n            <div className=\"flex items-center group cursor-pointer\">\r\n              <div className=\"relative\">\r\n                <Link href=\"/\">\r\n                  <Image \r\n                    src=\"/VesaLogo.svg\" \r\n                    alt=\"VESA Solutions Logo\" \r\n                    width={94}\r\n                    height={40}\r\n                    priority\r\n                    className=\"transition-transform duration-300 group-hover:scale-105 w-20 h-auto sm:w-[94px]\"\r\n                  />\r\n                </Link>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Desktop Navigation */}\r\n            <nav className=\"hidden lg:flex items-center space-x-12\">\r\n              {/* SEO Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setSeoDropdownOpen(true)}\r\n                onMouseLeave={() => setSeoDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/seo-search-engine-optimization\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-green-600 transition-all duration-300 group\">\r\n                  SEO\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-green-500 to-green-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  seoDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">SEO Services</div>\r\n                      {seoSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100/50 transition-all duration-200 ease-out border border-transparent hover:border-green-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-green-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Web Development Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setWebDevDropdownOpen(true)}\r\n                onMouseLeave={() => setWebDevDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/web-development\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group\">\r\n                  WEB DEVELOPMENT\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  webDevDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">Web Development</div>\r\n                      {webDevSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Digital Marketing Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setDigitalMarketingDropdownOpen(true)}\r\n                onMouseLeave={() => setDigitalMarketingDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/digital-marketing\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-purple-600 transition-all duration-300 group\">\r\n                  DIGITAL MARKETING\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-purple-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  digitalMarketingDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">Digital Marketing</div>\r\n                      {digitalMarketingSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50/50 transition-all duration-200 ease-out border border-transparent hover:border-indigo-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-purple-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-purple-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Services */}\r\n              <Link href=\"/services\" className=\"relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300\">\r\n                SERVICES\r\n                <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n              </Link>\r\n\r\n              {/* About Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setAboutDropdownOpen(true)}\r\n                onMouseLeave={() => setAboutDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/about\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group\">\r\n                  ABOUT\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  aboutDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">About</div>\r\n                      {aboutSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Contact */}\r\n              <Link href=\"/contact-us\" className=\"relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300\">\r\n                CONTACT\r\n                <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n              </Link>\r\n            </nav>\r\n\r\n\r\n            {/* CTA Button and Mobile Menu */}\r\n            <div className=\"flex items-center space-x-3 sm:space-x-4\">\r\n              {/* Desktop CTA */}\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                className=\"hidden sm:block group relative bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-sm font-semibold px-6 lg:px-8 py-2.5 lg:py-3 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-blue-500/25\"\r\n              >\r\n                <span className=\"relative z-10\">GET FREE PROPOSAL</span>\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n              </Link>\r\n\r\n              {/* Mobile Menu Button */}\r\n              <button\r\n                className=\"lg:hidden relative p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-all duration-300 group\"\r\n                onClick={toggleMobileMenu}\r\n                aria-label=\"Toggle mobile menu\"\r\n              >\r\n                <div className=\"w-6 h-6 relative\">\r\n                  <span className={`absolute top-1 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${\r\n                    mobileMenuOpen ? 'rotate-45 translate-y-2' : ''\r\n                  }`}></span>\r\n                  <span className={`absolute top-2.5 left-0 w-6 h-0.5 bg-current transition-all duration-300 ${\r\n                    mobileMenuOpen ? 'opacity-0' : ''\r\n                  }`}></span>\r\n                  <span className={`absolute top-4 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${\r\n                    mobileMenuOpen ? '-rotate-45 -translate-y-2' : ''\r\n                  }`}></span>\r\n                </div>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Mobile Menu */}\r\n      {mounted && mobileMenuOpen && (\r\n        <div className=\"lg:hidden fixed inset-0 z-40 bg-white overflow-y-auto\">\r\n          {/* Mobile Header */}\r\n          <div className=\"flex justify-between items-center p-4 border-b border-gray-200\">\r\n            <Link href=\"/\" onClick={closeMobileMenu}>\r\n              <Image\r\n                src=\"/VesaLogo.svg\"\r\n                alt=\"VESA Solutions Logo\"\r\n                width={80}\r\n                height={34}\r\n                priority\r\n                className=\"w-20 h-auto\"\r\n              />\r\n            </Link>\r\n            <button\r\n              onClick={closeMobileMenu}\r\n              className=\"p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-colors\"\r\n              aria-label=\"Close mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 relative\">\r\n                <span className=\"absolute top-2.5 left-0 w-6 h-0.5 bg-current transform rotate-45\"></span>\r\n                <span className=\"absolute top-2.5 left-0 w-6 h-0.5 bg-current transform -rotate-45\"></span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Mobile Navigation */}\r\n          <div className=\"p-4 space-y-6\">\r\n            {/* SEO Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setSeoDropdownOpen(!seoDropdownOpen)}\r\n              >\r\n                <Link href=\"/seo-search-engine-optimization\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-green-600 transition-colors\">\r\n                  SEO\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {seoDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {seoSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-green-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Web Development Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setWebDevDropdownOpen(!webDevDropdownOpen)}\r\n              >\r\n                <Link href=\"/web-development\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\r\n                  WEB DEVELOPMENT\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {webDevDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {webDevSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-blue-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Digital Marketing Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setDigitalMarketingDropdownOpen(!digitalMarketingDropdownOpen)}\r\n              >\r\n                <Link href=\"/digital-marketing\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-purple-600 transition-colors\">\r\n                  DIGITAL MARKETING\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {digitalMarketingDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {digitalMarketingSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-purple-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Services */}\r\n            <div>\r\n              <Link\r\n                href=\"/services\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\"\r\n              >\r\n                SERVICES\r\n              </Link>\r\n            </div>\r\n\r\n            {/* About Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setAboutDropdownOpen(!aboutDropdownOpen)}\r\n              >\r\n                <Link href=\"/about\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\r\n                  ABOUT\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {aboutDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {aboutSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-blue-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Contact */}\r\n            <div>\r\n              <Link\r\n                href=\"/contact-us\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\"\r\n              >\r\n                CONTACT\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Mobile CTA Button */}\r\n            <div className=\"pt-4\">\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block w-full text-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-6 py-4 rounded-full transition-all duration-300 transform hover:scale-105\"\r\n              >\r\n                GET FREE PROPOSAL\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWA,MAAM,SAAgC,CAAC,EAAE,YAAY,IAAI,EAAE;;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAC1F,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAEpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,mEAAmE;IACnE,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;4BAAE;YACR,WAAW;QACb;2BAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,gBAAgB;gBAClB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA,qBAAqB;YACrB;oCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;2BAAG;QAAC;KAAe;IAEnB,kCAAkC;IAClC,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,YAAY,gBAAgB;wBACxC,kBAAkB;oBACpB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;oCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;2BAAG;QAAC;KAAe;IAEnB,mBAAmB;IACnB,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAA4B,MAAM;YAAgB,aAAa;QAAiC;QACxG;YAAE,MAAM;YAAgC,MAAM;YAAiB,aAAa;QAAgC;QAC5G;YAAE,MAAM;YAA0B,MAAM;YAAkB,aAAa;QAAiC;QACxG;YAAE,MAAM;YAAuB,MAAM;YAAc,aAAa;QAAgC;QAChG;YAAE,MAAM;YAAmB,MAAM;YAAoB,aAAa;QAAiC;QACnG;YAAE,MAAM;YAAiB,MAAM;YAAkB,aAAa;QAA6B;KAC5F;IAED,+BAA+B;IAC/B,MAAM,oBAAoB;QACxB;YAAE,MAAM;YAA8B,MAAM;YAA+B,aAAa;QAA4B;QACpH;YAAE,MAAM;YAA0B,MAAM;YAA0B,aAAa;QAAyB;QACxG;YAAE,MAAM;YAA0B,MAAM;YAA2B,aAAa;QAAqB;QACrG;YAAE,MAAM;YAA8B,MAAM;YAA+B,aAAa;QAA0B;QAClH;YAAE,MAAM;YAA+B,MAAM;YAAgC,aAAa;QAA0B;QACpH;YAAE,MAAM;YAAiC,MAAM;YAAgC,aAAa;QAAoB;KACjH;IAED,iCAAiC;IACjC,MAAM,8BAA8B;QAClC;YAAE,MAAM;YAAmB,MAAM;YAAQ,aAAa;QAAmC;QACzF;YAAE,MAAM;YAAmB,MAAM;YAAoB,aAAa;QAAmC;QACrG;YAAE,MAAM;YAA0B,MAAM;YAAiB,aAAa;QAAmC;QACzG;YAAE,MAAM;YAAqB,MAAM;YAAsB,aAAa;QAAgC;QACtG;YAAE,MAAM;YAA2B,MAAM;YAA4B,aAAa;QAA+B;QACjH;YAAE,MAAM;YAAyB,MAAM;YAA0B,aAAa;QAAiC;KAChH;IAED,qBAAqB;IACrB,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAgB,MAAM;YAAiB,aAAa;QAA+B;QAC3F;YAAE,MAAM;YAAQ,MAAM;YAAS,aAAa;QAAoC;KACjF;IAED,MAAM,mBAAmB;QACvB,kBAAkB,CAAC;IACrB;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,qDAAqD;QACrD,mBAAmB;QACnB,sBAAsB;QACtB,gCAAgC;QAChC,qBAAqB;IACvB;IAEA,qBACE;;0BACE,0JAAC;gBAAO,WAAW,CAAC,0GAA0G,EAC5H,YACI,2CACA,+DACJ;0BACA,cAAA,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,0JAAC,yHAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;;;;;0CAOlB,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC;wCACC,WAAU;wCACV,cAAc,IAAM,mBAAmB;wCACvC,cAAc,IAAM,mBAAmB;;0DAEvC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,WAAU;;oDAA6H;kEAElL,0JAAC,gNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,kBAAkB,eAAe,IAAI;;;;;;kEACjH,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,0JAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,kBAAkB,sCAAsC,sCACxD;0DACA,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,0JAAC,wHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,0JAAC;wCACC,WAAU;wCACV,cAAc,IAAM,sBAAsB;wCAC1C,cAAc,IAAM,sBAAsB;;0DAE1C,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;;oDAA4H;kEAElK,0JAAC,gNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,qBAAqB,eAAe,IAAI;;;;;;kEACpH,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,0JAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,qBAAqB,sCAAsC,sCAC3D;0DACA,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,0JAAC,wHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,0JAAC;wCACC,WAAU;wCACV,cAAc,IAAM,gCAAgC;wCACpD,cAAc,IAAM,gCAAgC;;0DAEpD,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;;oDAA8H;kEAEtK,0JAAC,gNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,+BAA+B,eAAe,IAAI;;;;;;kEAC9H,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,0JAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,+BAA+B,sCAAsC,sCACrE;0DACA,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,4BAA4B,GAAG,CAAC,CAAC,SAAS,sBACzC,0JAAC,wHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;;4CAAmH;0DAElJ,0JAAC;gDAAK,WAAU;;;;;;;;;;;;kDAIlB,0JAAC;wCACC,WAAU;wCACV,cAAc,IAAM,qBAAqB;wCACzC,cAAc,IAAM,qBAAqB;;0DAEzC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;;oDAA4H;kEAExJ,0JAAC,gNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,oBAAoB,eAAe,IAAI;;;;;;kEACnH,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,0JAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,oBAAoB,sCAAsC,sCAC1D;0DACA,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,0JAAC,wHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;;4CAAmH;0DAEpJ,0JAAC;gDAAK,WAAU;;;;;;;;;;;;;;;;;;0CAMpB,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC,wHAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,0JAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,0JAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,0JAAC;wCACC,WAAU;wCACV,SAAS;wCACT,cAAW;kDAEX,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAK,WAAW,CAAC,+FAA+F,EAC/G,iBAAiB,4BAA4B,IAC7C;;;;;;8DACF,0JAAC;oDAAK,WAAW,CAAC,yEAAyE,EACzF,iBAAiB,cAAc,IAC/B;;;;;;8DACF,0JAAC;oDAAK,WAAW,CAAC,+FAA+F,EAC/G,iBAAiB,8BAA8B,IAC/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASb,WAAW,gCACV,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;;0CACb,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,SAAS;0CACtB,cAAA,0JAAC,yHAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAGd,0JAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAK,WAAU;;;;;;sDAChB,0JAAC;4CAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMtB,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;;kDACC,0JAAC;wCACC,WAAU;wCACV,SAAS,IAAM,mBAAmB,CAAC;;0DAEnC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,SAAS;gDAAiB,WAAU;0DAA6E;;;;;;0DAG9J,0JAAC,gNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,kBAAkB,eAAe,IAAI;;;;;;;;;;;;oCAE7G,iCACC,0JAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,0JAAC;;kDACC,0JAAC;wCACC,WAAU;wCACV,SAAS,IAAM,sBAAsB,CAAC;;0DAEtC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,SAAS;gDAAiB,WAAU;0DAA4E;;;;;;0DAG9I,0JAAC,gNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,qBAAqB,eAAe,IAAI;;;;;;;;;;;;oCAEhH,oCACC,0JAAC;wCAAI,WAAU;kDACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,0JAAC;;kDACC,0JAAC;wCACC,WAAU;wCACV,SAAS,IAAM,gCAAgC,CAAC;;0DAEhD,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,SAAS;gDAAiB,WAAU;0DAA8E;;;;;;0DAGlJ,0JAAC,gNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,+BAA+B,eAAe,IAAI;;;;;;;;;;;;oCAE1H,8CACC,0JAAC;wCAAI,WAAU;kDACZ,4BAA4B,GAAG,CAAC,CAAC,SAAS,sBACzC,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,0JAAC;0CACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;0CAMH,0JAAC;;kDACC,0JAAC;wCACC,WAAU;wCACV,SAAS,IAAM,qBAAqB,CAAC;;0DAErC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,SAAS;gDAAiB,WAAU;0DAA4E;;;;;;0DAGpI,0JAAC,gNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,oBAAoB,eAAe,IAAI;;;;;;;;;;;;oCAE/G,mCACC,0JAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,0JAAC;0CACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;0CAMH,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAlgBM;KAAA;uCAogBS", "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Footer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { Mail, Phone, MapPin, ArrowRight, Facebook, Instagram, Linkedin, Twitter, MessageCircle, Globe } from 'lucide-react';\r\n\r\nconst Footer: React.FC = () => {\r\n  return (\r\n    <footer className=\"relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden min-h-screen flex flex-col justify-center\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0 opacity-5\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/20 to-transparent\"></div>\r\n        <div className=\"absolute top-20 right-0 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute bottom-0 left-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative max-w-7xl mx-auto px-6 py-16\">\r\n        {/* Top Section - CTA */}\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent\">\r\n            Ready to Grow Your Business?\r\n          </h2>\r\n          <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\r\n            Let&apos;s create something amazing together. Get your free consultation today.\r\n          </p>\r\n          <Link href=\"/free-estimate\">\r\n            <button className=\"group inline-flex items-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl shadow-blue-500/25\">\r\n              Get Free Proposal\r\n              <ArrowRight size={20} className=\"ml-2 transition-transform duration-300 group-hover:translate-x-1\" />\r\n            </button>\r\n          </Link>\r\n        </div>\r\n\r\n        {/* Main Content */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12\">\r\n          {/* Company Info */}\r\n          <div className=\"lg:col-span-2\">\r\n            <div className=\"flex items-center mb-6\">\r\n              <Link href=\"/\">\r\n                <Image \r\n                  src=\"/VesaLogo.svg\" \r\n                  alt=\"VESA Solutions Logo\" \r\n                  width={120} \r\n                  height={46}\r\n                  className=\"w-30 h-auto transition-transform duration-300 hover:scale-105\"\r\n                />\r\n              </Link>\r\n            </div>\r\n            <p className=\"text-gray-300 text-lg leading-relaxed mb-8\">\r\n              Full-service digital marketing agency helping businesses attract, impress, and convert more leads online since 2015.\r\n            </p>\r\n            \r\n            {/* Contact Info */}\r\n            <div className=\"space-y-4\">\r\n              <a\r\n                href=\"mailto:<EMAIL>\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Mail size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span><EMAIL></span>\r\n              </a>\r\n              <a\r\n                href=\"tel:+***********\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Phone size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span>****** 628 3793</span>\r\n              </a>\r\n              <a\r\n                href=\"tel:+355694046408\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Phone size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span>+355 69 404 6408</span>\r\n              </a>\r\n              <a \r\n                href=\"https://share.google/T9q3WjqOOmMHrBnJY\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"flex items-start text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <MapPin size={18} className=\"mr-3 text-blue-400 mt-0.5 flex-shrink-0 group-hover:scale-110 transition-transform\" />\r\n                <span>Bulevardi Dyrrah, Pallati 394, Kati 4-t<br />2001, Durrës, Albania</span>\r\n              </a>\r\n              <div className=\"flex items-center text-gray-300\">\r\n                <div className=\"w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse\"></div>\r\n                <span className=\"text-green-400 font-medium\">Open 24 Hours</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Services Grid */}\r\n          <div className=\"lg:col-span-3 grid grid-cols-1 md:grid-cols-4 gap-8\">\r\n            {/* SEO Services */}\r\n            <div>\r\n              <Link href=\"/seo-search-engine-optimization\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-green-400 mb-6 relative transition-colors duration-300\">\r\n                  SEO Services\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-green-400 to-green-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/on-page-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  On-Page SEO\r\n                </Link></li>\r\n                <li><Link href=\"/off-page-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Off-Page SEO\r\n                </Link></li>\r\n                <li><Link href=\"/technical-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Technical SEO\r\n                </Link></li>\r\n                <li><Link href=\"/local-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Local SEO\r\n                </Link></li>\r\n                <li><Link href=\"/content-writing\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Content Writing\r\n                </Link></li>\r\n                <li><Link href=\"/seo-analytics\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  SEO Analytics\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Web Development */}\r\n            <div>\r\n              <Link href=\"/web-development\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-blue-400 mb-6 relative transition-colors duration-300\">\r\n                  Web Development\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/custom-website-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Custom Websites\r\n                </Link></li>\r\n                <li><Link href=\"/ecommerce-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  E-commerce Dev\r\n                </Link></li>\r\n                <li><Link href=\"/mobile-app-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Mobile Apps\r\n                </Link></li>\r\n                <li><Link href=\"/website-speed-optimization\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 flex-shrink-0 transition-all duration-300 group-hover:w-2\"></span>\r\n                  <span className=\"whitespace-nowrap\">Speed Optimization</span>\r\n                </Link></li>\r\n                <li><Link href=\"/web-application-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Web Applications\r\n                </Link></li>\r\n                <li><Link href=\"/website-maintenance-support\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Maintenance\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Digital Marketing */}\r\n            <div>\r\n              <Link href=\"/digital-marketing\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-purple-400 mb-6 relative transition-colors duration-300\">\r\n                  Digital Marketing\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-purple-400 to-purple-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/ppc\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  PPC Advertising\r\n                </Link></li>\r\n                <li><Link href=\"/email-marketing\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Email Marketing\r\n                </Link></li>\r\n                <li><Link href=\"/social-media\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Social Media\r\n                </Link></li>\r\n                <li><Link href=\"/branding-services\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Branding Services\r\n                </Link></li>\r\n                <li><Link href=\"/conversion-optimization\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Conversion Optimization\r\n                </Link></li>\r\n                <li><Link href=\"/reputation-management\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Reputation Management\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Company */}\r\n            <div>\r\n              <h3 className=\"text-lg font-bold text-white mb-6 relative\">\r\n                Company\r\n                <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600\"></span>\r\n              </h3>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/services\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Services\r\n                </Link></li>\r\n                <li><Link href=\"/case-studies\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Case Studies\r\n                </Link></li>\r\n                <li><Link href=\"/about\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  About Us\r\n                </Link></li>\r\n                <li><Link href=\"/contact-us\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Contact Us\r\n                </Link></li>\r\n                <li><Link href=\"/blog\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Blog\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Social Media & Bottom Section */}\r\n        <div className=\"flex flex-col lg:flex-row justify-between items-center pt-8 border-t border-gray-700\">\r\n          <div className=\"mb-6 lg:mb-0\">\r\n            <h4 className=\"text-white font-semibold mb-4\">Follow Our Journey</h4>\r\n            <div className=\"flex space-x-4\">\r\n              <a\r\n                href=\"https://m.facebook.com/VesaSolutions/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Facebook\"\r\n              >\r\n                <Facebook size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://www.instagram.com/vesasolutions/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-gradient-to-r hover:from-pink-500 hover:to-purple-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Instagram\"\r\n              >\r\n                <Instagram size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://al.linkedin.com/company/vesasolutions\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-700 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on LinkedIn\"\r\n              >\r\n                <Linkedin size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"#\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-500 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Twitter\"\r\n              >\r\n                <Twitter size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://wa.me/***********\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-green-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Chat with us on WhatsApp\"\r\n              >\r\n                <MessageCircle size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://vesasolutions.com/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-indigo-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Visit our website\"\r\n              >\r\n                <Globe size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"text-center lg:text-right\">\r\n            <div className=\"text-2xl font-bold text-white mb-2\">Growing Businesses Since 2015</div>\r\n            <div className=\"text-gray-400 text-sm\">Trusted by 200+ companies worldwide</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Copyright */}\r\n        <div className=\"mt-12 pt-8 border-t border-gray-700 text-center\">\r\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\r\n            <p className=\"text-gray-400 text-sm\">\r\n              © 2025 Vesa Solutions Marketing Agency. All rights reserved.\r\n            </p>\r\n            <div className=\"flex flex-wrap justify-center md:justify-end space-x-6 text-sm\">\r\n              <Link href=\"/privacy-policy\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Privacy Policy</Link>\r\n              <Link href=\"/terms-of-service\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Terms of Service</Link>\r\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Sitemap</Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAOA,MAAM,SAAmB;IACvB,qBACE,0JAAC;QAAO,WAAU;;0BAEhB,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAG,WAAU;0CAA4G;;;;;;0CAG1H,0JAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,0JAAC;oCAAO,WAAU;;wCAA2P;sDAE3Q,0JAAC,8MAAA,CAAA,aAAU;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMtC,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,0JAAC,yHAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;kDAIhB,0JAAC;wCAAE,WAAU;kDAA6C;;;;;;kDAK1D,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,0JAAC,8LAAA,CAAA,OAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC1B,0JAAC;kEAAK;;;;;;;;;;;;0DAER,0JAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,0JAAC,gMAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC3B,0JAAC;kEAAK;;;;;;;;;;;;0DAER,0JAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,0JAAC,gMAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC3B,0JAAC;kEAAK;;;;;;;;;;;;0DAER,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,0JAAC,sMAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC5B,0JAAC;;4DAAK;0EAAuC,0JAAC;;;;;4DAAK;;;;;;;;;;;;;0DAErD,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAI,WAAU;;;;;;kEACf,0JAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;0CAMnD,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC;;0DACC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,WAAU;0DACrD,cAAA,0JAAC;oDAAG,WAAU;;wDAAiG;sEAE7G,0JAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,0JAAC;gDAAG,WAAU;;kEACZ,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAe,WAAU;;8EACtC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EACxC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAa,WAAU;;8EACpC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;;8EAC1C,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EACxC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;;;;;;;;;;;;;kDAOlH,0JAAC;;0DACC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;0DACtC,cAAA,0JAAC;oDAAG,WAAU;;wDAAgG;sEAE5G,0JAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,0JAAC;gDAAG,WAAU;;kEACZ,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA8B,WAAU;;8EACrD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAyB,WAAU;;8EAChD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA0B,WAAU;;8EACjD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA8B,WAAU;;8EACrD,0JAAC;oEAAK,WAAU;;;;;;8EAChB,0JAAC;oEAAK,WAAU;8EAAoB;;;;;;;;;;;;;;;;;kEAEtC,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA+B,WAAU;;8EACtD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA+B,WAAU;;8EACtD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;;;;;;;;;;;;;kDAOjH,0JAAC;;0DACC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DACxC,cAAA,0JAAC;oDAAG,WAAU;;wDAAkG;sEAE9G,0JAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,0JAAC;gDAAG,WAAU;;kEACZ,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAO,WAAU;;8EAC9B,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;;8EAC1C,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAqB,WAAU;;8EAC5C,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA2B,WAAU;;8EAClD,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAyB,WAAU;;8EAChD,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;;;;;;;;;;;;;kDAOnH,0JAAC;;0DACC,0JAAC;gDAAG,WAAU;;oDAA6C;kEAEzD,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAElB,0JAAC;gDAAG,WAAU;;kEACZ,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAY,WAAU;;8EACnC,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;;8EAChC,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAc,WAAU;;8EACrC,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAQ,WAAU;;8EAC/B,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrH,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,sMAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEhC,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,wMAAA,CAAA,YAAS;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEjC,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,sMAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEhC,0JAAC;gDACC,MAAK;gDACL,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,oMAAA,CAAA,UAAO;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAE/B,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,oNAAA,CAAA,gBAAa;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAErC,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,gMAAA,CAAA,QAAK;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAKjC,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,0JAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAK3C,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,0JAAC;oCAAI,WAAU;;sDACb,0JAAC,wHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAkB,WAAU;sDAAsD;;;;;;sDAC7F,0JAAC,wHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAoB,WAAU;sDAAsD;;;;;;sDAC/F,0JAAC,wHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpG;KArTM;uCAuTS", "debugId": null}}, {"offset": {"line": 2974, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/blog/BlogArchiveHero.tsx"], "sourcesContent": ["// components/blog/BlogArchiveHero.tsx - Hero section for blog archive page\nimport React from 'react'\nimport { BookOpen } from 'lucide-react'\n\nconst BlogArchiveHero: React.FC = () => {\n  return (\n    <section className=\"relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 text-white py-20 overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/20 to-transparent\"></div>\n        <div className=\"absolute top-20 right-0 w-72 h-72 bg-blue-500/20 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-0 left-20 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          {/* Badge */}\n          <div className=\"inline-flex items-center bg-blue-500/20 backdrop-blur-sm border border-blue-400/30 rounded-full px-4 py-2 mb-6\">\n            <BookOpen size={16} className=\"mr-2 text-blue-300\" />\n            <span className=\"text-blue-100 text-sm font-medium\">Digital Marketing Insights</span>\n          </div>\n\n          {/* Main Heading */}\n          <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight\">\n            Expert Insights for\n            <br />\n            <span className=\"text-blue-400\">Digital Growth</span>\n          </h1>\n\n          {/* Subheading */}\n          <p className=\"text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed\">\n            Stay ahead of the curve with actionable strategies, industry trends, and proven tactics\n            to accelerate your digital marketing success.\n          </p>\n        </div>\n      </div>\n\n      {/* Bottom Wave */}\n      <div className=\"absolute bottom-0 left-0 w-full -mb-px\">\n        <svg\n          viewBox=\"0 0 1440 120\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"w-full h-auto block\"\n          preserveAspectRatio=\"none\"\n        >\n          <path\n            d=\"M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 *********** 720 120C600 *********** 360 120C240 *********** 60 120H0Z\"\n            fill=\"rgb(249 250 251)\"\n          />\n        </svg>\n      </div>\n    </section>\n  )\n}\n\nexport default BlogArchiveHero\n"], "names": [], "mappings": "AAAA,2EAA2E;;;;;AAE3E;;;AAEA,MAAM,kBAA4B;IAChC,qBACE,0JAAC;QAAQ,WAAU;;0BAEjB,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBAAI,WAAU;;sCAEb,0JAAC;4BAAI,WAAU;;8CACb,0JAAC,0MAAA,CAAA,WAAQ;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC9B,0JAAC;oCAAK,WAAU;8CAAoC;;;;;;;;;;;;sCAItD,0JAAC;4BAAG,WAAU;;gCAAgE;8CAE5E,0JAAC;;;;;8CACD,0JAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAIlC,0JAAC;4BAAE,WAAU;sCAA2E;;;;;;;;;;;;;;;;;0BAQ5F,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBACC,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;oBACV,qBAAoB;8BAEpB,cAAA,0JAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;;;;;;;;;;;AAMjB;KAlDM;uCAoDS", "debugId": null}}, {"offset": {"line": 3134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/blog/BlogFilters.tsx"], "sourcesContent": ["// components/blog/BlogFilters.tsx - Blog filtering component\nimport React, { useState } from 'react'\nimport { Search, X, Filter, ChevronDown, ChevronUp } from 'lucide-react'\nimport { BlogCategory, BlogTag, BlogFilters as BlogFiltersType } from '@/types/blog'\n\ninterface BlogFiltersProps {\n  categories: BlogCategory[]\n  tags: BlogTag[]\n  onFilterChange: (filters: BlogFiltersType) => void\n  loading: boolean\n}\n\nconst BlogFilters: React.FC<BlogFiltersProps> = ({\n  categories,\n  tags,\n  onFilterChange,\n  loading\n}) => {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('')\n  const [selectedTag, setSelectedTag] = useState('')\n  const [showFilters, setShowFilters] = useState(false)\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    applyFilters()\n  }\n\n  const applyFilters = () => {\n    const filters: BlogFiltersType = {}\n\n    if (searchTerm.trim()) filters.search = searchTerm.trim()\n    if (selectedCategory) filters.category = selectedCategory\n    if (selectedTag) filters.tag = selectedTag\n\n    onFilterChange(filters)\n  }\n\n  const clearFilters = () => {\n    setSearchTerm('')\n    setSelectedCategory('')\n    setSelectedTag('')\n    onFilterChange({})\n  }\n\n  const hasActiveFilters = searchTerm || selectedCategory || selectedTag\n\n  const getCategoryColor = (color: string) => {\n    const colors = {\n      blue: 'bg-blue-100 text-blue-800 border-blue-200',\n      green: 'bg-green-100 text-green-800 border-green-200',\n      purple: 'bg-purple-100 text-purple-800 border-purple-200',\n      orange: 'bg-orange-100 text-orange-800 border-orange-200',\n      red: 'bg-red-100 text-red-800 border-red-200',\n      teal: 'bg-teal-100 text-teal-800 border-teal-200'\n    }\n    return colors[color as keyof typeof colors] || colors.blue\n  }\n\n  // Deduplicate categories and tags to prevent duplicates\n  const uniqueCategories = categories.filter((category, index, self) =>\n    index === self.findIndex(c => c._id === category._id)\n  )\n\n  const uniqueTags = tags.filter((tag, index, self) =>\n    index === self.findIndex(t => t._id === tag._id)\n  )\n\n  return (\n    <div className=\"mb-12\">\n      {/* Search Bar */}\n      <div className=\"max-w-3xl mx-auto mb-8\">\n        <form onSubmit={handleSearch} className=\"relative\">\n          <div className=\"relative\">\n            <Search size={20} className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search articles...\"\n              value={searchTerm}\n              onChange={(e) => {\n                setSearchTerm(e.target.value)\n                if (e.target.value === '') {\n                  applyFilters()\n                }\n              }}\n              className=\"w-full pl-12 pr-24 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-lg shadow-sm\"\n              disabled={loading}\n            />\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50\"\n            >\n              Search\n            </button>\n          </div>\n        </form>\n      </div>\n\n      {/* Filter Toggle Button */}\n      <div className=\"text-center mb-6\">\n        <button\n          onClick={() => setShowFilters(!showFilters)}\n          className=\"inline-flex items-center bg-white border border-gray-200 rounded-xl px-6 py-3 hover:bg-gray-50 transition-all duration-200 shadow-sm\"\n        >\n          <Filter size={18} className=\"mr-2 text-gray-600\" />\n          <span className=\"font-medium text-gray-700\">\n            {showFilters ? 'Hide Filters' : 'Show Filters'}\n          </span>\n          {hasActiveFilters && (\n            <span className=\"ml-2 bg-blue-600 text-white text-xs rounded-full px-2 py-1 font-medium\">\n              {[searchTerm, selectedCategory, selectedTag].filter(Boolean).length}\n            </span>\n          )}\n          {showFilters ? (\n            <ChevronUp size={18} className=\"ml-2 text-gray-400\" />\n          ) : (\n            <ChevronDown size={18} className=\"ml-2 text-gray-400\" />\n          )}\n        </button>\n      </div>\n\n      {/* Quick Filters - Categories and Tags in horizontal layout */}\n      {showFilters && (\n        <div className=\"max-w-5xl mx-auto mb-8\">\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            {/* Categories */}\n            <div className=\"mb-6\">\n              <h3 className=\"text-sm font-semibold text-gray-700 mb-4 flex items-center\">\n                <span className=\"w-2 h-2 bg-blue-500 rounded-full mr-2\"></span>\n                Categories\n              </h3>\n              <div className=\"flex flex-wrap gap-2\">\n                <button\n                  onClick={() => {\n                    setSelectedCategory('')\n                    applyFilters()\n                  }}\n                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${\n                    !selectedCategory\n                      ? 'bg-blue-600 text-white shadow-md'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                  }`}\n                >\n                  All\n                </button>\n                {uniqueCategories.map((category) => (\n                  <button\n                    key={category._id}\n                    onClick={() => {\n                      setSelectedCategory(category.slug.current)\n                      applyFilters()\n                    }}\n                    className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${\n                      selectedCategory === category.slug.current\n                        ? `${getCategoryColor(category.color)} shadow-md border`\n                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                    }`}\n                  >\n                    {category.title}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Tags */}\n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-700 mb-4 flex items-center\">\n                <span className=\"w-2 h-2 bg-purple-500 rounded-full mr-2\"></span>\n                Tags\n              </h3>\n              <div className=\"flex flex-wrap gap-2\">\n                <button\n                  onClick={() => {\n                    setSelectedTag('')\n                    applyFilters()\n                  }}\n                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${\n                    !selectedTag\n                      ? 'bg-purple-600 text-white shadow-md'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                  }`}\n                >\n                  All\n                </button>\n                {uniqueTags.slice(0, 8).map((tag) => (\n                  <button\n                    key={tag._id}\n                    onClick={() => {\n                      setSelectedTag(tag.slug.current)\n                      applyFilters()\n                    }}\n                    className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${\n                      selectedTag === tag.slug.current\n                        ? 'bg-purple-100 text-purple-800 border border-purple-200 shadow-md'\n                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                    }`}\n                  >\n                    {tag.title}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Active Filters Summary */}\n      {hasActiveFilters && (\n        <div className=\"max-w-5xl mx-auto mb-6\">\n          <div className=\"flex flex-wrap items-center gap-3\">\n            <span className=\"text-sm text-gray-600 font-medium\">Active filters:</span>\n            {searchTerm && (\n              <span className=\"inline-flex items-center bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm border border-blue-200\">\n                &quot;{searchTerm}&quot;\n                <button\n                  onClick={() => {\n                    setSearchTerm('')\n                    applyFilters()\n                  }}\n                  className=\"ml-2 hover:text-blue-900 transition-colors\"\n                >\n                  <X size={14} />\n                </button>\n              </span>\n            )}\n            {selectedCategory && (\n              <span className=\"inline-flex items-center bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm border border-green-200\">\n                {uniqueCategories.find(c => c.slug.current === selectedCategory)?.title}\n                <button\n                  onClick={() => {\n                    setSelectedCategory('')\n                    applyFilters()\n                  }}\n                  className=\"ml-2 hover:text-green-900 transition-colors\"\n                >\n                  <X size={14} />\n                </button>\n              </span>\n            )}\n            {selectedTag && (\n              <span className=\"inline-flex items-center bg-purple-50 text-purple-700 px-3 py-1 rounded-full text-sm border border-purple-200\">\n                {uniqueTags.find(t => t.slug.current === selectedTag)?.title}\n                <button\n                  onClick={() => {\n                    setSelectedTag('')\n                    applyFilters()\n                  }}\n                  className=\"ml-2 hover:text-purple-900 transition-colors\"\n                >\n                  <X size={14} />\n                </button>\n              </span>\n            )}\n            <button\n              onClick={clearFilters}\n              className=\"text-sm text-gray-500 hover:text-red-600 transition-colors duration-200 ml-2\"\n            >\n              Clear all\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default BlogFilters\n"], "names": [], "mappings": "AAAA,6DAA6D;;;;;AAC7D;AACA;AAAA;AAAA;AAAA;AAAA;;;;;AAUA,MAAM,cAA0C,CAAC,EAC/C,UAAU,EACV,IAAI,EACJ,cAAc,EACd,OAAO,EACR;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,UAA2B,CAAC;QAElC,IAAI,WAAW,IAAI,IAAI,QAAQ,MAAM,GAAG,WAAW,IAAI;QACvD,IAAI,kBAAkB,QAAQ,QAAQ,GAAG;QACzC,IAAI,aAAa,QAAQ,GAAG,GAAG;QAE/B,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,cAAc;QACd,oBAAoB;QACpB,eAAe;QACf,eAAe,CAAC;IAClB;IAEA,MAAM,mBAAmB,cAAc,oBAAoB;IAE3D,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,KAAK;YACL,MAAM;QACR;QACA,OAAO,MAAM,CAAC,MAA6B,IAAI,OAAO,IAAI;IAC5D;IAEA,wDAAwD;IACxD,MAAM,mBAAmB,WAAW,MAAM,CAAC,CAAC,UAAU,OAAO,OAC3D,UAAU,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,SAAS,GAAG;IAGtD,MAAM,aAAa,KAAK,MAAM,CAAC,CAAC,KAAK,OAAO,OAC1C,UAAU,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,IAAI,GAAG;IAGjD,qBACE,0JAAC;QAAI,WAAU;;0BAEb,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBAAK,UAAU;oBAAc,WAAU;8BACtC,cAAA,0JAAC;wBAAI,WAAU;;0CACb,0JAAC,kMAAA,CAAA,SAAM;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC5B,0JAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC;oCACT,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC5B,IAAI,EAAE,MAAM,CAAC,KAAK,KAAK,IAAI;wCACzB;oCACF;gCACF;gCACA,WAAU;gCACV,UAAU;;;;;;0CAEZ,0JAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBACC,SAAS,IAAM,eAAe,CAAC;oBAC/B,WAAU;;sCAEV,0JAAC,kMAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC5B,0JAAC;4BAAK,WAAU;sCACb,cAAc,iBAAiB;;;;;;wBAEjC,kCACC,0JAAC;4BAAK,WAAU;sCACb;gCAAC;gCAAY;gCAAkB;6BAAY,CAAC,MAAM,CAAC,SAAS,MAAM;;;;;;wBAGtE,4BACC,0JAAC,4MAAA,CAAA,YAAS;4BAAC,MAAM;4BAAI,WAAU;;;;;iDAE/B,0JAAC,gNAAA,CAAA,cAAW;4BAAC,MAAM;4BAAI,WAAU;;;;;;;;;;;;;;;;;YAMtC,6BACC,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBAAI,WAAU;;sCAEb,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAG,WAAU;;sDACZ,0JAAC;4CAAK,WAAU;;;;;;wCAA+C;;;;;;;8CAGjE,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CACC,SAAS;gDACP,oBAAoB;gDACpB;4CACF;4CACA,WAAW,CAAC,uEAAuE,EACjF,CAAC,mBACG,qCACA,+CACJ;sDACH;;;;;;wCAGA,iBAAiB,GAAG,CAAC,CAAC,yBACrB,0JAAC;gDAEC,SAAS;oDACP,oBAAoB,SAAS,IAAI,CAAC,OAAO;oDACzC;gDACF;gDACA,WAAW,CAAC,uEAAuE,EACjF,qBAAqB,SAAS,IAAI,CAAC,OAAO,GACtC,GAAG,iBAAiB,SAAS,KAAK,EAAE,iBAAiB,CAAC,GACtD,+CACJ;0DAED,SAAS,KAAK;+CAXV,SAAS,GAAG;;;;;;;;;;;;;;;;;sCAkBzB,0JAAC;;8CACC,0JAAC;oCAAG,WAAU;;sDACZ,0JAAC;4CAAK,WAAU;;;;;;wCAAiD;;;;;;;8CAGnE,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CACC,SAAS;gDACP,eAAe;gDACf;4CACF;4CACA,WAAW,CAAC,uEAAuE,EACjF,CAAC,cACG,uCACA,+CACJ;sDACH;;;;;;wCAGA,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC3B,0JAAC;gDAEC,SAAS;oDACP,eAAe,IAAI,IAAI,CAAC,OAAO;oDAC/B;gDACF;gDACA,WAAW,CAAC,uEAAuE,EACjF,gBAAgB,IAAI,IAAI,CAAC,OAAO,GAC5B,qEACA,+CACJ;0DAED,IAAI,KAAK;+CAXL,IAAI,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAqBzB,kCACC,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAK,WAAU;sCAAoC;;;;;;wBACnD,4BACC,0JAAC;4BAAK,WAAU;;gCAA0G;gCACjH;gCAAW;8CAClB,0JAAC;oCACC,SAAS;wCACP,cAAc;wCACd;oCACF;oCACA,WAAU;8CAEV,cAAA,0JAAC,wLAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;;;;;;;wBAId,kCACC,0JAAC;4BAAK,WAAU;;gCACb,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,OAAO,KAAK,mBAAmB;8CAClE,0JAAC;oCACC,SAAS;wCACP,oBAAoB;wCACpB;oCACF;oCACA,WAAU;8CAEV,cAAA,0JAAC,wLAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;;;;;;;wBAId,6BACC,0JAAC;4BAAK,WAAU;;gCACb,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,OAAO,KAAK,cAAc;8CACvD,0JAAC;oCACC,SAAS;wCACP,eAAe;wCACf;oCACF;oCACA,WAAU;8CAEV,cAAA,0JAAC,wLAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAIf,0JAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA7PM;KAAA;uCA+PS", "debugId": null}}, {"offset": {"line": 3582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/lib/sanity-blog.ts"], "sourcesContent": ["// lib/sanity-blog.ts - Sanity client utilities for blog system\nimport { createClient } from '@sanity/client'\nimport imageUrlBuilder from '@sanity/image-url'\nimport { BlogPost, BlogListItem, BlogCategory, BlogTag, BlogFilters, SanityImageObject } from '@/types/blog'\n\nexport const client = createClient({\n  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,\n  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,\n  apiVersion: '2023-05-03',\n  useCdn: false,\n})\n\n// Image URL builder\nconst builder = imageUrlBuilder(client)\n\nexport function urlForImage(source: SanityImageObject) {\n  return builder.image(source)\n}\n\n// GROQ Queries\nconst blogPostFields = `\n  _id,\n  title,\n  slug,\n  excerpt,\n  featuredImage,\n  content,\n  publishedAt,\n  categories[]->{\n    _id,\n    title,\n    slug,\n    description,\n    color\n  },\n  tags[]->{\n    _id,\n    title,\n    slug,\n    description\n  },\n  featured,\n  readingTime,\n  seo\n`\n\nconst blogListFields = `\n  _id,\n  title,\n  slug,\n  excerpt,\n  featuredImage,\n  publishedAt,\n  categories[]->{\n    _id,\n    title,\n    slug,\n    color\n  },\n  tags[]->{\n    _id,\n    title,\n    slug\n  },\n  featured,\n  readingTime\n`\n\n// Get all blog posts with pagination and filtering\nexport async function getBlogPosts(filters: BlogFilters = {}): Promise<{\n  posts: BlogListItem[];\n  totalPosts: number;\n  hasMore: boolean;\n}> {\n  const { category, tag, search, page = 1, limit = 12 } = filters\n  const offset = (page - 1) * limit\n\n  let filterQuery = '*[_type == \"blogPost\"'\n  const filterConditions: string[] = []\n\n  if (category) {\n    filterConditions.push(`\"${category}\" in categories[]->slug.current`)\n  }\n  \n  if (tag) {\n    filterConditions.push(`\"${tag}\" in tags[]->slug.current`)\n  }\n  \n  if (search) {\n    filterConditions.push(`(title match \"${search}*\" || excerpt match \"${search}*\")`)\n  }\n\n  if (filterConditions.length > 0) {\n    filterQuery += ` && (${filterConditions.join(' && ')})`\n  }\n\n  filterQuery += ']'\n\n  const query = `{\n    \"posts\": ${filterQuery} | order(publishedAt desc) [${offset}...${offset + limit}] {\n      ${blogListFields}\n    },\n    \"totalPosts\": count(${filterQuery})\n  }`\n\n  const result = await client.fetch(query)\n  \n  return {\n    posts: result.posts || [],\n    totalPosts: result.totalPosts || 0,\n    hasMore: result.totalPosts > offset + limit\n  }\n}\n\n// Get featured blog posts\nexport async function getFeaturedBlogPosts(limit: number = 3): Promise<BlogListItem[]> {\n  const query = `*[_type == \"blogPost\" && featured == true] | order(publishedAt desc) [0...${limit}] {\n    ${blogListFields}\n  }`\n  \n  return await client.fetch(query)\n}\n\n// Get single blog post by slug\nexport async function getBlogPost(slug: string): Promise<BlogPost | null> {\n  const query = `*[_type == \"blogPost\" && slug.current == $slug][0] {\n    ${blogPostFields}\n  }`\n  \n  return await client.fetch(query, { slug })\n}\n\n// Get related blog posts\nexport async function getRelatedBlogPosts(postId: string, categories: string[], limit: number = 3): Promise<BlogListItem[]> {\n  const query = `*[_type == \"blogPost\" && _id != $postId && count(categories[]->slug.current[@ in $categories]) > 0] | order(publishedAt desc) [0...${limit}] {\n    ${blogListFields}\n  }`\n  \n  return await client.fetch(query, { postId, categories })\n}\n\n// Get all blog categories\nexport async function getBlogCategories(): Promise<BlogCategory[]> {\n  const query = `*[_type == \"blogCategory\"] | order(title asc) {\n    _id,\n    title,\n    slug,\n    description,\n    color\n  }`\n  \n  return await client.fetch(query)\n}\n\n// Get all blog tags\nexport async function getBlogTags(): Promise<BlogTag[]> {\n  const query = `*[_type == \"blogTag\"] | order(title asc) {\n    _id,\n    title,\n    slug,\n    description\n  }`\n  \n  return await client.fetch(query)\n}\n\n\n\n// Get blog post slugs for static generation\nexport async function getBlogPostSlugs(): Promise<string[]> {\n  const query = `*[_type == \"blogPost\" && defined(slug.current)].slug.current`\n  return await client.fetch(query)\n}\n\n// Get recent blog posts for homepage or sidebar\nexport async function getRecentBlogPosts(limit: number = 5): Promise<BlogListItem[]> {\n  const query = `*[_type == \"blogPost\"] | order(publishedAt desc) [0...${limit}] {\n    ${blogListFields}\n  }`\n  \n  return await client.fetch(query)\n}\n"], "names": [], "mappings": "AAAA,+DAA+D;;;;;;;;;;;;;AAMlD;AALb;AACA;;;AAGO,MAAM,SAAS,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE;IACjC,SAAS;IACT,OAAO;IACP,YAAY;IACZ,QAAQ;AACV;AAEA,oBAAoB;AACpB,MAAM,UAAU,CAAA,GAAA,6KAAA,CAAA,UAAe,AAAD,EAAE;AAEzB,SAAS,YAAY,MAAyB;IACnD,OAAO,QAAQ,KAAK,CAAC;AACvB;AAEA,eAAe;AACf,MAAM,iBAAiB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAwBxB,CAAC;AAED,MAAM,iBAAiB,CAAC;;;;;;;;;;;;;;;;;;;;AAoBxB,CAAC;AAGM,eAAe,aAAa,UAAuB,CAAC,CAAC;IAK1D,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG;IACxD,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;IAE5B,IAAI,cAAc;IAClB,MAAM,mBAA6B,EAAE;IAErC,IAAI,UAAU;QACZ,iBAAiB,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,+BAA+B,CAAC;IACrE;IAEA,IAAI,KAAK;QACP,iBAAiB,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,yBAAyB,CAAC;IAC1D;IAEA,IAAI,QAAQ;QACV,iBAAiB,IAAI,CAAC,CAAC,cAAc,EAAE,OAAO,qBAAqB,EAAE,OAAO,GAAG,CAAC;IAClF;IAEA,IAAI,iBAAiB,MAAM,GAAG,GAAG;QAC/B,eAAe,CAAC,KAAK,EAAE,iBAAiB,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzD;IAEA,eAAe;IAEf,MAAM,QAAQ,CAAC;aACJ,EAAE,YAAY,4BAA4B,EAAE,OAAO,GAAG,EAAE,SAAS,MAAM;MAC9E,EAAE,eAAe;;wBAEC,EAAE,YAAY;GACnC,CAAC;IAEF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC;IAElC,OAAO;QACL,OAAO,OAAO,KAAK,IAAI,EAAE;QACzB,YAAY,OAAO,UAAU,IAAI;QACjC,SAAS,OAAO,UAAU,GAAG,SAAS;IACxC;AACF;AAGO,eAAe,qBAAqB,QAAgB,CAAC;IAC1D,MAAM,QAAQ,CAAC,0EAA0E,EAAE,MAAM;IAC/F,EAAE,eAAe;GAClB,CAAC;IAEF,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAGO,eAAe,YAAY,IAAY;IAC5C,MAAM,QAAQ,CAAC;IACb,EAAE,eAAe;GAClB,CAAC;IAEF,OAAO,MAAM,OAAO,KAAK,CAAC,OAAO;QAAE;IAAK;AAC1C;AAGO,eAAe,oBAAoB,MAAc,EAAE,UAAoB,EAAE,QAAgB,CAAC;IAC/F,MAAM,QAAQ,CAAC,mIAAmI,EAAE,MAAM;IACxJ,EAAE,eAAe;GAClB,CAAC;IAEF,OAAO,MAAM,OAAO,KAAK,CAAC,OAAO;QAAE;QAAQ;IAAW;AACxD;AAGO,eAAe;IACpB,MAAM,QAAQ,CAAC;;;;;;GAMd,CAAC;IAEF,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAGO,eAAe;IACpB,MAAM,QAAQ,CAAC;;;;;GAKd,CAAC;IAEF,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAKO,eAAe;IACpB,MAAM,QAAQ,CAAC,4DAA4D,CAAC;IAC5E,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAGO,eAAe,mBAAmB,QAAgB,CAAC;IACxD,MAAM,QAAQ,CAAC,sDAAsD,EAAE,MAAM;IAC3E,EAAE,eAAe;GAClB,CAAC;IAEF,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 3750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/blog/BlogGrid.tsx"], "sourcesContent": ["// components/blog/BlogGrid.tsx - Blog posts grid component\nimport React from 'react'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { Calendar, Clock, ArrowRight } from 'lucide-react'\nimport { BlogListItem } from '@/types/blog'\nimport { urlForImage } from '@/lib/sanity-blog'\n\ninterface BlogGridProps {\n  posts: BlogListItem[]\n  loading: boolean\n  onLoadMore?: () => void\n  hasMore?: boolean\n}\n\nconst BlogGrid: React.FC<BlogGridProps> = ({\n  posts,\n  loading,\n  onLoadMore,\n  hasMore\n}) => {\n  const getCategoryColor = (color: string) => {\n    const colors = {\n      blue: 'bg-blue-100 text-blue-800',\n      green: 'bg-green-100 text-green-800',\n      purple: 'bg-purple-100 text-purple-800',\n      orange: 'bg-orange-100 text-orange-800',\n      red: 'bg-red-100 text-red-800',\n      teal: 'bg-teal-100 text-teal-800'\n    }\n    return colors[color as keyof typeof colors] || colors.blue\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    })\n  }\n\n  if (loading && posts.length === 0) {\n    return (\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n        {Array.from({ length: 6 }).map((_, index) => (\n          <div key={index} className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden animate-pulse\">\n            <div className=\"w-full h-48 bg-gray-200\"></div>\n            <div className=\"p-6\">\n              <div className=\"flex gap-2 mb-3\">\n                <div className=\"h-6 bg-gray-200 rounded-full w-20\"></div>\n              </div>\n              <div className=\"h-6 bg-gray-200 rounded mb-3\"></div>\n              <div className=\"h-4 bg-gray-200 rounded mb-2\"></div>\n              <div className=\"h-4 bg-gray-200 rounded mb-4 w-3/4\"></div>\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-8 h-8 bg-gray-200 rounded-full\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-20\"></div>\n                </div>\n                <div className=\"h-4 bg-gray-200 rounded w-16\"></div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    )\n  }\n\n  if (posts.length === 0 && !loading) {\n    return (\n      <div className=\"text-center py-16\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n            <Calendar size={32} className=\"text-gray-400\" />\n          </div>\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No posts found</h3>\n          <p className=\"text-gray-600\">\n            Try adjusting your search terms or filters to find what you&apos;re looking for.\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div>\n      {/* Featured Posts (first 3 posts if they exist) */}\n      {posts.some(post => post.featured) && (\n        <div className=\"mb-16\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-8\">Featured Articles</h2>\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {posts.filter(post => post.featured).slice(0, 3).map((post) => (\n              <Link key={post._id} href={`/blog/${post.slug.current}`}>\n                <article className=\"group bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1\">\n                  <div className=\"relative h-56 overflow-hidden\">\n                    {post.featuredImage ? (\n                      <Image\n                        src={urlForImage(post.featuredImage).width(400).height(224).url()}\n                        alt={post.title}\n                        fill\n                        className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                      />\n                    ) : (\n                      <div className=\"w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center\">\n                        <div className=\"text-blue-600 text-center\">\n                          <div className=\"text-2xl font-bold mb-2\">VESA</div>\n                          <div className=\"text-sm\">Blog Article</div>\n                        </div>\n                      </div>\n                    )}\n                    <div className=\"absolute top-4 left-4\">\n                      <span className=\"bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium\">\n                        Featured\n                      </span>\n                    </div>\n                  </div>\n                  <div className=\"p-6\">\n                    <div className=\"flex flex-wrap gap-2 mb-3\">\n                      {post.categories.slice(0, 2).map((category) => (\n                        <span\n                          key={category._id}\n                          className={`px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(category.color)}`}\n                        >\n                          {category.title}\n                        </span>\n                      ))}\n                    </div>\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2\">\n                      {post.title}\n                    </h3>\n                    <p className=\"text-gray-600 mb-4 line-clamp-3\">\n                      {post.excerpt}\n                    </p>\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center gap-3\">\n                        <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                          <span className=\"text-blue-600 text-xs font-semibold\">V</span>\n                        </div>\n                        <span className=\"text-sm text-gray-600\">VESA Solutions</span>\n                      </div>\n                      <div className=\"flex items-center gap-4 text-sm text-gray-500\">\n                        <div className=\"flex items-center gap-1\">\n                          <Calendar size={14} />\n                          <span>{formatDate(post.publishedAt)}</span>\n                        </div>\n                        {post.readingTime && (\n                          <div className=\"flex items-center gap-1\">\n                            <Clock size={14} />\n                            <span>{post.readingTime} min</span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </article>\n              </Link>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* All Posts Grid */}\n      <div className=\"mb-12\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-8\">\n          {posts.some(post => post.featured) ? 'Latest Articles' : 'All Articles'}\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {posts.filter(post => !post.featured || posts.filter(p => p.featured).length > 3).map((post) => (\n            <Link key={post._id} href={`/blog/${post.slug.current}`}>\n              <article className=\"group bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1\">\n                <div className=\"relative h-48 overflow-hidden\">\n                  {post.featuredImage ? (\n                    <Image\n                      src={urlForImage(post.featuredImage).width(400).height(192).url()}\n                      alt={post.title}\n                      fill\n                      className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                  ) : (\n                    <div className=\"w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center\">\n                      <div className=\"text-blue-600 text-center\">\n                        <div className=\"text-xl font-bold mb-1\">VESA</div>\n                        <div className=\"text-xs\">Blog Article</div>\n                      </div>\n                    </div>\n                  )}\n                </div>\n                <div className=\"p-6\">\n                  <div className=\"flex flex-wrap gap-2 mb-3\">\n                    {post.categories.slice(0, 2).map((category) => (\n                      <span\n                        key={category._id}\n                        className={`px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(category.color)}`}\n                      >\n                        {category.title}\n                      </span>\n                    ))}\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2\">\n                    {post.title}\n                  </h3>\n                  <p className=\"text-gray-600 mb-4 line-clamp-2\">\n                    {post.excerpt}\n                  </p>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center\">\n                        <span className=\"text-blue-600 text-xs font-semibold\">V</span>\n                      </div>\n                      <span className=\"text-sm text-gray-600\">VESA Solutions</span>\n                    </div>\n                    <div className=\"flex items-center gap-3 text-xs text-gray-500\">\n                      <div className=\"flex items-center gap-1\">\n                        <Calendar size={12} />\n                        <span>{formatDate(post.publishedAt)}</span>\n                      </div>\n                      {post.readingTime && (\n                        <div className=\"flex items-center gap-1\">\n                          <Clock size={12} />\n                          <span>{post.readingTime}m</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </article>\n            </Link>\n          ))}\n        </div>\n      </div>\n\n      {/* Load More Button */}\n      {hasMore && onLoadMore && (\n        <div className=\"text-center\">\n          <button\n            onClick={onLoadMore}\n            disabled={loading}\n            className=\"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition-colors duration-200 disabled:opacity-50\"\n          >\n            {loading ? (\n              <>\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                Loading...\n              </>\n            ) : (\n              <>\n                Load More Articles\n                <ArrowRight size={16} className=\"ml-2\" />\n              </>\n            )}\n          </button>\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default BlogGrid\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAE3D;AACA;AACA;AAAA;AAAA;AAEA;;;;;;AASA,MAAM,WAAoC,CAAC,EACzC,KAAK,EACL,OAAO,EACP,UAAU,EACV,OAAO,EACR;IACC,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,KAAK;YACL,MAAM;QACR;QACA,OAAO,MAAM,CAAC,MAA6B,IAAI,OAAO,IAAI;IAC5D;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,IAAI,WAAW,MAAM,MAAM,KAAK,GAAG;QACjC,qBACE,0JAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,0JAAC;oBAAgB,WAAU;;sCACzB,0JAAC;4BAAI,WAAU;;;;;;sCACf,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,0JAAC;oCAAI,WAAU;;;;;;8CACf,0JAAC;oCAAI,WAAU;;;;;;8CACf,0JAAC;oCAAI,WAAU;;;;;;8CACf,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;;;;;;8DACf,0JAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,0JAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;mBAdX;;;;;;;;;;IAqBlB;IAEA,IAAI,MAAM,MAAM,KAAK,KAAK,CAAC,SAAS;QAClC,qBACE,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC,sMAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;;;;;;kCAEhC,0JAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,0JAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAMrC;IAEA,qBACE,0JAAC;;YAEE,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,mBAC/B,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,0JAAC;wBAAI,WAAU;kCACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACpD,0JAAC,wHAAA,CAAA,UAAI;gCAAgB,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE;0CACrD,cAAA,0JAAC;oCAAQ,WAAU;;sDACjB,0JAAC;4CAAI,WAAU;;gDACZ,KAAK,aAAa,iBACjB,0JAAC,yHAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,aAAa,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG;oDAC/D,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;yEAGZ,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAA0B;;;;;;0EACzC,0JAAC;gEAAI,WAAU;0EAAU;;;;;;;;;;;;;;;;;8DAI/B,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAK,WAAU;kEAAoE;;;;;;;;;;;;;;;;;sDAKxF,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;8DACZ,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAChC,0JAAC;4DAEC,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,SAAS,KAAK,GAAG;sEAE1F,SAAS,KAAK;2DAHV,SAAS,GAAG;;;;;;;;;;8DAOvB,0JAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,0JAAC;oDAAE,WAAU;8DACV,KAAK,OAAO;;;;;;8DAEf,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEAAI,WAAU;8EACb,cAAA,0JAAC;wEAAK,WAAU;kFAAsC;;;;;;;;;;;8EAExD,0JAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;sEAE1C,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEAAI,WAAU;;sFACb,0JAAC,sMAAA,CAAA,WAAQ;4EAAC,MAAM;;;;;;sFAChB,0JAAC;sFAAM,WAAW,KAAK,WAAW;;;;;;;;;;;;gEAEnC,KAAK,WAAW,kBACf,0JAAC;oEAAI,WAAU;;sFACb,0JAAC,gMAAA,CAAA,QAAK;4EAAC,MAAM;;;;;;sFACb,0JAAC;;gFAAM,KAAK,WAAW;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAxD3B,KAAK,GAAG;;;;;;;;;;;;;;;;0BAsE3B,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAG,WAAU;kCACX,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,IAAI,oBAAoB;;;;;;kCAE3D,0JAAC;wBAAI,WAAU;kCACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ,IAAI,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrF,0JAAC,wHAAA,CAAA,UAAI;gCAAgB,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE;0CACrD,cAAA,0JAAC;oCAAQ,WAAU;;sDACjB,0JAAC;4CAAI,WAAU;sDACZ,KAAK,aAAa,iBACjB,0JAAC,yHAAA,CAAA,UAAK;gDACJ,KAAK,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,aAAa,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG;gDAC/D,KAAK,KAAK,KAAK;gDACf,IAAI;gDACJ,WAAU;;;;;qEAGZ,0JAAC;gDAAI,WAAU;0DACb,cAAA,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAI,WAAU;sEAAyB;;;;;;sEACxC,0JAAC;4DAAI,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;sDAKjC,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;8DACZ,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAChC,0JAAC;4DAEC,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,SAAS,KAAK,GAAG;sEAE1F,SAAS,KAAK;2DAHV,SAAS,GAAG;;;;;;;;;;8DAOvB,0JAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,0JAAC;oDAAE,WAAU;8DACV,KAAK,OAAO;;;;;;8DAEf,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEAAI,WAAU;8EACb,cAAA,0JAAC;wEAAK,WAAU;kFAAsC;;;;;;;;;;;8EAExD,0JAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;sEAE1C,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEAAI,WAAU;;sFACb,0JAAC,sMAAA,CAAA,WAAQ;4EAAC,MAAM;;;;;;sFAChB,0JAAC;sFAAM,WAAW,KAAK,WAAW;;;;;;;;;;;;gEAEnC,KAAK,WAAW,kBACf,0JAAC;oEAAI,WAAU;;sFACb,0JAAC,gMAAA,CAAA,QAAK;4EAAC,MAAM;;;;;;sFACb,0JAAC;;gFAAM,KAAK,WAAW;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAnD3B,KAAK,GAAG;;;;;;;;;;;;;;;;YAgExB,WAAW,4BACV,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBACC,SAAS;oBACT,UAAU;oBACV,WAAU;8BAET,wBACC;;0CACE,0JAAC;gCAAI,WAAU;;;;;;4BAAuE;;qDAIxF;;4BAAE;0CAEA,0JAAC,8MAAA,CAAA,aAAU;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;KAhPM;uCAkPS", "debugId": null}}, {"offset": {"line": 4477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/blog/BlogPagination.tsx"], "sourcesContent": ["// components/blog/BlogPagination.tsx - Pagination component for blog\nimport React from 'react'\nimport { ChevronLeft, ChevronRight } from 'lucide-react'\n\ninterface BlogPaginationProps {\n  currentPage: number\n  totalPosts: number\n  postsPerPage: number\n  onPageChange: (page: number) => void\n  loading: boolean\n}\n\nconst BlogPagination: React.FC<BlogPaginationProps> = ({\n  currentPage,\n  totalPosts,\n  postsPerPage,\n  onPageChange,\n  loading\n}) => {\n  const totalPages = Math.ceil(totalPosts / postsPerPage)\n\n  if (totalPages <= 1) return null\n\n  const getVisiblePages = () => {\n    const delta = 2\n    const range = []\n    const rangeWithDots = []\n\n    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {\n      range.push(i)\n    }\n\n    if (currentPage - delta > 2) {\n      rangeWithDots.push(1, '...')\n    } else {\n      rangeWithDots.push(1)\n    }\n\n    rangeWithDots.push(...range)\n\n    if (currentPage + delta < totalPages - 1) {\n      rangeWithDots.push('...', totalPages)\n    } else {\n      rangeWithDots.push(totalPages)\n    }\n\n    return rangeWithDots\n  }\n\n  const visiblePages = getVisiblePages()\n\n  return (\n    <div className=\"flex items-center justify-between mt-12 pt-8 border-t border-gray-200\">\n      {/* Results Info */}\n      <div className=\"text-sm text-gray-600\">\n        Showing {((currentPage - 1) * postsPerPage) + 1} to {Math.min(currentPage * postsPerPage, totalPosts)} of {totalPosts} articles\n      </div>\n\n      {/* Pagination Controls */}\n      <div className=\"flex items-center space-x-2\">\n        {/* Previous Button */}\n        <button\n          onClick={() => onPageChange(currentPage - 1)}\n          disabled={currentPage === 1 || loading}\n          className=\"inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\"\n        >\n          <ChevronLeft size={16} className=\"mr-1\" />\n          Previous\n        </button>\n\n        {/* Page Numbers */}\n        <div className=\"hidden sm:flex items-center space-x-1\">\n          {visiblePages.map((page, index) => (\n            <React.Fragment key={index}>\n              {page === '...' ? (\n                <span className=\"px-3 py-2 text-gray-500\">...</span>\n              ) : (\n                <button\n                  onClick={() => onPageChange(page as number)}\n                  disabled={loading}\n                  className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${\n                    currentPage === page\n                      ? 'bg-blue-600 text-white'\n                      : 'text-gray-700 hover:bg-gray-100 disabled:opacity-50'\n                  }`}\n                >\n                  {page}\n                </button>\n              )}\n            </React.Fragment>\n          ))}\n        </div>\n\n        {/* Mobile Page Info */}\n        <div className=\"sm:hidden px-3 py-2 text-sm text-gray-600\">\n          {currentPage} of {totalPages}\n        </div>\n\n        {/* Next Button */}\n        <button\n          onClick={() => onPageChange(currentPage + 1)}\n          disabled={currentPage === totalPages || loading}\n          className=\"inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\"\n        >\n          Next\n          <ChevronRight size={16} className=\"ml-1\" />\n        </button>\n      </div>\n    </div>\n  )\n}\n\nexport default BlogPagination\n"], "names": [], "mappings": "AAAA,qEAAqE;;;;;AACrE;AACA;AAAA;;;;AAUA,MAAM,iBAAgD,CAAC,EACrD,WAAW,EACX,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,OAAO,EACR;IACC,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;IAE1C,IAAI,cAAc,GAAG,OAAO;IAE5B,MAAM,kBAAkB;QACtB,MAAM,QAAQ;QACd,MAAM,QAAQ,EAAE;QAChB,MAAM,gBAAgB,EAAE;QAExB,IAAK,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,cAAc,QAAQ,KAAK,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc,QAAQ,IAAK;YACtG,MAAM,IAAI,CAAC;QACb;QAEA,IAAI,cAAc,QAAQ,GAAG;YAC3B,cAAc,IAAI,CAAC,GAAG;QACxB,OAAO;YACL,cAAc,IAAI,CAAC;QACrB;QAEA,cAAc,IAAI,IAAI;QAEtB,IAAI,cAAc,QAAQ,aAAa,GAAG;YACxC,cAAc,IAAI,CAAC,OAAO;QAC5B,OAAO;YACL,cAAc,IAAI,CAAC;QACrB;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,qBACE,0JAAC;QAAI,WAAU;;0BAEb,0JAAC;gBAAI,WAAU;;oBAAwB;oBAC3B,CAAC,cAAc,CAAC,IAAI,eAAgB;oBAAE;oBAAK,KAAK,GAAG,CAAC,cAAc,cAAc;oBAAY;oBAAK;oBAAW;;;;;;;0BAIxH,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBACC,SAAS,IAAM,aAAa,cAAc;wBAC1C,UAAU,gBAAgB,KAAK;wBAC/B,WAAU;;0CAEV,0JAAC,gNAAA,CAAA,cAAW;gCAAC,MAAM;gCAAI,WAAU;;;;;;4BAAS;;;;;;;kCAK5C,0JAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,0JAAC,0HAAA,CAAA,UAAK,CAAC,QAAQ;0CACZ,SAAS,sBACR,0JAAC;oCAAK,WAAU;8CAA0B;;;;;yDAE1C,0JAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,UAAU;oCACV,WAAW,CAAC,wEAAwE,EAClF,gBAAgB,OACZ,2BACA,uDACJ;8CAED;;;;;;+BAbc;;;;;;;;;;kCAqBzB,0JAAC;wBAAI,WAAU;;4BACZ;4BAAY;4BAAK;;;;;;;kCAIpB,0JAAC;wBACC,SAAS,IAAM,aAAa,cAAc;wBAC1C,UAAU,gBAAgB,cAAc;wBACxC,WAAU;;4BACX;0CAEC,0JAAC,kNAAA,CAAA,eAAY;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAK5C;KAlGM;uCAoGS", "debugId": null}}, {"offset": {"line": 4642, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/blog/single/BlogPostHero.tsx"], "sourcesContent": ["// components/blog/single/BlogPostHero.tsx - Hero section for individual blog posts\nimport React from 'react'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport { Calendar, Clock, User, ArrowLeft } from 'lucide-react'\nimport { BlogPost } from '@/types/blog'\nimport { urlForImage } from '@/lib/sanity-blog'\n\ninterface BlogPostHeroProps {\n  post: BlogPost\n}\n\nconst BlogPostHero: React.FC<BlogPostHeroProps> = ({ post }) => {\n  // const getCategoryColor = (color: string) => {\n  //   const colors = {\n  //     blue: 'bg-blue-100 text-blue-800',\n  //     green: 'bg-green-100 text-green-800',\n  //     purple: 'bg-purple-100 text-purple-800',\n  //     orange: 'bg-orange-100 text-orange-800',\n  //     red: 'bg-red-100 text-red-800',\n  //     teal: 'bg-teal-100 text-teal-800'\n  //   }\n  //   return colors[color as keyof typeof colors] || colors.blue\n  // }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    })\n  }\n\n  return (\n    <section className=\"relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 text-white py-12 overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/20 to-transparent\"></div>\n        <div className=\"absolute top-20 right-0 w-72 h-72 bg-blue-500/20 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-0 left-20 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Back to Blog */}\n        <div className=\"mb-6\">\n          <Link\n            href=\"/blog\"\n            className=\"inline-flex items-center text-blue-200 hover:text-white transition-colors duration-200\"\n          >\n            <ArrowLeft size={16} className=\"mr-2\" />\n            Back to Blog\n          </Link>\n        </div>\n\n        <div className=\"text-center max-w-3xl mx-auto\">\n          {/* Categories */}\n          <div className=\"flex flex-wrap justify-center gap-2 mb-4\">\n            {post.categories.map((category) => (\n              <span\n                key={category._id}\n                className=\"bg-white/20 backdrop-blur-sm border border-white/30 text-white px-3 py-1 rounded-full text-sm font-medium\"\n              >\n                {category.title}\n              </span>\n            ))}\n            {post.featured && (\n              <span className=\"bg-blue-500/30 backdrop-blur-sm border border-blue-400/30 text-blue-100 px-3 py-1 rounded-full text-sm font-medium\">\n                Featured\n              </span>\n            )}\n          </div>\n\n          {/* Title */}\n          <h1 className=\"text-3xl md:text-4xl lg:text-5xl font-bold mb-4 leading-tight\">\n            {post.title}\n          </h1>\n\n          {/* Excerpt */}\n          <p className=\"text-lg md:text-xl text-blue-100 mb-6 leading-relaxed\">\n            {post.excerpt}\n          </p>\n\n          {/* Meta Information */}\n          <div className=\"flex flex-wrap justify-center items-center gap-4 text-blue-200 text-sm\">\n            {/* Date */}\n            <div className=\"flex items-center gap-1\">\n              <Calendar size={14} />\n              <span>{formatDate(post.publishedAt)}</span>\n            </div>\n\n            {/* Reading Time */}\n            {post.readingTime && (\n              <div className=\"flex items-center gap-1\">\n                <Clock size={14} />\n                <span>{post.readingTime} min read</span>\n              </div>\n            )}\n\n            {/* Author */}\n            <div className=\"flex items-center gap-1\">\n              <User size={14} />\n              <span>VESA Solutions</span>\n            </div>\n          </div>\n\n          {/* Featured Image */}\n          {post.featuredImage && (\n            <div className=\"mt-8\">\n              <div className=\"relative h-64 md:h-80 rounded-lg overflow-hidden shadow-lg mx-auto max-w-2xl\">\n                <Image\n                  src={urlForImage(post.featuredImage).width(800).height(400).url()}\n                  alt={post.title}\n                  fill\n                  className=\"object-cover\"\n                  priority\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent\"></div>\n              </div>\n            </div>\n          )}\n\n        </div>\n      </div>\n\n      {/* Bottom Wave */}\n      <div className=\"absolute bottom-0 left-0 w-full -mb-px\">\n        <svg\n          viewBox=\"0 0 1440 120\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"w-full h-auto block\"\n          preserveAspectRatio=\"none\"\n        >\n          <path\n            d=\"M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 *********** 720 120C600 *********** 360 120C240 *********** 60 120H0Z\"\n            fill=\"white\"\n          />\n        </svg>\n      </div>\n    </section>\n  )\n}\n\nexport default BlogPostHero\n"], "names": [], "mappings": "AAAA,mFAAmF;;;;;AAEnF;AACA;AACA;AAAA;AAAA;AAAA;AAEA;;;;;;AAMA,MAAM,eAA4C,CAAC,EAAE,IAAI,EAAE;IACzD,gDAAgD;IAChD,qBAAqB;IACrB,yCAAyC;IACzC,4CAA4C;IAC5C,+CAA+C;IAC/C,+CAA+C;IAC/C,sCAAsC;IACtC,wCAAwC;IACxC,MAAM;IACN,+DAA+D;IAC/D,IAAI;IAEJ,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,qBACE,0JAAC;QAAQ,WAAU;;0BAEjB,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,0JAAC,4MAAA,CAAA,YAAS;oCAAC,MAAM;oCAAI,WAAU;;;;;;gCAAS;;;;;;;;;;;;kCAK5C,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;;oCACZ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,yBACpB,0JAAC;4CAEC,WAAU;sDAET,SAAS,KAAK;2CAHV,SAAS,GAAG;;;;;oCAMpB,KAAK,QAAQ,kBACZ,0JAAC;wCAAK,WAAU;kDAAqH;;;;;;;;;;;;0CAOzI,0JAAC;gCAAG,WAAU;0CACX,KAAK,KAAK;;;;;;0CAIb,0JAAC;gCAAE,WAAU;0CACV,KAAK,OAAO;;;;;;0CAIf,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC;wCAAI,WAAU;;0DACb,0JAAC,sMAAA,CAAA,WAAQ;gDAAC,MAAM;;;;;;0DAChB,0JAAC;0DAAM,WAAW,KAAK,WAAW;;;;;;;;;;;;oCAInC,KAAK,WAAW,kBACf,0JAAC;wCAAI,WAAU;;0DACb,0JAAC,gMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;0DACb,0JAAC;;oDAAM,KAAK,WAAW;oDAAC;;;;;;;;;;;;;kDAK5B,0JAAC;wCAAI,WAAU;;0DACb,0JAAC,8LAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;0DACZ,0JAAC;0DAAK;;;;;;;;;;;;;;;;;;4BAKT,KAAK,aAAa,kBACjB,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;;sDACb,0JAAC,yHAAA,CAAA,UAAK;4CACJ,KAAK,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,aAAa,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG;4CAC/D,KAAK,KAAK,KAAK;4CACf,IAAI;4CACJ,WAAU;4CACV,QAAQ;;;;;;sDAEV,0JAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzB,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBACC,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;oBACV,qBAAoB;8BAEpB,cAAA,0JAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;;;;;;;;;;;AAMjB;KAjIM;uCAmIS", "debugId": null}}, {"offset": {"line": 4957, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/blog/single/BlogPostContent.tsx"], "sourcesContent": ["// components/blog/single/BlogPostContent.tsx - Blog post content with Portable Text\nimport React from 'react'\nimport Image from 'next/image'\nimport { PortableText } from '@portabletext/react'\nimport { BlogPost } from '@/types/blog'\nimport { urlForImage } from '@/lib/sanity-blog'\n\ninterface BlogPostContentProps {\n  post: BlogPost\n}\n\ninterface ImageValue {\n  _type: 'image'\n  asset: {\n    _ref: string\n    _type: 'reference'\n  }\n  alt?: string\n  caption?: string\n}\n\n\n\nconst BlogPostContent: React.FC<BlogPostContentProps> = ({ post }) => {\n  const portableTextComponents = {\n    types: {\n      image: ({ value }: { value: ImageValue }) => (\n        <div className=\"my-8\">\n          <div className=\"relative rounded-lg overflow-hidden shadow-lg\">\n            <Image\n              src={urlForImage(value).width(800).height(450).url()}\n              alt={value.alt || 'Blog post image'}\n              width={800}\n              height={450}\n              className=\"w-full h-auto\"\n            />\n          </div>\n          {value.caption && (\n            <p className=\"text-sm text-gray-600 text-center mt-2 italic\">\n              {value.caption}\n            </p>\n          )}\n        </div>\n      ),\n    },\n    block: {\n      normal: ({ children }: { children?: React.ReactNode }) => (\n        <p className=\"mb-6 text-gray-700 leading-relaxed text-lg\">\n          {children}\n        </p>\n      ),\n      h2: ({ children }: { children?: React.ReactNode }) => (\n        <h2 className=\"text-2xl md:text-3xl font-bold text-gray-900 mt-12 mb-6 leading-tight\">\n          {children}\n        </h2>\n      ),\n      h3: ({ children }: { children?: React.ReactNode }) => (\n        <h3 className=\"text-xl md:text-2xl font-semibold text-gray-900 mt-10 mb-4 leading-tight\">\n          {children}\n        </h3>\n      ),\n      h4: ({ children }: { children?: React.ReactNode }) => (\n        <h4 className=\"text-lg md:text-xl font-semibold text-gray-900 mt-8 mb-3 leading-tight\">\n          {children}\n        </h4>\n      ),\n      blockquote: ({ children }: { children?: React.ReactNode }) => (\n        <blockquote className=\"border-l-4 border-blue-500 pl-6 py-4 my-8 bg-blue-50 rounded-r-lg\">\n          <div className=\"text-lg text-gray-700 italic leading-relaxed\">\n            {children}\n          </div>\n        </blockquote>\n      ),\n    },\n    list: {\n      bullet: ({ children }: { children?: React.ReactNode }) => (\n        <ul className=\"mb-6 space-y-2 text-gray-700\">\n          {children}\n        </ul>\n      ),\n      number: ({ children }: { children?: React.ReactNode }) => (\n        <ol className=\"mb-6 space-y-2 text-gray-700 list-decimal list-inside\">\n          {children}\n        </ol>\n      ),\n    },\n    listItem: {\n      bullet: ({ children }: { children?: React.ReactNode }) => (\n        <li className=\"flex items-start\">\n          <span className=\"text-blue-500 mr-2 mt-2\">•</span>\n          <span className=\"text-lg leading-relaxed\">{children}</span>\n        </li>\n      ),\n      number: ({ children }: { children?: React.ReactNode }) => (\n        <li className=\"text-lg leading-relaxed ml-4\">\n          {children}\n        </li>\n      ),\n    },\n    marks: {\n      strong: ({ children }: { children?: React.ReactNode }) => (\n        <strong className=\"font-semibold text-gray-900\">{children}</strong>\n      ),\n      em: ({ children }: { children?: React.ReactNode }) => (\n        <em className=\"italic\">{children}</em>\n      ),\n      code: ({ children }: { children?: React.ReactNode }) => (\n        <code className=\"bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono\">\n          {children}\n        </code>\n      ),\n      link: ({ children, value }: { children?: React.ReactNode; value?: { href: string } }) => (\n        <a\n          href={value?.href}\n          target={value?.href?.startsWith('http') ? '_blank' : '_self'}\n          rel={value?.href?.startsWith('http') ? 'noopener noreferrer' : undefined}\n          className=\"text-blue-600 hover:text-blue-800 underline transition-colors duration-200\"\n        >\n          {children}\n        </a>\n      ),\n    },\n  } as const\n\n  return (\n    <article className=\"prose prose-lg max-w-none\">\n      <div className=\"blog-content\">\n        <PortableText\n          value={post.content}\n          components={portableTextComponents}\n        />\n      </div>\n\n      {/* Article Footer */}\n      <div className=\"mt-12 pt-8 border-t border-gray-200\">\n        <div className=\"flex flex-wrap gap-2\">\n          <span className=\"text-sm text-gray-600 font-medium\">Tags:</span>\n          {post.tags && post.tags.length > 0 ? (\n            post.tags.map((tag) => (\n              <span\n                key={tag._id}\n                className=\"inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-gray-200 transition-colors duration-200\"\n              >\n                {tag.title}\n              </span>\n            ))\n          ) : (\n            <span className=\"text-sm text-gray-500\">No tags</span>\n          )}\n        </div>\n      </div>\n\n      <style jsx global>{`\n        .blog-content h2 {\n          scroll-margin-top: 100px;\n        }\n        .blog-content h3 {\n          scroll-margin-top: 100px;\n        }\n        .blog-content h4 {\n          scroll-margin-top: 100px;\n        }\n        .blog-content pre {\n          background: #f8f9fa;\n          border: 1px solid #e9ecef;\n          border-radius: 8px;\n          padding: 1rem;\n          overflow-x: auto;\n          margin: 2rem 0;\n        }\n        .blog-content code {\n          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n        }\n        .blog-content table {\n          width: 100%;\n          border-collapse: collapse;\n          margin: 2rem 0;\n        }\n        .blog-content th,\n        .blog-content td {\n          border: 1px solid #e9ecef;\n          padding: 0.75rem;\n          text-align: left;\n        }\n        .blog-content th {\n          background: #f8f9fa;\n          font-weight: 600;\n        }\n      `}</style>\n    </article>\n  )\n}\n\nexport default BlogPostContent\n"], "names": [], "mappings": "AAAA,oFAAoF;;;;;;AAEpF;AACA;AAEA;;;;;;AAkBA,MAAM,kBAAkD,CAAC,EAAE,IAAI,EAAE;IAC/D,MAAM,yBAAyB;QAC7B,OAAO;YACL,OAAO,CAAC,EAAE,KAAK,EAAyB,iBACtC,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC,yHAAA,CAAA,UAAK;gCACJ,KAAK,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG;gCAClD,KAAK,MAAM,GAAG,IAAI;gCAClB,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;wBAGb,MAAM,OAAO,kBACZ,0JAAC;4BAAE,WAAU;sCACV,MAAM,OAAO;;;;;;;;;;;;QAKxB;QACA,OAAO;YACL,QAAQ,CAAC,EAAE,QAAQ,EAAkC,iBACnD,0JAAC;oBAAE,WAAU;8BACV;;;;;;YAGL,IAAI,CAAC,EAAE,QAAQ,EAAkC,iBAC/C,0JAAC;oBAAG,WAAU;8BACX;;;;;;YAGL,IAAI,CAAC,EAAE,QAAQ,EAAkC,iBAC/C,0JAAC;oBAAG,WAAU;8BACX;;;;;;YAGL,IAAI,CAAC,EAAE,QAAQ,EAAkC,iBAC/C,0JAAC;oBAAG,WAAU;8BACX;;;;;;YAGL,YAAY,CAAC,EAAE,QAAQ,EAAkC,iBACvD,0JAAC;oBAAW,WAAU;8BACpB,cAAA,0JAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;QAIT;QACA,MAAM;YACJ,QAAQ,CAAC,EAAE,QAAQ,EAAkC,iBACnD,0JAAC;oBAAG,WAAU;8BACX;;;;;;YAGL,QAAQ,CAAC,EAAE,QAAQ,EAAkC,iBACnD,0JAAC;oBAAG,WAAU;8BACX;;;;;;QAGP;QACA,UAAU;YACR,QAAQ,CAAC,EAAE,QAAQ,EAAkC,iBACnD,0JAAC;oBAAG,WAAU;;sCACZ,0JAAC;4BAAK,WAAU;sCAA0B;;;;;;sCAC1C,0JAAC;4BAAK,WAAU;sCAA2B;;;;;;;;;;;;YAG/C,QAAQ,CAAC,EAAE,QAAQ,EAAkC,iBACnD,0JAAC;oBAAG,WAAU;8BACX;;;;;;QAGP;QACA,OAAO;YACL,QAAQ,CAAC,EAAE,QAAQ,EAAkC,iBACnD,0JAAC;oBAAO,WAAU;8BAA+B;;;;;;YAEnD,IAAI,CAAC,EAAE,QAAQ,EAAkC,iBAC/C,0JAAC;oBAAG,WAAU;8BAAU;;;;;;YAE1B,MAAM,CAAC,EAAE,QAAQ,EAAkC,iBACjD,0JAAC;oBAAK,WAAU;8BACb;;;;;;YAGL,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,EAA4D,iBAClF,0JAAC;oBACC,MAAM,OAAO;oBACb,QAAQ,OAAO,MAAM,WAAW,UAAU,WAAW;oBACrD,KAAK,OAAO,MAAM,WAAW,UAAU,wBAAwB;oBAC/D,WAAU;8BAET;;;;;;QAGP;IACF;IAEA,qBACE,0JAAC;kDAAkB;;0BACjB,0JAAC;0DAAc;0BACb,cAAA,0JAAC,oKAAA,CAAA,eAAY;oBACX,OAAO,KAAK,OAAO;oBACnB,YAAY;;;;;;;;;;;0BAKhB,0JAAC;0DAAc;0BACb,cAAA,0JAAC;8DAAc;;sCACb,0JAAC;sEAAe;sCAAoC;;;;;;wBACnD,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,IAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,oBACb,0JAAC;0EAEW;0CAET,IAAI,KAAK;+BAHL,IAAI,GAAG;;;;sDAOhB,0JAAC;sEAAe;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CpD;KAxKM;uCA0KS", "debugId": null}}, {"offset": {"line": 5231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/blog/single/BlogPostSocial.tsx"], "sourcesContent": ["// components/blog/single/BlogPostSocial.tsx - Social sharing component\nimport React from 'react'\nimport { Share2, Facebook, Twitter, Linkedin, Link as LinkIcon } from 'lucide-react'\nimport { BlogPost } from '@/types/blog'\n\ninterface BlogPostSocialProps {\n  post: BlogPost\n}\n\nconst BlogPostSocial: React.FC<BlogPostSocialProps> = ({ post }) => {\n  const postUrl = `https://vesasolutions.com/blog/${post.slug.current}`\n  const postTitle = encodeURIComponent(post.title)\n  // const postDescription = encodeURIComponent(post.excerpt)\n\n  const shareLinks = {\n    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(postUrl)}`,\n    twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(postUrl)}&text=${postTitle}`,\n    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(postUrl)}`\n  }\n\n  const copyToClipboard = async () => {\n    try {\n      await navigator.clipboard.writeText(postUrl)\n      // You could add a toast notification here\n      alert('Link copied to clipboard!')\n    } catch (err) {\n      console.error('Failed to copy link:', err)\n    }\n  }\n\n  const handleShare = (platform: string) => {\n    const url = shareLinks[platform as keyof typeof shareLinks]\n    if (url) {\n      window.open(url, '_blank', 'width=600,height=400')\n    }\n  }\n\n  return (\n    <div className=\"mt-8 pt-6 border-t border-gray-200\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-2\">\n          <Share2 size={20} className=\"text-gray-600\" />\n          <span className=\"text-gray-700 font-medium\">Share this article:</span>\n        </div>\n        \n        <div className=\"flex items-center gap-3\">\n          <button\n            onClick={() => handleShare('facebook')}\n            className=\"inline-flex items-center justify-center w-10 h-10 bg-blue-600 hover:bg-blue-700 text-white rounded-full transition-colors duration-200\"\n            title=\"Share on Facebook\"\n            aria-label=\"Share on Facebook\"\n          >\n            <Facebook size={16} />\n          </button>\n\n          <button\n            onClick={() => handleShare('twitter')}\n            className=\"inline-flex items-center justify-center w-10 h-10 bg-sky-500 hover:bg-sky-600 text-white rounded-full transition-colors duration-200\"\n            title=\"Share on Twitter\"\n            aria-label=\"Share on Twitter\"\n          >\n            <Twitter size={16} />\n          </button>\n\n          <button\n            onClick={() => handleShare('linkedin')}\n            className=\"inline-flex items-center justify-center w-10 h-10 bg-blue-700 hover:bg-blue-800 text-white rounded-full transition-colors duration-200\"\n            title=\"Share on LinkedIn\"\n            aria-label=\"Share on LinkedIn\"\n          >\n            <Linkedin size={16} />\n          </button>\n\n          <button\n            onClick={copyToClipboard}\n            className=\"inline-flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-700 text-white rounded-full transition-colors duration-200\"\n            title=\"Copy link\"\n            aria-label=\"Copy link to clipboard\"\n          >\n            <LinkIcon size={16} />\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport default BlogPostSocial\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AAEvE;AAAA;AAAA;AAAA;AAAA;;;AAOA,MAAM,iBAAgD,CAAC,EAAE,IAAI,EAAE;IAC7D,MAAM,UAAU,CAAC,+BAA+B,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE;IACrE,MAAM,YAAY,mBAAmB,KAAK,KAAK;IAC/C,2DAA2D;IAE3D,MAAM,aAAa;QACjB,UAAU,CAAC,6CAA6C,EAAE,mBAAmB,UAAU;QACvF,SAAS,CAAC,qCAAqC,EAAE,mBAAmB,SAAS,MAAM,EAAE,WAAW;QAChG,UAAU,CAAC,oDAAoD,EAAE,mBAAmB,UAAU;IAChG;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,0CAA0C;YAC1C,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,MAAM,UAAU,CAAC,SAAoC;QAC3D,IAAI,KAAK;YACP,OAAO,IAAI,CAAC,KAAK,UAAU;QAC7B;IACF;IAEA,qBACE,0JAAC;QAAI,WAAU;kBACb,cAAA,0JAAC;YAAI,WAAU;;8BACb,0JAAC;oBAAI,WAAU;;sCACb,0JAAC,sMAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC5B,0JAAC;4BAAK,WAAU;sCAA4B;;;;;;;;;;;;8BAG9C,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BACC,SAAS,IAAM,YAAY;4BAC3B,WAAU;4BACV,OAAM;4BACN,cAAW;sCAEX,cAAA,0JAAC,sMAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;;;;;;sCAGlB,0JAAC;4BACC,SAAS,IAAM,YAAY;4BAC3B,WAAU;4BACV,OAAM;4BACN,cAAW;sCAEX,cAAA,0JAAC,oMAAA,CAAA,UAAO;gCAAC,MAAM;;;;;;;;;;;sCAGjB,0JAAC;4BACC,SAAS,IAAM,YAAY;4BAC3B,WAAU;4BACV,OAAM;4BACN,cAAW;sCAEX,cAAA,0JAAC,sMAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;;;;;;sCAGlB,0JAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAM;4BACN,cAAW;sCAEX,cAAA,0JAAC,8LAAA,CAAA,OAAQ;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;KA5EM;uCA8ES", "debugId": null}}, {"offset": {"line": 5399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/blog/single/BlogPostNavigation.tsx"], "sourcesContent": ["// components/blog/single/BlogPostNavigation.tsx - Previous/Next post navigation\nimport React, { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { ChevronLeft, ChevronRight } from 'lucide-react'\n\ninterface BlogPostNavigationProps {\n  currentSlug: string\n}\n\ninterface NavPost {\n  title: string\n  slug: string\n}\n\nconst BlogPostNavigation: React.FC<BlogPostNavigationProps> = ({ currentSlug }) => {\n  const [prevPost, setPrevPost] = useState<NavPost | null>(null)\n  const [nextPost, setNextPost] = useState<NavPost | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    const fetchNavigation = async () => {\n      try {\n        const response = await fetch(`/api/blog/navigation?slug=${currentSlug}`)\n        if (response.ok) {\n          const data = await response.json()\n          setPrevPost(data.prevPost)\n          setNextPost(data.nextPost)\n        }\n      } catch (error) {\n        console.error('Error fetching navigation:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchNavigation()\n  }, [currentSlug])\n\n  if (loading || (!prevPost && !nextPost)) {\n    return null\n  }\n\n  return (\n    <section className=\"bg-gray-50 py-12\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Previous Post */}\n          <div className=\"flex justify-start\">\n            {prevPost ? (\n              <Link href={`/blog/${prevPost.slug}`}>\n                <div className=\"group bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 w-full\">\n                  <div className=\"flex items-center text-blue-600 mb-2\">\n                    <ChevronLeft size={16} className=\"mr-1\" />\n                    <span className=\"text-sm font-medium\">Previous Article</span>\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2\">\n                    {prevPost.title}\n                  </h3>\n                </div>\n              </Link>\n            ) : (\n              <div className=\"w-full\"></div>\n            )}\n          </div>\n\n          {/* Next Post */}\n          <div className=\"flex justify-end\">\n            {nextPost ? (\n              <Link href={`/blog/${nextPost.slug}`}>\n                <div className=\"group bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 w-full text-right\">\n                  <div className=\"flex items-center justify-end text-blue-600 mb-2\">\n                    <span className=\"text-sm font-medium\">Next Article</span>\n                    <ChevronRight size={16} className=\"ml-1\" />\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2\">\n                    {nextPost.title}\n                  </h3>\n                </div>\n              </Link>\n            ) : (\n              <div className=\"w-full\"></div>\n            )}\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default BlogPostNavigation\n"], "names": [], "mappings": "AAAA,gFAAgF;;;;;AAChF;AACA;AACA;AAAA;;;;;;AAWA,MAAM,qBAAwD,CAAC,EAAE,WAAW,EAAE;;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAkB;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAkB;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;gEAAkB;oBACtB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,CAAC,0BAA0B,EAAE,aAAa;wBACvE,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,YAAY,KAAK,QAAQ;4BACzB,YAAY,KAAK,QAAQ;wBAC3B;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC9C,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;uCAAG;QAAC;KAAY;IAEhB,IAAI,WAAY,CAAC,YAAY,CAAC,UAAW;QACvC,OAAO;IACT;IAEA,qBACE,0JAAC;QAAQ,WAAU;kBACjB,cAAA,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;kCACZ,yBACC,0JAAC,wHAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,MAAM,EAAE,SAAS,IAAI,EAAE;sCAClC,cAAA,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;;0DACb,0JAAC,gNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DACjC,0JAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,0JAAC;wCAAG,WAAU;kDACX,SAAS,KAAK;;;;;;;;;;;;;;;;iDAKrB,0JAAC;4BAAI,WAAU;;;;;;;;;;;kCAKnB,0JAAC;wBAAI,WAAU;kCACZ,yBACC,0JAAC,wHAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,MAAM,EAAE,SAAS,IAAI,EAAE;sCAClC,cAAA,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,0JAAC,kNAAA,CAAA,eAAY;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;;kDAEpC,0JAAC;wCAAG,WAAU;kDACX,SAAS,KAAK;;;;;;;;;;;;;;;;iDAKrB,0JAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B;GAzEM;KAAA;uCA2ES", "debugId": null}}, {"offset": {"line": 5606, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/blog/single/RelatedPosts.tsx"], "sourcesContent": ["// components/blog/single/RelatedPosts.tsx - Related posts section\nimport React from 'react'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { Calendar, Clock } from 'lucide-react'\nimport { BlogListItem } from '@/types/blog'\nimport { urlForImage } from '@/lib/sanity-blog'\n\ninterface RelatedPostsProps {\n  posts: BlogListItem[]\n}\n\nconst RelatedPosts: React.FC<RelatedPostsProps> = ({ posts }) => {\n  if (posts.length === 0) return null\n\n  const getCategoryColor = (color: string) => {\n    const colors = {\n      blue: 'bg-blue-100 text-blue-800',\n      green: 'bg-green-100 text-green-800',\n      purple: 'bg-purple-100 text-purple-800',\n      orange: 'bg-orange-100 text-orange-800',\n      red: 'bg-red-100 text-red-800',\n      teal: 'bg-teal-100 text-teal-800'\n    }\n    return colors[color as keyof typeof colors] || colors.blue\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    })\n  }\n\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Related Articles</h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Continue reading with these related articles that might interest you.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {posts.slice(0, 6).map((post) => (\n            <Link key={post._id} href={`/blog/${post.slug.current}`}>\n              <article className=\"group bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1\">\n                <div className=\"relative h-48 overflow-hidden\">\n                  {post.featuredImage ? (\n                    <Image\n                      src={urlForImage(post.featuredImage).width(400).height(192).url()}\n                      alt={post.title}\n                      fill\n                      className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                  ) : (\n                    <div className=\"w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center\">\n                      <div className=\"text-blue-600 text-center\">\n                        <div className=\"text-xl font-bold mb-1\">VESA</div>\n                        <div className=\"text-xs\">Blog Article</div>\n                      </div>\n                    </div>\n                  )}\n                </div>\n                <div className=\"p-6\">\n                  <div className=\"flex flex-wrap gap-2 mb-3\">\n                    {post.categories.slice(0, 2).map((category) => (\n                      <span\n                        key={category._id}\n                        className={`px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(category.color)}`}\n                      >\n                        {category.title}\n                      </span>\n                    ))}\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2\">\n                    {post.title}\n                  </h3>\n                  <p className=\"text-gray-600 mb-4 line-clamp-2\">\n                    {post.excerpt}\n                  </p>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center\">\n                        <span className=\"text-blue-600 text-xs font-semibold\">V</span>\n                      </div>\n                      <span className=\"text-sm text-gray-600\">VESA Solutions</span>\n                    </div>\n                    <div className=\"flex items-center gap-3 text-xs text-gray-500\">\n                      <div className=\"flex items-center gap-1\">\n                        <Calendar size={12} />\n                        <span>{formatDate(post.publishedAt)}</span>\n                      </div>\n                      {post.readingTime && (\n                        <div className=\"flex items-center gap-1\">\n                          <Clock size={12} />\n                          <span>{post.readingTime}m</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </article>\n            </Link>\n          ))}\n        </div>\n\n        <div className=\"text-center mt-12\">\n          <Link href=\"/blog\">\n            <button className=\"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition-colors duration-200\">\n              View All Articles\n            </button>\n          </Link>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default RelatedPosts\n"], "names": [], "mappings": "AAAA,kEAAkE;;;;;AAElE;AACA;AACA;AAAA;AAEA;;;;;;AAMA,MAAM,eAA4C,CAAC,EAAE,KAAK,EAAE;IAC1D,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,KAAK;YACL,MAAM;QACR;QACA,OAAO,MAAM,CAAC,MAA6B,IAAI,OAAO,IAAI;IAC5D;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,qBACE,0JAAC;QAAQ,WAAU;kBACjB,cAAA,0JAAC;YAAI,WAAU;;8BACb,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,0JAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,0JAAC;oBAAI,WAAU;8BACZ,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACtB,0JAAC,wHAAA,CAAA,UAAI;4BAAgB,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE;sCACrD,cAAA,0JAAC;gCAAQ,WAAU;;kDACjB,0JAAC;wCAAI,WAAU;kDACZ,KAAK,aAAa,iBACjB,0JAAC,yHAAA,CAAA,UAAK;4CACJ,KAAK,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,aAAa,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG;4CAC/D,KAAK,KAAK,KAAK;4CACf,IAAI;4CACJ,WAAU;;;;;iEAGZ,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAI,WAAU;kEAAyB;;;;;;kEACxC,0JAAC;wDAAI,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;kDAKjC,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAI,WAAU;0DACZ,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAChC,0JAAC;wDAEC,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,SAAS,KAAK,GAAG;kEAE1F,SAAS,KAAK;uDAHV,SAAS,GAAG;;;;;;;;;;0DAOvB,0JAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAEb,0JAAC;gDAAE,WAAU;0DACV,KAAK,OAAO;;;;;;0DAEf,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC;oEAAK,WAAU;8EAAsC;;;;;;;;;;;0EAExD,0JAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;;kFACb,0JAAC,sMAAA,CAAA,WAAQ;wEAAC,MAAM;;;;;;kFAChB,0JAAC;kFAAM,WAAW,KAAK,WAAW;;;;;;;;;;;;4DAEnC,KAAK,WAAW,kBACf,0JAAC;gEAAI,WAAU;;kFACb,0JAAC,gMAAA,CAAA,QAAK;wEAAC,MAAM;;;;;;kFACb,0JAAC;;4EAAM,KAAK,WAAW;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAnD3B,KAAK,GAAG;;;;;;;;;;8BA8DvB,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,0JAAC;4BAAO,WAAU;sCAAsI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpK;KA3GM;uCA6GS", "debugId": null}}, {"offset": {"line": 5928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/blog/single/index.ts"], "sourcesContent": ["// components/blog/single/index.ts - Single blog post components exports\nexport { default as BlogPostHero } from './BlogPostHero'\nexport { default as BlogPostContent } from './BlogPostContent'\nexport { default as BlogPostSocial } from './BlogPostSocial'\nexport { default as BlogPostNavigation } from './BlogPostNavigation'\nexport { default as RelatedPosts } from './RelatedPosts'\n"], "names": [], "mappings": "AAAA,wEAAwE;;AACxE;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 5962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/blog/index.ts"], "sourcesContent": ["// components/blog/index.ts - Blog components exports\nexport { default as BlogArchiveHero } from './BlogArchiveHero'\nexport { default as BlogFilters } from './BlogFilters'\nexport { default as BlogGrid } from './BlogGrid'\nexport { default as BlogPagination } from './BlogPagination'\n\n// Single blog post components\nexport * from './single'\n"], "names": [], "mappings": "AAAA,qDAAqD;;AACrD;AACA;AACA;AACA;AAEA,8BAA8B;AAC9B", "debugId": null}}, {"offset": {"line": 6037, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/pages/blog.tsx"], "sourcesContent": ["// pages/blog.tsx - Blog archive page following homepage design patterns\nimport React, {useState} from 'react'\nimport { GetStaticProps } from 'next'\nimport Head from 'next/head'\nimport Header from '@/components/global/Header'\nimport Footer from '@/components/global/Footer'\nimport { BlogArchiveHero, BlogGrid, BlogFilters, BlogPagination } from '@/components/blog'\nimport { getBlogPosts, getBlogCategories, getBlogTags } from '@/lib/sanity-blog'\nimport { BlogListItem, BlogCategory, BlogTag, BlogFilters as BlogFiltersType } from '@/types/blog'\n\ninterface BlogPageProps {\n  initialPosts: BlogListItem[]\n  categories: BlogCategory[]\n  tags: BlogTag[]\n  totalPosts: number\n  hasMore: boolean\n}\n\nconst BlogPage: React.FC<BlogPageProps> = ({\n  initialPosts,\n  categories,\n  tags,\n  totalPosts,\n  hasMore: initialHasMore\n}) => {\n  const [posts, setPosts] = useState<BlogListItem[]>(initialPosts)\n  const [loading, setLoading] = useState(false)\n  const [hasMore, setHasMore] = useState(initialHasMore)\n  const [currentPage, setCurrentPage] = useState(1)\n  const [filters, setFilters] = useState<BlogFiltersType>({})\n\n  // Handle filter changes\n  const handleFilterChange = async (newFilters: BlogFiltersType) => {\n    setLoading(true)\n    setCurrentPage(1)\n    setFilters(newFilters)\n\n    try {\n      const response = await fetch('/api/blog?' + new URLSearchParams({\n        ...newFilters,\n        page: '1',\n        limit: '12'\n      } as Record<string, string>))\n      \n      const data = await response.json()\n      setPosts(data.posts)\n      setHasMore(data.hasMore)\n    } catch (error) {\n      console.error('Error filtering posts:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // Handle pagination\n  const handlePageChange = async (page: number) => {\n    setLoading(true)\n    setCurrentPage(page)\n\n    try {\n      const response = await fetch('/api/blog?' + new URLSearchParams({\n        ...filters,\n        page: page.toString(),\n        limit: '12'\n      } as Record<string, string>))\n      \n      const data = await response.json()\n      setPosts(data.posts)\n      setHasMore(data.hasMore)\n    } catch (error) {\n      console.error('Error loading page:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // Load more posts (for infinite scroll)\n  const loadMorePosts = async () => {\n    if (loading || !hasMore) return\n\n    setLoading(true)\n    const nextPage = currentPage + 1\n\n    try {\n      const response = await fetch('/api/blog?' + new URLSearchParams({\n        ...filters,\n        page: nextPage.toString(),\n        limit: '12'\n      } as Record<string, string>))\n      \n      const data = await response.json()\n      setPosts(prev => [...prev, ...data.posts])\n      setHasMore(data.hasMore)\n      setCurrentPage(nextPage)\n    } catch (error) {\n      console.error('Error loading more posts:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <>\n      <Head>\n        <title>Blog - Digital Marketing Insights | VESA Solutions</title>\n        <meta \n          name=\"description\" \n          content=\"Stay updated with the latest digital marketing trends, SEO tips, and industry insights from VESA Solutions. Expert advice to grow your business online.\" \n        />\n        <meta name=\"keywords\" content=\"digital marketing blog, SEO tips, marketing insights, VESA Solutions\" />\n        <meta property=\"og:title\" content=\"Blog - Digital Marketing Insights | VESA Solutions\" />\n        <meta property=\"og:description\" content=\"Stay updated with the latest digital marketing trends, SEO tips, and industry insights from VESA Solutions.\" />\n        <meta property=\"og:type\" content=\"website\" />\n        <meta property=\"og:url\" content=\"https://vesasolutions.com/blog\" />\n        <link rel=\"canonical\" href=\"https://vesasolutions.com/blog\" />\n      </Head>\n\n      <Header isVisible={true} />\n      \n      <main>\n        {/* Hero Section */}\n        <BlogArchiveHero />\n\n        {/* Blog Content */}\n        <section className=\"py-16 bg-gray-50\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            {/* Filters */}\n            <BlogFilters\n              categories={categories}\n              tags={tags}\n              onFilterChange={handleFilterChange}\n              loading={loading}\n            />\n\n            {/* Blog Grid */}\n            <BlogGrid\n              posts={posts}\n              loading={loading}\n              onLoadMore={loadMorePosts}\n              hasMore={hasMore}\n            />\n\n            {/* Pagination */}\n            <BlogPagination\n              currentPage={currentPage}\n              totalPosts={totalPosts}\n              postsPerPage={12}\n              onPageChange={handlePageChange}\n              loading={loading}\n            />\n          </div>\n        </section>\n      </main>\n\n      <Footer />\n    </>\n  )\n}\n\nexport const getStaticProps: GetStaticProps = async () => {\n  try {\n    const [postsData, categories, tags] = await Promise.all([\n      getBlogPosts({ page: 1, limit: 12 }),\n      getBlogCategories(),\n      getBlogTags()\n    ])\n\n    return {\n      props: {\n        initialPosts: postsData.posts,\n        categories,\n        tags,\n        totalPosts: postsData.totalPosts,\n        hasMore: postsData.hasMore\n      },\n      revalidate: 60 // Revalidate every minute\n    }\n  } catch (error) {\n    console.error('Error fetching blog data:', error)\n    return {\n      props: {\n        initialPosts: [],\n        categories: [],\n        tags: [],\n        totalPosts: 0,\n        hasMore: false\n      },\n      revalidate: 60\n    }\n  }\n}\n\nexport default BlogPage\n"], "names": [], "mappings": "AAAA,wEAAwE;;;;;;AACxE;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AAYA,MAAM,WAAoC,CAAC,EACzC,YAAY,EACZ,UAAU,EACV,IAAI,EACJ,UAAU,EACV,SAAS,cAAc,EACxB;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAkB;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAmB,CAAC;IAEzD,wBAAwB;IACxB,MAAM,qBAAqB,OAAO;QAChC,WAAW;QACX,eAAe;QACf,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe,IAAI,gBAAgB;gBAC9D,GAAG,UAAU;gBACb,MAAM;gBACN,OAAO;YACT;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS,KAAK,KAAK;YACnB,WAAW,KAAK,OAAO;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,oBAAoB;IACpB,MAAM,mBAAmB,OAAO;QAC9B,WAAW;QACX,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe,IAAI,gBAAgB;gBAC9D,GAAG,OAAO;gBACV,MAAM,KAAK,QAAQ;gBACnB,OAAO;YACT;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS,KAAK,KAAK;YACnB,WAAW,KAAK,OAAO;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,WAAW;QACb;IACF;IAEA,wCAAwC;IACxC,MAAM,gBAAgB;QACpB,IAAI,WAAW,CAAC,SAAS;QAEzB,WAAW;QACX,MAAM,WAAW,cAAc;QAE/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe,IAAI,gBAAgB;gBAC9D,GAAG,OAAO;gBACV,MAAM,SAAS,QAAQ;gBACvB,OAAO;YACT;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS,CAAA,OAAQ;uBAAI;uBAAS,KAAK,KAAK;iBAAC;YACzC,WAAW,KAAK,OAAO;YACvB,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;kCAAM;;;;;;kCACP,0JAAC;wBACC,MAAK;wBACL,SAAQ;;;;;;kCAEV,0JAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,0JAAC;wBAAK,UAAS;wBAAW,SAAQ;;;;;;kCAClC,0JAAC;wBAAK,UAAS;wBAAiB,SAAQ;;;;;;kCACxC,0JAAC;wBAAK,UAAS;wBAAU,SAAQ;;;;;;kCACjC,0JAAC;wBAAK,UAAS;wBAAS,SAAQ;;;;;;kCAChC,0JAAC;wBAAK,KAAI;wBAAY,MAAK;;;;;;;;;;;;0BAG7B,0JAAC,2HAAA,CAAA,UAAM;gBAAC,WAAW;;;;;;0BAEnB,0JAAC;;kCAEC,0JAAC,gLAAA,CAAA,kBAAe;;;;;kCAGhB,0JAAC;wBAAQ,WAAU;kCACjB,cAAA,0JAAC;4BAAI,WAAU;;8CAEb,0JAAC,wKAAA,CAAA,cAAW;oCACV,YAAY;oCACZ,MAAM;oCACN,gBAAgB;oCAChB,SAAS;;;;;;8CAIX,0JAAC,kKAAA,CAAA,WAAQ;oCACP,OAAO;oCACP,SAAS;oCACT,YAAY;oCACZ,SAAS;;;;;;8CAIX,0JAAC,8KAAA,CAAA,iBAAc;oCACb,aAAa;oCACb,YAAY;oCACZ,cAAc;oCACd,cAAc;oCACd,SAAS;;;;;;;;;;;;;;;;;;;;;;;0BAMjB,0JAAC,2HAAA,CAAA,UAAM;;;;;;;AAGb;GA3IM;KAAA;;uCA8KS", "debugId": null}}, {"offset": {"line": 6291, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/blog\";\n\n/// <reference types=\"next/client\" />\r\n\r\n// inserted by rust code\r\ndeclare const PAGE_PATH: string\r\n\r\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\r\n;(window.__NEXT_P = window.__NEXT_P || []).push([\r\n  PAGE_PATH,\r\n  () => {\r\n    return require('PAGE')\r\n  },\r\n])\r\n// @ts-expect-error module.hot exists\r\nif (module.hot) {\r\n  // @ts-expect-error module.hot exists\r\n  module.hot.dispose(function () {\r\n    window.__NEXT_P.push([PAGE_PATH])\r\n  })\r\n}\r\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}