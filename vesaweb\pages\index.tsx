// pages/index.tsx
import React, { useRef } from 'react';
import { GetStaticProps } from 'next';
import Head from 'next/head';
import Header from '@/components/global/Header';
import Footer from '@/components/global/Footer';
import {
  HeroSection,
  ReputationManagementSection,
  DigitalStrategySliderSection,
  ClientTestimonialsSection,
  WhyChooseVesaSection,
  HorizontalScrollSection,
  SchedulingSection,
  DigitalMarketingServicesSection,
  PlatformLogosSection,
  DigitalStrategyArticlesSection,
  ContactCTASection,
  PartnerCarousel
} from '@/components/home';
import { getRecentBlogPosts } from '@/lib/sanity-blog';
import { BlogListItem } from '@/types/blog';
import HomepageSchema from '@/components/seo/HomepageSchema';

interface HomeProps {
  blogPosts: BlogListItem[];
}

const Home: React.FC<HomeProps> = ({ blogPosts }) => {
  const footerRef = useRef<HTMLDivElement>(null);

  return (
    <>
      <Head>
        <title>Vesa Solutions - Digital Marketing That Actually Works</title>
        <meta name="description" content="Expert digital marketing services including SEO, web design, social media marketing, and online reputation management. Get more leads, conversions, and sales." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      {/* Schema Markup for Homepage */}
      <HomepageSchema />

      <Header />
      
      <main>
        <HeroSection />
        <PartnerCarousel />
        <ReputationManagementSection />
        <DigitalStrategySliderSection />
        <ClientTestimonialsSection />
        <WhyChooseVesaSection />
        <HorizontalScrollSection />
        <SchedulingSection />
        <DigitalMarketingServicesSection />
        <PlatformLogosSection />
        <DigitalStrategyArticlesSection blogPosts={blogPosts} />
        <ContactCTASection />
      </main>

      <div ref={footerRef}>
        <Footer />
      </div>
    </>
  );
};

export const getStaticProps: GetStaticProps = async () => {
  try {
    // Get recent blog posts for the homepage
    const blogPosts = await getRecentBlogPosts(4);

    return {
      props: {
        blogPosts
      },
      revalidate: 300 // Revalidate every 5 minutes
    };
  } catch (error) {
    console.error('Error fetching blog posts for homepage:', error);
    return {
      props: {
        blogPosts: []
      },
      revalidate: 300
    };
  }
};

export default Home;