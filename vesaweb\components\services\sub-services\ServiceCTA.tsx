// ServiceCTA.tsx - Fixed with complete icon support
import React from 'react'
import { IconRenderer } from '@/components/global/IconRender'
import { ServiceCTA as ServiceCTAType } from '@/types/subService'
import ServiceContactForm from '@/components/global/Form'

interface ServiceCTAProps {
  data?: ServiceCTAType
  serviceName?: string
}

// Helper function to safely get error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  if (typeof error === 'string') {
    return error
  }
  return 'An unknown error occurred'
}

interface FormData {
  businessName: string
  fullName: string
  email: string
  phone: string
  location: string
  website?: string
  message: string
  service: string
  userCountry?: string
  timestamp?: string
}

export const ServiceCTA: React.FC<ServiceCTAProps> = ({ data, serviceName = 'Service' }) => {
  if (!data) return null

  const handleFormSubmit = async (formData: FormData) => {
    try {
      console.log('Service form submitted:', formData)
      
      // Send to API
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Failed to submit form')
      }

      if (!result.success) {
        throw new Error(result.message || 'Form submission failed')
      }

      console.log('Form submitted successfully:', result)
      
    } catch (error) {
      console.error('Form submission error:', error)
      throw new Error(getErrorMessage(error))
    }
  }

  // Convert serviceName to valid type - Updated to handle all services dynamically
  const getServiceType = (name: string): "seo" | "local-seo" | "ppc" | "web-design" | "social-media" | "email-marketing" | "branding" | "conversion-optimization" | "reputation-management" => {
    const normalized = name.toLowerCase().replace(/\s+/g, '-').replace(/services?$/, '')

    // Handle keyword-based detection for better matching
    if (normalized.includes('local') && normalized.includes('seo')) return 'local-seo'
    if (normalized.includes('ppc') || normalized.includes('advertising')) return 'ppc'
    if (normalized.includes('web') && (normalized.includes('design') || normalized.includes('development'))) return 'web-design'
    if (normalized.includes('social') && normalized.includes('media')) return 'social-media'
    if (normalized.includes('email') && normalized.includes('marketing')) return 'email-marketing'
    if (normalized.includes('branding') || normalized.includes('brand')) return 'branding'
    if (normalized.includes('conversion') && normalized.includes('optimization')) return 'conversion-optimization'
    if (normalized.includes('reputation') && normalized.includes('management')) return 'reputation-management'

    // Handle specific service titles from Sanity (without "services" suffix)
    switch (normalized) {
      case 'local-seo':
        return 'local-seo'
      case 'ppc-advertising':
      case 'ppc':
        return 'ppc'
      case 'web-design':
      case 'web-development':
        return 'web-design'
      case 'social-media-marketing':
      case 'social-media':
        return 'social-media'
      case 'email-marketing':
        return 'email-marketing'
      case 'branding':
        return 'branding'
      case 'conversion-optimization':
        return 'conversion-optimization'
      case 'reputation-management':
        return 'reputation-management'
      default:
        // If it contains 'seo' but not 'local', default to seo
        if (normalized.includes('seo')) return 'seo'
        return 'seo'
    }
  }

  return (
    <section className="bg-blue-50 py-16">
      <div className="max-w-6xl mx-auto px-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            {data.title && (
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-6">
                {data.title}
              </h2>
            )}
            
            {data.description && (
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                {data.description}
              </p>
            )}
            
            {data.benefits && data.benefits.length > 0 && (
              <div className="space-y-4 mb-8">
                {data.benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center">
                    <IconRenderer iconName="CheckCircle" className="text-blue-600 mr-3" size={20} />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            )}
            

          </div>
          
          <ServiceContactForm 
            service={{
              name: serviceName,
              type: getServiceType(serviceName),
              ctaText: data.formSettings?.ctaText || `Get My Free ${serviceName} Analysis`,
              messagePlaceholder: data.formSettings?.messagePlaceholder || `Tell us about your business and ${serviceName.toLowerCase()} goals*`,
              benefits: data.benefits || []
            }}
            onSubmit={handleFormSubmit}
          />
        </div>
      </div>
    </section>
  )
}