// FAQ.tsx - Fixed with IconRenderer
import React, { useState } from 'react'
import { IconRenderer } from '@/components/global/IconRender'
import { FAQ as FAQType } from '@/types/subService'

interface FAQProps {
  data?: FAQType[]
}

export const FAQ: React.FC<FAQProps> = ({ data }) => {
  const [openFaq, setOpenFaq] = useState<number | null>(null)

  if (!data || data.length === 0) return null

  return (
    <section className="py-24 bg-white">
      <div className="max-w-4xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Frequently Asked Questions
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            Get answers to common questions about our services and how they can help your business grow.
          </p>
        </div>

        <div className="space-y-6">
          {data.map((faq, index) => (
            <div key={index} className="bg-gray-50 rounded-2xl overflow-hidden shadow-lg border border-gray-200 transition-all duration-300 hover:shadow-xl">
              <button 
                onClick={() => setOpenFaq(openFaq === index ? null : index)}
                className="w-full p-8 text-left flex justify-between items-center hover:bg-gray-100 transition-colors duration-200"
              >
                <h3 className="text-xl font-semibold text-gray-800 pr-8">
                  {faq.question}
                </h3>
                <IconRenderer 
                  iconName="ChevronDown"
                  size={24} 
                  className={`text-blue-600 transform transition-all duration-300 ease-in-out flex-shrink-0 ${
                    openFaq === index ? 'rotate-180 scale-110' : 'rotate-0 scale-100'
                  }`}
                />
              </button>
              <div 
                className={`bg-white transition-all duration-500 ease-in-out overflow-hidden ${
                  openFaq === index 
                    ? 'max-h-96 opacity-100' 
                    : 'max-h-0 opacity-0'
                }`}
              >
                <div 
                  className={`px-8 pb-8 transform transition-all duration-500 ease-out ${
                    openFaq === index 
                      ? 'translate-y-0 opacity-100' 
                      : '-translate-y-4 opacity-0'
                  }`}
                >
                  <div className="h-px bg-gradient-to-r from-transparent via-blue-200 to-transparent mb-6"></div>
                  <p className="text-gray-600 leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}