{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { ChevronDown } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\n\r\ninterface HeaderProps {\r\n  isVisible?: boolean;\r\n}\r\n\r\nconst Header: React.FC<HeaderProps> = ({ isVisible = true }) => {\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);\r\n  const [seoDropdownOpen, setSeoDropdownOpen] = useState<boolean>(false);\r\n  const [webDevDropdownOpen, setWebDevDropdownOpen] = useState<boolean>(false);\r\n  const [digitalMarketingDropdownOpen, setDigitalMarketingDropdownOpen] = useState<boolean>(false);\r\n  const [aboutDropdownOpen, setAboutDropdownOpen] = useState<boolean>(false);\r\n\r\n  const [mounted, setMounted] = useState<boolean>(false);\r\n\r\n  // Ensure component is hydrated before rendering state-dependent UI\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Handle body scroll when mobile menu is open\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) {\r\n      document.body.style.overflow = 'hidden';\r\n    } else {\r\n      document.body.style.overflow = 'unset';\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [mobileMenuOpen]);\r\n\r\n  // Close mobile menu on escape key\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (e.key === 'Escape' && mobileMenuOpen) {\r\n        setMobileMenuOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleEscape);\r\n    return () => document.removeEventListener('keydown', handleEscape);\r\n  }, [mobileMenuOpen]);\r\n\r\n  // SEO Sub-services\r\n  const seoSubServices = [\r\n    { name: 'On-Page SEO Optimization', href: '/on-page-seo', description: 'Optimize content and structure' },\r\n    { name: 'Off-Page SEO & Link Building', href: '/off-page-seo', description: 'Build authority and backlinks' },\r\n    { name: 'Technical SEO Services', href: '/technical-seo', description: 'Optimize technical performance' },\r\n    { name: 'Local SEO Marketing', href: '/local-seo', description: 'Dominate local search results' },\r\n    { name: 'Content Writing', href: '/content-writing', description: 'SEO-optimized content creation' },\r\n    { name: 'SEO Analytics', href: '/seo-analytics', description: 'Comprehensive SEO analysis' }\r\n  ];\r\n\r\n  // Web Development Sub-services\r\n  const webDevSubServices = [\r\n    { name: 'Custom Website Development', href: '/custom-website-development', description: 'Unique, tailored websites' },\r\n    { name: 'E-commerce Development', href: '/ecommerce-development', description: 'Online store solutions' },\r\n    { name: 'Mobile App Development', href: '/mobile-app-development', description: 'iOS & Android apps' },\r\n    { name: 'Website Speed Optimization', href: '/website-speed-optimization', description: 'Lightning-fast websites' },\r\n    { name: 'Web Application Development', href: '/web-application-development', description: 'Custom web applications' },\r\n    { name: 'Website Maintenance & Support', href: '/website-maintenance-support', description: '24/7 website care' }\r\n  ];\r\n\r\n  // Digital Marketing Sub-services\r\n  const digitalMarketingSubServices = [\r\n    { name: 'PPC Advertising', href: '/ppc', description: 'Targeted pay-per-click campaigns' },\r\n    { name: 'Email Marketing', href: '/email-marketing', description: 'Automated campaigns that convert' },\r\n    { name: 'Social Media Marketing', href: '/social-media', description: 'Engage your audience effectively' },\r\n    { name: 'Branding Services', href: '/branding-services', description: 'Build powerful brand identity' },\r\n    { name: 'Conversion Optimization', href: '/conversion-optimization', description: 'Turn visitors into customers' },\r\n    { name: 'Reputation Management', href: '/reputation-management', description: 'Protect and enhance your brand' }\r\n  ];\r\n\r\n  // About Sub-services\r\n  const aboutSubServices = [\r\n    { name: 'Case Studies', href: '/case-studies', description: 'View our successful projects' },\r\n    { name: 'Blog', href: '/blog', description: 'Latest insights and industry news' }\r\n  ];\r\n\r\n  const toggleMobileMenu = () => {\r\n    setMobileMenuOpen(!mobileMenuOpen);\r\n  };\r\n\r\n  const closeMobileMenu = () => {\r\n    setMobileMenuOpen(false);\r\n    // Reset all dropdown states when closing mobile menu\r\n    setSeoDropdownOpen(false);\r\n    setWebDevDropdownOpen(false);\r\n    setDigitalMarketingDropdownOpen(false);\r\n    setAboutDropdownOpen(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <header className={`w-full bg-white/95 backdrop-blur-md border-b border-gray-100 z-50 transition-all duration-500 ease-in-out ${\r\n        isVisible \r\n          ? 'sticky top-0 translate-y-0 opacity-100' \r\n          : 'fixed top-0 -translate-y-full opacity-0 pointer-events-none'\r\n      }`}>\r\n        <div className=\"max-w-[1440px] mx-auto px-4 sm:px-6 py-3 sm:py-4\">\r\n          <div className=\"flex justify-between items-center\">\r\n            {/* Logo */}\r\n            <div className=\"flex items-center group cursor-pointer\">\r\n              <div className=\"relative\">\r\n                <Link href=\"/\">\r\n                  <Image \r\n                    src=\"/VesaLogo.svg\" \r\n                    alt=\"VESA Solutions Logo\" \r\n                    width={94}\r\n                    height={40}\r\n                    priority\r\n                    className=\"transition-transform duration-300 group-hover:scale-105 w-20 h-auto sm:w-[94px]\"\r\n                  />\r\n                </Link>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Desktop Navigation */}\r\n            <nav className=\"hidden lg:flex items-center space-x-12\">\r\n              {/* SEO Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setSeoDropdownOpen(true)}\r\n                onMouseLeave={() => setSeoDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/seo-search-engine-optimization\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-green-600 transition-all duration-300 group\">\r\n                  SEO\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-green-500 to-green-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  seoDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">SEO Services</div>\r\n                      {seoSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100/50 transition-all duration-200 ease-out border border-transparent hover:border-green-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-green-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Web Development Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setWebDevDropdownOpen(true)}\r\n                onMouseLeave={() => setWebDevDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/web-development\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group\">\r\n                  WEB DEVELOPMENT\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  webDevDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">Web Development</div>\r\n                      {webDevSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Digital Marketing Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setDigitalMarketingDropdownOpen(true)}\r\n                onMouseLeave={() => setDigitalMarketingDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/digital-marketing\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-purple-600 transition-all duration-300 group\">\r\n                  DIGITAL MARKETING\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-purple-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  digitalMarketingDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">Digital Marketing</div>\r\n                      {digitalMarketingSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50/50 transition-all duration-200 ease-out border border-transparent hover:border-indigo-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-purple-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-purple-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Services */}\r\n              <Link href=\"/services\" className=\"relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300\">\r\n                SERVICES\r\n                <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n              </Link>\r\n\r\n              {/* About Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setAboutDropdownOpen(true)}\r\n                onMouseLeave={() => setAboutDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/about\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group\">\r\n                  ABOUT\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  aboutDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">About</div>\r\n                      {aboutSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Contact */}\r\n              <Link href=\"/contact-us\" className=\"relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300\">\r\n                CONTACT\r\n                <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n              </Link>\r\n            </nav>\r\n\r\n\r\n            {/* CTA Button and Mobile Menu */}\r\n            <div className=\"flex items-center space-x-3 sm:space-x-4\">\r\n              {/* Desktop CTA */}\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                className=\"hidden sm:block group relative bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-sm font-semibold px-6 lg:px-8 py-2.5 lg:py-3 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-blue-500/25\"\r\n              >\r\n                <span className=\"relative z-10\">GET FREE PROPOSAL</span>\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n              </Link>\r\n\r\n              {/* Mobile Menu Button */}\r\n              <button\r\n                className=\"lg:hidden relative p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-all duration-300 group\"\r\n                onClick={toggleMobileMenu}\r\n                aria-label=\"Toggle mobile menu\"\r\n              >\r\n                <div className=\"w-6 h-6 relative\">\r\n                  <span className={`absolute top-1 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${\r\n                    mobileMenuOpen ? 'rotate-45 translate-y-2' : ''\r\n                  }`}></span>\r\n                  <span className={`absolute top-2.5 left-0 w-6 h-0.5 bg-current transition-all duration-300 ${\r\n                    mobileMenuOpen ? 'opacity-0' : ''\r\n                  }`}></span>\r\n                  <span className={`absolute top-4 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${\r\n                    mobileMenuOpen ? '-rotate-45 -translate-y-2' : ''\r\n                  }`}></span>\r\n                </div>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Mobile Menu */}\r\n      {mounted && mobileMenuOpen && (\r\n        <div className=\"lg:hidden fixed inset-0 z-40 bg-white overflow-y-auto\">\r\n          {/* Mobile Header */}\r\n          <div className=\"flex justify-between items-center p-4 border-b border-gray-200\">\r\n            <Link href=\"/\" onClick={closeMobileMenu}>\r\n              <Image\r\n                src=\"/VesaLogo.svg\"\r\n                alt=\"VESA Solutions Logo\"\r\n                width={80}\r\n                height={34}\r\n                priority\r\n                className=\"w-20 h-auto\"\r\n              />\r\n            </Link>\r\n            <button\r\n              onClick={closeMobileMenu}\r\n              className=\"p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-colors\"\r\n              aria-label=\"Close mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 relative\">\r\n                <span className=\"absolute top-2.5 left-0 w-6 h-0.5 bg-current transform rotate-45\"></span>\r\n                <span className=\"absolute top-2.5 left-0 w-6 h-0.5 bg-current transform -rotate-45\"></span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Mobile Navigation */}\r\n          <div className=\"p-4 space-y-6\">\r\n            {/* SEO Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setSeoDropdownOpen(!seoDropdownOpen)}\r\n              >\r\n                <Link href=\"/seo-search-engine-optimization\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-green-600 transition-colors\">\r\n                  SEO\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {seoDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {seoSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-green-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Web Development Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setWebDevDropdownOpen(!webDevDropdownOpen)}\r\n              >\r\n                <Link href=\"/web-development\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\r\n                  WEB DEVELOPMENT\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {webDevDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {webDevSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-blue-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Digital Marketing Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setDigitalMarketingDropdownOpen(!digitalMarketingDropdownOpen)}\r\n              >\r\n                <Link href=\"/digital-marketing\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-purple-600 transition-colors\">\r\n                  DIGITAL MARKETING\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {digitalMarketingDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {digitalMarketingSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-purple-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Services */}\r\n            <div>\r\n              <Link\r\n                href=\"/services\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\"\r\n              >\r\n                SERVICES\r\n              </Link>\r\n            </div>\r\n\r\n            {/* About Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setAboutDropdownOpen(!aboutDropdownOpen)}\r\n              >\r\n                <Link href=\"/about\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\r\n                  ABOUT\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {aboutDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {aboutSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-blue-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Contact */}\r\n            <div>\r\n              <Link\r\n                href=\"/contact-us\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\"\r\n              >\r\n                CONTACT\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Mobile CTA Button */}\r\n            <div className=\"pt-4\">\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block w-full text-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-6 py-4 rounded-full transition-all duration-300 transform hover:scale-105\"\r\n              >\r\n                GET FREE PROPOSAL\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWA,MAAM,SAAgC,CAAC,EAAE,YAAY,IAAI,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAC1F,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAEpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,mEAAmE;IACnE,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,qBAAqB;QACrB,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAe;IAEnB,kCAAkC;IAClC,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,YAAY,gBAAgB;gBACxC,kBAAkB;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;KAAe;IAEnB,mBAAmB;IACnB,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAA4B,MAAM;YAAgB,aAAa;QAAiC;QACxG;YAAE,MAAM;YAAgC,MAAM;YAAiB,aAAa;QAAgC;QAC5G;YAAE,MAAM;YAA0B,MAAM;YAAkB,aAAa;QAAiC;QACxG;YAAE,MAAM;YAAuB,MAAM;YAAc,aAAa;QAAgC;QAChG;YAAE,MAAM;YAAmB,MAAM;YAAoB,aAAa;QAAiC;QACnG;YAAE,MAAM;YAAiB,MAAM;YAAkB,aAAa;QAA6B;KAC5F;IAED,+BAA+B;IAC/B,MAAM,oBAAoB;QACxB;YAAE,MAAM;YAA8B,MAAM;YAA+B,aAAa;QAA4B;QACpH;YAAE,MAAM;YAA0B,MAAM;YAA0B,aAAa;QAAyB;QACxG;YAAE,MAAM;YAA0B,MAAM;YAA2B,aAAa;QAAqB;QACrG;YAAE,MAAM;YAA8B,MAAM;YAA+B,aAAa;QAA0B;QAClH;YAAE,MAAM;YAA+B,MAAM;YAAgC,aAAa;QAA0B;QACpH;YAAE,MAAM;YAAiC,MAAM;YAAgC,aAAa;QAAoB;KACjH;IAED,iCAAiC;IACjC,MAAM,8BAA8B;QAClC;YAAE,MAAM;YAAmB,MAAM;YAAQ,aAAa;QAAmC;QACzF;YAAE,MAAM;YAAmB,MAAM;YAAoB,aAAa;QAAmC;QACrG;YAAE,MAAM;YAA0B,MAAM;YAAiB,aAAa;QAAmC;QACzG;YAAE,MAAM;YAAqB,MAAM;YAAsB,aAAa;QAAgC;QACtG;YAAE,MAAM;YAA2B,MAAM;YAA4B,aAAa;QAA+B;QACjH;YAAE,MAAM;YAAyB,MAAM;YAA0B,aAAa;QAAiC;KAChH;IAED,qBAAqB;IACrB,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAgB,MAAM;YAAiB,aAAa;QAA+B;QAC3F;YAAE,MAAM;YAAQ,MAAM;YAAS,aAAa;QAAoC;KACjF;IAED,MAAM,mBAAmB;QACvB,kBAAkB,CAAC;IACrB;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,qDAAqD;QACrD,mBAAmB;QACnB,sBAAsB;QACtB,gCAAgC;QAChC,qBAAqB;IACvB;IAEA,qBACE;;0BACE,qKAAC;gBAAO,WAAW,CAAC,0GAA0G,EAC5H,YACI,2CACA,+DACJ;0BACA,cAAA,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,qKAAC,sHAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;;;;;0CAOlB,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC;wCACC,WAAU;wCACV,cAAc,IAAM,mBAAmB;wCACvC,cAAc,IAAM,mBAAmB;;0DAEvC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,WAAU;;oDAA6H;kEAElL,qKAAC,6MAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,kBAAkB,eAAe,IAAI;;;;;;kEACjH,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,qKAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,kBAAkB,sCAAsC,sCACxD;0DACA,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,qKAAC,qHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAI,WAAU;;kGACb,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,qKAAC;wCACC,WAAU;wCACV,cAAc,IAAM,sBAAsB;wCAC1C,cAAc,IAAM,sBAAsB;;0DAE1C,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;;oDAA4H;kEAElK,qKAAC,6MAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,qBAAqB,eAAe,IAAI;;;;;;kEACpH,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,qKAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,qBAAqB,sCAAsC,sCAC3D;0DACA,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,qKAAC,qHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAI,WAAU;;kGACb,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,qKAAC;wCACC,WAAU;wCACV,cAAc,IAAM,gCAAgC;wCACpD,cAAc,IAAM,gCAAgC;;0DAEpD,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;;oDAA8H;kEAEtK,qKAAC,6MAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,+BAA+B,eAAe,IAAI;;;;;;kEAC9H,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,qKAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,+BAA+B,sCAAsC,sCACrE;0DACA,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,4BAA4B,GAAG,CAAC,CAAC,SAAS,sBACzC,qKAAC,qHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAI,WAAU;;kGACb,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;;4CAAmH;0DAElJ,qKAAC;gDAAK,WAAU;;;;;;;;;;;;kDAIlB,qKAAC;wCACC,WAAU;wCACV,cAAc,IAAM,qBAAqB;wCACzC,cAAc,IAAM,qBAAqB;;0DAEzC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;;oDAA4H;kEAExJ,qKAAC,6MAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,oBAAoB,eAAe,IAAI;;;;;;kEACnH,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,qKAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,oBAAoB,sCAAsC,sCAC1D;0DACA,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,qKAAC,qHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAI,WAAU;;kGACb,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;;4CAAmH;0DAEpJ,qKAAC;gDAAK,WAAU;;;;;;;;;;;;;;;;;;0CAMpB,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC,qHAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,qKAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,qKAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,qKAAC;wCACC,WAAU;wCACV,SAAS;wCACT,cAAW;kDAEX,cAAA,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAK,WAAW,CAAC,+FAA+F,EAC/G,iBAAiB,4BAA4B,IAC7C;;;;;;8DACF,qKAAC;oDAAK,WAAW,CAAC,yEAAyE,EACzF,iBAAiB,cAAc,IAC/B;;;;;;8DACF,qKAAC;oDAAK,WAAW,CAAC,+FAA+F,EAC/G,iBAAiB,8BAA8B,IAC/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASb,WAAW,gCACV,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;;0CACb,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,SAAS;0CACtB,cAAA,qKAAC,sHAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAGd,qKAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAK,WAAU;;;;;;sDAChB,qKAAC;4CAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMtB,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;;kDACC,qKAAC;wCACC,WAAU;wCACV,SAAS,IAAM,mBAAmB,CAAC;;0DAEnC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,SAAS;gDAAiB,WAAU;0DAA6E;;;;;;0DAG9J,qKAAC,6MAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,kBAAkB,eAAe,IAAI;;;;;;;;;;;;oCAE7G,iCACC,qKAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,qKAAC,qHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,qKAAC;;kDACC,qKAAC;wCACC,WAAU;wCACV,SAAS,IAAM,sBAAsB,CAAC;;0DAEtC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,SAAS;gDAAiB,WAAU;0DAA4E;;;;;;0DAG9I,qKAAC,6MAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,qBAAqB,eAAe,IAAI;;;;;;;;;;;;oCAEhH,oCACC,qKAAC;wCAAI,WAAU;kDACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,qKAAC,qHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,qKAAC;;kDACC,qKAAC;wCACC,WAAU;wCACV,SAAS,IAAM,gCAAgC,CAAC;;0DAEhD,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,SAAS;gDAAiB,WAAU;0DAA8E;;;;;;0DAGlJ,qKAAC,6MAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,+BAA+B,eAAe,IAAI;;;;;;;;;;;;oCAE1H,8CACC,qKAAC;wCAAI,WAAU;kDACZ,4BAA4B,GAAG,CAAC,CAAC,SAAS,sBACzC,qKAAC,qHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,qKAAC;0CACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;0CAMH,qKAAC;;kDACC,qKAAC;wCACC,WAAU;wCACV,SAAS,IAAM,qBAAqB,CAAC;;0DAErC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,SAAS;gDAAiB,WAAU;0DAA4E;;;;;;0DAGpI,qKAAC,6MAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,oBAAoB,eAAe,IAAI;;;;;;;;;;;;oCAE/G,mCACC,qKAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,qKAAC,qHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,qKAAC;0CACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;0CAMH,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Footer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { Mail, Phone, MapPin, ArrowRight, Facebook, Instagram, Linkedin, Twitter, MessageCircle, Globe } from 'lucide-react';\r\n\r\nconst Footer: React.FC = () => {\r\n  return (\r\n    <footer className=\"relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden min-h-screen flex flex-col justify-center\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0 opacity-5\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/20 to-transparent\"></div>\r\n        <div className=\"absolute top-20 right-0 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute bottom-0 left-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative max-w-7xl mx-auto px-6 py-16\">\r\n        {/* Top Section - CTA */}\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent\">\r\n            Ready to Grow Your Business?\r\n          </h2>\r\n          <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\r\n            Let&apos;s create something amazing together. Get your free consultation today.\r\n          </p>\r\n          <Link href=\"/free-estimate\">\r\n            <button className=\"group inline-flex items-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl shadow-blue-500/25\">\r\n              Get Free Proposal\r\n              <ArrowRight size={20} className=\"ml-2 transition-transform duration-300 group-hover:translate-x-1\" />\r\n            </button>\r\n          </Link>\r\n        </div>\r\n\r\n        {/* Main Content */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12\">\r\n          {/* Company Info */}\r\n          <div className=\"lg:col-span-2\">\r\n            <div className=\"flex items-center mb-6\">\r\n              <Link href=\"/\">\r\n                <Image \r\n                  src=\"/VesaLogo.svg\" \r\n                  alt=\"VESA Solutions Logo\" \r\n                  width={120} \r\n                  height={46}\r\n                  className=\"w-30 h-auto transition-transform duration-300 hover:scale-105\"\r\n                />\r\n              </Link>\r\n            </div>\r\n            <p className=\"text-gray-300 text-lg leading-relaxed mb-8\">\r\n              Full-service digital marketing agency helping businesses attract, impress, and convert more leads online since 2015.\r\n            </p>\r\n            \r\n            {/* Contact Info */}\r\n            <div className=\"space-y-4\">\r\n              <a\r\n                href=\"mailto:<EMAIL>\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Mail size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span><EMAIL></span>\r\n              </a>\r\n              <a\r\n                href=\"tel:+***********\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Phone size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span>****** 628 3793</span>\r\n              </a>\r\n              <a\r\n                href=\"tel:+355694046408\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Phone size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span>+355 69 404 6408</span>\r\n              </a>\r\n              <a \r\n                href=\"https://share.google/T9q3WjqOOmMHrBnJY\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"flex items-start text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <MapPin size={18} className=\"mr-3 text-blue-400 mt-0.5 flex-shrink-0 group-hover:scale-110 transition-transform\" />\r\n                <span>Bulevardi Dyrrah, Pallati 394, Kati 4-t<br />2001, Durrës, Albania</span>\r\n              </a>\r\n              <div className=\"flex items-center text-gray-300\">\r\n                <div className=\"w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse\"></div>\r\n                <span className=\"text-green-400 font-medium\">Open 24 Hours</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Services Grid */}\r\n          <div className=\"lg:col-span-3 grid grid-cols-1 md:grid-cols-4 gap-8\">\r\n            {/* SEO Services */}\r\n            <div>\r\n              <Link href=\"/seo-search-engine-optimization\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-green-400 mb-6 relative transition-colors duration-300\">\r\n                  SEO Services\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-green-400 to-green-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/on-page-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  On-Page SEO\r\n                </Link></li>\r\n                <li><Link href=\"/off-page-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Off-Page SEO\r\n                </Link></li>\r\n                <li><Link href=\"/technical-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Technical SEO\r\n                </Link></li>\r\n                <li><Link href=\"/local-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Local SEO\r\n                </Link></li>\r\n                <li><Link href=\"/content-writing\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Content Writing\r\n                </Link></li>\r\n                <li><Link href=\"/seo-analytics\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  SEO Analytics\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Web Development */}\r\n            <div>\r\n              <Link href=\"/web-development\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-blue-400 mb-6 relative transition-colors duration-300\">\r\n                  Web Development\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/custom-website-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Custom Websites\r\n                </Link></li>\r\n                <li><Link href=\"/ecommerce-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  E-commerce Dev\r\n                </Link></li>\r\n                <li><Link href=\"/mobile-app-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Mobile Apps\r\n                </Link></li>\r\n                <li><Link href=\"/website-speed-optimization\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 flex-shrink-0 transition-all duration-300 group-hover:w-2\"></span>\r\n                  <span className=\"whitespace-nowrap\">Speed Optimization</span>\r\n                </Link></li>\r\n                <li><Link href=\"/web-application-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Web Applications\r\n                </Link></li>\r\n                <li><Link href=\"/website-maintenance-support\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Maintenance\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Digital Marketing */}\r\n            <div>\r\n              <Link href=\"/digital-marketing\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-purple-400 mb-6 relative transition-colors duration-300\">\r\n                  Digital Marketing\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-purple-400 to-purple-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/ppc\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  PPC Advertising\r\n                </Link></li>\r\n                <li><Link href=\"/email-marketing\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Email Marketing\r\n                </Link></li>\r\n                <li><Link href=\"/social-media\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Social Media\r\n                </Link></li>\r\n                <li><Link href=\"/branding-services\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Branding Services\r\n                </Link></li>\r\n                <li><Link href=\"/conversion-optimization\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Conversion Optimization\r\n                </Link></li>\r\n                <li><Link href=\"/reputation-management\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Reputation Management\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Company */}\r\n            <div>\r\n              <h3 className=\"text-lg font-bold text-white mb-6 relative\">\r\n                Company\r\n                <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600\"></span>\r\n              </h3>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/services\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Services\r\n                </Link></li>\r\n                <li><Link href=\"/case-studies\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Case Studies\r\n                </Link></li>\r\n                <li><Link href=\"/about\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  About Us\r\n                </Link></li>\r\n                <li><Link href=\"/contact-us\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Contact Us\r\n                </Link></li>\r\n                <li><Link href=\"/blog\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Blog\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Social Media & Bottom Section */}\r\n        <div className=\"flex flex-col lg:flex-row justify-between items-center pt-8 border-t border-gray-700\">\r\n          <div className=\"mb-6 lg:mb-0\">\r\n            <h4 className=\"text-white font-semibold mb-4\">Follow Our Journey</h4>\r\n            <div className=\"flex space-x-4\">\r\n              <a\r\n                href=\"https://m.facebook.com/VesaSolutions/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Facebook\"\r\n              >\r\n                <Facebook size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://www.instagram.com/vesasolutions/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-gradient-to-r hover:from-pink-500 hover:to-purple-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Instagram\"\r\n              >\r\n                <Instagram size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://al.linkedin.com/company/vesasolutions\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-700 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on LinkedIn\"\r\n              >\r\n                <Linkedin size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"#\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-500 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Twitter\"\r\n              >\r\n                <Twitter size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://wa.me/***********\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-green-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Chat with us on WhatsApp\"\r\n              >\r\n                <MessageCircle size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://vesasolutions.com/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-indigo-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Visit our website\"\r\n              >\r\n                <Globe size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"text-center lg:text-right\">\r\n            <div className=\"text-2xl font-bold text-white mb-2\">Growing Businesses Since 2015</div>\r\n            <div className=\"text-gray-400 text-sm\">Trusted by 200+ companies worldwide</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Copyright */}\r\n        <div className=\"mt-12 pt-8 border-t border-gray-700 text-center\">\r\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\r\n            <p className=\"text-gray-400 text-sm\">\r\n              © 2025 Vesa Solutions Marketing Agency. All rights reserved.\r\n            </p>\r\n            <div className=\"flex flex-wrap justify-center md:justify-end space-x-6 text-sm\">\r\n              <Link href=\"/privacy-policy\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Privacy Policy</Link>\r\n              <Link href=\"/terms-of-service\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Terms of Service</Link>\r\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Sitemap</Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAOA,MAAM,SAAmB;IACvB,qBACE,qKAAC;QAAO,WAAU;;0BAEhB,qKAAC;gBAAI,WAAU;;kCACb,qKAAC;wBAAI,WAAU;;;;;;kCACf,qKAAC;wBAAI,WAAU;;;;;;kCACf,qKAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAG,WAAU;0CAA4G;;;;;;0CAG1H,qKAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,qKAAC;oCAAO,WAAU;;wCAA2P;sDAE3Q,qKAAC,2MAAA,CAAA,aAAU;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMtC,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,qKAAC,sHAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;kDAIhB,qKAAC;wCAAE,WAAU;kDAA6C;;;;;;kDAK1D,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,qKAAC,2LAAA,CAAA,OAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC1B,qKAAC;kEAAK;;;;;;;;;;;;0DAER,qKAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,qKAAC,6LAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC3B,qKAAC;kEAAK;;;;;;;;;;;;0DAER,qKAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,qKAAC,6LAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC3B,qKAAC;kEAAK;;;;;;;;;;;;0DAER,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,qKAAC,mMAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC5B,qKAAC;;4DAAK;0EAAuC,qKAAC;;;;;4DAAK;;;;;;;;;;;;;0DAErD,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;;;;;;kEACf,qKAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;0CAMnD,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC;;0DACC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,WAAU;0DACrD,cAAA,qKAAC;oDAAG,WAAU;;wDAAiG;sEAE7G,qKAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,qKAAC;gDAAG,WAAU;;kEACZ,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAe,WAAU;;8EACtC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EACxC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAa,WAAU;;8EACpC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;;8EAC1C,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EACxC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;;;;;;;;;;;;;kDAOlH,qKAAC;;0DACC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;0DACtC,cAAA,qKAAC;oDAAG,WAAU;;wDAAgG;sEAE5G,qKAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,qKAAC;gDAAG,WAAU;;kEACZ,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA8B,WAAU;;8EACrD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAyB,WAAU;;8EAChD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA0B,WAAU;;8EACjD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA8B,WAAU;;8EACrD,qKAAC;oEAAK,WAAU;;;;;;8EAChB,qKAAC;oEAAK,WAAU;8EAAoB;;;;;;;;;;;;;;;;;kEAEtC,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA+B,WAAU;;8EACtD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA+B,WAAU;;8EACtD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;;;;;;;;;;;;;kDAOjH,qKAAC;;0DACC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DACxC,cAAA,qKAAC;oDAAG,WAAU;;wDAAkG;sEAE9G,qKAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,qKAAC;gDAAG,WAAU;;kEACZ,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAO,WAAU;;8EAC9B,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;;8EAC1C,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAqB,WAAU;;8EAC5C,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA2B,WAAU;;8EAClD,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAyB,WAAU;;8EAChD,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;;;;;;;;;;;;;kDAOnH,qKAAC;;0DACC,qKAAC;gDAAG,WAAU;;oDAA6C;kEAEzD,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAElB,qKAAC;gDAAG,WAAU;;kEACZ,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAY,WAAU;;8EACnC,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;;8EAChC,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAc,WAAU;;8EACrC,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAQ,WAAU;;8EAC/B,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrH,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,mMAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEhC,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,qMAAA,CAAA,YAAS;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEjC,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,mMAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEhC,qKAAC;gDACC,MAAK;gDACL,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,iMAAA,CAAA,UAAO;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAE/B,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,iNAAA,CAAA,gBAAa;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAErC,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,6LAAA,CAAA,QAAK;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAKjC,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,qKAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAK3C,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,qKAAC;oCAAI,WAAU;;sDACb,qKAAC,qHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAkB,WAAU;sDAAsD;;;;;;sDAC7F,qKAAC,qHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAoB,WAAU;sDAAsD;;;;;;sDAC/F,qKAAC,qHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpG;uCAEe", "debugId": null}}, {"offset": {"line": 2533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Form.tsx"], "sourcesContent": ["// components/global/Form.tsx - Fixed with static colors (not affected by dark/light mode)\r\nimport React, { useState, useEffect } from 'react';\r\nimport PhoneInput from 'react-phone-number-input';\r\nimport 'react-phone-number-input/style.css';\r\nimport { useForm, Controller } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { z } from 'zod';\r\nimport { getCountryCallingCode, Country } from 'react-phone-number-input';\r\n\r\n// Form validation schema\r\nconst contactSchema = z.object({\r\n  businessName: z.string().min(2, 'Business name is required'),\r\n  fullName: z.string().min(2, 'Your name is required'),\r\n  email: z.string().email('Valid email is required'),\r\n  phone: z.string().min(10, 'Valid phone number is required'),\r\n  location: z.string().min(2, 'Business location is required'),\r\n  website: z.string().url().optional().or(z.literal('')),\r\n  message: z.string().min(10, 'Please describe your goals (minimum 10 characters)'),\r\n  service: z.string(),\r\n});\r\n\r\ntype ContactFormData = z.infer<typeof contactSchema>;\r\n\r\n// Extended form data type for submission\r\ninterface ExtendedFormData extends ContactFormData {\r\n  userCountry?: string;\r\n  timestamp?: string;\r\n}\r\n\r\ninterface ServiceContactFormProps {\r\n  service: {\r\n    name: string;\r\n    type: 'seo' | 'local-seo' | 'ppc' | 'web-design' | 'social-media' | 'email-marketing' | 'branding' | 'conversion-optimization' | 'reputation-management';\r\n    ctaText?: string;\r\n    messagePlaceholder?: string;\r\n    benefits?: string[];\r\n  };\r\n  className?: string;\r\n  onSubmit?: (data: ExtendedFormData) => Promise<void>;\r\n  initialWebsite?: string;\r\n}\r\n\r\n// API Response types\r\ninterface ApiResponse {\r\n  message: string;\r\n  success: boolean;\r\n  error?: string;\r\n}\r\n\r\n// Helper function to safely get error message\r\nfunction getErrorMessage(error: unknown): string {\r\n  if (error instanceof Error) {\r\n    return error.message;\r\n  }\r\n  if (typeof error === 'string') {\r\n    return error;\r\n  }\r\n  return 'An unknown error occurred';\r\n}\r\n\r\nconst ServiceContactForm: React.FC<ServiceContactFormProps> = ({\r\n  service,\r\n  className = '',\r\n  onSubmit,\r\n  initialWebsite = ''\r\n}) => {\r\n  const [userCountry, setUserCountry] = useState<string>('US');\r\n  const [defaultPhoneCountry, setDefaultPhoneCountry] = useState<Country>('US');\r\n  const [initialPhoneValue, setInitialPhoneValue] = useState<string>('');\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\r\n  const [errorMessage, setErrorMessage] = useState<string>('');\r\n\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    control,\r\n    formState: { errors },\r\n    reset,\r\n    setValue\r\n  } = useForm<ContactFormData>({\r\n    resolver: zodResolver(contactSchema),\r\n    defaultValues: {\r\n      service: service.name,\r\n      website: initialWebsite,\r\n    }\r\n  });\r\n\r\n  // Auto-detect user country on component mount\r\n  useEffect(() => {\r\n    const detectUserCountry = async () => {\r\n      try {\r\n        // Method 1: Try IP-based detection (most reliable)\r\n        const response = await fetch('https://ipapi.co/json/');\r\n        const data = await response.json();\r\n        \r\n        if (data.country_code) {\r\n          const countryCode = data.country_code;\r\n          setUserCountry(countryCode);\r\n          setDefaultPhoneCountry(countryCode as Country);\r\n          \r\n          // Get the country calling code and set initial phone value\r\n          try {\r\n            const callingCode = getCountryCallingCode(countryCode as Country);\r\n            const initialValue = `+${callingCode}`;\r\n            setInitialPhoneValue(initialValue);\r\n            setValue('phone', initialValue);\r\n          } catch {\r\n            console.log('Error getting calling code');\r\n            setInitialPhoneValue('+1');\r\n            setValue('phone', '+1');\r\n          }\r\n          return;\r\n        }\r\n      } catch {\r\n        console.log('IP detection failed, trying locale detection');\r\n      }\r\n\r\n      try {\r\n        const locale = navigator.language || (navigator.languages && navigator.languages[0]);\r\n        const countryCode = locale?.split('-')[1] || 'US';\r\n        setUserCountry(countryCode);\r\n        setDefaultPhoneCountry(countryCode as Country);\r\n        \r\n        // Get the country calling code and set initial phone value\r\n        try {\r\n          const callingCode = getCountryCallingCode(countryCode as Country);\r\n          const initialValue = `+${callingCode}`;\r\n          setInitialPhoneValue(initialValue);\r\n          setValue('phone', initialValue);\r\n        } catch {\r\n          console.log('Error getting calling code');\r\n          setInitialPhoneValue('+1');\r\n          setValue('phone', '+1');\r\n        }\r\n      } catch {\r\n        // Method 3: Default to US\r\n        setUserCountry('US');\r\n        setDefaultPhoneCountry('US');\r\n        setInitialPhoneValue('+1');\r\n        setValue('phone', '+1');\r\n      }\r\n    };\r\n\r\n    detectUserCountry();\r\n  }, [setValue]);\r\n\r\n  // Dynamic form content based on service type\r\n  const getServiceContent = () => {\r\n    // Determine if this is a quote-based service (web development, design, etc.)\r\n    const isQuoteService = service.type === 'web-design' ||\r\n                          service.name.toLowerCase().includes('website') ||\r\n                          service.name.toLowerCase().includes('web design') ||\r\n                          service.name.toLowerCase().includes('web development') ||\r\n                          service.name.toLowerCase().includes('development')\r\n\r\n    const actionWord = isQuoteService ? 'Quote' : 'Analysis'\r\n\r\n    const baseContent = {\r\n      title: `Get Your Free ${service.name} ${actionWord}`,\r\n      subtitle: isQuoteService\r\n        ? `Get a custom ${service.name.toLowerCase()} quote tailored to your business needs and goals.`\r\n        : `See exactly how your business can improve with our ${service.name.toLowerCase()} strategies.`,\r\n      ctaText: service.ctaText || `Get My Free ${service.name} ${actionWord}`,\r\n      messagePlaceholder: service.messagePlaceholder || `Tell us about your ${service.name.toLowerCase()} ${isQuoteService ? 'requirements' : 'goals'}*`,\r\n    };\r\n\r\n    switch (service.type) {\r\n      case 'local-seo':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle for local SEO\r\n          subtitle: 'See exactly how your business ranks against local competitors and discover untapped opportunities.',\r\n          benefits: service.benefits || [\r\n            'Complete local SEO audit & competitor analysis',\r\n            'Google My Business optimization recommendations',\r\n            'Local keyword opportunities & strategy roadmap',\r\n            'Citation audit & local link building plan'\r\n          ]\r\n        };\r\n\r\n      case 'seo':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle for SEO\r\n          subtitle: 'Discover exactly how to dominate Google search results in your industry.',\r\n          benefits: service.benefits || [\r\n            'Complete SEO audit & competitor analysis',\r\n            'Keyword opportunities & content strategy',\r\n            'Technical SEO recommendations',\r\n            'Link building and authority plan'\r\n          ]\r\n        };\r\n\r\n      case 'ppc':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle for PPC\r\n          subtitle: 'See how to maximize your advertising ROI and reduce cost per acquisition.',\r\n          benefits: service.benefits || [\r\n            'PPC account audit & optimization plan',\r\n            'Keyword research & bid strategy',\r\n            'Ad copy and landing page recommendations',\r\n            'Campaign structure & targeting analysis'\r\n          ]\r\n        };\r\n\r\n      case 'web-design':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title and subtitle for web services\r\n          benefits: service.benefits || [\r\n            'Complete website audit & UX analysis',\r\n            'Design recommendations & best practices',\r\n            'Mobile optimization strategy',\r\n            'Conversion optimization plan'\r\n          ]\r\n        };\r\n\r\n      case 'social-media':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle\r\n          subtitle: 'See how to build engaged communities and drive social media ROI.',\r\n          benefits: service.benefits || [\r\n            'Social media audit & competitor analysis',\r\n            'Content strategy & engagement plan',\r\n            'Platform optimization recommendations',\r\n            'Social advertising strategy'\r\n          ]\r\n        };\r\n\r\n      case 'email-marketing':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle\r\n          subtitle: 'Discover how to build relationships that drive revenue through email.',\r\n          benefits: service.benefits || [\r\n            'Email marketing audit & performance analysis',\r\n            'List building & segmentation strategy',\r\n            'Automation workflow recommendations',\r\n            'Campaign optimization plan'\r\n          ]\r\n        };\r\n\r\n      case 'branding':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle\r\n          subtitle: 'See how to build a powerful brand identity that resonates with your audience.',\r\n          benefits: service.benefits || [\r\n            'Complete brand audit & competitive analysis',\r\n            'Brand positioning & messaging strategy',\r\n            'Visual identity recommendations',\r\n            'Brand implementation roadmap'\r\n          ]\r\n        };\r\n\r\n      case 'conversion-optimization':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle\r\n          subtitle: 'Discover how to turn more visitors into customers with data-driven optimization.',\r\n          benefits: service.benefits || [\r\n            'Conversion audit & performance analysis',\r\n            'A/B testing strategy & recommendations',\r\n            'User experience optimization plan',\r\n            'Revenue growth projections'\r\n          ]\r\n        };\r\n\r\n      case 'reputation-management':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle\r\n          subtitle: 'See how to protect and enhance your online reputation across all platforms.',\r\n          benefits: service.benefits || [\r\n            'Complete reputation audit & risk assessment',\r\n            'Review management strategy',\r\n            'Crisis prevention & response plan',\r\n            'Brand authority building roadmap'\r\n          ]\r\n        };\r\n\r\n      default:\r\n        return {\r\n          ...baseContent,\r\n          benefits: service.benefits || [\r\n            'Comprehensive analysis of your current situation',\r\n            'Custom strategy recommendations',\r\n            'Implementation roadmap',\r\n            'Performance tracking plan'\r\n          ]\r\n        };\r\n    }\r\n  };\r\n\r\n  const serviceContent = getServiceContent();\r\n\r\n  const onFormSubmit = async (data: ContactFormData) => {\r\n    setIsSubmitting(true);\r\n    setSubmitStatus('idle');\r\n    setErrorMessage('');\r\n    \r\n    try {\r\n      // Add user country and timestamp to form data\r\n      const formDataWithExtras: ExtendedFormData = {\r\n        ...data,\r\n        userCountry,\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n\r\n      if (onSubmit) {\r\n        // Use custom onSubmit if provided\r\n        await onSubmit(formDataWithExtras);\r\n        setSubmitStatus('success');\r\n        reset();\r\n        // Reset phone to initial value after form reset\r\n        setTimeout(() => {\r\n          setValue('phone', initialPhoneValue);\r\n        }, 100);\r\n      } else {\r\n        // Default: Send to API\r\n        const response = await fetch('/api/contact', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify(formDataWithExtras),\r\n        });\r\n\r\n        const result: ApiResponse = await response.json();\r\n\r\n        if (!response.ok) {\r\n          throw new Error(result.message || 'Failed to submit form');\r\n        }\r\n\r\n        if (result.success) {\r\n          setSubmitStatus('success');\r\n          reset();\r\n          // Reset phone to initial value after form reset\r\n          setTimeout(() => {\r\n            setValue('phone', initialPhoneValue);\r\n          }, 100);\r\n        } else {\r\n          throw new Error(result.message || 'Form submission failed');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Form submission error:', error);\r\n      const errorMsg = getErrorMessage(error);\r\n      setErrorMessage(errorMsg);\r\n      setSubmitStatus('error');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Success state\r\n  if (submitStatus === 'success') {\r\n    return (\r\n      <div \r\n        className={`rounded-2xl shadow-xl p-8 ${className}`}\r\n        style={{ backgroundColor: '#ffffff' }}\r\n      >\r\n        <div className=\"text-center\">\r\n          <div \r\n            className=\"w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\"\r\n            style={{ backgroundColor: '#dcfce7' }}\r\n          >\r\n            <svg \r\n              className=\"w-8 h-8\" \r\n              fill=\"none\" \r\n              stroke=\"currentColor\" \r\n              viewBox=\"0 0 24 24\"\r\n              style={{ color: '#16a34a' }}\r\n            >\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\"></path>\r\n            </svg>\r\n          </div>\r\n          <h3 \r\n            className=\"text-2xl font-bold mb-2\"\r\n            style={{ color: '#1f2937' }}\r\n          >\r\n            Thank You! 🎉\r\n          </h3>\r\n          <p \r\n            className=\"mb-4\"\r\n            style={{ color: '#4b5563' }}\r\n          >\r\n            Your request has been submitted successfully. We&apos;ll contact you within 24 hours with your free {service.name.toLowerCase()} analysis.\r\n          </p>\r\n          <p \r\n            className=\"text-sm mb-6\"\r\n            style={{ color: '#6b7280' }}\r\n          >\r\n            Check your email for confirmation and next steps.\r\n          </p>\r\n          <button \r\n            onClick={() => {\r\n              setSubmitStatus('idle');\r\n              setErrorMessage('');\r\n              // Reset phone to initial value when showing form again\r\n              setTimeout(() => {\r\n                setValue('phone', initialPhoneValue);\r\n              }, 100);\r\n            }}\r\n            className=\"font-semibold transition-colors\"\r\n            style={{ color: '#2563eb' }}\r\n            onMouseEnter={(e) => {\r\n              e.currentTarget.style.color = '#1d4ed8';\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              e.currentTarget.style.color = '#2563eb';\r\n            }}\r\n          >\r\n            Submit Another Request\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div \r\n      className={`rounded-2xl shadow-xl p-8 ${className}`}\r\n      style={{ backgroundColor: '#ffffff' }}\r\n    >\r\n      <h3 \r\n        className=\"text-2xl font-bold mb-2\"\r\n        style={{ color: '#1f2937' }}\r\n      >\r\n        {serviceContent.title}\r\n      </h3>\r\n      <p \r\n        className=\"mb-6\"\r\n        style={{ color: '#4b5563' }}\r\n      >\r\n        {serviceContent.subtitle}\r\n      </p>\r\n\r\n      <form onSubmit={handleSubmit(onFormSubmit)} className=\"space-y-4\">\r\n        {/* Business Name & Your Name */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n          <div>\r\n            <input \r\n              {...register('businessName')}\r\n              type=\"text\" \r\n              placeholder=\"Business Name*\" \r\n              className=\"w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors\"\r\n              style={{ \r\n                border: '1px solid #d1d5db',\r\n                color: '#374151',\r\n                backgroundColor: '#ffffff'\r\n              }}\r\n              onFocus={(e) => {\r\n                e.target.style.borderColor = 'transparent';\r\n                e.target.style.boxShadow = '0 0 0 2px #3b82f6';\r\n              }}\r\n              onBlur={(e) => {\r\n                e.target.style.borderColor = '#d1d5db';\r\n                e.target.style.boxShadow = 'none';\r\n              }}\r\n            />\r\n            {errors.businessName && (\r\n              <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n                {errors.businessName.message}\r\n              </p>\r\n            )}\r\n          </div>\r\n          <div>\r\n            <input \r\n              {...register('fullName')}\r\n              type=\"text\" \r\n              placeholder=\"Your Name*\" \r\n              className=\"w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors\"\r\n              style={{ \r\n                border: '1px solid #d1d5db',\r\n                color: '#374151',\r\n                backgroundColor: '#ffffff'\r\n              }}\r\n              onFocus={(e) => {\r\n                e.target.style.borderColor = 'transparent';\r\n                e.target.style.boxShadow = '0 0 0 2px #3b82f6';\r\n              }}\r\n              onBlur={(e) => {\r\n                e.target.style.borderColor = '#d1d5db';\r\n                e.target.style.boxShadow = 'none';\r\n              }}\r\n            />\r\n            {errors.fullName && (\r\n              <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n                {errors.fullName.message}\r\n              </p>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Email */}\r\n        <div>\r\n          <input \r\n            {...register('email')}\r\n            type=\"email\" \r\n            placeholder=\"Email Address*\" \r\n            className=\"w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors\"\r\n            style={{ \r\n              border: '1px solid #d1d5db',\r\n              color: '#374151',\r\n              backgroundColor: '#ffffff'\r\n            }}\r\n            onFocus={(e) => {\r\n              e.target.style.borderColor = 'transparent';\r\n              e.target.style.boxShadow = '0 0 0 2px #3b82f6';\r\n            }}\r\n            onBlur={(e) => {\r\n              e.target.style.borderColor = '#d1d5db';\r\n              e.target.style.boxShadow = 'none';\r\n            }}\r\n          />\r\n          {errors.email && (\r\n            <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n              {errors.email.message}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Phone Number with Country Detection and Pre-filled Prefix */}\r\n        <div>\r\n          <Controller\r\n            name=\"phone\"\r\n            control={control}\r\n            render={({ field }) => (\r\n              <div className=\"flex gap-2\">\r\n                <PhoneInput\r\n                  {...field}\r\n                  value={field.value || initialPhoneValue}\r\n                  defaultCountry={defaultPhoneCountry}\r\n                  placeholder=\"Phone Number*\"\r\n                  className=\"w-full\"\r\n                  style={{\r\n                    '--PhoneInputCountryFlag-borderColor': 'transparent',\r\n                    '--PhoneInput-color': '#374151',\r\n                  }}\r\n                  numberInputProps={{\r\n                    className: 'flex-1 px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors',\r\n                    style: { \r\n                      border: '1px solid #d1d5db',\r\n                      color: '#374151',\r\n                      backgroundColor: '#ffffff'\r\n                    }\r\n                  }}\r\n                  countrySelectProps={{\r\n                    className: 'px-3 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors',\r\n                    style: { \r\n                      border: '1px solid #d1d5db',\r\n                      backgroundColor: '#ffffff',\r\n                      minWidth: '80px',\r\n                      width: '80px'\r\n                    }\r\n                  }}\r\n                  onChange={(value) => {\r\n                    field.onChange(value);\r\n                    // If user clears the field but country is still selected, restore the prefix\r\n                    if (!value && defaultPhoneCountry) {\r\n                      try {\r\n                        const callingCode = getCountryCallingCode(defaultPhoneCountry);\r\n                        const prefixValue = `+${callingCode}`;\r\n                        setTimeout(() => {\r\n                          field.onChange(prefixValue);\r\n                        }, 10);\r\n                      } catch {\r\n                        console.log('Error restoring prefix');\r\n                      }\r\n                    }\r\n                  }}\r\n                />\r\n              </div>\r\n            )}\r\n          />\r\n          {errors.phone && (\r\n            <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n              {errors.phone.message}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Business Location */}\r\n        <div>\r\n          <input \r\n            {...register('location')}\r\n            type=\"text\" \r\n            placeholder=\"Business Location (City, State)*\" \r\n            className=\"w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors\"\r\n            style={{ \r\n              border: '1px solid #d1d5db',\r\n              color: '#374151',\r\n              backgroundColor: '#ffffff'\r\n            }}\r\n            onFocus={(e) => {\r\n              e.target.style.borderColor = 'transparent';\r\n              e.target.style.boxShadow = '0 0 0 2px #3b82f6';\r\n            }}\r\n            onBlur={(e) => {\r\n              e.target.style.borderColor = '#d1d5db';\r\n              e.target.style.boxShadow = 'none';\r\n            }}\r\n          />\r\n          {errors.location && (\r\n            <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n              {errors.location.message}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Website URL */}\r\n        <div>\r\n          <input \r\n            {...register('website')}\r\n            type=\"url\" \r\n            placeholder=\"Website URL (optional)\" \r\n            className=\"w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors\"\r\n            style={{ \r\n              border: '1px solid #d1d5db',\r\n              color: '#374151',\r\n              backgroundColor: '#ffffff'\r\n            }}\r\n            onFocus={(e) => {\r\n              e.target.style.borderColor = 'transparent';\r\n              e.target.style.boxShadow = '0 0 0 2px #3b82f6';\r\n            }}\r\n            onBlur={(e) => {\r\n              e.target.style.borderColor = '#d1d5db';\r\n              e.target.style.boxShadow = 'none';\r\n            }}\r\n          />\r\n          {errors.website && (\r\n            <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n              {errors.website.message}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Message */}\r\n        <div>\r\n          <textarea \r\n            {...register('message')}\r\n            placeholder={serviceContent.messagePlaceholder}\r\n            rows={3}\r\n            className=\"w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 resize-none transition-colors\"\r\n            style={{ \r\n              border: '1px solid #d1d5db',\r\n              color: '#374151',\r\n              backgroundColor: '#ffffff'\r\n            }}\r\n            onFocus={(e) => {\r\n              e.target.style.borderColor = 'transparent';\r\n              e.target.style.boxShadow = '0 0 0 2px #3b82f6';\r\n            }}\r\n            onBlur={(e) => {\r\n              e.target.style.borderColor = '#d1d5db';\r\n              e.target.style.boxShadow = 'none';\r\n            }}\r\n          />\r\n          {errors.message && (\r\n            <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n              {errors.message.message}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Hidden service field */}\r\n        <input {...register('service')} type=\"hidden\" />\r\n\r\n        {/* Error message */}\r\n        {submitStatus === 'error' && (\r\n          <div \r\n            className=\"px-4 py-3 rounded-lg\"\r\n            style={{ \r\n              backgroundColor: '#fef2f2',\r\n              border: '1px solid #fecaca',\r\n              color: '#b91c1c'\r\n            }}\r\n          >\r\n            <div className=\"flex items-center\">\r\n              <svg \r\n                className=\"w-5 h-5 mr-2 flex-shrink-0\" \r\n                fill=\"currentColor\" \r\n                viewBox=\"0 0 20 20\"\r\n                style={{ color: '#b91c1c' }}\r\n              >\r\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n              </svg>\r\n              <div>\r\n                <p className=\"text-sm font-medium\">Form submission failed</p>\r\n                <p className=\"text-sm mt-1\">{errorMessage || 'Please try again or call us directly at (555) 123-4567.'}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Submit Button */}\r\n        <button \r\n          type=\"submit\"\r\n          disabled={isSubmitting}\r\n          className=\"w-full font-bold py-4 rounded-lg transition-all duration-300 flex items-center justify-center\"\r\n          style={{\r\n            background: isSubmitting \r\n              ? 'linear-gradient(to right, #9ca3af, #6b7280)'\r\n              : 'linear-gradient(to right, #2563eb, #1d4ed8)',\r\n            color: '#ffffff',\r\n            cursor: isSubmitting ? 'not-allowed' : 'pointer'\r\n          }}\r\n          onMouseEnter={(e) => {\r\n            if (!isSubmitting) {\r\n              e.currentTarget.style.background = 'linear-gradient(to right, #1d4ed8, #1e40af)';\r\n            }\r\n          }}\r\n          onMouseLeave={(e) => {\r\n            if (!isSubmitting) {\r\n              e.currentTarget.style.background = 'linear-gradient(to right, #2563eb, #1d4ed8)';\r\n            }\r\n          }}\r\n        >\r\n          {isSubmitting ? (\r\n            <>\r\n              <svg \r\n                className=\"animate-spin -ml-1 mr-3 h-5 w-5\" \r\n                xmlns=\"http://www.w3.org/2000/svg\" \r\n                fill=\"none\" \r\n                viewBox=\"0 0 24 24\"\r\n                style={{ color: '#ffffff' }}\r\n              >\r\n                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n              </svg>\r\n              Submitting...\r\n            </>\r\n          ) : (\r\n            serviceContent.ctaText\r\n          )}\r\n        </button>\r\n      </form>\r\n\r\n      {/* Trust indicator */}\r\n      <p \r\n        className=\"text-xs text-center mt-4\"\r\n        style={{ color: '#6b7280' }}\r\n      >\r\n        🔒 Your information is secure and will never be shared. We&apos;ll contact you within 24 hours.\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ServiceContactForm;"], "names": [], "mappings": "AAAA,0FAA0F;;;;;AAC1F;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;AAGA,yBAAyB;AACzB,MAAM,gBAAgB,sGAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,cAAc,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,UAAU,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAC1B,UAAU,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,SAAS,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,sGAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAClD,SAAS,sGAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAC5B,SAAS,sGAAA,CAAA,IAAC,CAAC,MAAM;AACnB;AA8BA,8CAA8C;AAC9C,SAAS,gBAAgB,KAAc;IACrC,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAEA,MAAM,qBAAwD,CAAC,EAC7D,OAAO,EACP,YAAY,EAAE,EACd,QAAQ,EACR,iBAAiB,EAAE,EACpB;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IACxE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAgC;IAC/E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACL,QAAQ,EACT,GAAG,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,SAAS,QAAQ,IAAI;YACrB,SAAS;QACX;IACF;IAEA,8CAA8C;IAC9C,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI;gBACF,mDAAmD;gBACnD,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,YAAY,EAAE;oBACrB,MAAM,cAAc,KAAK,YAAY;oBACrC,eAAe;oBACf,uBAAuB;oBAEvB,2DAA2D;oBAC3D,IAAI;wBACF,MAAM,cAAc,CAAA,GAAA,kKAAA,CAAA,wBAAqB,AAAD,EAAE;wBAC1C,MAAM,eAAe,CAAC,CAAC,EAAE,aAAa;wBACtC,qBAAqB;wBACrB,SAAS,SAAS;oBACpB,EAAE,OAAM;wBACN,QAAQ,GAAG,CAAC;wBACZ,qBAAqB;wBACrB,SAAS,SAAS;oBACpB;oBACA;gBACF;YACF,EAAE,OAAM;gBACN,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI;gBACF,MAAM,SAAS,UAAU,QAAQ,IAAK,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,EAAE;gBACnF,MAAM,cAAc,QAAQ,MAAM,IAAI,CAAC,EAAE,IAAI;gBAC7C,eAAe;gBACf,uBAAuB;gBAEvB,2DAA2D;gBAC3D,IAAI;oBACF,MAAM,cAAc,CAAA,GAAA,kKAAA,CAAA,wBAAqB,AAAD,EAAE;oBAC1C,MAAM,eAAe,CAAC,CAAC,EAAE,aAAa;oBACtC,qBAAqB;oBACrB,SAAS,SAAS;gBACpB,EAAE,OAAM;oBACN,QAAQ,GAAG,CAAC;oBACZ,qBAAqB;oBACrB,SAAS,SAAS;gBACpB;YACF,EAAE,OAAM;gBACN,0BAA0B;gBAC1B,eAAe;gBACf,uBAAuB;gBACvB,qBAAqB;gBACrB,SAAS,SAAS;YACpB;QACF;QAEA;IACF,GAAG;QAAC;KAAS;IAEb,6CAA6C;IAC7C,MAAM,oBAAoB;QACxB,6EAA6E;QAC7E,MAAM,iBAAiB,QAAQ,IAAI,KAAK,gBAClB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,cACpC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,iBACpC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,sBACpC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QAE1D,MAAM,aAAa,iBAAiB,UAAU;QAE9C,MAAM,cAAc;YAClB,OAAO,CAAC,cAAc,EAAE,QAAQ,IAAI,CAAC,CAAC,EAAE,YAAY;YACpD,UAAU,iBACN,CAAC,aAAa,EAAE,QAAQ,IAAI,CAAC,WAAW,GAAG,iDAAiD,CAAC,GAC7F,CAAC,mDAAmD,EAAE,QAAQ,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC;YAClG,SAAS,QAAQ,OAAO,IAAI,CAAC,YAAY,EAAE,QAAQ,IAAI,CAAC,CAAC,EAAE,YAAY;YACvE,oBAAoB,QAAQ,kBAAkB,IAAI,CAAC,mBAAmB,EAAE,QAAQ,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,iBAAiB,iBAAiB,QAAQ,CAAC,CAAC;QACpJ;QAEA,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,6DAA6D;oBAC7D,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,uDAAuD;oBACvD,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,uDAAuD;oBACvD,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,kDAAkD;oBAClD,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,+CAA+C;oBAC/C,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,+CAA+C;oBAC/C,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,+CAA+C;oBAC/C,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,+CAA+C;oBAC/C,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,+CAA+C;oBAC/C,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF;gBACE,OAAO;oBACL,GAAG,WAAW;oBACd,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;QACJ;IACF;IAEA,MAAM,iBAAiB;IAEvB,MAAM,eAAe,OAAO;QAC1B,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,8CAA8C;YAC9C,MAAM,qBAAuC;gBAC3C,GAAG,IAAI;gBACP;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,IAAI,UAAU;gBACZ,kCAAkC;gBAClC,MAAM,SAAS;gBACf,gBAAgB;gBAChB;gBACA,gDAAgD;gBAChD,WAAW;oBACT,SAAS,SAAS;gBACpB,GAAG;YACL,OAAO;gBACL,uBAAuB;gBACvB,MAAM,WAAW,MAAM,MAAM,gBAAgB;oBAC3C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,MAAM,SAAsB,MAAM,SAAS,IAAI;gBAE/C,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,gBAAgB;oBAChB;oBACA,gDAAgD;oBAChD,WAAW;wBACT,SAAS,SAAS;oBACpB,GAAG;gBACL,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,WAAW,gBAAgB;YACjC,gBAAgB;YAChB,gBAAgB;QAClB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,gBAAgB;IAChB,IAAI,iBAAiB,WAAW;QAC9B,qBACE,qKAAC;YACC,WAAW,CAAC,0BAA0B,EAAE,WAAW;YACnD,OAAO;gBAAE,iBAAiB;YAAU;sBAEpC,cAAA,qKAAC;gBAAI,WAAU;;kCACb,qKAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAU;kCAEpC,cAAA,qKAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,OAAO;gCAAE,OAAO;4BAAU;sCAE1B,cAAA,qKAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;;;;;;;kCAGzE,qKAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAU;kCAC3B;;;;;;kCAGD,qKAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAU;;4BAC3B;4BACsG,QAAQ,IAAI,CAAC,WAAW;4BAAG;;;;;;;kCAElI,qKAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAU;kCAC3B;;;;;;kCAGD,qKAAC;wBACC,SAAS;4BACP,gBAAgB;4BAChB,gBAAgB;4BAChB,uDAAuD;4BACvD,WAAW;gCACT,SAAS,SAAS;4BACpB,GAAG;wBACL;wBACA,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAU;wBAC1B,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;wBAChC;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;wBAChC;kCACD;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,qKAAC;QACC,WAAW,CAAC,0BAA0B,EAAE,WAAW;QACnD,OAAO;YAAE,iBAAiB;QAAU;;0BAEpC,qKAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,OAAO;gBAAU;0BAEzB,eAAe,KAAK;;;;;;0BAEvB,qKAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,OAAO;gBAAU;0BAEzB,eAAe,QAAQ;;;;;;0BAG1B,qKAAC;gBAAK,UAAU,aAAa;gBAAe,WAAU;;kCAEpD,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;;kDACC,qKAAC;wCACE,GAAG,SAAS,eAAe;wCAC5B,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,OAAO;4CACL,QAAQ;4CACR,OAAO;4CACP,iBAAiB;wCACnB;wCACA,SAAS,CAAC;4CACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;4CAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wCAC7B;wCACA,QAAQ,CAAC;4CACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;4CAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wCAC7B;;;;;;oCAED,OAAO,YAAY,kBAClB,qKAAC;wCAAE,WAAU;wCAAe,OAAO;4CAAE,OAAO;wCAAU;kDACnD,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;0CAIlC,qKAAC;;kDACC,qKAAC;wCACE,GAAG,SAAS,WAAW;wCACxB,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,OAAO;4CACL,QAAQ;4CACR,OAAO;4CACP,iBAAiB;wCACnB;wCACA,SAAS,CAAC;4CACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;4CAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wCAC7B;wCACA,QAAQ,CAAC;4CACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;4CAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wCAC7B;;;;;;oCAED,OAAO,QAAQ,kBACd,qKAAC;wCAAE,WAAU;wCAAe,OAAO;4CAAE,OAAO;wCAAU;kDACnD,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;kCAOhC,qKAAC;;0CACC,qKAAC;gCACE,GAAG,SAAS,QAAQ;gCACrB,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,OAAO;oCACL,QAAQ;oCACR,OAAO;oCACP,iBAAiB;gCACnB;gCACA,SAAS,CAAC;oCACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;gCACA,QAAQ,CAAC;oCACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;;;;;;4BAED,OAAO,KAAK,kBACX,qKAAC;gCAAE,WAAU;gCAAe,OAAO;oCAAE,OAAO;gCAAU;0CACnD,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAM3B,qKAAC;;0CACC,qKAAC,0IAAA,CAAA,aAAU;gCACT,MAAK;gCACL,SAAS;gCACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC,kKAAA,CAAA,UAAU;4CACR,GAAG,KAAK;4CACT,OAAO,MAAM,KAAK,IAAI;4CACtB,gBAAgB;4CAChB,aAAY;4CACZ,WAAU;4CACV,OAAO;gDACL,uCAAuC;gDACvC,sBAAsB;4CACxB;4CACA,kBAAkB;gDAChB,WAAW;gDACX,OAAO;oDACL,QAAQ;oDACR,OAAO;oDACP,iBAAiB;gDACnB;4CACF;4CACA,oBAAoB;gDAClB,WAAW;gDACX,OAAO;oDACL,QAAQ;oDACR,iBAAiB;oDACjB,UAAU;oDACV,OAAO;gDACT;4CACF;4CACA,UAAU,CAAC;gDACT,MAAM,QAAQ,CAAC;gDACf,6EAA6E;gDAC7E,IAAI,CAAC,SAAS,qBAAqB;oDACjC,IAAI;wDACF,MAAM,cAAc,CAAA,GAAA,kKAAA,CAAA,wBAAqB,AAAD,EAAE;wDAC1C,MAAM,cAAc,CAAC,CAAC,EAAE,aAAa;wDACrC,WAAW;4DACT,MAAM,QAAQ,CAAC;wDACjB,GAAG;oDACL,EAAE,OAAM;wDACN,QAAQ,GAAG,CAAC;oDACd;gDACF;4CACF;;;;;;;;;;;;;;;;4BAKP,OAAO,KAAK,kBACX,qKAAC;gCAAE,WAAU;gCAAe,OAAO;oCAAE,OAAO;gCAAU;0CACnD,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAM3B,qKAAC;;0CACC,qKAAC;gCACE,GAAG,SAAS,WAAW;gCACxB,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,OAAO;oCACL,QAAQ;oCACR,OAAO;oCACP,iBAAiB;gCACnB;gCACA,SAAS,CAAC;oCACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;gCACA,QAAQ,CAAC;oCACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;;;;;;4BAED,OAAO,QAAQ,kBACd,qKAAC;gCAAE,WAAU;gCAAe,OAAO;oCAAE,OAAO;gCAAU;0CACnD,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;kCAM9B,qKAAC;;0CACC,qKAAC;gCACE,GAAG,SAAS,UAAU;gCACvB,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,OAAO;oCACL,QAAQ;oCACR,OAAO;oCACP,iBAAiB;gCACnB;gCACA,SAAS,CAAC;oCACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;gCACA,QAAQ,CAAC;oCACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;;;;;;4BAED,OAAO,OAAO,kBACb,qKAAC;gCAAE,WAAU;gCAAe,OAAO;oCAAE,OAAO;gCAAU;0CACnD,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;kCAM7B,qKAAC;;0CACC,qKAAC;gCACE,GAAG,SAAS,UAAU;gCACvB,aAAa,eAAe,kBAAkB;gCAC9C,MAAM;gCACN,WAAU;gCACV,OAAO;oCACL,QAAQ;oCACR,OAAO;oCACP,iBAAiB;gCACnB;gCACA,SAAS,CAAC;oCACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;gCACA,QAAQ,CAAC;oCACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;;;;;;4BAED,OAAO,OAAO,kBACb,qKAAC;gCAAE,WAAU;gCAAe,OAAO;oCAAE,OAAO;gCAAU;0CACnD,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;kCAM7B,qKAAC;wBAAO,GAAG,SAAS,UAAU;wBAAE,MAAK;;;;;;oBAGpC,iBAAiB,yBAChB,qKAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,QAAQ;4BACR,OAAO;wBACT;kCAEA,cAAA,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;oCACR,OAAO;wCAAE,OAAO;oCAAU;8CAE1B,cAAA,qKAAC;wCAAK,UAAS;wCAAU,GAAE;wCAA0N,UAAS;;;;;;;;;;;8CAEhQ,qKAAC;;sDACC,qKAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,qKAAC;4CAAE,WAAU;sDAAgB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;kCAOrD,qKAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;wBACV,OAAO;4BACL,YAAY,eACR,gDACA;4BACJ,OAAO;4BACP,QAAQ,eAAe,gBAAgB;wBACzC;wBACA,cAAc,CAAC;4BACb,IAAI,CAAC,cAAc;gCACjB,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;4BACrC;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,CAAC,cAAc;gCACjB,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;4BACrC;wBACF;kCAEC,6BACC;;8CACE,qKAAC;oCACC,WAAU;oCACV,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,OAAO;wCAAE,OAAO;oCAAU;;sDAE1B,qKAAC;4CAAO,WAAU;4CAAa,IAAG;4CAAK,IAAG;4CAAK,GAAE;4CAAK,QAAO;4CAAe,aAAY;;;;;;sDACxF,qKAAC;4CAAK,WAAU;4CAAa,MAAK;4CAAe,GAAE;;;;;;;;;;;;gCAC/C;;2CAIR,eAAe,OAAO;;;;;;;;;;;;0BAM5B,qKAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,OAAO;gBAAU;0BAC3B;;;;;;;;;;;;AAKP;uCAEe", "debugId": null}}, {"offset": {"line": 3495, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/web-dev/main-web-dev/WebDevHeroSection.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\nimport { ArrowRight, Phone } from 'lucide-react';\nimport ServiceContactForm from '@/components/global/Form';\n\n// Helper function to safely get error message\nfunction getErrorMessage(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  if (typeof error === 'string') {\n    return error;\n  }\n  return 'An unknown error occurred';\n}\n\ninterface FormData {\n  businessName: string;\n  fullName: string;\n  email: string;\n  phone: string;\n  location: string;\n  website?: string;\n  message: string;\n  service: string;\n  userCountry?: string;\n  timestamp?: string;\n}\n\nconst WebDevHeroSection: React.FC = () => {\n  const handleFormSubmit = async (data: FormData) => {\n    try {\n      console.log('Web Development form submitted:', data);\n      \n      // Send to API\n      const response = await fetch('/api/contact', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.message || 'Failed to submit form');\n      }\n\n      if (!result.success) {\n        throw new Error(result.message || 'Form submission failed');\n      }\n\n      // Success is handled by the form component\n      console.log('Form submitted successfully:', result);\n      \n    } catch (error) {\n      console.error('Form submission error:', error);\n      // Re-throw the error so the form component can handle it\n      throw new Error(getErrorMessage(error));\n    }\n  };\n\n  return (\n    <section className=\"relative min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 overflow-hidden\">\n      {/* Background Image */}\n      <div className=\"absolute inset-0\">\n        <Image\n          src=\"https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=1920&h=1080&fit=crop\"\n          alt=\"Web Development Code\"\n          fill\n          className=\"object-cover opacity-20\"\n        />\n        <div className=\"absolute inset-0 bg-gradient-to-r from-blue-900/80 to-blue-800/80\"></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-6 py-20 flex items-center min-h-screen\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center w-full\">\n          <div>\n            <div className=\"bg-green-600 text-white text-sm font-bold px-4 py-2 rounded-full inline-block mb-6\">\n              Don&apos;t Settle For Template Websites\n            </div>\n            \n            <h1 className=\"text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight mb-8\">\n              Custom Web Development That\n              <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-blue-400\"> Converts Visitors</span>\n            </h1>\n            \n            <p className=\"text-xl text-blue-100 mb-12 leading-relaxed\">\n              Get a professionally designed, lightning-fast website that turns visitors into customers. Our custom web development solutions are built for performance, conversions, and growth.\n            </p>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-3 gap-8 mb-12\">\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-bold text-blue-300 mb-2\">99.9%</div>\n                <div className=\"text-blue-100\">Uptime Guarantee</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-bold text-blue-300 mb-2\">&lt;3s</div>\n                <div className=\"text-blue-100\">Load Time</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-bold text-blue-300 mb-2\">100%</div>\n                <div className=\"text-blue-100\">Mobile Responsive</div>\n              </div>\n            </div>\n\n            <a\n              href=\"tel:+***********\"\n              className=\"group bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold px-10 py-5 rounded-full transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-blue-500/25 inline-flex items-center justify-center text-lg\"\n            >\n              <Phone className=\"mr-3\" size={24} />\n              Get A Free Web Development Quote\n              <ArrowRight size={24} className=\"ml-3 transition-transform duration-300 group-hover:translate-x-1\" />\n            </a>\n          </div>\n\n          {/* Contact Form */}\n          <div className=\"bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl\">\n            <ServiceContactForm \n              service={{\n                name: 'Web Development',\n                type: 'web-design',\n                ctaText: 'Get My Free Web Development Consultation',\n                messagePlaceholder: 'Tell us about your website goals and requirements*',\n                benefits: [\n                  'Custom website design & development',\n                  'Mobile-responsive & fast-loading',\n                  'SEO-optimized structure',\n                  'Ongoing support & maintenance'\n                ]\n              }}\n              onSubmit={handleFormSubmit}\n              className=\"shadow-none\"\n            />\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default WebDevHeroSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;;;;;;;;;AAEA,8CAA8C;AAC9C,SAAS,gBAAgB,KAAc;IACrC,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAeA,MAAM,oBAA8B;IAClC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,2CAA2C;YAC3C,QAAQ,GAAG,CAAC,gCAAgC;QAE9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,yDAAyD;YACzD,MAAM,IAAI,MAAM,gBAAgB;QAClC;IACF;IAEA,qBACE,qKAAC;QAAQ,WAAU;;0BAEjB,qKAAC;gBAAI,WAAU;;kCACb,qKAAC,sHAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,IAAI;wBACJ,WAAU;;;;;;kCAEZ,qKAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,qKAAC;gBAAI,WAAU;0BACb,cAAA,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;;8CACC,qKAAC;oCAAI,WAAU;8CAAqF;;;;;;8CAIpG,qKAAC;oCAAG,WAAU;;wCAA2E;sDAEvF,qKAAC;4CAAK,WAAU;sDAA2E;;;;;;;;;;;;8CAG7F,qKAAC;oCAAE,WAAU;8CAA8C;;;;;;8CAK3D,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,qKAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;sDAEjC,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,qKAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;sDAEjC,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,qKAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAInC,qKAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,qKAAC,6LAAA,CAAA,QAAK;4CAAC,WAAU;4CAAO,MAAM;;;;;;wCAAM;sDAEpC,qKAAC,2MAAA,CAAA,aAAU;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAKpC,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC,sHAAA,CAAA,UAAkB;gCACjB,SAAS;oCACP,MAAM;oCACN,MAAM;oCACN,SAAS;oCACT,oBAAoB;oCACpB,UAAU;wCACR;wCACA;wCACA;wCACA;qCACD;gCACH;gCACA,UAAU;gCACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;uCAEe", "debugId": null}}, {"offset": {"line": 3788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/web-dev/main-web-dev/WebDevAboutSection.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\nimport { CheckCircle } from 'lucide-react';\n\nconst WebDevAboutSection: React.FC = () => {\n  return (\n    <section className=\"py-24 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        {/* What is Web Development Section */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-24\">\n          <div>\n            <Image\n              src=\"https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=600&h=400&fit=crop\"\n              alt=\"Professional Web Development\"\n              width={600}\n              height={400}\n              className=\"w-full rounded-3xl shadow-xl\"\n            />\n          </div>\n          <div>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-8\">\n              Professional Web Development That Drives Business Growth\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-6 leading-relaxed\">\n              Web development is more than just creating a website—it&apos;s about building a powerful digital platform that converts visitors into customers and drives sustainable business growth.\n            </p>\n            <p className=\"text-lg text-gray-600 mb-8 leading-relaxed\">\n              At VESA Solutions, we combine cutting-edge technology with proven conversion strategies to create websites that not only look amazing but deliver measurable results for your business.\n            </p>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8\">\n              <div className=\"flex items-center\">\n                <CheckCircle className=\"text-green-500 mr-3 flex-shrink-0\" size={24} />\n                <span className=\"text-gray-700\">Custom Design & Development</span>\n              </div>\n              <div className=\"flex items-center\">\n                <CheckCircle className=\"text-green-500 mr-3 flex-shrink-0\" size={24} />\n                <span className=\"text-gray-700\">Mobile-First Responsive Design</span>\n              </div>\n              <div className=\"flex items-center\">\n                <CheckCircle className=\"text-green-500 mr-3 flex-shrink-0\" size={24} />\n                <span className=\"text-gray-700\">Lightning-Fast Performance</span>\n              </div>\n              <div className=\"flex items-center\">\n                <CheckCircle className=\"text-green-500 mr-3 flex-shrink-0\" size={24} />\n                <span className=\"text-gray-700\">SEO-Optimized Structure</span>\n              </div>\n              <div className=\"flex items-center\">\n                <CheckCircle className=\"text-green-500 mr-3 flex-shrink-0\" size={24} />\n                <span className=\"text-gray-700\">Security & Maintenance</span>\n              </div>\n              <div className=\"flex items-center\">\n                <CheckCircle className=\"text-green-500 mr-3 flex-shrink-0\" size={24} />\n                <span className=\"text-gray-700\">Analytics & Conversion Tracking</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Why Choose VESA Section */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n          <div>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-8\">\n              Why Choose VESA for Your Web Development?\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-6 leading-relaxed\">\n              We don&apos;t just build websites—we create digital experiences that drive results. Our proven development process ensures your website performs at the highest level.\n            </p>\n            \n            <div className=\"space-y-6\">\n              <div className=\"bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-2xl\">\n                <h4 className=\"text-xl font-bold text-gray-800 mb-3\">Performance-First Approach</h4>\n                <p className=\"text-gray-600\">Every website we build is optimized for speed, with load times under 3 seconds and 98%+ uptime guarantee.</p>\n              </div>\n              \n              <div className=\"bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-2xl\">\n                <h4 className=\"text-xl font-bold text-gray-800 mb-3\">Conversion-Focused Design</h4>\n                <p className=\"text-gray-600\">We design with your business goals in mind, creating user experiences that guide visitors toward taking action.</p>\n              </div>\n              \n              <div className=\"bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-2xl\">\n                <h4 className=\"text-xl font-bold text-gray-800 mb-3\">Ongoing Support & Maintenance</h4>\n                <p className=\"text-gray-600\">Your website is never &quot;finished.&quot; We provide continuous updates, security monitoring, and performance optimization.</p>\n              </div>\n            </div>\n          </div>\n          <div>\n            <Image\n              src=\"https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop\"\n              alt=\"Web Development Process\"\n              width={600}\n              height={400}\n              className=\"w-full rounded-3xl shadow-xl\"\n            />\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default WebDevAboutSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,qBAA+B;IACnC,qBACE,qKAAC;QAAQ,WAAU;kBACjB,cAAA,qKAAC;YAAI,WAAU;;8BAEb,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;sCACC,cAAA,qKAAC,sHAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAGd,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,qKAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAG1D,qKAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAI1D,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;oDAAoC,MAAM;;;;;;8DACjE,qKAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;oDAAoC,MAAM;;;;;;8DACjE,qKAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;oDAAoC,MAAM;;;;;;8DACjE,qKAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;oDAAoC,MAAM;;;;;;8DACjE,qKAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;oDAAoC,MAAM;;;;;;8DACjE,qKAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,qKAAC;4CAAI,WAAU;;8DACb,qKAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;oDAAoC,MAAM;;;;;;8DACjE,qKAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOxC,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,qKAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAI1D,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,qKAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAG/B,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,qKAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAG/B,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,qKAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;sCAInC,qKAAC;sCACC,cAAA,qKAAC,sHAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;uCAEe", "debugId": null}}, {"offset": {"line": 4173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/web-dev/main-web-dev/WebDevServicesSection.tsx"], "sourcesContent": ["import React from 'react';\nimport <PERSON> from 'next/link';\nimport { Globe, Smartphone, Zap, ShoppingCart, Code, Database, CheckCircle } from 'lucide-react';\n\nconst WebDevServicesSection: React.FC = () => {\n  const webDevServices = [\n    {\n      icon: Globe,\n      title: 'Custom Website Development',\n      slug: 'custom-website-development',\n      description: 'Fully custom websites built from scratch to match your brand and business requirements, optimized for performance and conversions.',\n      features: ['Custom Design & Branding', 'Responsive Mobile Design', 'Content Management System', 'SEO-Optimized Structure', 'Performance Optimization', 'Security Implementation'],\n      benefits: 'Unique brand presence, better user experience, higher conversion rates, and complete control over functionality.',\n      results: '+250% in Conversions'\n    },\n    {\n      icon: ShoppingCart,\n      title: 'E-commerce Development',\n      slug: 'ecommerce-development',\n      description: 'Professional e-commerce solutions that drive sales with secure payment processing, inventory management, and conversion optimization.',\n      features: ['Shopping Cart Integration', 'Payment Gateway Setup', 'Inventory Management', 'Order Processing System', 'Customer Account Portal', 'Mobile Commerce Optimization'],\n      benefits: 'Increased online sales, streamlined operations, better customer experience, and scalable growth platform.',\n      results: '+180% in Online Sales'\n    },\n    {\n      icon: Smartphone,\n      title: 'Mobile App Development',\n      slug: 'mobile-app-development',\n      description: 'Native and cross-platform mobile applications that engage users and extend your business reach across all devices.',\n      features: ['iOS & Android Development', 'Cross-Platform Solutions', 'User Interface Design', 'App Store Optimization', 'Push Notifications', 'Analytics Integration'],\n      benefits: 'Expanded market reach, improved customer engagement, additional revenue streams, and enhanced brand loyalty.',\n      results: '+300% User Engagement'\n    },\n    {\n      icon: Zap,\n      title: 'Website Speed Optimization',\n      slug: 'website-speed-optimization',\n      description: 'Comprehensive performance optimization to ensure your website loads in under 3 seconds and provides exceptional user experience.',\n      features: ['Core Web Vitals Optimization', 'Image Compression & Optimization', 'Code Minification', 'CDN Implementation', 'Database Optimization', 'Caching Solutions'],\n      benefits: 'Better search rankings, reduced bounce rates, improved user satisfaction, and higher conversion rates.',\n      results: '+85% Faster Load Times'\n    },\n    {\n      icon: Code,\n      title: 'Web Application Development',\n      slug: 'web-application-development',\n      description: 'Complex web applications and software solutions that streamline business processes and improve operational efficiency.',\n      features: ['Custom Functionality Development', 'Database Design & Integration', 'User Authentication Systems', 'API Development & Integration', 'Cloud Deployment', 'Scalable Architecture'],\n      benefits: 'Automated workflows, improved efficiency, better data management, and competitive advantage through technology.',\n      results: '+200% Operational Efficiency'\n    },\n    {\n      icon: Database,\n      title: 'Website Maintenance & Support',\n      slug: 'website-maintenance-support',\n      description: 'Ongoing website maintenance, security updates, and technical support to keep your website running smoothly and securely.',\n      features: ['Regular Security Updates', 'Performance Monitoring', 'Backup & Recovery', 'Content Updates', 'Bug Fixes & Troubleshooting', '24/7 Technical Support'],\n      benefits: 'Peace of mind, consistent performance, enhanced security, and focus on your core business activities.',\n      results: '99.9% Uptime Guarantee'\n    }\n  ];\n\n  return (\n    <section className=\"py-24 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        <div className=\"text-center mb-20\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-6\">\n            Comprehensive Web Development Services\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed\">\n            From custom websites to complex web applications, we provide end-to-end development solutions that drive business growth and deliver exceptional user experiences.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {webDevServices.map((service, index) => {\n            const Icon = service.icon;\n            return (\n              <Link href={`/${service.slug}`} key={index}>\n                <div className=\"bg-white border border-gray-200 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer group h-full flex flex-col\">\n                  <div className=\"flex items-start justify-between mb-6\">\n                    <div className=\"bg-blue-600 p-4 rounded-2xl group-hover:bg-blue-700 transition-colors\">\n                      <Icon size={32} className=\"text-white\" />\n                    </div>\n                    <div className=\"bg-green-50 px-4 py-2 rounded-full\">\n                      <span className=\"text-green-600 font-bold text-sm\">{service.results}</span>\n                    </div>\n                  </div>\n\n                  <h3 className=\"text-2xl font-bold text-gray-800 mb-4 group-hover:text-blue-600 transition-colors\">\n                    {service.title}\n                  </h3>\n\n                  <p className=\"text-gray-600 mb-6 leading-relaxed flex-1\">\n                    {service.description}\n                  </p>\n\n                  <div className=\"mb-6\">\n                    <h4 className=\"font-semibold text-gray-800 mb-3\">Key Features:</h4>\n                    <ul className=\"space-y-2\">\n                      {service.features.slice(0, 4).map((feature, featureIndex) => (\n                        <li key={featureIndex} className=\"flex items-center text-gray-700 text-sm\">\n                          <CheckCircle size={16} className=\"text-green-500 mr-2 flex-shrink-0\" />\n                          {feature}\n                        </li>\n                      ))}\n                      {service.features.length > 4 && (\n                        <li className=\"text-blue-600 text-sm font-medium\">\n                          +{service.features.length - 4} more features\n                        </li>\n                      )}\n                    </ul>\n                  </div>\n\n                  <div className=\"bg-blue-50 p-4 rounded-2xl mb-6\">\n                    <div className=\"text-gray-600 text-sm mb-2\">Expected Results:</div>\n                    <div className=\"text-gray-800 font-medium\">{service.benefits}</div>\n                  </div>\n\n                  <div className=\"bg-blue-600 text-white text-center py-3 rounded-xl font-semibold group-hover:bg-blue-700 transition-colors\">\n                    Learn More About {service.title}\n                  </div>\n                </div>\n              </Link>\n            );\n          })}\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-12 text-white\">\n            <h3 className=\"text-3xl md:text-4xl font-bold mb-6\">\n              Ready to Transform Your Online Presence?\n            </h3>\n            <p className=\"text-xl text-blue-100 mb-8 max-w-3xl mx-auto\">\n              Let&apos;s discuss your web development needs and create a solution that drives real results for your business.\n            </p>\n            <button className=\"bg-white text-blue-600 font-bold px-10 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-xl\">\n              Get Your Free Web Development Consultation\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default WebDevServicesSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,wBAAkC;IACtC,MAAM,iBAAiB;QACrB;YACE,MAAM,6LAAA,CAAA,QAAK;YACX,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;gBAAC;gBAA4B;gBAA4B;gBAA6B;gBAA2B;gBAA4B;aAA0B;YACjL,UAAU;YACV,SAAS;QACX;QACA;YACE,MAAM,+MAAA,CAAA,eAAY;YAClB,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;gBAAC;gBAA6B;gBAAyB;gBAAwB;gBAA2B;gBAA2B;aAA+B;YAC9K,UAAU;YACV,SAAS;QACX;QACA;YACE,MAAM,uMAAA,CAAA,aAAU;YAChB,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;gBAAC;gBAA6B;gBAA4B;gBAAyB;gBAA0B;gBAAsB;aAAwB;YACrK,UAAU;YACV,SAAS;QACX;QACA;YACE,MAAM,yLAAA,CAAA,MAAG;YACT,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;gBAAC;gBAAgC;gBAAoC;gBAAqB;gBAAsB;gBAAyB;aAAoB;YACvK,UAAU;YACV,SAAS;QACX;QACA;YACE,MAAM,2LAAA,CAAA,OAAI;YACV,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;gBAAC;gBAAoC;gBAAiC;gBAA+B;gBAAiC;gBAAoB;aAAwB;YAC5L,UAAU;YACV,SAAS;QACX;QACA;YACE,MAAM,mMAAA,CAAA,WAAQ;YACd,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;gBAAC;gBAA4B;gBAA0B;gBAAqB;gBAAmB;gBAA+B;aAAyB;YACjK,UAAU;YACV,SAAS;QACX;KACD;IAED,qBACE,qKAAC;QAAQ,WAAU;kBACjB,cAAA,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,qKAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAKzE,qKAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,SAAS;wBAC5B,MAAM,OAAO,QAAQ,IAAI;wBACzB,qBACE,qKAAC,qHAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,IAAI,EAAE;sCAC5B,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAK,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAE5B,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAK,WAAU;8DAAoC,QAAQ,OAAO;;;;;;;;;;;;;;;;;kDAIvE,qKAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAGhB,qKAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAGtB,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,qKAAC;gDAAG,WAAU;;oDACX,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,6BAC1C,qKAAC;4DAAsB,WAAU;;8EAC/B,qKAAC,oNAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAChC;;2DAFM;;;;;oDAKV,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,qKAAC;wDAAG,WAAU;;4DAAoC;4DAC9C,QAAQ,QAAQ,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;;;;;;;kDAMtC,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;0DAA6B;;;;;;0DAC5C,qKAAC;gDAAI,WAAU;0DAA6B,QAAQ,QAAQ;;;;;;;;;;;;kDAG9D,qKAAC;wCAAI,WAAU;;4CAA6G;4CACxG,QAAQ,KAAK;;;;;;;;;;;;;2BA1CA;;;;;oBA+CzC;;;;;;8BAIF,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,qKAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,qKAAC;gCAAO,WAAU;0CAA6I;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3K;uCAEe", "debugId": null}}, {"offset": {"line": 4549, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/web-dev/main-web-dev/WebDevIndustrySection.tsx"], "sourcesContent": ["import React from 'react';\nimport { Building, ShoppingBag, Heart, GraduationCap, Car, Home, Briefcase, Users } from 'lucide-react';\n\nconst WebDevIndustrySection: React.FC = () => {\n  const industries = [\n    {\n      icon: ShoppingBag,\n      name: 'E-commerce & Retail',\n      description: 'Custom online stores with advanced shopping features, payment integration, and inventory management.',\n      specialties: ['Product Catalogs', 'Shopping Carts', 'Payment Gateways', 'Inventory Systems']\n    },\n    {\n      icon: Heart,\n      name: 'Healthcare & Medical',\n      description: 'HIPAA-compliant websites and patient portals with appointment scheduling and secure communications.',\n      specialties: ['Patient Portals', 'Appointment Systems', 'HIPAA Compliance', 'Telemedicine Integration']\n    },\n    {\n      icon: Building,\n      name: 'Professional Services',\n      description: 'Professional websites for law firms, accounting, consulting, and other service-based businesses.',\n      specialties: ['Client Portals', 'Case Management', 'Appointment Booking', 'Document Sharing']\n    },\n    {\n      icon: GraduationCap,\n      name: 'Education & Training',\n      description: 'Learning management systems, course platforms, and educational websites with interactive features.',\n      specialties: ['LMS Development', 'Course Management', 'Student Portals', 'Online Testing']\n    },\n    {\n      icon: Home,\n      name: 'Real Estate',\n      description: 'Property listing websites with advanced search, virtual tours, and lead generation systems.',\n      specialties: ['Property Listings', 'Search Filters', 'Virtual Tours', 'Lead Capture']\n    },\n    {\n      icon: Car,\n      name: 'Automotive',\n      description: 'Dealership websites with inventory management, financing calculators, and service scheduling.',\n      specialties: ['Inventory Management', 'Financing Tools', 'Service Booking', 'Vehicle Comparisons']\n    },\n    {\n      icon: Briefcase,\n      name: 'Financial Services',\n      description: 'Secure financial websites with client portals, calculators, and compliance-ready features.',\n      specialties: ['Client Portals', 'Financial Calculators', 'Compliance Features', 'Secure Transactions']\n    },\n    {\n      icon: Users,\n      name: 'Non-Profit Organizations',\n      description: 'Mission-driven websites with donation systems, volunteer management, and event coordination.',\n      specialties: ['Donation Systems', 'Volunteer Portals', 'Event Management', 'Impact Tracking']\n    }\n  ];\n\n  return (\n    <section className=\"py-24 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        <div className=\"text-center mb-20\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-8\">\n            Industry-Specific Web Development Solutions\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-4xl mx-auto\">\n            We understand that every industry has unique requirements. Our web development solutions are tailored to meet the specific needs and regulations of your business sector.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {industries.map((industry, index) => {\n            const IconComponent = industry.icon;\n            return (\n              <div key={index} className=\"bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2\">\n                <div className=\"bg-gradient-to-br from-blue-100 to-blue-200 w-16 h-16 rounded-2xl flex items-center justify-center mb-6\">\n                  <IconComponent className=\"text-blue-600\" size={32} />\n                </div>\n                \n                <h3 className=\"text-xl font-bold text-gray-800 mb-4\">{industry.name}</h3>\n                <p className=\"text-gray-600 mb-6 leading-relaxed\">{industry.description}</p>\n                \n                <div>\n                  <h4 className=\"font-semibold text-gray-800 mb-3\">Specialties:</h4>\n                  <div className=\"space-y-2\">\n                    {industry.specialties.map((specialty, idx) => (\n                      <div key={idx} className=\"text-sm text-gray-600 bg-white px-3 py-1 rounded-full\">\n                        {specialty}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"mt-20 bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-12 text-white\">\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              Proven Results Across All Industries\n            </h3>\n            <p className=\"text-xl text-blue-100 max-w-3xl mx-auto\">\n              Our industry-specific approach delivers measurable results for businesses of all sizes and sectors.\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 text-center\">\n            <div>\n              <div className=\"text-4xl md:text-5xl font-bold text-blue-200 mb-2\">200+</div>\n              <div className=\"text-blue-100\">Happy Clients</div>\n            </div>\n            <div>\n              <div className=\"text-4xl md:text-5xl font-bold text-blue-200 mb-2\">300%</div>\n              <div className=\"text-blue-100\">Average ROI</div>\n            </div>\n            <div>\n              <div className=\"text-4xl md:text-5xl font-bold text-blue-200 mb-2\">5+</div>\n              <div className=\"text-blue-100\">Years Experience</div>\n            </div>\n            <div>\n              <div className=\"text-4xl md:text-5xl font-bold text-blue-200 mb-2\">99%</div>\n              <div className=\"text-blue-100\">Google Searches</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default WebDevIndustrySection;\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAEA,MAAM,wBAAkC;IACtC,MAAM,aAAa;QACjB;YACE,MAAM,6MAAA,CAAA,cAAW;YACjB,MAAM;YACN,aAAa;YACb,aAAa;gBAAC;gBAAoB;gBAAkB;gBAAoB;aAAoB;QAC9F;QACA;YACE,MAAM,6LAAA,CAAA,QAAK;YACX,MAAM;YACN,aAAa;YACb,aAAa;gBAAC;gBAAmB;gBAAuB;gBAAoB;aAA2B;QACzG;QACA;YACE,MAAM,mMAAA,CAAA,WAAQ;YACd,MAAM;YACN,aAAa;YACb,aAAa;gBAAC;gBAAkB;gBAAmB;gBAAuB;aAAmB;QAC/F;QACA;YACE,MAAM,iNAAA,CAAA,gBAAa;YACnB,MAAM;YACN,aAAa;YACb,aAAa;gBAAC;gBAAmB;gBAAqB;gBAAmB;aAAiB;QAC5F;QACA;YACE,MAAM,4LAAA,CAAA,OAAI;YACV,MAAM;YACN,aAAa;YACb,aAAa;gBAAC;gBAAqB;gBAAkB;gBAAiB;aAAe;QACvF;QACA;YACE,MAAM,yLAAA,CAAA,MAAG;YACT,MAAM;YACN,aAAa;YACb,aAAa;gBAAC;gBAAwB;gBAAmB;gBAAmB;aAAsB;QACpG;QACA;YACE,MAAM,qMAAA,CAAA,YAAS;YACf,MAAM;YACN,aAAa;YACb,aAAa;gBAAC;gBAAkB;gBAAyB;gBAAuB;aAAsB;QACxG;QACA;YACE,MAAM,6LAAA,CAAA,QAAK;YACX,MAAM;YACN,aAAa;YACb,aAAa;gBAAC;gBAAoB;gBAAqB;gBAAoB;aAAkB;QAC/F;KACD;IAED,qBACE,qKAAC;QAAQ,WAAU;kBACjB,cAAA,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,qKAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,qKAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,UAAU;wBACzB,MAAM,gBAAgB,SAAS,IAAI;wBACnC,qBACE,qKAAC;4BAAgB,WAAU;;8CACzB,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAc,WAAU;wCAAgB,MAAM;;;;;;;;;;;8CAGjD,qKAAC;oCAAG,WAAU;8CAAwC,SAAS,IAAI;;;;;;8CACnE,qKAAC;oCAAE,WAAU;8CAAsC,SAAS,WAAW;;;;;;8CAEvE,qKAAC;;sDACC,qKAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,qKAAC;4CAAI,WAAU;sDACZ,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,WAAW,oBACpC,qKAAC;oDAAc,WAAU;8DACtB;mDADO;;;;;;;;;;;;;;;;;2BAZR;;;;;oBAoBd;;;;;;8BAIF,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,qKAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;;sDACC,qKAAC;4CAAI,WAAU;sDAAoD;;;;;;sDACnE,qKAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,qKAAC;;sDACC,qKAAC;4CAAI,WAAU;sDAAoD;;;;;;sDACnE,qKAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,qKAAC;;sDACC,qKAAC;4CAAI,WAAU;sDAAoD;;;;;;sDACnE,qKAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,qKAAC;;sDACC,qKAAC;4CAAI,WAAU;sDAAoD;;;;;;sDACnE,qKAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;uCAEe", "debugId": null}}, {"offset": {"line": 4923, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/web-dev/main-web-dev/WebDevProcessSection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst WebDevProcessSection: React.FC = () => {\n  return (\n    <section className=\"py-24 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        <div className=\"text-center mb-20\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-8\">\n            Our Proven Web Development Process\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-4xl mx-auto\">\n            VESA Solutions follows a systematic, client-focused approach that ensures your website is delivered on time, on budget, and exceeds your expectations.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12 mb-16\">\n          <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-3xl p-8\">\n            <div className=\"bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6\">\n              1\n            </div>\n            <h4 className=\"text-xl font-bold text-gray-800 mb-4\">Discovery & Planning</h4>\n            <p className=\"text-gray-600 mb-4\">Comprehensive analysis of your business goals, target audience, and technical requirements to create a detailed project roadmap.</p>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• Business goals assessment</li>\n              <li>• Target audience research</li>\n              <li>• Technical requirements analysis</li>\n              <li>• Project timeline & milestones</li>\n            </ul>\n          </div>\n\n          <div className=\"bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-8\">\n            <div className=\"bg-green-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6\">\n              2\n            </div>\n            <h4 className=\"text-xl font-bold text-gray-800 mb-4\">Design & Prototyping</h4>\n            <p className=\"text-gray-600 mb-4\">Creating wireframes, mockups, and interactive prototypes to visualize the user experience before development begins.</p>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• Wireframe creation</li>\n              <li>• Visual design mockups</li>\n              <li>• Interactive prototypes</li>\n              <li>• Client feedback & revisions</li>\n            </ul>\n          </div>\n\n          <div className=\"bg-gradient-to-br from-orange-50 to-orange-100 rounded-3xl p-8\">\n            <div className=\"bg-orange-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6\">\n              3\n            </div>\n            <h4 className=\"text-xl font-bold text-gray-800 mb-4\">Development & Testing</h4>\n            <p className=\"text-gray-600 mb-4\">Building your website using modern technologies with rigorous testing to ensure optimal performance and functionality.</p>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• Frontend & backend development</li>\n              <li>• Responsive design implementation</li>\n              <li>• Performance optimization</li>\n              <li>• Cross-browser testing</li>\n            </ul>\n          </div>\n\n          <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 rounded-3xl p-8\">\n            <div className=\"bg-purple-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6\">\n              4\n            </div>\n            <h4 className=\"text-xl font-bold text-gray-800 mb-4\">Launch & Optimization</h4>\n            <p className=\"text-gray-600 mb-4\">Deploying your website with ongoing monitoring, optimization, and support to ensure continued success.</p>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• Website deployment & launch</li>\n              <li>• Performance monitoring</li>\n              <li>• SEO optimization</li>\n              <li>• Ongoing support & maintenance</li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Timeline Section */}\n        <div className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-3xl p-12\">\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-3xl md:text-4xl font-bold text-gray-800 mb-4\">\n              Typical Project Timeline\n            </h3>\n            <p className=\"text-xl text-gray-600\">\n              Most web development projects follow this timeline, though complex projects may require additional time.\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 text-center\">\n            <div className=\"bg-white rounded-2xl p-6 shadow-lg\">\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">1-2</div>\n              <div className=\"text-gray-800 font-semibold mb-2\">Weeks</div>\n              <div className=\"text-sm text-gray-600\">Discovery & Planning Phase</div>\n            </div>\n            <div className=\"bg-white rounded-2xl p-6 shadow-lg\">\n              <div className=\"text-3xl font-bold text-green-600 mb-2\">2-3</div>\n              <div className=\"text-gray-800 font-semibold mb-2\">Weeks</div>\n              <div className=\"text-sm text-gray-600\">Design & Prototyping Phase</div>\n            </div>\n            <div className=\"bg-white rounded-2xl p-6 shadow-lg\">\n              <div className=\"text-3xl font-bold text-orange-600 mb-2\">4-8</div>\n              <div className=\"text-gray-800 font-semibold mb-2\">Weeks</div>\n              <div className=\"text-sm text-gray-600\">Development & Testing Phase</div>\n            </div>\n            <div className=\"bg-white rounded-2xl p-6 shadow-lg\">\n              <div className=\"text-3xl font-bold text-purple-600 mb-2\">1</div>\n              <div className=\"text-gray-800 font-semibold mb-2\">Week</div>\n              <div className=\"text-sm text-gray-600\">Launch & Optimization Phase</div>\n            </div>\n          </div>\n          \n          <div className=\"text-center mt-8\">\n            <p className=\"text-gray-600 mb-4\">\n              <strong>Total Timeline:</strong> 8-14 weeks for most projects\n            </p>\n            <p className=\"text-sm text-gray-500\">\n              Complex e-commerce or web applications may require 12-20 weeks depending on features and integrations.\n            </p>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default WebDevProcessSection;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,uBAAiC;IACrC,qBACE,qKAAC;QAAQ,WAAU;kBACjB,cAAA,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,qKAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;8CAAwG;;;;;;8CAGvH,qKAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,qKAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,qKAAC;oCAAG,WAAU;;sDACZ,qKAAC;sDAAG;;;;;;sDACJ,qKAAC;sDAAG;;;;;;sDACJ,qKAAC;sDAAG;;;;;;sDACJ,qKAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;8CAAyG;;;;;;8CAGxH,qKAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,qKAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,qKAAC;oCAAG,WAAU;;sDACZ,qKAAC;sDAAG;;;;;;sDACJ,qKAAC;sDAAG;;;;;;sDACJ,qKAAC;sDAAG;;;;;;sDACJ,qKAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;8CAA0G;;;;;;8CAGzH,qKAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,qKAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,qKAAC;oCAAG,WAAU;;sDACZ,qKAAC;sDAAG;;;;;;sDACJ,qKAAC;sDAAG;;;;;;sDACJ,qKAAC;sDAAG;;;;;;sDACJ,qKAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;8CAA0G;;;;;;8CAGzH,qKAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,qKAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,qKAAC;oCAAG,WAAU;;sDACZ,qKAAC;sDAAG;;;;;;sDACJ,qKAAC;sDAAG;;;;;;sDACJ,qKAAC;sDAAG;;;;;;sDACJ,qKAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;8BAMV,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,qKAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;sDAAwC;;;;;;sDACvD,qKAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,qKAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;sDAAyC;;;;;;sDACxD,qKAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,qKAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;sDAA0C;;;;;;sDACzD,qKAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,qKAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;sDAA0C;;;;;;sDACzD,qKAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,qKAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAI3C,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAE,WAAU;;sDACX,qKAAC;sDAAO;;;;;;wCAAwB;;;;;;;8CAElC,qKAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;uCAEe", "debugId": null}}, {"offset": {"line": 5476, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/web-dev/main-web-dev/WebDevCTASection.tsx"], "sourcesContent": ["import React from 'react';\nimport { Phone, Mail } from 'lucide-react';\n\nconst WebDevCTASection: React.FC = () => {\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-blue-50 to-blue-100\">\n      <div className=\"max-w-4xl mx-auto px-6 text-center\">\n        <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-6\">\n          Ready to Build Your Dream Website?\n        </h2>\n        <p className=\"text-xl text-gray-600 mb-12 leading-relaxed\">\n          Let&apos;s discuss your vision and create a website that not only looks amazing but drives real business results. Our team is ready to bring your ideas to life.\n        </p>\n\n        <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\">\n          <a\n            href=\"tel:+***********\"\n            className=\"group bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-xl shadow-blue-500/25 inline-flex items-center justify-center text-lg\"\n          >\n            <Phone className=\"mr-3\" size={24} />\n            Get Started Today\n          </a>\n\n          <a\n            href=\"mailto:<EMAIL>\"\n            className=\"group bg-white hover:bg-gray-50 text-blue-600 font-bold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-xl border-2 border-blue-500 hover:border-blue-600 inline-flex items-center justify-center text-lg\"\n          >\n            <Mail className=\"mr-3\" size={24} />\n            Discuss Your Project\n          </a>\n        </div>\n        \n        <p className=\"text-gray-500 mt-8 text-sm\">\n          Free consultation • Custom solutions • Professional results guaranteed\n        </p>\n      </div>\n    </section>\n  );\n};\n\nexport default WebDevCTASection;\n"], "names": [], "mappings": ";;;;AACA;AAAA;;;AAEA,MAAM,mBAA6B;IACjC,qBACE,qKAAC;QAAQ,WAAU;kBACjB,cAAA,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAG,WAAU;8BAAoD;;;;;;8BAGlE,qKAAC;oBAAE,WAAU;8BAA8C;;;;;;8BAI3D,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BACC,MAAK;4BACL,WAAU;;8CAEV,qKAAC,6LAAA,CAAA,QAAK;oCAAC,WAAU;oCAAO,MAAM;;;;;;gCAAM;;;;;;;sCAItC,qKAAC;4BACC,MAAK;4BACL,WAAU;;8CAEV,qKAAC,2LAAA,CAAA,OAAI;oCAAC,WAAU;oCAAO,MAAM;;;;;;gCAAM;;;;;;;;;;;;;8BAKvC,qKAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAMlD;uCAEe", "debugId": null}}, {"offset": {"line": 5580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/web-dev/main-web-dev/WebDevTechnologySection.tsx"], "sourcesContent": ["import React from 'react';\nimport { Code, Database, Cloud, Shield, Smartphone, Monitor } from 'lucide-react';\n\nconst WebDevTechnologySection: React.FC = () => {\n  const technologies = [\n    {\n      name: 'React & Next.js',\n      description: 'Modern JavaScript frameworks for fast, interactive user interfaces',\n      purpose: 'Frontend development with server-side rendering and optimal performance'\n    },\n    {\n      name: 'Node.js & Express',\n      description: 'Scalable backend development with JavaScript runtime environment',\n      purpose: 'API development, server-side logic, and database integration'\n    },\n    {\n      name: 'WordPress & Headless CMS',\n      description: 'Content management systems for easy content updates and management',\n      purpose: 'User-friendly content management with flexible frontend options'\n    },\n    {\n      name: 'MongoDB & PostgreSQL',\n      description: 'Modern database solutions for reliable data storage and retrieval',\n      purpose: 'Scalable data management and complex query optimization'\n    },\n    {\n      name: 'AWS & Google Cloud',\n      description: 'Enterprise-grade cloud hosting and infrastructure services',\n      purpose: 'Reliable hosting, automatic scaling, and global content delivery'\n    },\n    {\n      name: 'Shopify & WooCommerce',\n      description: 'E-commerce platforms for online stores and payment processing',\n      purpose: 'Complete e-commerce solutions with inventory and order management'\n    }\n  ];\n\n  return (\n    <section className=\"py-24 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        <div className=\"text-center mb-20\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-8\">\n            Cutting-Edge Technologies We Use\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-4xl mx-auto\">\n            We leverage the latest technologies and frameworks to build websites that are fast, secure, scalable, and future-proof.\n          </p>\n        </div>\n\n        {/* Technology Stack */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\">\n          {technologies.map((tech, index) => (\n            <div key={index} className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300\">\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4\">{tech.name}</h4>\n              <p className=\"text-gray-600 mb-4\">{tech.description}</p>\n              <p className=\"text-sm text-blue-600 font-medium\">{tech.purpose}</p>\n            </div>\n          ))}\n        </div>\n\n        {/* Modern Web Standards */}\n        <div className=\"bg-white rounded-3xl p-12 shadow-xl mb-20\">\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-3xl md:text-4xl font-bold text-gray-800 mb-4\">\n              Built for the Modern Web\n            </h3>\n            <p className=\"text-xl text-gray-600\">\n              Every website we develop follows modern web standards and best practices for optimal performance and user experience.\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl\">\n                <Smartphone className=\"text-blue-600 mb-4 mx-auto\" size={48} />\n                <h4 className=\"text-xl font-bold text-gray-800 mb-4\">Mobile-First Design</h4>\n                <p className=\"text-gray-600\">Responsive design that works perfectly on all devices, from smartphones to desktops.</p>\n              </div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl\">\n                <Monitor className=\"text-green-600 mb-4 mx-auto\" size={48} />\n                <h4 className=\"text-xl font-bold text-gray-800 mb-4\">Progressive Web Apps</h4>\n                <p className=\"text-gray-600\">App-like experiences that work offline and can be installed on any device.</p>\n              </div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl\">\n                <Shield className=\"text-blue-600 mb-4 mx-auto\" size={48} />\n                <h4 className=\"text-xl font-bold text-gray-800 mb-4\">Security First</h4>\n                <p className=\"text-gray-600\">SSL certificates, secure coding practices, and regular security updates included.</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Performance Features */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n          <div>\n            <h3 className=\"text-3xl md:text-4xl font-bold text-gray-800 mb-8\">\n              Performance That Drives Results\n            </h3>\n            <p className=\"text-xl text-gray-600 mb-8\">\n              We optimize every aspect of your website for maximum speed, search engine visibility, and user engagement.\n            </p>\n            \n            <div className=\"space-y-6\">\n              <div className=\"flex items-start\">\n                <div className=\"bg-blue-100 p-3 rounded-lg mr-4 flex-shrink-0\">\n                  <Code className=\"text-blue-600\" size={24} />\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-800 mb-2\">Clean, Optimized Code</h4>\n                  <p className=\"text-gray-600\">Hand-written, semantic code that loads fast and ranks well in search engines.</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-start\">\n                <div className=\"bg-blue-100 p-3 rounded-lg mr-4 flex-shrink-0\">\n                  <Database className=\"text-blue-600\" size={24} />\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-800 mb-2\">Optimized Database Queries</h4>\n                  <p className=\"text-gray-600\">Efficient data retrieval and caching strategies for lightning-fast page loads.</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-start\">\n                <div className=\"bg-green-100 p-3 rounded-lg mr-4 flex-shrink-0\">\n                  <Cloud className=\"text-green-600\" size={24} />\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-800 mb-2\">CDN & Cloud Optimization</h4>\n                  <p className=\"text-gray-600\">Global content delivery networks ensure fast loading times worldwide.</p>\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"bg-gradient-to-br from-blue-600 to-blue-800 rounded-3xl p-8 text-white\">\n            <h4 className=\"text-2xl font-bold mb-6\">Performance Guarantees</h4>\n            <div className=\"space-y-6\">\n              <div className=\"flex justify-between items-center\">\n                <span>Page Load Time</span>\n                <span className=\"font-bold text-blue-200\">&lt; 3 seconds</span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span>Uptime Guarantee</span>\n                <span className=\"font-bold text-blue-200\">99.9%</span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span>Mobile Performance Score</span>\n                <span className=\"font-bold text-blue-200\">90+</span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span>SEO Score</span>\n                <span className=\"font-bold text-blue-200\">95+</span>\n              </div>\n            </div>\n            <div className=\"mt-8 p-4 bg-blue-700 rounded-2xl\">\n              <p className=\"text-sm text-blue-100\">\n                <strong>Money-Back Guarantee:</strong> If we don&apos;t meet these performance standards, we&apos;ll refund your investment.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default WebDevTechnologySection;\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAEA,MAAM,0BAAoC;IACxC,MAAM,eAAe;QACnB;YACE,MAAM;YACN,aAAa;YACb,SAAS;QACX;QACA;YACE,MAAM;YACN,aAAa;YACb,SAAS;QACX;QACA;YACE,MAAM;YACN,aAAa;YACb,SAAS;QACX;QACA;YACE,MAAM;YACN,aAAa;YACb,SAAS;QACX;QACA;YACE,MAAM;YACN,aAAa;YACb,SAAS;QACX;QACA;YACE,MAAM;YACN,aAAa;YACb,SAAS;QACX;KACD;IAED,qBACE,qKAAC;QAAQ,WAAU;kBACjB,cAAA,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,qKAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,qKAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,qKAAC;4BAAgB,WAAU;;8CACzB,qKAAC;oCAAG,WAAU;8CAAwC,KAAK,IAAI;;;;;;8CAC/D,qKAAC;oCAAE,WAAU;8CAAsB,KAAK,WAAW;;;;;;8CACnD,qKAAC;oCAAE,WAAU;8CAAqC,KAAK,OAAO;;;;;;;2BAHtD;;;;;;;;;;8BASd,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,qKAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAI,WAAU;;0DACb,qKAAC,uMAAA,CAAA,aAAU;gDAAC,WAAU;gDAA6B,MAAM;;;;;;0DACzD,qKAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,qKAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAGjC,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAI,WAAU;;0DACb,qKAAC,iMAAA,CAAA,UAAO;gDAAC,WAAU;gDAA8B,MAAM;;;;;;0DACvD,qKAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,qKAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAGjC,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAI,WAAU;;0DACb,qKAAC,+LAAA,CAAA,SAAM;gDAAC,WAAU;gDAA6B,MAAM;;;;;;0DACrD,qKAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,qKAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOrC,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;;8CACC,qKAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,qKAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAI1C,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC,2LAAA,CAAA,OAAI;wDAAC,WAAU;wDAAgB,MAAM;;;;;;;;;;;8DAExC,qKAAC;;sEACC,qKAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,qKAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAIjC,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC,mMAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAgB,MAAM;;;;;;;;;;;8DAE5C,qKAAC;;sEACC,qKAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,qKAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAIjC,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC,6LAAA,CAAA,QAAK;wDAAC,WAAU;wDAAiB,MAAM;;;;;;;;;;;8DAE1C,qKAAC;;sEACC,qKAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,qKAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMrC,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;8DAAK;;;;;;8DACN,qKAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;sDAE5C,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;8DAAK;;;;;;8DACN,qKAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;sDAE5C,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;8DAAK;;;;;;8DACN,qKAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;sDAE5C,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;8DAAK;;;;;;8DACN,qKAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;8CAG9C,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAE,WAAU;;0DACX,qKAAC;0DAAO;;;;;;4CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;uCAEe", "debugId": null}}, {"offset": {"line": 6209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/web-dev/main-web-dev/WebDevFAQSection.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { ChevronDown } from 'lucide-react';\n\nconst WebDevFAQSection: React.FC = () => {\n  const [openFaq, setOpenFaq] = useState<number | null>(null);\n\n  const faqs = [\n    {\n      question: 'How long does it take to develop a custom website?',\n      answer: 'Most custom websites take 8-14 weeks to complete, depending on complexity and features. Simple business websites may take 6-8 weeks, while complex e-commerce or web applications can take 12-20 weeks. We provide a detailed timeline during the planning phase and keep you updated throughout the process.'\n    },\n    {\n      question: 'What is the difference between custom development and using templates?',\n      answer: 'Custom development means building your website from scratch to match your exact requirements, brand, and business goals. Templates are pre-made designs that limit customization. Custom development offers better performance, unique design, scalability, and SEO optimization, while templates are faster but less flexible.'\n    },\n    {\n      question: 'Do you provide ongoing maintenance and support after launch?',\n      answer: 'Yes, we offer comprehensive maintenance packages that include security updates, performance monitoring, content updates, backup services, and technical support. We believe in long-term partnerships and ensure your website continues to perform optimally after launch.'\n    },\n    {\n      question: 'Will my website be mobile-responsive and SEO-friendly?',\n      answer: 'Absolutely. Every website we develop is mobile-first, meaning it\\'s designed to work perfectly on all devices. We also implement SEO best practices including optimized code structure, fast loading times, proper meta tags, and search engine-friendly URLs to help your site rank well in Google.'\n    },\n    {\n      question: 'What technologies do you use for web development?',\n      answer: 'We use modern, proven technologies including React, Next.js, Node.js, WordPress, and various databases like MongoDB and PostgreSQL. We choose the best technology stack based on your specific needs, ensuring optimal performance, security, and scalability.'\n    },\n    {\n      question: 'Can you integrate third-party services and APIs?',\n      answer: 'Yes, we specialize in integrating various third-party services including payment gateways, CRM systems, email marketing platforms, social media APIs, analytics tools, and custom business applications. We ensure seamless integration that enhances your website\\'s functionality.'\n    },\n    {\n      question: 'How much does custom web development cost?',\n      answer: 'Web development costs vary based on complexity, features, and timeline. Simple business websites start around $5,000, while complex e-commerce or web applications can range from $15,000-$50,000+. We provide detailed quotes after understanding your specific requirements during our free consultation.'\n    },\n    {\n      question: 'Do you provide training on how to manage my website?',\n      answer: 'Yes, we provide comprehensive training on your content management system, including how to update content, add new pages, manage products (for e-commerce), and basic maintenance tasks. We also provide documentation and ongoing support to ensure you\\'re comfortable managing your website.'\n    }\n  ];\n\n  return (\n    <section className=\"py-24 bg-gray-50\">\n      <div className=\"max-w-4xl mx-auto px-6\">\n        <div className=\"text-center mb-20\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-8\">\n            Frequently Asked Questions About Web Development\n          </h2>\n          <p className=\"text-xl text-gray-600\">\n            Get answers to common questions about web development services, processes, and what to expect when working with VESA Solutions.\n          </p>\n        </div>\n\n        <div className=\"space-y-6\">\n          {faqs.map((faq, index) => (\n            <div key={index} className=\"bg-white rounded-2xl overflow-hidden shadow-lg\">\n              <button \n                onClick={() => setOpenFaq(openFaq === index ? null : index)}\n                className=\"w-full p-8 text-left flex justify-between items-center hover:bg-gray-50 transition-colors\"\n              >\n                <h3 className=\"text-xl font-semibold text-gray-800 pr-8\">\n                  {faq.question}\n                </h3>\n                <ChevronDown\n                  size={24}\n                  className={`text-blue-600 transform transition-transform ${openFaq === index ? 'rotate-180' : ''}`}\n                />\n              </button>\n              {openFaq === index && (\n                <div className=\"px-8 pb-8\">\n                  <div className=\"border-t border-gray-200 pt-6\">\n                    <p className=\"text-gray-600 leading-relaxed\">\n                      {faq.answer}\n                    </p>\n                  </div>\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n\n        {/* Contact CTA */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-12 text-white\">\n            <h3 className=\"text-3xl md:text-4xl font-bold mb-6\">\n              Still Have Questions?\n            </h3>\n            <p className=\"text-xl text-blue-100 mb-8 max-w-3xl mx-auto\">\n              Our web development experts are here to help. Get personalized answers to your questions and a custom quote for your project.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-white text-blue-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\">\n                Schedule Free Consultation\n              </button>\n              <button className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-all duration-300\">\n                Call (555) 123-4567\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default WebDevFAQSection;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,mBAA6B;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,MAAM,OAAO;QACX;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;KACD;IAED,qBACE,qKAAC;QAAQ,WAAU;kBACjB,cAAA,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,qKAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,qKAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,qKAAC;4BAAgB,WAAU;;8CACzB,qKAAC;oCACC,SAAS,IAAM,WAAW,YAAY,QAAQ,OAAO;oCACrD,WAAU;;sDAEV,qKAAC;4CAAG,WAAU;sDACX,IAAI,QAAQ;;;;;;sDAEf,qKAAC,6MAAA,CAAA,cAAW;4CACV,MAAM;4CACN,WAAW,CAAC,6CAA6C,EAAE,YAAY,QAAQ,eAAe,IAAI;;;;;;;;;;;;gCAGrG,YAAY,uBACX,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAE,WAAU;sDACV,IAAI,MAAM;;;;;;;;;;;;;;;;;2BAjBX;;;;;;;;;;8BA2Bd,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,qKAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAO,WAAU;kDAAkI;;;;;;kDAGpJ,qKAAC;wCAAO,WAAU;kDAAmI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnK;uCAEe", "debugId": null}}, {"offset": {"line": 6424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/pages/web-development.tsx"], "sourcesContent": ["import React from 'react';\nimport Head from 'next/head';\nimport Link from 'next/link';\nimport Header from '@/components/global/Header';\nimport Footer from '@/components/global/Footer';\n\n// Import web development section components\nimport WebDevHeroSection from '@/components/services/web-dev/main-web-dev/WebDevHeroSection';\nimport WebDevAboutSection from '@/components/services/web-dev/main-web-dev/WebDevAboutSection';\nimport WebDevServicesSection from '@/components/services/web-dev/main-web-dev/WebDevServicesSection';\nimport WebDevIndustrySection from '@/components/services/web-dev/main-web-dev/WebDevIndustrySection';\nimport WebDevProcessSection from '@/components/services/web-dev/main-web-dev/WebDevProcessSection';\nimport WebDevCTASection from '@/components/services/web-dev/main-web-dev/WebDevCTASection';\nimport WebDevTechnologySection from '@/components/services/web-dev/main-web-dev/WebDevTechnologySection';\n// import WebDevResultsSection from '@/components/services/web-dev/main-web-dev/WebDevResultsSection';\nimport WebDevFAQSection from '@/components/services/web-dev/main-web-dev/WebDevFAQSection';\n\nconst WebDevelopmentPage: React.FC = () => {\n  return (\n    <>\n      <Head>\n        <title>Professional Web Development Services That Drive Results | Vesa Solutions</title>\n        <meta name=\"description\" content=\"Custom web development solutions that convert visitors into customers. Modern, fast, and mobile-responsive websites. Free consultation included.\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n      </Head>\n      \n      <Header />\n      \n      {/* Breadcrumbs */}\n      <div className=\"bg-gray-50 py-4\">\n        <div className=\"max-w-7xl mx-auto px-6\">\n          <nav className=\"text-sm\" aria-label=\"Breadcrumb\">\n            <ol className=\"flex items-center space-x-2\">\n              <li>\n                <Link href=\"/\" className=\"text-gray-500 hover:text-blue-600 transition-colors\">\n                  Home\n                </Link>\n              </li>\n              <li className=\"text-gray-400\">/</li>\n              <li>\n                <Link href=\"/services\" className=\"text-gray-500 hover:text-blue-600 transition-colors\">\n                  Services\n                </Link>\n              </li>\n              <li className=\"text-gray-400\">/</li>\n              <li className=\"text-gray-900 font-medium\" aria-current=\"page\">\n                Website Development\n              </li>\n            </ol>\n          </nav>\n        </div>\n      </div>\n      \n      <main className=\"min-h-screen bg-white\">\n        {/* All Web Development Page Sections */}\n        <WebDevHeroSection />\n        <WebDevAboutSection />\n        <WebDevServicesSection />\n        <WebDevIndustrySection />\n        <WebDevProcessSection />\n        <WebDevCTASection />\n        <WebDevTechnologySection />\n        {/* <WebDevResultsSection /> */}\n        <WebDevFAQSection />\n      </main>\n      \n      <Footer />\n    </>\n  );\n};\n\nexport default WebDevelopmentPage;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAEA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sGAAsG;AACtG;;;;;;;;;;;;;;;;;;AAEA,MAAM,qBAA+B;IACnC,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,qKAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;;;;;;;0BAGhC,qKAAC,wHAAA,CAAA,UAAM;;;;;0BAGP,qKAAC;gBAAI,WAAU;0BACb,cAAA,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;wBAAU,cAAW;kCAClC,cAAA,qKAAC;4BAAG,WAAU;;8CACZ,qKAAC;8CACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAsD;;;;;;;;;;;8CAIjF,qKAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,qKAAC;8CACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAsD;;;;;;;;;;;8CAIzF,qKAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,qKAAC;oCAAG,WAAU;oCAA4B,gBAAa;8CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtE,qKAAC;gBAAK,WAAU;;kCAEd,qKAAC,yKAAA,CAAA,UAAiB;;;;;kCAClB,qKAAC,0KAAA,CAAA,UAAkB;;;;;kCACnB,qKAAC,6KAAA,CAAA,UAAqB;;;;;kCACtB,qKAAC,6KAAA,CAAA,UAAqB;;;;;kCACtB,qKAAC,4KAAA,CAAA,UAAoB;;;;;kCACrB,qKAAC,wKAAA,CAAA,UAAgB;;;;;kCACjB,qKAAC,+KAAA,CAAA,UAAuB;;;;;kCAExB,qKAAC,wKAAA,CAAA,UAAgB;;;;;;;;;;;0BAGnB,qKAAC,wHAAA,CAAA,UAAM;;;;;;;AAGb;uCAEe", "debugId": null}}]}