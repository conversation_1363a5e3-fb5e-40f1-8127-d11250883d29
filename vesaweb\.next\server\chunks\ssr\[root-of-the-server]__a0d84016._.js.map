{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { ChevronDown } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\n\r\ninterface HeaderProps {\r\n  isVisible?: boolean;\r\n}\r\n\r\nconst Header: React.FC<HeaderProps> = ({ isVisible = true }) => {\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);\r\n  const [seoDropdownOpen, setSeoDropdownOpen] = useState<boolean>(false);\r\n  const [webDevDropdownOpen, setWebDevDropdownOpen] = useState<boolean>(false);\r\n  const [digitalMarketingDropdownOpen, setDigitalMarketingDropdownOpen] = useState<boolean>(false);\r\n  const [aboutDropdownOpen, setAboutDropdownOpen] = useState<boolean>(false);\r\n\r\n  const [mounted, setMounted] = useState<boolean>(false);\r\n\r\n  // Ensure component is hydrated before rendering state-dependent UI\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Handle body scroll when mobile menu is open\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) {\r\n      document.body.style.overflow = 'hidden';\r\n    } else {\r\n      document.body.style.overflow = 'unset';\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [mobileMenuOpen]);\r\n\r\n  // Close mobile menu on escape key\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (e.key === 'Escape' && mobileMenuOpen) {\r\n        setMobileMenuOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleEscape);\r\n    return () => document.removeEventListener('keydown', handleEscape);\r\n  }, [mobileMenuOpen]);\r\n\r\n  // SEO Sub-services\r\n  const seoSubServices = [\r\n    { name: 'On-Page SEO Optimization', href: '/on-page-seo', description: 'Optimize content and structure' },\r\n    { name: 'Off-Page SEO & Link Building', href: '/off-page-seo', description: 'Build authority and backlinks' },\r\n    { name: 'Technical SEO Services', href: '/technical-seo', description: 'Optimize technical performance' },\r\n    { name: 'Local SEO Marketing', href: '/local-seo', description: 'Dominate local search results' },\r\n    { name: 'Content Writing', href: '/content-writing', description: 'SEO-optimized content creation' },\r\n    { name: 'SEO Analytics', href: '/seo-analytics', description: 'Comprehensive SEO analysis' }\r\n  ];\r\n\r\n  // Web Development Sub-services\r\n  const webDevSubServices = [\r\n    { name: 'Custom Website Development', href: '/custom-website-development', description: 'Unique, tailored websites' },\r\n    { name: 'E-commerce Development', href: '/ecommerce-development', description: 'Online store solutions' },\r\n    { name: 'Mobile App Development', href: '/mobile-app-development', description: 'iOS & Android apps' },\r\n    { name: 'Website Speed Optimization', href: '/website-speed-optimization', description: 'Lightning-fast websites' },\r\n    { name: 'Web Application Development', href: '/web-application-development', description: 'Custom web applications' },\r\n    { name: 'Website Maintenance & Support', href: '/website-maintenance-support', description: '24/7 website care' }\r\n  ];\r\n\r\n  // Digital Marketing Sub-services\r\n  const digitalMarketingSubServices = [\r\n    { name: 'PPC Advertising', href: '/ppc', description: 'Targeted pay-per-click campaigns' },\r\n    { name: 'Email Marketing', href: '/email-marketing', description: 'Automated campaigns that convert' },\r\n    { name: 'Social Media Marketing', href: '/social-media', description: 'Engage your audience effectively' },\r\n    { name: 'Branding Services', href: '/branding-services', description: 'Build powerful brand identity' },\r\n    { name: 'Conversion Optimization', href: '/conversion-optimization', description: 'Turn visitors into customers' },\r\n    { name: 'Reputation Management', href: '/reputation-management', description: 'Protect and enhance your brand' }\r\n  ];\r\n\r\n  // About Sub-services\r\n  const aboutSubServices = [\r\n    { name: 'Case Studies', href: '/case-studies', description: 'View our successful projects' },\r\n    { name: 'Blog', href: '/blog', description: 'Latest insights and industry news' }\r\n  ];\r\n\r\n  const toggleMobileMenu = () => {\r\n    setMobileMenuOpen(!mobileMenuOpen);\r\n  };\r\n\r\n  const closeMobileMenu = () => {\r\n    setMobileMenuOpen(false);\r\n    // Reset all dropdown states when closing mobile menu\r\n    setSeoDropdownOpen(false);\r\n    setWebDevDropdownOpen(false);\r\n    setDigitalMarketingDropdownOpen(false);\r\n    setAboutDropdownOpen(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <header className={`w-full bg-white/95 backdrop-blur-md border-b border-gray-100 z-50 transition-all duration-500 ease-in-out ${\r\n        isVisible \r\n          ? 'sticky top-0 translate-y-0 opacity-100' \r\n          : 'fixed top-0 -translate-y-full opacity-0 pointer-events-none'\r\n      }`}>\r\n        <div className=\"max-w-[1440px] mx-auto px-4 sm:px-6 py-3 sm:py-4\">\r\n          <div className=\"flex justify-between items-center\">\r\n            {/* Logo */}\r\n            <div className=\"flex items-center group cursor-pointer\">\r\n              <div className=\"relative\">\r\n                <Link href=\"/\">\r\n                  <Image \r\n                    src=\"/VesaLogo.svg\" \r\n                    alt=\"VESA Solutions Logo\" \r\n                    width={94}\r\n                    height={40}\r\n                    priority\r\n                    className=\"transition-transform duration-300 group-hover:scale-105 w-20 h-auto sm:w-[94px]\"\r\n                  />\r\n                </Link>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Desktop Navigation */}\r\n            <nav className=\"hidden lg:flex items-center space-x-12\">\r\n              {/* SEO Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setSeoDropdownOpen(true)}\r\n                onMouseLeave={() => setSeoDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/seo-search-engine-optimization\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-green-600 transition-all duration-300 group\">\r\n                  SEO\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-green-500 to-green-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  seoDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">SEO Services</div>\r\n                      {seoSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100/50 transition-all duration-200 ease-out border border-transparent hover:border-green-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-green-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Web Development Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setWebDevDropdownOpen(true)}\r\n                onMouseLeave={() => setWebDevDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/web-development\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group\">\r\n                  WEB DEVELOPMENT\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  webDevDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">Web Development</div>\r\n                      {webDevSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Digital Marketing Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setDigitalMarketingDropdownOpen(true)}\r\n                onMouseLeave={() => setDigitalMarketingDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/digital-marketing\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-purple-600 transition-all duration-300 group\">\r\n                  DIGITAL MARKETING\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-purple-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  digitalMarketingDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">Digital Marketing</div>\r\n                      {digitalMarketingSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50/50 transition-all duration-200 ease-out border border-transparent hover:border-indigo-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-purple-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-purple-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Services */}\r\n              <Link href=\"/services\" className=\"relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300\">\r\n                SERVICES\r\n                <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n              </Link>\r\n\r\n              {/* About Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setAboutDropdownOpen(true)}\r\n                onMouseLeave={() => setAboutDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/about\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group\">\r\n                  ABOUT\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  aboutDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">About</div>\r\n                      {aboutSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Contact */}\r\n              <Link href=\"/contact-us\" className=\"relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300\">\r\n                CONTACT\r\n                <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n              </Link>\r\n            </nav>\r\n\r\n\r\n            {/* CTA Button and Mobile Menu */}\r\n            <div className=\"flex items-center space-x-3 sm:space-x-4\">\r\n              {/* Desktop CTA */}\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                className=\"hidden sm:block group relative bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-sm font-semibold px-6 lg:px-8 py-2.5 lg:py-3 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-blue-500/25\"\r\n              >\r\n                <span className=\"relative z-10\">GET FREE PROPOSAL</span>\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n              </Link>\r\n\r\n              {/* Mobile Menu Button */}\r\n              <button\r\n                className=\"lg:hidden relative p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-all duration-300 group\"\r\n                onClick={toggleMobileMenu}\r\n                aria-label=\"Toggle mobile menu\"\r\n              >\r\n                <div className=\"w-6 h-6 relative\">\r\n                  <span className={`absolute top-1 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${\r\n                    mobileMenuOpen ? 'rotate-45 translate-y-2' : ''\r\n                  }`}></span>\r\n                  <span className={`absolute top-2.5 left-0 w-6 h-0.5 bg-current transition-all duration-300 ${\r\n                    mobileMenuOpen ? 'opacity-0' : ''\r\n                  }`}></span>\r\n                  <span className={`absolute top-4 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${\r\n                    mobileMenuOpen ? '-rotate-45 -translate-y-2' : ''\r\n                  }`}></span>\r\n                </div>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Mobile Menu */}\r\n      {mounted && mobileMenuOpen && (\r\n        <div className=\"lg:hidden fixed inset-0 z-40 bg-white overflow-y-auto\">\r\n          {/* Mobile Header */}\r\n          <div className=\"flex justify-between items-center p-4 border-b border-gray-200\">\r\n            <Link href=\"/\" onClick={closeMobileMenu}>\r\n              <Image\r\n                src=\"/VesaLogo.svg\"\r\n                alt=\"VESA Solutions Logo\"\r\n                width={80}\r\n                height={34}\r\n                priority\r\n                className=\"w-20 h-auto\"\r\n              />\r\n            </Link>\r\n            <button\r\n              onClick={closeMobileMenu}\r\n              className=\"p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-colors\"\r\n              aria-label=\"Close mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 relative\">\r\n                <span className=\"absolute top-2.5 left-0 w-6 h-0.5 bg-current transform rotate-45\"></span>\r\n                <span className=\"absolute top-2.5 left-0 w-6 h-0.5 bg-current transform -rotate-45\"></span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Mobile Navigation */}\r\n          <div className=\"p-4 space-y-6\">\r\n            {/* SEO Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setSeoDropdownOpen(!seoDropdownOpen)}\r\n              >\r\n                <Link href=\"/seo-search-engine-optimization\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-green-600 transition-colors\">\r\n                  SEO\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {seoDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {seoSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-green-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Web Development Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setWebDevDropdownOpen(!webDevDropdownOpen)}\r\n              >\r\n                <Link href=\"/web-development\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\r\n                  WEB DEVELOPMENT\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {webDevDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {webDevSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-blue-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Digital Marketing Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setDigitalMarketingDropdownOpen(!digitalMarketingDropdownOpen)}\r\n              >\r\n                <Link href=\"/digital-marketing\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-purple-600 transition-colors\">\r\n                  DIGITAL MARKETING\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {digitalMarketingDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {digitalMarketingSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-purple-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Services */}\r\n            <div>\r\n              <Link\r\n                href=\"/services\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\"\r\n              >\r\n                SERVICES\r\n              </Link>\r\n            </div>\r\n\r\n            {/* About Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setAboutDropdownOpen(!aboutDropdownOpen)}\r\n              >\r\n                <Link href=\"/about\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\r\n                  ABOUT\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {aboutDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {aboutSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-blue-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Contact */}\r\n            <div>\r\n              <Link\r\n                href=\"/contact-us\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\"\r\n              >\r\n                CONTACT\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Mobile CTA Button */}\r\n            <div className=\"pt-4\">\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block w-full text-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-6 py-4 rounded-full transition-all duration-300 transform hover:scale-105\"\r\n              >\r\n                GET FREE PROPOSAL\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWA,MAAM,SAAgC,CAAC,EAAE,YAAY,IAAI,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAC1F,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAEpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,mEAAmE;IACnE,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,qBAAqB;QACrB,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAe;IAEnB,kCAAkC;IAClC,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,YAAY,gBAAgB;gBACxC,kBAAkB;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;KAAe;IAEnB,mBAAmB;IACnB,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAA4B,MAAM;YAAgB,aAAa;QAAiC;QACxG;YAAE,MAAM;YAAgC,MAAM;YAAiB,aAAa;QAAgC;QAC5G;YAAE,MAAM;YAA0B,MAAM;YAAkB,aAAa;QAAiC;QACxG;YAAE,MAAM;YAAuB,MAAM;YAAc,aAAa;QAAgC;QAChG;YAAE,MAAM;YAAmB,MAAM;YAAoB,aAAa;QAAiC;QACnG;YAAE,MAAM;YAAiB,MAAM;YAAkB,aAAa;QAA6B;KAC5F;IAED,+BAA+B;IAC/B,MAAM,oBAAoB;QACxB;YAAE,MAAM;YAA8B,MAAM;YAA+B,aAAa;QAA4B;QACpH;YAAE,MAAM;YAA0B,MAAM;YAA0B,aAAa;QAAyB;QACxG;YAAE,MAAM;YAA0B,MAAM;YAA2B,aAAa;QAAqB;QACrG;YAAE,MAAM;YAA8B,MAAM;YAA+B,aAAa;QAA0B;QAClH;YAAE,MAAM;YAA+B,MAAM;YAAgC,aAAa;QAA0B;QACpH;YAAE,MAAM;YAAiC,MAAM;YAAgC,aAAa;QAAoB;KACjH;IAED,iCAAiC;IACjC,MAAM,8BAA8B;QAClC;YAAE,MAAM;YAAmB,MAAM;YAAQ,aAAa;QAAmC;QACzF;YAAE,MAAM;YAAmB,MAAM;YAAoB,aAAa;QAAmC;QACrG;YAAE,MAAM;YAA0B,MAAM;YAAiB,aAAa;QAAmC;QACzG;YAAE,MAAM;YAAqB,MAAM;YAAsB,aAAa;QAAgC;QACtG;YAAE,MAAM;YAA2B,MAAM;YAA4B,aAAa;QAA+B;QACjH;YAAE,MAAM;YAAyB,MAAM;YAA0B,aAAa;QAAiC;KAChH;IAED,qBAAqB;IACrB,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAgB,MAAM;YAAiB,aAAa;QAA+B;QAC3F;YAAE,MAAM;YAAQ,MAAM;YAAS,aAAa;QAAoC;KACjF;IAED,MAAM,mBAAmB;QACvB,kBAAkB,CAAC;IACrB;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,qDAAqD;QACrD,mBAAmB;QACnB,sBAAsB;QACtB,gCAAgC;QAChC,qBAAqB;IACvB;IAEA,qBACE;;0BACE,qKAAC;gBAAO,WAAW,CAAC,0GAA0G,EAC5H,YACI,2CACA,+DACJ;0BACA,cAAA,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,qKAAC,sHAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;;;;;0CAOlB,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC;wCACC,WAAU;wCACV,cAAc,IAAM,mBAAmB;wCACvC,cAAc,IAAM,mBAAmB;;0DAEvC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,WAAU;;oDAA6H;kEAElL,qKAAC,6MAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,kBAAkB,eAAe,IAAI;;;;;;kEACjH,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,qKAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,kBAAkB,sCAAsC,sCACxD;0DACA,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,qKAAC,qHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAI,WAAU;;kGACb,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,qKAAC;wCACC,WAAU;wCACV,cAAc,IAAM,sBAAsB;wCAC1C,cAAc,IAAM,sBAAsB;;0DAE1C,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;;oDAA4H;kEAElK,qKAAC,6MAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,qBAAqB,eAAe,IAAI;;;;;;kEACpH,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,qKAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,qBAAqB,sCAAsC,sCAC3D;0DACA,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,qKAAC,qHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAI,WAAU;;kGACb,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,qKAAC;wCACC,WAAU;wCACV,cAAc,IAAM,gCAAgC;wCACpD,cAAc,IAAM,gCAAgC;;0DAEpD,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;;oDAA8H;kEAEtK,qKAAC,6MAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,+BAA+B,eAAe,IAAI;;;;;;kEAC9H,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,qKAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,+BAA+B,sCAAsC,sCACrE;0DACA,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,4BAA4B,GAAG,CAAC,CAAC,SAAS,sBACzC,qKAAC,qHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAI,WAAU;;kGACb,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;;4CAAmH;0DAElJ,qKAAC;gDAAK,WAAU;;;;;;;;;;;;kDAIlB,qKAAC;wCACC,WAAU;wCACV,cAAc,IAAM,qBAAqB;wCACzC,cAAc,IAAM,qBAAqB;;0DAEzC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;;oDAA4H;kEAExJ,qKAAC,6MAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,oBAAoB,eAAe,IAAI;;;;;;kEACnH,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,qKAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,oBAAoB,sCAAsC,sCAC1D;0DACA,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,qKAAC,qHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAI,WAAU;;kGACb,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;;4CAAmH;0DAEpJ,qKAAC;gDAAK,WAAU;;;;;;;;;;;;;;;;;;0CAMpB,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC,qHAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,qKAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,qKAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,qKAAC;wCACC,WAAU;wCACV,SAAS;wCACT,cAAW;kDAEX,cAAA,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAK,WAAW,CAAC,+FAA+F,EAC/G,iBAAiB,4BAA4B,IAC7C;;;;;;8DACF,qKAAC;oDAAK,WAAW,CAAC,yEAAyE,EACzF,iBAAiB,cAAc,IAC/B;;;;;;8DACF,qKAAC;oDAAK,WAAW,CAAC,+FAA+F,EAC/G,iBAAiB,8BAA8B,IAC/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASb,WAAW,gCACV,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;;0CACb,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,SAAS;0CACtB,cAAA,qKAAC,sHAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAGd,qKAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAK,WAAU;;;;;;sDAChB,qKAAC;4CAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMtB,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;;kDACC,qKAAC;wCACC,WAAU;wCACV,SAAS,IAAM,mBAAmB,CAAC;;0DAEnC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,SAAS;gDAAiB,WAAU;0DAA6E;;;;;;0DAG9J,qKAAC,6MAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,kBAAkB,eAAe,IAAI;;;;;;;;;;;;oCAE7G,iCACC,qKAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,qKAAC,qHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,qKAAC;;kDACC,qKAAC;wCACC,WAAU;wCACV,SAAS,IAAM,sBAAsB,CAAC;;0DAEtC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,SAAS;gDAAiB,WAAU;0DAA4E;;;;;;0DAG9I,qKAAC,6MAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,qBAAqB,eAAe,IAAI;;;;;;;;;;;;oCAEhH,oCACC,qKAAC;wCAAI,WAAU;kDACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,qKAAC,qHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,qKAAC;;kDACC,qKAAC;wCACC,WAAU;wCACV,SAAS,IAAM,gCAAgC,CAAC;;0DAEhD,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,SAAS;gDAAiB,WAAU;0DAA8E;;;;;;0DAGlJ,qKAAC,6MAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,+BAA+B,eAAe,IAAI;;;;;;;;;;;;oCAE1H,8CACC,qKAAC;wCAAI,WAAU;kDACZ,4BAA4B,GAAG,CAAC,CAAC,SAAS,sBACzC,qKAAC,qHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,qKAAC;0CACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;0CAMH,qKAAC;;kDACC,qKAAC;wCACC,WAAU;wCACV,SAAS,IAAM,qBAAqB,CAAC;;0DAErC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,SAAS;gDAAiB,WAAU;0DAA4E;;;;;;0DAGpI,qKAAC,6MAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,oBAAoB,eAAe,IAAI;;;;;;;;;;;;oCAE/G,mCACC,qKAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,qKAAC,qHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,qKAAC;0CACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;0CAMH,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Footer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { Mail, Phone, MapPin, ArrowRight, Facebook, Instagram, Linkedin, Twitter, MessageCircle, Globe } from 'lucide-react';\r\n\r\nconst Footer: React.FC = () => {\r\n  return (\r\n    <footer className=\"relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden min-h-screen flex flex-col justify-center\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0 opacity-5\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/20 to-transparent\"></div>\r\n        <div className=\"absolute top-20 right-0 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute bottom-0 left-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative max-w-7xl mx-auto px-6 py-16\">\r\n        {/* Top Section - CTA */}\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent\">\r\n            Ready to Grow Your Business?\r\n          </h2>\r\n          <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\r\n            Let&apos;s create something amazing together. Get your free consultation today.\r\n          </p>\r\n          <Link href=\"/free-estimate\">\r\n            <button className=\"group inline-flex items-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl shadow-blue-500/25\">\r\n              Get Free Proposal\r\n              <ArrowRight size={20} className=\"ml-2 transition-transform duration-300 group-hover:translate-x-1\" />\r\n            </button>\r\n          </Link>\r\n        </div>\r\n\r\n        {/* Main Content */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12\">\r\n          {/* Company Info */}\r\n          <div className=\"lg:col-span-2\">\r\n            <div className=\"flex items-center mb-6\">\r\n              <Link href=\"/\">\r\n                <Image \r\n                  src=\"/VesaLogo.svg\" \r\n                  alt=\"VESA Solutions Logo\" \r\n                  width={120} \r\n                  height={46}\r\n                  className=\"w-30 h-auto transition-transform duration-300 hover:scale-105\"\r\n                />\r\n              </Link>\r\n            </div>\r\n            <p className=\"text-gray-300 text-lg leading-relaxed mb-8\">\r\n              Full-service digital marketing agency helping businesses attract, impress, and convert more leads online since 2015.\r\n            </p>\r\n            \r\n            {/* Contact Info */}\r\n            <div className=\"space-y-4\">\r\n              <a\r\n                href=\"mailto:<EMAIL>\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Mail size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span><EMAIL></span>\r\n              </a>\r\n              <a\r\n                href=\"tel:+***********\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Phone size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span>****** 628 3793</span>\r\n              </a>\r\n              <a\r\n                href=\"tel:+355694046408\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Phone size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span>+355 69 404 6408</span>\r\n              </a>\r\n              <a \r\n                href=\"https://share.google/T9q3WjqOOmMHrBnJY\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"flex items-start text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <MapPin size={18} className=\"mr-3 text-blue-400 mt-0.5 flex-shrink-0 group-hover:scale-110 transition-transform\" />\r\n                <span>Bulevardi Dyrrah, Pallati 394, Kati 4-t<br />2001, Durrës, Albania</span>\r\n              </a>\r\n              <div className=\"flex items-center text-gray-300\">\r\n                <div className=\"w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse\"></div>\r\n                <span className=\"text-green-400 font-medium\">Open 24 Hours</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Services Grid */}\r\n          <div className=\"lg:col-span-3 grid grid-cols-1 md:grid-cols-4 gap-8\">\r\n            {/* SEO Services */}\r\n            <div>\r\n              <Link href=\"/seo-search-engine-optimization\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-green-400 mb-6 relative transition-colors duration-300\">\r\n                  SEO Services\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-green-400 to-green-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/on-page-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  On-Page SEO\r\n                </Link></li>\r\n                <li><Link href=\"/off-page-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Off-Page SEO\r\n                </Link></li>\r\n                <li><Link href=\"/technical-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Technical SEO\r\n                </Link></li>\r\n                <li><Link href=\"/local-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Local SEO\r\n                </Link></li>\r\n                <li><Link href=\"/content-writing\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Content Writing\r\n                </Link></li>\r\n                <li><Link href=\"/seo-analytics\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  SEO Analytics\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Web Development */}\r\n            <div>\r\n              <Link href=\"/web-development\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-blue-400 mb-6 relative transition-colors duration-300\">\r\n                  Web Development\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/custom-website-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Custom Websites\r\n                </Link></li>\r\n                <li><Link href=\"/ecommerce-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  E-commerce Dev\r\n                </Link></li>\r\n                <li><Link href=\"/mobile-app-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Mobile Apps\r\n                </Link></li>\r\n                <li><Link href=\"/website-speed-optimization\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 flex-shrink-0 transition-all duration-300 group-hover:w-2\"></span>\r\n                  <span className=\"whitespace-nowrap\">Speed Optimization</span>\r\n                </Link></li>\r\n                <li><Link href=\"/web-application-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Web Applications\r\n                </Link></li>\r\n                <li><Link href=\"/website-maintenance-support\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Maintenance\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Digital Marketing */}\r\n            <div>\r\n              <Link href=\"/digital-marketing\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-purple-400 mb-6 relative transition-colors duration-300\">\r\n                  Digital Marketing\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-purple-400 to-purple-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/ppc\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  PPC Advertising\r\n                </Link></li>\r\n                <li><Link href=\"/email-marketing\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Email Marketing\r\n                </Link></li>\r\n                <li><Link href=\"/social-media\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Social Media\r\n                </Link></li>\r\n                <li><Link href=\"/branding-services\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Branding Services\r\n                </Link></li>\r\n                <li><Link href=\"/conversion-optimization\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Conversion Optimization\r\n                </Link></li>\r\n                <li><Link href=\"/reputation-management\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Reputation Management\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Company */}\r\n            <div>\r\n              <h3 className=\"text-lg font-bold text-white mb-6 relative\">\r\n                Company\r\n                <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600\"></span>\r\n              </h3>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/services\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Services\r\n                </Link></li>\r\n                <li><Link href=\"/case-studies\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Case Studies\r\n                </Link></li>\r\n                <li><Link href=\"/about\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  About Us\r\n                </Link></li>\r\n                <li><Link href=\"/contact-us\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Contact Us\r\n                </Link></li>\r\n                <li><Link href=\"/blog\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Blog\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Social Media & Bottom Section */}\r\n        <div className=\"flex flex-col lg:flex-row justify-between items-center pt-8 border-t border-gray-700\">\r\n          <div className=\"mb-6 lg:mb-0\">\r\n            <h4 className=\"text-white font-semibold mb-4\">Follow Our Journey</h4>\r\n            <div className=\"flex space-x-4\">\r\n              <a\r\n                href=\"https://m.facebook.com/VesaSolutions/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Facebook\"\r\n              >\r\n                <Facebook size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://www.instagram.com/vesasolutions/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-gradient-to-r hover:from-pink-500 hover:to-purple-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Instagram\"\r\n              >\r\n                <Instagram size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://al.linkedin.com/company/vesasolutions\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-700 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on LinkedIn\"\r\n              >\r\n                <Linkedin size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"#\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-500 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Twitter\"\r\n              >\r\n                <Twitter size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://wa.me/***********\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-green-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Chat with us on WhatsApp\"\r\n              >\r\n                <MessageCircle size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://vesasolutions.com/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-indigo-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Visit our website\"\r\n              >\r\n                <Globe size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"text-center lg:text-right\">\r\n            <div className=\"text-2xl font-bold text-white mb-2\">Growing Businesses Since 2015</div>\r\n            <div className=\"text-gray-400 text-sm\">Trusted by 200+ companies worldwide</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Copyright */}\r\n        <div className=\"mt-12 pt-8 border-t border-gray-700 text-center\">\r\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\r\n            <p className=\"text-gray-400 text-sm\">\r\n              © 2025 Vesa Solutions Marketing Agency. All rights reserved.\r\n            </p>\r\n            <div className=\"flex flex-wrap justify-center md:justify-end space-x-6 text-sm\">\r\n              <Link href=\"/privacy-policy\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Privacy Policy</Link>\r\n              <Link href=\"/terms-of-service\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Terms of Service</Link>\r\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Sitemap</Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAOA,MAAM,SAAmB;IACvB,qBACE,qKAAC;QAAO,WAAU;;0BAEhB,qKAAC;gBAAI,WAAU;;kCACb,qKAAC;wBAAI,WAAU;;;;;;kCACf,qKAAC;wBAAI,WAAU;;;;;;kCACf,qKAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAG,WAAU;0CAA4G;;;;;;0CAG1H,qKAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,qKAAC;oCAAO,WAAU;;wCAA2P;sDAE3Q,qKAAC,2MAAA,CAAA,aAAU;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMtC,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,qKAAC,sHAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;kDAIhB,qKAAC;wCAAE,WAAU;kDAA6C;;;;;;kDAK1D,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,qKAAC,2LAAA,CAAA,OAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC1B,qKAAC;kEAAK;;;;;;;;;;;;0DAER,qKAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,qKAAC,6LAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC3B,qKAAC;kEAAK;;;;;;;;;;;;0DAER,qKAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,qKAAC,6LAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC3B,qKAAC;kEAAK;;;;;;;;;;;;0DAER,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,qKAAC,mMAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC5B,qKAAC;;4DAAK;0EAAuC,qKAAC;;;;;4DAAK;;;;;;;;;;;;;0DAErD,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;;;;;;kEACf,qKAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;0CAMnD,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC;;0DACC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,WAAU;0DACrD,cAAA,qKAAC;oDAAG,WAAU;;wDAAiG;sEAE7G,qKAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,qKAAC;gDAAG,WAAU;;kEACZ,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAe,WAAU;;8EACtC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EACxC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAa,WAAU;;8EACpC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;;8EAC1C,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EACxC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;;;;;;;;;;;;;kDAOlH,qKAAC;;0DACC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;0DACtC,cAAA,qKAAC;oDAAG,WAAU;;wDAAgG;sEAE5G,qKAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,qKAAC;gDAAG,WAAU;;kEACZ,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA8B,WAAU;;8EACrD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAyB,WAAU;;8EAChD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA0B,WAAU;;8EACjD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA8B,WAAU;;8EACrD,qKAAC;oEAAK,WAAU;;;;;;8EAChB,qKAAC;oEAAK,WAAU;8EAAoB;;;;;;;;;;;;;;;;;kEAEtC,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA+B,WAAU;;8EACtD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA+B,WAAU;;8EACtD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;;;;;;;;;;;;;kDAOjH,qKAAC;;0DACC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DACxC,cAAA,qKAAC;oDAAG,WAAU;;wDAAkG;sEAE9G,qKAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,qKAAC;gDAAG,WAAU;;kEACZ,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAO,WAAU;;8EAC9B,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;;8EAC1C,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAqB,WAAU;;8EAC5C,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA2B,WAAU;;8EAClD,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAyB,WAAU;;8EAChD,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;;;;;;;;;;;;;kDAOnH,qKAAC;;0DACC,qKAAC;gDAAG,WAAU;;oDAA6C;kEAEzD,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAElB,qKAAC;gDAAG,WAAU;;kEACZ,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAY,WAAU;;8EACnC,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;;8EAChC,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAc,WAAU;;8EACrC,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAQ,WAAU;;8EAC/B,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrH,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,mMAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEhC,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,qMAAA,CAAA,YAAS;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEjC,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,mMAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEhC,qKAAC;gDACC,MAAK;gDACL,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,iMAAA,CAAA,UAAO;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAE/B,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,iNAAA,CAAA,gBAAa;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAErC,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,6LAAA,CAAA,QAAK;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAKjC,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,qKAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAK3C,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,qKAAC;oCAAI,WAAU;;sDACb,qKAAC,qHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAkB,WAAU;sDAAsD;;;;;;sDAC7F,qKAAC,qHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAoB,WAAU;sDAAsD;;;;;;sDAC/F,qKAAC,qHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpG;uCAEe", "debugId": null}}, {"offset": {"line": 2511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/lib/sanity-case-studies.ts"], "sourcesContent": ["// lib/sanity-case-studies.ts - Sanity client utilities for case studies\nimport { createClient } from '@sanity/client'\nimport imageUrlBuilder from '@sanity/image-url'\n\nexport const client = createClient({\n  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,\n  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,\n  apiVersion: '2023-05-03',\n  useCdn: false,\n})\n\n// Sanity image type\nexport interface SanityImageObject {\n  _type: 'image';\n  asset: {\n    _ref: string;\n    _type: 'reference';\n  };\n  alt?: string;\n  caption?: string;\n}\n\n// Image URL builder\nconst builder = imageUrlBuilder(client)\n\nexport function urlFor(source: SanityImageObject) {\n  return builder.image(source)\n}\n\n// TypeScript interfaces for case studies\nexport interface CaseStudyMetric {\n  label: string;\n  beforeValue?: string;\n  afterValue: string;\n  improvement?: string;\n  icon?: string;\n}\n\nexport interface CaseStudyClient {\n  name: string;\n  industry: string;\n  location?: string;\n  website?: string;\n  logo?: SanityImageObject;\n  companySize?: string;\n}\n\nexport interface CaseStudyProject {\n  serviceType: string;\n  duration?: string;\n  challenge: Array<{\n    _key: string;\n    _type: string;\n    children: Array<{\n      _key: string;\n      _type: 'span';\n      marks: string[];\n      text: string;\n    }>;\n    markDefs: unknown[];\n    style: string;\n  }>;\n  goals: string[];\n}\n\nexport interface CaseStudySolution {\n  approach: Array<{\n    _key: string;\n    _type: string;\n    children: Array<{\n      _key: string;\n      _type: 'span';\n      marks: string[];\n      text: string;\n    }>;\n    markDefs: unknown[];\n    style: string;\n  }>;\n  implementation?: Array<{\n    title: string;\n    description: string;\n    timeline?: string;\n  }>;\n  tools?: string[];\n}\n\nexport interface CaseStudyResults {\n  overview: string;\n  metrics: CaseStudyMetric[];\n  timeline?: string;\n}\n\nexport interface CaseStudyTestimonial {\n  quote: string;\n  author: string;\n  position: string;\n  authorImage?: SanityImageObject;\n  rating?: number;\n}\n\nexport interface CaseStudy {\n  _id: string;\n  title: string;\n  slug: {\n    current: string;\n  };\n  shortDescription: string;\n  featured: boolean;\n  publishedAt: string;\n  client: CaseStudyClient;\n  project: CaseStudyProject;\n  solution: CaseStudySolution;\n  results: CaseStudyResults;\n  testimonial: CaseStudyTestimonial;\n  featuredImage: SanityImageObject;\n  beforeAfterImages?: {\n    before?: SanityImageObject;\n    after?: SanityImageObject;\n  };\n  gallery?: SanityImageObject[];\n  seo?: {\n    metaTitle?: string;\n    metaDescription?: string;\n    keywords?: string[];\n    ogImage?: SanityImageObject;\n  };\n}\n\n// GROQ queries for case studies\nexport const caseStudyQueries = {\n  // Get all case studies with basic info\n  all: `*[_type == \"caseStudy\"] | order(publishedAt desc) {\n    _id,\n    title,\n    slug,\n    shortDescription,\n    featured,\n    publishedAt,\n    client,\n    project {\n      serviceType,\n      duration\n    },\n    results {\n      overview,\n      metrics[] {\n        label,\n        afterValue,\n        improvement,\n        icon\n      }\n    },\n    testimonial {\n      quote,\n      author,\n      position,\n      rating\n    },\n    featuredImage,\n    seo\n  }`,\n\n  // Get featured case studies\n  featured: `*[_type == \"caseStudy\" && featured == true] | order(publishedAt desc) {\n    _id,\n    title,\n    slug,\n    shortDescription,\n    client,\n    project {\n      serviceType,\n      duration\n    },\n    results {\n      overview,\n      metrics[] {\n        label,\n        afterValue,\n        improvement,\n        icon\n      }\n    },\n    testimonial {\n      quote,\n      author,\n      position,\n      rating\n    },\n    featuredImage\n  }`,\n\n  // Get case studies by industry\n  byIndustry: (industry: string) => `*[_type == \"caseStudy\" && client.industry == \"${industry}\"] | order(publishedAt desc) {\n    _id,\n    title,\n    slug,\n    shortDescription,\n    client,\n    project {\n      serviceType,\n      duration\n    },\n    results {\n      metrics[] {\n        label,\n        afterValue,\n        improvement\n      }\n    },\n    featuredImage\n  }`,\n\n  // Get single case study by slug\n  bySlug: (slug: string) => `*[_type == \"caseStudy\" && slug.current == \"${slug}\"][0] {\n    _id,\n    title,\n    slug,\n    shortDescription,\n    featured,\n    publishedAt,\n    client,\n    project,\n    solution,\n    results,\n    testimonial,\n    featuredImage,\n    beforeAfterImages,\n    gallery,\n    seo\n  }`,\n\n  // Get case studies for sitemap\n  sitemap: `*[_type == \"caseStudy\"] {\n    slug,\n    publishedAt\n  }`\n};\n\n// API functions\nexport async function getAllCaseStudies(): Promise<CaseStudy[]> {\n  try {\n    return await client.fetch(caseStudyQueries.all);\n  } catch (error) {\n    console.error('Error fetching case studies:', error);\n    return [];\n  }\n}\n\nexport async function getFeaturedCaseStudies(): Promise<CaseStudy[]> {\n  try {\n    return await client.fetch(caseStudyQueries.featured);\n  } catch (error) {\n    console.error('Error fetching featured case studies:', error);\n    return [];\n  }\n}\n\nexport async function getCaseStudiesByIndustry(industry: string): Promise<CaseStudy[]> {\n  try {\n    return await client.fetch(caseStudyQueries.byIndustry(industry));\n  } catch (error) {\n    console.error('Error fetching case studies by industry:', error);\n    return [];\n  }\n}\n\nexport async function getCaseStudyBySlug(slug: string): Promise<CaseStudy | null> {\n  try {\n    return await client.fetch(caseStudyQueries.bySlug(slug));\n  } catch (error) {\n    console.error('Error fetching case study by slug:', error);\n    return null;\n  }\n}\n\nexport async function getCaseStudiesForSitemap() {\n  try {\n    return await client.fetch(caseStudyQueries.sitemap);\n  } catch (error) {\n    console.error('Error fetching case studies for sitemap:', error);\n    return [];\n  }\n}\n\n// Helper functions\nexport function getIndustryColor(industry: string): string {\n  const industryColors: { [key: string]: string } = {\n    'ecommerce': 'blue',\n    'healthcare': 'green',\n    'professional-services': 'purple',\n    'education': 'indigo',\n    'real-estate': 'orange',\n    'automotive': 'red',\n    'financial': 'yellow',\n    'non-profit': 'pink',\n    'technology': 'cyan',\n    'manufacturing': 'gray',\n    'food-beverage': 'lime',\n    'other': 'slate'\n  };\n  \n  return industryColors[industry] || 'blue';\n}\n\nexport function formatMetricValue(value: string): string {\n  // Format metric values for display\n  if (value.includes('%')) {\n    return value;\n  }\n  if (value.includes('+')) {\n    return value;\n  }\n  if (value.includes('-')) {\n    return value;\n  }\n  if (!isNaN(Number(value))) {\n    return `+${value}%`;\n  }\n  return value;\n}\n\nexport function getServiceTypeColor(serviceType: string): string {\n  const serviceColors: { [key: string]: string } = {\n    'seo': 'blue',\n    'web-development': 'purple',\n    'ppc': 'green',\n    'social-media': 'pink',\n    'content-marketing': 'orange',\n    'email-marketing': 'yellow',\n    'reputation': 'red',\n    'full-strategy': 'indigo'\n  };\n  \n  return serviceColors[serviceType] || 'blue';\n}\n"], "names": [], "mappings": "AAAA,wEAAwE;;;;;;;;;;;;;;AACxE;AACA;;;;;;;AAEO,MAAM,SAAS,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE;IACjC,SAAS;IACT,OAAO;IACP,YAAY;IACZ,QAAQ;AACV;AAaA,oBAAoB;AACpB,MAAM,UAAU,CAAA,GAAA,yIAAA,CAAA,UAAe,AAAD,EAAE;AAEzB,SAAS,OAAO,MAAyB;IAC9C,OAAO,QAAQ,KAAK,CAAC;AACvB;AAsGO,MAAM,mBAAmB;IAC9B,uCAAuC;IACvC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BL,CAAC;IAEF,4BAA4B;IAC5B,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BV,CAAC;IAEF,+BAA+B;IAC/B,YAAY,CAAC,WAAqB,CAAC,8CAA8C,EAAE,SAAS;;;;;;;;;;;;;;;;;;GAkB3F,CAAC;IAEF,gCAAgC;IAChC,QAAQ,CAAC,OAAiB,CAAC,2CAA2C,EAAE,KAAK;;;;;;;;;;;;;;;;GAgB5E,CAAC;IAEF,+BAA+B;IAC/B,SAAS,CAAC;;;GAGT,CAAC;AACJ;AAGO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,OAAO,KAAK,CAAC,iBAAiB,GAAG;IAChD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,EAAE;IACX;AACF;AAEO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,OAAO,KAAK,CAAC,iBAAiB,QAAQ;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO,EAAE;IACX;AACF;AAEO,eAAe,yBAAyB,QAAgB;IAC7D,IAAI;QACF,OAAO,MAAM,OAAO,KAAK,CAAC,iBAAiB,UAAU,CAAC;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO,EAAE;IACX;AACF;AAEO,eAAe,mBAAmB,IAAY;IACnD,IAAI;QACF,OAAO,MAAM,OAAO,KAAK,CAAC,iBAAiB,MAAM,CAAC;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;IACT;AACF;AAEO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,OAAO,KAAK,CAAC,iBAAiB,OAAO;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO,EAAE;IACX;AACF;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,MAAM,iBAA4C;QAChD,aAAa;QACb,cAAc;QACd,yBAAyB;QACzB,aAAa;QACb,eAAe;QACf,cAAc;QACd,aAAa;QACb,cAAc;QACd,cAAc;QACd,iBAAiB;QACjB,iBAAiB;QACjB,SAAS;IACX;IAEA,OAAO,cAAc,CAAC,SAAS,IAAI;AACrC;AAEO,SAAS,kBAAkB,KAAa;IAC7C,mCAAmC;IACnC,IAAI,MAAM,QAAQ,CAAC,MAAM;QACvB,OAAO;IACT;IACA,IAAI,MAAM,QAAQ,CAAC,MAAM;QACvB,OAAO;IACT;IACA,IAAI,MAAM,QAAQ,CAAC,MAAM;QACvB,OAAO;IACT;IACA,IAAI,CAAC,MAAM,OAAO,SAAS;QACzB,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IACrB;IACA,OAAO;AACT;AAEO,SAAS,oBAAoB,WAAmB;IACrD,MAAM,gBAA2C;QAC/C,OAAO;QACP,mBAAmB;QACnB,OAAO;QACP,gBAAgB;QAChB,qBAAqB;QACrB,mBAAmB;QACnB,cAAc;QACd,iBAAiB;IACnB;IAEA,OAAO,aAAa,CAAC,YAAY,IAAI;AACvC", "debugId": null}}, {"offset": {"line": 2741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/case-studies/CaseStudiesArchiveSection.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { CaseStudy, urlFor } from '@/lib/sanity-case-studies';\nimport { ArrowRight, Calendar, Building, Star, Filter } from 'lucide-react';\n\ninterface CaseStudiesArchiveSectionProps {\n  caseStudies: CaseStudy[];\n}\n\nconst CaseStudiesArchiveSection: React.FC<CaseStudiesArchiveSectionProps> = ({ caseStudies }) => {\n  const [selectedIndustry, setSelectedIndustry] = useState<string>('all');\n  const [selectedService, setSelectedService] = useState<string>('all');\n\n  // Get unique industries and services for filters\n  const industries = ['all', ...Array.from(new Set(caseStudies.map(cs => cs.client.industry)))];\n  const services = ['all', ...Array.from(new Set(caseStudies.map(cs => cs.project.serviceType)))];\n\n  // Filter case studies based on selected filters\n  const filteredCaseStudies = caseStudies.filter(caseStudy => {\n    const industryMatch = selectedIndustry === 'all' || caseStudy.client.industry === selectedIndustry;\n    const serviceMatch = selectedService === 'all' || caseStudy.project.serviceType === selectedService;\n    return industryMatch && serviceMatch;\n  });\n\n  // Format industry name for display\n  const formatIndustryName = (industry: string) => {\n    if (industry === 'all') return 'All Industries';\n    return industry.split('-').map(word => \n      word.charAt(0).toUpperCase() + word.slice(1)\n    ).join(' ');\n  };\n\n  // Format service name for display\n  const formatServiceName = (service: string) => {\n    if (service === 'all') return 'All Services';\n    return service.split('-').map(word => \n      word.charAt(0).toUpperCase() + word.slice(1)\n    ).join(' ');\n  };\n\n  // Get primary metric for display\n  const getPrimaryMetric = (caseStudy: CaseStudy) => {\n    if (caseStudy.results.metrics && caseStudy.results.metrics.length > 0) {\n      const metric = caseStudy.results.metrics[0];\n      return metric.improvement || metric.afterValue;\n    }\n    return null;\n  };\n\n  return (\n    <>\n      {/* Hero Section */}\n      <section className=\"py-24 bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900\">\n        <div className=\"max-w-7xl mx-auto px-6 text-center\">\n          <div className=\"bg-green-600 text-white text-sm font-bold px-4 py-2 rounded-full inline-block mb-6\">\n            Real Results, Real Success Stories\n          </div>\n          \n          <h1 className=\"text-5xl md:text-6xl font-bold text-white leading-tight mb-8\">\n            Client Success Stories\n          </h1>\n          \n          <p className=\"text-xl text-blue-100 mb-12 leading-relaxed max-w-4xl mx-auto\">\n            Explore our portfolio of successful digital marketing campaigns and transformations. Each case study represents a real business that achieved remarkable growth through our proven strategies.\n          </p>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 mb-12\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-blue-300 mb-2\">{caseStudies.length}+</div>\n              <div className=\"text-blue-100\">Success Stories</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-blue-300 mb-2\">300%</div>\n              <div className=\"text-blue-100\">Average ROI</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-blue-300 mb-2\">15+</div>\n              <div className=\"text-blue-100\">Industries</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-blue-300 mb-2\">98%</div>\n              <div className=\"text-blue-100\">Client Satisfaction</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Filters and Archive */}\n      <section className=\"py-24 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-6\">\n          {/* Filters */}\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg mb-12\">\n            <div className=\"flex items-center mb-6\">\n              <Filter className=\"text-blue-600 mr-3\" size={24} />\n              <h2 className=\"text-2xl font-bold text-gray-800\">Filter Case Studies</h2>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Industry Filter */}\n              <div>\n                <label className=\"block text-sm font-semibold text-gray-700 mb-3\">\n                  Filter by Industry\n                </label>\n                <select\n                  value={selectedIndustry}\n                  onChange={(e) => setSelectedIndustry(e.target.value)}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  {industries.map(industry => (\n                    <option key={industry} value={industry}>\n                      {formatIndustryName(industry)}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Service Filter */}\n              <div>\n                <label className=\"block text-sm font-semibold text-gray-700 mb-3\">\n                  Filter by Service\n                </label>\n                <select\n                  value={selectedService}\n                  onChange={(e) => setSelectedService(e.target.value)}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  {services.map(service => (\n                    <option key={service} value={service}>\n                      {formatServiceName(service)}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {/* Results Count */}\n            <div className=\"mt-6 text-center\">\n              <p className=\"text-gray-600\">\n                Showing <span className=\"font-semibold text-blue-600\">{filteredCaseStudies.length}</span> of {caseStudies.length} case studies\n              </p>\n            </div>\n          </div>\n\n          {/* Case Studies Grid */}\n          {filteredCaseStudies.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {filteredCaseStudies.map((caseStudy) => (\n                <Link \n                  key={caseStudy._id}\n                  href={`/case-studies/${caseStudy.slug.current}`}\n                  className=\"group bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2\"\n                >\n                  {/* Featured Image */}\n                  <div className=\"relative\">\n                    {caseStudy.featuredImage ? (\n                      <Image\n                        src={urlFor(caseStudy.featuredImage).width(400).height(250).url()}\n                        alt={caseStudy.title}\n                        width={400}\n                        height={250}\n                        className=\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n                      />\n                    ) : (\n                      <div className=\"w-full h-64 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center\">\n                        <Building className=\"text-blue-600\" size={48} />\n                      </div>\n                    )}\n                    \n                    {/* Featured Badge */}\n                    {caseStudy.featured && (\n                      <div className=\"absolute top-4 left-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                        Featured\n                      </div>\n                    )}\n                    \n                    {/* Primary Metric */}\n                    {getPrimaryMetric(caseStudy) && (\n                      <div className=\"absolute top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-full font-bold text-sm\">\n                        {getPrimaryMetric(caseStudy)}\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-8\">\n                    {/* Client Info */}\n                    <div className=\"flex items-center mb-4\">\n                      {caseStudy.client.logo ? (\n                        <Image\n                          src={urlFor(caseStudy.client.logo).height(40).url()}\n                          alt={caseStudy.client.name}\n                          width={40}\n                          height={32}\n                          className=\"h-8 mr-3\"\n                        />\n                      ) : (\n                        <div className=\"w-8 h-8 bg-gray-200 rounded mr-3\"></div>\n                      )}\n                      <div>\n                        <div className=\"font-semibold text-gray-800\">{caseStudy.client.name}</div>\n                        <div className=\"text-sm text-gray-600\">{formatIndustryName(caseStudy.client.industry)}</div>\n                      </div>\n                    </div>\n\n                    {/* Title */}\n                    <h3 className=\"text-xl font-bold text-gray-800 mb-4 group-hover:text-blue-600 transition-colors line-clamp-2\">\n                      {caseStudy.title}\n                    </h3>\n\n                    {/* Description */}\n                    <p className=\"text-gray-600 mb-6 leading-relaxed line-clamp-3\">\n                      {caseStudy.shortDescription}\n                    </p>\n\n                    {/* Meta Info */}\n                    <div className=\"flex items-center justify-between mb-6\">\n                      <div className=\"bg-blue-100 px-3 py-1 rounded-full\">\n                        <span className=\"text-blue-600 font-semibold text-sm\">\n                          {formatServiceName(caseStudy.project.serviceType)}\n                        </span>\n                      </div>\n                      \n                      {caseStudy.project.duration && (\n                        <div className=\"flex items-center text-gray-500 text-sm\">\n                          <Calendar size={16} className=\"mr-1\" />\n                          {caseStudy.project.duration}\n                        </div>\n                      )}\n                    </div>\n\n                    {/* Testimonial Rating */}\n                    {caseStudy.testimonial?.rating && (\n                      <div className=\"flex items-center mb-4\">\n                        <div className=\"flex mr-2\">\n                          {[...Array(caseStudy.testimonial.rating)].map((_, i) => (\n                            <Star key={i} size={16} className=\"text-yellow-400 fill-current\" />\n                          ))}\n                        </div>\n                        <span className=\"text-sm text-gray-600\">\n                          {caseStudy.testimonial.author}\n                        </span>\n                      </div>\n                    )}\n\n                    {/* Read More */}\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-blue-600 font-semibold group-hover:text-blue-700\">\n                        Read Full Case Study\n                      </span>\n                      <ArrowRight \n                        size={20} \n                        className=\"text-blue-600 group-hover:translate-x-1 transition-transform duration-300\" \n                      />\n                    </div>\n                  </div>\n                </Link>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-16\">\n              <Building className=\"text-gray-400 mx-auto mb-4\" size={64} />\n              <h3 className=\"text-2xl font-bold text-gray-600 mb-2\">\n                No case studies found\n              </h3>\n              <p className=\"text-gray-500 mb-6\">\n                Try adjusting your filters to see more results.\n              </p>\n              <button\n                onClick={() => {\n                  setSelectedIndustry('all');\n                  setSelectedService('all');\n                }}\n                className=\"bg-blue-600 text-white font-semibold px-6 py-3 rounded-xl hover:bg-blue-700 transition-colors\"\n              >\n                Clear Filters\n              </button>\n            </div>\n          )}\n        </div>\n      </section>\n    </>\n  );\n};\n\nexport default CaseStudiesArchiveSection;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;AAMA,MAAM,4BAAsE,CAAC,EAAE,WAAW,EAAE;IAC1F,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAU;IAE/D,iDAAiD;IACjD,MAAM,aAAa;QAAC;WAAU,MAAM,IAAI,CAAC,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,KAAM,GAAG,MAAM,CAAC,QAAQ;KAAI;IAC7F,MAAM,WAAW;QAAC;WAAU,MAAM,IAAI,CAAC,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,KAAM,GAAG,OAAO,CAAC,WAAW;KAAI;IAE/F,gDAAgD;IAChD,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA;QAC7C,MAAM,gBAAgB,qBAAqB,SAAS,UAAU,MAAM,CAAC,QAAQ,KAAK;QAClF,MAAM,eAAe,oBAAoB,SAAS,UAAU,OAAO,CAAC,WAAW,KAAK;QACpF,OAAO,iBAAiB;IAC1B;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,CAAC;QAC1B,IAAI,aAAa,OAAO,OAAO;QAC/B,OAAO,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAC7B,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IAC1C,IAAI,CAAC;IACT;IAEA,kCAAkC;IAClC,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY,OAAO,OAAO;QAC9B,OAAO,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAC5B,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IAC1C,IAAI,CAAC;IACT;IAEA,iCAAiC;IACjC,MAAM,mBAAmB,CAAC;QACxB,IAAI,UAAU,OAAO,CAAC,OAAO,IAAI,UAAU,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG;YACrE,MAAM,SAAS,UAAU,OAAO,CAAC,OAAO,CAAC,EAAE;YAC3C,OAAO,OAAO,WAAW,IAAI,OAAO,UAAU;QAChD;QACA,OAAO;IACT;IAEA,qBACE;;0BAEE,qKAAC;gBAAQ,WAAU;0BACjB,cAAA,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAI,WAAU;sCAAqF;;;;;;sCAIpG,qKAAC;4BAAG,WAAU;sCAA+D;;;;;;sCAI7E,qKAAC;4BAAE,WAAU;sCAAgE;;;;;;sCAK7E,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;;gDAAyC,YAAY,MAAM;gDAAC;;;;;;;sDAC3E,qKAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;sDAAwC;;;;;;sDACvD,qKAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;sDAAwC;;;;;;sDACvD,qKAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;sDAAwC;;;;;;sDACvD,qKAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,qKAAC;gBAAQ,WAAU;0BACjB,cAAA,qKAAC;oBAAI,WAAU;;sCAEb,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;;sDACb,qKAAC,+LAAA,CAAA,SAAM;4CAAC,WAAU;4CAAqB,MAAM;;;;;;sDAC7C,qKAAC;4CAAG,WAAU;sDAAmC;;;;;;;;;;;;8CAGnD,qKAAC;oCAAI,WAAU;;sDAEb,qKAAC;;8DACC,qKAAC;oDAAM,WAAU;8DAAiD;;;;;;8DAGlE,qKAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;oDACnD,WAAU;8DAET,WAAW,GAAG,CAAC,CAAA,yBACd,qKAAC;4DAAsB,OAAO;sEAC3B,mBAAmB;2DADT;;;;;;;;;;;;;;;;sDAQnB,qKAAC;;8DACC,qKAAC;oDAAM,WAAU;8DAAiD;;;;;;8DAGlE,qKAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oDAClD,WAAU;8DAET,SAAS,GAAG,CAAC,CAAA,wBACZ,qKAAC;4DAAqB,OAAO;sEAC1B,kBAAkB;2DADR;;;;;;;;;;;;;;;;;;;;;;8CASrB,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAE,WAAU;;4CAAgB;0DACnB,qKAAC;gDAAK,WAAU;0DAA+B,oBAAoB,MAAM;;;;;;4CAAQ;4CAAK,YAAY,MAAM;4CAAC;;;;;;;;;;;;;;;;;;wBAMtH,oBAAoB,MAAM,GAAG,kBAC5B,qKAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC,0BACxB,qKAAC,qHAAA,CAAA,UAAI;oCAEH,MAAM,CAAC,cAAc,EAAE,UAAU,IAAI,CAAC,OAAO,EAAE;oCAC/C,WAAU;;sDAGV,qKAAC;4CAAI,WAAU;;gDACZ,UAAU,aAAa,iBACtB,qKAAC,sHAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,yHAAA,CAAA,SAAM,AAAD,EAAE,UAAU,aAAa,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG;oDAC/D,KAAK,UAAU,KAAK;oDACpB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;yEAGZ,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC,mMAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAgB,MAAM;;;;;;;;;;;gDAK7C,UAAU,QAAQ,kBACjB,qKAAC;oDAAI,WAAU;8DAA6F;;;;;;gDAM7G,iBAAiB,4BAChB,qKAAC;oDAAI,WAAU;8DACZ,iBAAiB;;;;;;;;;;;;sDAMxB,qKAAC;4CAAI,WAAU;;8DAEb,qKAAC;oDAAI,WAAU;;wDACZ,UAAU,MAAM,CAAC,IAAI,iBACpB,qKAAC,sHAAA,CAAA,UAAK;4DACJ,KAAK,CAAA,GAAA,yHAAA,CAAA,SAAM,AAAD,EAAE,UAAU,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG;4DACjD,KAAK,UAAU,MAAM,CAAC,IAAI;4DAC1B,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;iFAGZ,qKAAC;4DAAI,WAAU;;;;;;sEAEjB,qKAAC;;8EACC,qKAAC;oEAAI,WAAU;8EAA+B,UAAU,MAAM,CAAC,IAAI;;;;;;8EACnE,qKAAC;oEAAI,WAAU;8EAAyB,mBAAmB,UAAU,MAAM,CAAC,QAAQ;;;;;;;;;;;;;;;;;;8DAKxF,qKAAC;oDAAG,WAAU;8DACX,UAAU,KAAK;;;;;;8DAIlB,qKAAC;oDAAE,WAAU;8DACV,UAAU,gBAAgB;;;;;;8DAI7B,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;sEACb,cAAA,qKAAC;gEAAK,WAAU;0EACb,kBAAkB,UAAU,OAAO,CAAC,WAAW;;;;;;;;;;;wDAInD,UAAU,OAAO,CAAC,QAAQ,kBACzB,qKAAC;4DAAI,WAAU;;8EACb,qKAAC,mMAAA,CAAA,WAAQ;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAC7B,UAAU,OAAO,CAAC,QAAQ;;;;;;;;;;;;;gDAMhC,UAAU,WAAW,EAAE,wBACtB,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;sEACZ;mEAAI,MAAM,UAAU,WAAW,CAAC,MAAM;6DAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBAChD,qKAAC,2LAAA,CAAA,OAAI;oEAAS,MAAM;oEAAI,WAAU;mEAAvB;;;;;;;;;;sEAGf,qKAAC;4DAAK,WAAU;sEACb,UAAU,WAAW,CAAC,MAAM;;;;;;;;;;;;8DAMnC,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAK,WAAU;sEAAwD;;;;;;sEAGxE,qKAAC,2MAAA,CAAA,aAAU;4DACT,MAAM;4DACN,WAAU;;;;;;;;;;;;;;;;;;;mCAvGX,UAAU,GAAG;;;;;;;;;iDA+GxB,qKAAC;4BAAI,WAAU;;8CACb,qKAAC,mMAAA,CAAA,WAAQ;oCAAC,WAAU;oCAA6B,MAAM;;;;;;8CACvD,qKAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,qKAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,qKAAC;oCACC,SAAS;wCACP,oBAAoB;wCACpB,mBAAmB;oCACrB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 3405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/case-studies/CaseStudiesCTASection.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Phone, Calendar, Award, TrendingUp } from 'lucide-react';\n\nconst CaseStudiesCTASection: React.FC = () => {\n\n  const successStats = [\n    {\n      icon: TrendingUp,\n      value: '500+',\n      label: 'Successful Projects',\n      description: 'Completed across all industries'\n    },\n    {\n      icon: Award,\n      value: '300%',\n      label: 'Average ROI',\n      description: 'Return on investment for clients'\n    },\n    {\n      icon: Phone,\n      value: '98%',\n      label: 'Client Satisfaction',\n      description: 'Happy clients who recommend us'\n    }\n  ];\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-blue-50 to-blue-100\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        {/* Main CTA Header */}\n        <div className=\"text-center mb-20\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-6\">\n            Ready to Write Your Own Success Story?\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed\">\n            These case studies represent real businesses that took action and achieved extraordinary results. Your success story could be next. Let&apos;s discuss how we can help you achieve similar growth.\n          </p>\n        </div>\n\n        {/* Success Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\n          {successStats.map((stat, index) => {\n            const IconComponent = stat.icon;\n            return (\n              <div key={index} className=\"bg-white rounded-2xl p-8 text-center shadow-lg hover:shadow-xl transition-all duration-300\">\n                <div className=\"bg-blue-100 p-4 rounded-2xl inline-block mb-6\">\n                  <IconComponent className=\"text-blue-600\" size={32} />\n                </div>\n                <div className=\"text-4xl font-bold text-gray-800 mb-2\">{stat.value}</div>\n                <div className=\"text-xl font-semibold text-gray-800 mb-2\">{stat.label}</div>\n                <div className=\"text-gray-600\">{stat.description}</div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* CTA Buttons */}\n        <div className=\"text-center mb-16\">\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact-us\"\n              className=\"bg-blue-600 text-white font-bold px-10 py-4 rounded-full hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 flex items-center justify-center\"\n            >\n              <Phone className=\"mr-2\" size={20} />\n              Get Your Free Consultation\n            </Link>\n            <Link\n              href=\"/free-estimate\"\n              className=\"border-2 border-blue-600 text-blue-600 font-bold px-10 py-4 rounded-full hover:bg-blue-600 hover:text-white transition-all duration-300 flex items-center justify-center\"\n            >\n              <Calendar className=\"mr-2\" size={20} />\n              Request Free Estimate\n            </Link>\n          </div>\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-12 text-white text-center\">\n          <h3 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Join Our Growing List of Success Stories\n          </h3>\n          <p className=\"text-xl text-blue-100 mb-8 max-w-3xl mx-auto\">\n            Every success story started with a single conversation. Let&apos;s discuss how we can help you achieve the growth and results you&apos;re looking for.\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a\n              href=\"tel:+***********\"\n              className=\"bg-white text-blue-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 flex items-center justify-center\"\n            >\n              <Phone className=\"mr-2\" size={20} />\n              Call ****** 628 3793\n            </a>\n            <Link\n              href=\"/free-estimate\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-all duration-300 flex items-center justify-center\"\n            >\n              <Calendar className=\"mr-2\" size={20} />\n              Schedule Free Consultation\n            </Link>\n          </div>\n\n          <div className=\"mt-8 text-blue-100 text-sm\">\n            <p>No long-term contracts • No setup fees • Results-driven approach</p>\n          </div>\n        </div>\n      </div>\n    </section>\n\n  );\n};\n\nexport default CaseStudiesCTASection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,wBAAkC;IAEtC,MAAM,eAAe;QACnB;YACE,MAAM,2MAAA,CAAA,aAAU;YAChB,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,6LAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,6LAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,qKAAC;QAAQ,WAAU;kBACjB,cAAA,qKAAC;YAAI,WAAU;;8BAEb,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,qKAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAMzE,qKAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,MAAM;wBACvB,MAAM,gBAAgB,KAAK,IAAI;wBAC/B,qBACE,qKAAC;4BAAgB,WAAU;;8CACzB,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAc,WAAU;wCAAgB,MAAM;;;;;;;;;;;8CAEjD,qKAAC;oCAAI,WAAU;8CAAyC,KAAK,KAAK;;;;;;8CAClE,qKAAC;oCAAI,WAAU;8CAA4C,KAAK,KAAK;;;;;;8CACrE,qKAAC;oCAAI,WAAU;8CAAiB,KAAK,WAAW;;;;;;;2BANxC;;;;;oBASd;;;;;;8BAIF,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;0CACb,qKAAC,qHAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,qKAAC,6LAAA,CAAA,QAAK;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;0CAGtC,qKAAC,qHAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,qKAAC,mMAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;;;;;;;;;;;;8BAO7C,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,qKAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,qKAAC,6LAAA,CAAA,QAAK;4CAAC,WAAU;4CAAO,MAAM;;;;;;wCAAM;;;;;;;8CAGtC,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,qKAAC,mMAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAO,MAAM;;;;;;wCAAM;;;;;;;;;;;;;sCAK3C,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 3685, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/pages/case-studies.tsx"], "sourcesContent": ["import React from 'react';\nimport { GetStaticProps } from 'next';\nimport Head from 'next/head';\nimport Link from 'next/link';\nimport Header from '@/components/global/Header';\nimport Footer from '@/components/global/Footer';\nimport { getAllCaseStudies, CaseStudy } from '@/lib/sanity-case-studies';\n\n// Import case studies section components\nimport CaseStudiesArchiveSection from '@/components/case-studies/CaseStudiesArchiveSection';\nimport CaseStudiesCTASection from '@/components/case-studies/CaseStudiesCTASection';\n\ninterface CaseStudiesPageProps {\n  caseStudies: CaseStudy[];\n}\n\nconst CaseStudiesPage: React.FC<CaseStudiesPageProps> = ({ caseStudies }) => {\n  return (\n    <>\n      <Head>\n        <title>Client Success Stories & Case Studies | VESA Solutions</title>\n        <meta name=\"description\" content=\"Discover how VESA Solutions has helped businesses achieve remarkable growth through our digital marketing strategies. Real results, real clients, real success stories.\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n        <meta name=\"keywords\" content=\"case studies, client success stories, digital marketing results, SEO case studies, web development portfolio, VESA Solutions clients\" />\n        \n        {/* Open Graph */}\n        <meta property=\"og:title\" content=\"Client Success Stories & Case Studies | VESA Solutions\" />\n        <meta property=\"og:description\" content=\"Discover how VESA Solutions has helped businesses achieve remarkable growth through our digital marketing strategies.\" />\n        <meta property=\"og:type\" content=\"website\" />\n        <meta property=\"og:url\" content=\"https://vesasolutions.com/case-studies\" />\n        \n        {/* Twitter Card */}\n        <meta name=\"twitter:card\" content=\"summary_large_image\" />\n        <meta name=\"twitter:title\" content=\"Client Success Stories & Case Studies | VESA Solutions\" />\n        <meta name=\"twitter:description\" content=\"Discover how VESA Solutions has helped businesses achieve remarkable growth through our digital marketing strategies.\" />\n        \n        {/* Structured Data */}\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify({\n              \"@context\": \"https://schema.org\",\n              \"@type\": \"CollectionPage\",\n              \"name\": \"Case Studies\",\n              \"description\": \"Client success stories and case studies from VESA Solutions\",\n              \"url\": \"https://vesasolutions.com/case-studies\",\n              \"publisher\": {\n                \"@type\": \"Organization\",\n                \"name\": \"VESA Solutions\",\n                \"url\": \"https://vesasolutions.com\"\n              }\n            })\n          }}\n        />\n      </Head>\n      \n      <Header />\n      \n      {/* Breadcrumbs */}\n      <div className=\"bg-gray-50 py-4\">\n        <div className=\"max-w-7xl mx-auto px-6\">\n          <nav className=\"text-sm\" aria-label=\"Breadcrumb\">\n            <ol className=\"flex items-center space-x-2\">\n              <li>\n                <Link href=\"/\" className=\"text-gray-500 hover:text-blue-600 transition-colors\">\n                  Home\n                </Link>\n              </li>\n              <li className=\"text-gray-400\">/</li>\n              <li className=\"text-gray-900 font-medium\" aria-current=\"page\">\n                Case Studies\n              </li>\n            </ol>\n          </nav>\n        </div>\n      </div>\n      \n      <main className=\"min-h-screen bg-white\">\n        {/* Case Studies Archive */}\n        <CaseStudiesArchiveSection caseStudies={caseStudies} />\n        <CaseStudiesCTASection />\n      </main>\n      \n      <Footer />\n    </>\n  );\n};\n\nexport const getStaticProps: GetStaticProps = async () => {\n  const caseStudies = await getAllCaseStudies();\n\n  return {\n    props: {\n      caseStudies,\n    },\n    revalidate: 60, // Revalidate every minute\n  };\n};\n\nexport default CaseStudiesPage;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAEA,yCAAyC;AACzC;AACA;;;;;;;;;;;;;;AAMA,MAAM,kBAAkD,CAAC,EAAE,WAAW,EAAE;IACtE,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,qKAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,qKAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAG9B,qKAAC;wBAAK,UAAS;wBAAW,SAAQ;;;;;;kCAClC,qKAAC;wBAAK,UAAS;wBAAiB,SAAQ;;;;;;kCACxC,qKAAC;wBAAK,UAAS;wBAAU,SAAQ;;;;;;kCACjC,qKAAC;wBAAK,UAAS;wBAAS,SAAQ;;;;;;kCAGhC,qKAAC;wBAAK,MAAK;wBAAe,SAAQ;;;;;;kCAClC,qKAAC;wBAAK,MAAK;wBAAgB,SAAQ;;;;;;kCACnC,qKAAC;wBAAK,MAAK;wBAAsB,SAAQ;;;;;;kCAGzC,qKAAC;wBACC,MAAK;wBACL,yBAAyB;4BACvB,QAAQ,KAAK,SAAS,CAAC;gCACrB,YAAY;gCACZ,SAAS;gCACT,QAAQ;gCACR,eAAe;gCACf,OAAO;gCACP,aAAa;oCACX,SAAS;oCACT,QAAQ;oCACR,OAAO;gCACT;4BACF;wBACF;;;;;;;;;;;;0BAIJ,qKAAC,wHAAA,CAAA,UAAM;;;;;0BAGP,qKAAC;gBAAI,WAAU;0BACb,cAAA,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;wBAAU,cAAW;kCAClC,cAAA,qKAAC;4BAAG,WAAU;;8CACZ,qKAAC;8CACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAsD;;;;;;;;;;;8CAIjF,qKAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,qKAAC;oCAAG,WAAU;oCAA4B,gBAAa;8CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtE,qKAAC;gBAAK,WAAU;;kCAEd,qKAAC,oJAAA,CAAA,UAAyB;wBAAC,aAAa;;;;;;kCACxC,qKAAC,gJAAA,CAAA,UAAqB;;;;;;;;;;;0BAGxB,qKAAC,wHAAA,CAAA,UAAM;;;;;;;AAGb;AAEO,MAAM,iBAAiC;IAC5C,MAAM,cAAc,MAAM,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD;IAE1C,OAAO;QACL,OAAO;YACL;QACF;QACA,YAAY;IACd;AACF;uCAEe", "debugId": null}}]}