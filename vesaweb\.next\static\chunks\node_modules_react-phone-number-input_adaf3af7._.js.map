{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "formatPhoneNumber.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/libphonenumber/formatPhoneNumber.js"], "sourcesContent": ["import parsePhoneNumber from 'libphonenumber-js/core'\r\n\r\n/**\r\n * Formats a phone number.\r\n * Is a proxy for `libphonenumber-js`'s `.format()` function of a parsed `PhoneNumber`.\r\n * @param  {string} value\r\n * @param  {string} [format]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\r\nexport default function formatPhoneNumber(value, format, metadata) {\r\n\tif (!metadata) {\r\n\t\tif (typeof format === 'object') {\r\n\t\t\tmetadata = format\r\n\t\t\tformat = 'NATIONAL'\r\n\t\t}\r\n\t}\r\n\tif (!value) {\r\n\t\treturn ''\r\n\t}\r\n\tconst phoneNumber = parsePhoneNumber(value, metadata)\r\n\tif (!phoneNumber) {\r\n\t\treturn ''\r\n\t}\r\n\t// Deprecated.\r\n\t// Legacy `format`s.\r\n\tswitch (format) {\r\n\t\tcase 'National':\r\n\t\t\tformat = 'NATIONAL'\r\n\t\t\tbreak\r\n\t\tcase 'International':\r\n\t\t\tformat = 'INTERNATIONAL'\r\n\t\t\tbreak\r\n\t}\r\n\treturn phoneNumber.format(format)\r\n}\r\n\r\nexport function formatPhoneNumberIntl(value, metadata) {\r\n\treturn formatPhoneNumber(value, 'INTERNATIONAL', metadata)\r\n}"], "names": ["parsePhoneNumber", "formatPhoneNumber", "value", "format", "metadata", "_typeof", "phoneNumber", "formatPhoneNumberIntl"], "mappings": ";;;;AAAA,OAAOA,gBAAgB,MAAM,wBAAwB;;;;;;;;;;AAUtC,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAE;IAClE,IAAI,CAACA,QAAQ,EAAE;QACd,IAAIC,OAAA,CAAOF,MAAM,MAAK,QAAQ,EAAE;YAC/BC,QAAQ,GAAGD,MAAM;YACjBA,MAAM,GAAG,UAAU;QACpB;IACD;IACA,IAAI,CAACD,KAAK,EAAE;QACX,OAAO,EAAE;IACV;IACA,IAAMI,WAAW,mKAAGN,UAAAA,AAAgB,EAACE,KAAK,EAAEE,QAAQ,CAAC;IACrD,IAAI,CAACE,WAAW,EAAE;QACjB,OAAO,EAAE;IACV;IACA,cAAA;IACA,oBAAA;IACA,OAAQH,MAAM;QACb,KAAK,UAAU;YACdA,MAAM,GAAG,UAAU;YACnB;QACD,KAAK,eAAe;YACnBA,MAAM,GAAG,eAAe;YACxB;IACF;IACA,OAAOG,WAAW,CAACH,MAAM,CAACA,MAAM,CAAC;AAClC;AAEO,SAASI,qBAAqBA,CAACL,KAAK,EAAEE,QAAQ,EAAE;IACtD,OAAOH,iBAAiB,CAACC,KAAK,EAAE,eAAe,EAAEE,QAAQ,CAAC;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/locale/en.json.js"], "sourcesContent": ["export default {\n  \"ext\": \"ext.\",\n  \"country\": \"Phone number country\",\n  \"phone\": \"Phone\",\n  \"AB\": \"Abkhazia\",\n  \"AC\": \"Ascension Island\",\n  \"AD\": \"Andorra\",\n  \"AE\": \"United Arab Emirates\",\n  \"AF\": \"Afghanistan\",\n  \"AG\": \"Antigua and Barbuda\",\n  \"AI\": \"Anguilla\",\n  \"AL\": \"Albania\",\n  \"AM\": \"Armenia\",\n  \"AO\": \"Angola\",\n  \"AQ\": \"Antarctica\",\n  \"AR\": \"Argentina\",\n  \"AS\": \"American Samoa\",\n  \"AT\": \"Austria\",\n  \"AU\": \"Australia\",\n  \"AW\": \"Aruba\",\n  \"AX\": \"Åland Islands\",\n  \"AZ\": \"Azerbaijan\",\n  \"BA\": \"Bosnia and Herzegovina\",\n  \"BB\": \"Barbados\",\n  \"BD\": \"Bangladesh\",\n  \"BE\": \"Belgium\",\n  \"BF\": \"Burkina Faso\",\n  \"BG\": \"Bulgaria\",\n  \"BH\": \"Bahrain\",\n  \"BI\": \"Burundi\",\n  \"BJ\": \"Benin\",\n  \"BL\": \"Saint Barthélemy\",\n  \"BM\": \"Bermuda\",\n  \"BN\": \"Brunei Darussalam\",\n  \"BO\": \"Bolivia\",\n  \"BQ\": \"Bonaire, Sint Eustatius and Saba\",\n  \"BR\": \"Brazil\",\n  \"BS\": \"Bahamas\",\n  \"BT\": \"Bhutan\",\n  \"BV\": \"Bouvet Island\",\n  \"BW\": \"Botswana\",\n  \"BY\": \"Belarus\",\n  \"BZ\": \"Belize\",\n  \"CA\": \"Canada\",\n  \"CC\": \"Cocos (Keeling) Islands\",\n  \"CD\": \"Congo, Democratic Republic of the\",\n  \"CF\": \"Central African Republic\",\n  \"CG\": \"Congo\",\n  \"CH\": \"Switzerland\",\n  \"CI\": \"Cote d'Ivoire\",\n  \"CK\": \"Cook Islands\",\n  \"CL\": \"Chile\",\n  \"CM\": \"Cameroon\",\n  \"CN\": \"China\",\n  \"CO\": \"Colombia\",\n  \"CR\": \"Costa Rica\",\n  \"CU\": \"Cuba\",\n  \"CV\": \"Cape Verde\",\n  \"CW\": \"Curaçao\",\n  \"CX\": \"Christmas Island\",\n  \"CY\": \"Cyprus\",\n  \"CZ\": \"Czech Republic\",\n  \"DE\": \"Germany\",\n  \"DJ\": \"Djibouti\",\n  \"DK\": \"Denmark\",\n  \"DM\": \"Dominica\",\n  \"DO\": \"Dominican Republic\",\n  \"DZ\": \"Algeria\",\n  \"EC\": \"Ecuador\",\n  \"EE\": \"Estonia\",\n  \"EG\": \"Egypt\",\n  \"EH\": \"Western Sahara\",\n  \"ER\": \"Eritrea\",\n  \"ES\": \"Spain\",\n  \"ET\": \"Ethiopia\",\n  \"FI\": \"Finland\",\n  \"FJ\": \"Fiji\",\n  \"FK\": \"Falkland Islands\",\n  \"FM\": \"Federated States of Micronesia\",\n  \"FO\": \"Faroe Islands\",\n  \"FR\": \"France\",\n  \"GA\": \"Gabon\",\n  \"GB\": \"United Kingdom\",\n  \"GD\": \"Grenada\",\n  \"GE\": \"Georgia\",\n  \"GF\": \"French Guiana\",\n  \"GG\": \"Guernsey\",\n  \"GH\": \"Ghana\",\n  \"GI\": \"Gibraltar\",\n  \"GL\": \"Greenland\",\n  \"GM\": \"Gambia\",\n  \"GN\": \"Guinea\",\n  \"GP\": \"Guadeloupe\",\n  \"GQ\": \"Equatorial Guinea\",\n  \"GR\": \"Greece\",\n  \"GS\": \"South Georgia and the South Sandwich Islands\",\n  \"GT\": \"Guatemala\",\n  \"GU\": \"Guam\",\n  \"GW\": \"Guinea-Bissau\",\n  \"GY\": \"Guyana\",\n  \"HK\": \"Hong Kong\",\n  \"HM\": \"Heard Island and McDonald Islands\",\n  \"HN\": \"Honduras\",\n  \"HR\": \"Croatia\",\n  \"HT\": \"Haiti\",\n  \"HU\": \"Hungary\",\n  \"ID\": \"Indonesia\",\n  \"IE\": \"Ireland\",\n  \"IL\": \"Israel\",\n  \"IM\": \"Isle of Man\",\n  \"IN\": \"India\",\n  \"IO\": \"British Indian Ocean Territory\",\n  \"IQ\": \"Iraq\",\n  \"IR\": \"Iran\",\n  \"IS\": \"Iceland\",\n  \"IT\": \"Italy\",\n  \"JE\": \"Jersey\",\n  \"JM\": \"Jamaica\",\n  \"JO\": \"Jordan\",\n  \"JP\": \"Japan\",\n  \"KE\": \"Kenya\",\n  \"KG\": \"Kyrgyzstan\",\n  \"KH\": \"Cambodia\",\n  \"KI\": \"Kiribati\",\n  \"KM\": \"Comoros\",\n  \"KN\": \"Saint Kitts and Nevis\",\n  \"KP\": \"North Korea\",\n  \"KR\": \"South Korea\",\n  \"KW\": \"Kuwait\",\n  \"KY\": \"Cayman Islands\",\n  \"KZ\": \"Kazakhstan\",\n  \"LA\": \"Laos\",\n  \"LB\": \"Lebanon\",\n  \"LC\": \"Saint Lucia\",\n  \"LI\": \"Liechtenstein\",\n  \"LK\": \"Sri Lanka\",\n  \"LR\": \"Liberia\",\n  \"LS\": \"Lesotho\",\n  \"LT\": \"Lithuania\",\n  \"LU\": \"Luxembourg\",\n  \"LV\": \"Latvia\",\n  \"LY\": \"Libya\",\n  \"MA\": \"Morocco\",\n  \"MC\": \"Monaco\",\n  \"MD\": \"Moldova\",\n  \"ME\": \"Montenegro\",\n  \"MF\": \"Saint Martin (French Part)\",\n  \"MG\": \"Madagascar\",\n  \"MH\": \"Marshall Islands\",\n  \"MK\": \"North Macedonia\",\n  \"ML\": \"Mali\",\n  \"MM\": \"Myanmar\",\n  \"MN\": \"Mongolia\",\n  \"MO\": \"Macao\",\n  \"MP\": \"Northern Mariana Islands\",\n  \"MQ\": \"Martinique\",\n  \"MR\": \"Mauritania\",\n  \"MS\": \"Montserrat\",\n  \"MT\": \"Malta\",\n  \"MU\": \"Mauritius\",\n  \"MV\": \"Maldives\",\n  \"MW\": \"Malawi\",\n  \"MX\": \"Mexico\",\n  \"MY\": \"Malaysia\",\n  \"MZ\": \"Mozambique\",\n  \"NA\": \"Namibia\",\n  \"NC\": \"New Caledonia\",\n  \"NE\": \"Niger\",\n  \"NF\": \"Norfolk Island\",\n  \"NG\": \"Nigeria\",\n  \"NI\": \"Nicaragua\",\n  \"NL\": \"Netherlands\",\n  \"NO\": \"Norway\",\n  \"NP\": \"Nepal\",\n  \"NR\": \"Nauru\",\n  \"NU\": \"Niue\",\n  \"NZ\": \"New Zealand\",\n  \"OM\": \"Oman\",\n  \"OS\": \"South Ossetia\",\n  \"PA\": \"Panama\",\n  \"PE\": \"Peru\",\n  \"PF\": \"French Polynesia\",\n  \"PG\": \"Papua New Guinea\",\n  \"PH\": \"Philippines\",\n  \"PK\": \"Pakistan\",\n  \"PL\": \"Poland\",\n  \"PM\": \"Saint Pierre and Miquelon\",\n  \"PN\": \"Pitcairn\",\n  \"PR\": \"Puerto Rico\",\n  \"PS\": \"Palestine\",\n  \"PT\": \"Portugal\",\n  \"PW\": \"Palau\",\n  \"PY\": \"Paraguay\",\n  \"QA\": \"Qatar\",\n  \"RE\": \"Reunion\",\n  \"RO\": \"Romania\",\n  \"RS\": \"Serbia\",\n  \"RU\": \"Russia\",\n  \"RW\": \"Rwanda\",\n  \"SA\": \"Saudi Arabia\",\n  \"SB\": \"Solomon Islands\",\n  \"SC\": \"Seychelles\",\n  \"SD\": \"Sudan\",\n  \"SE\": \"Sweden\",\n  \"SG\": \"Singapore\",\n  \"SH\": \"Saint Helena\",\n  \"SI\": \"Slovenia\",\n  \"SJ\": \"Svalbard and Jan Mayen\",\n  \"SK\": \"Slovakia\",\n  \"SL\": \"Sierra Leone\",\n  \"SM\": \"San Marino\",\n  \"SN\": \"Senegal\",\n  \"SO\": \"Somalia\",\n  \"SR\": \"Suriname\",\n  \"SS\": \"South Sudan\",\n  \"ST\": \"Sao Tome and Principe\",\n  \"SV\": \"El Salvador\",\n  \"SX\": \"Sint Maarten\",\n  \"SY\": \"Syria\",\n  \"SZ\": \"Swaziland\",\n  \"TA\": \"Tristan da Cunha\",\n  \"TC\": \"Turks and Caicos Islands\",\n  \"TD\": \"Chad\",\n  \"TF\": \"French Southern Territories\",\n  \"TG\": \"Togo\",\n  \"TH\": \"Thailand\",\n  \"TJ\": \"Tajikistan\",\n  \"TK\": \"Tokelau\",\n  \"TL\": \"Timor-Leste\",\n  \"TM\": \"Turkmenistan\",\n  \"TN\": \"Tunisia\",\n  \"TO\": \"Tonga\",\n  \"TR\": \"Turkey\",\n  \"TT\": \"Trinidad and Tobago\",\n  \"TV\": \"Tuvalu\",\n  \"TW\": \"Taiwan\",\n  \"TZ\": \"Tanzania\",\n  \"UA\": \"Ukraine\",\n  \"UG\": \"Uganda\",\n  \"UM\": \"United States Minor Outlying Islands\",\n  \"US\": \"United States\",\n  \"UY\": \"Uruguay\",\n  \"UZ\": \"Uzbekistan\",\n  \"VA\": \"Holy See (Vatican City State)\",\n  \"VC\": \"Saint Vincent and the Grenadines\",\n  \"VE\": \"Venezuela\",\n  \"VG\": \"Virgin Islands, British\",\n  \"VI\": \"Virgin Islands, U.S.\",\n  \"VN\": \"Vietnam\",\n  \"VU\": \"Vanuatu\",\n  \"WF\": \"Wallis and Futuna\",\n  \"WS\": \"Samoa\",\n  \"XK\": \"Kosovo\",\n  \"YE\": \"Yemen\",\n  \"YT\": \"Mayotte\",\n  \"ZA\": \"South Africa\",\n  \"ZM\": \"Zambia\",\n  \"ZW\": \"Zimbabwe\",\n  \"ZZ\": \"International\"\n}"], "names": [], "mappings": ";;;uCAAe;IACb,OAAO;IACP,WAAW;IACX,SAAS;IACT,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IAC<PERSON>,MAAM;<PERSON><PERSON><PERSON>,MAAM;IAC<PERSON>,MAAM;IAC<PERSON>,MAAM;IAC<PERSON>,MAAM;IAC<PERSON>,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "file": "PropTypes.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/PropTypes.js"], "sourcesContent": ["import PropTypes from 'prop-types'\r\n\r\nexport const metadata = PropTypes.shape({\r\n\tcountry_calling_codes : PropTypes.object.isRequired,\r\n\tcountries : PropTypes.object.isRequired\r\n})\r\n\r\nexport const labels = PropTypes.objectOf(PropTypes.string)"], "names": ["PropTypes", "metadata", "shape", "country_calling_codes", "object", "isRequired", "countries", "labels", "objectOf", "string"], "mappings": ";;;;AAAA,OAAOA,SAAS,MAAM,YAAY;;AAE3B,IAAMC,QAAQ,sIAAGD,UAAS,CAACE,KAAK,CAAC;IACvCC,qBAAqB,qIAAGH,UAAS,CAACI,MAAM,CAACC,UAAU;IACnDC,SAAS,qIAAGN,UAAS,CAACI,MAAM,CAACC,UAAAA;AAC9B,CAAC,CAAC;AAEK,IAAME,MAAM,sIAAGP,UAAS,CAACQ,QAAQ,oIAACR,UAAS,CAACS,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "file": "inputValuePrefix.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/helpers/inputValuePrefix.js"], "sourcesContent": ["import { getCountryCallingCode } from 'libphonenumber-js/core'\r\n\r\nexport function getPrefixForFormattingValueAsPhoneNumber({\r\n\tinputFormat,\r\n\tcountry,\r\n\tmetadata\r\n}) {\r\n\treturn inputFormat === 'NATIONAL_PART_OF_INTERNATIONAL' ?\r\n\t\t`+${getCountryCallingCode(country, metadata)}` :\r\n\t\t''\r\n}\r\n\r\nexport function removePrefixFromFormattedPhoneNumber(value, prefix) {\r\n\tif (prefix) {\r\n\t\tvalue = value.slice(prefix.length)\r\n\t\tif (value[0] === ' ') {\r\n\t\t\tvalue = value.slice(1)\r\n\t\t}\r\n\t}\r\n\treturn value\r\n}"], "names": ["getCountryCallingCode", "getPrefixForFormattingValueAsPhoneNumber", "_ref", "inputFormat", "country", "metadata", "concat", "removePrefixFromFormattedPhoneNumber", "value", "prefix", "slice", "length"], "mappings": ";;;;AAAA,SAASA,qBAAqB,QAAQ,wBAAwB;;AAEvD,SAASC,wCAAwCA,CAAAC,IAAA,EAIrD;IAAA,IAHFC,WAAW,GAAAD,IAAA,CAAXC,WAAW,EACXC,OAAO,GAAAF,IAAA,CAAPE,OAAO,EACPC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;IAER,OAAOF,WAAW,KAAK,gCAAgC,GAAA,IAAAG,MAAA,yJAClDN,wBAAAA,AAAqB,EAACI,OAAO,EAAEC,QAAQ,CAAC,IAC5C,EAAE;AACJ;AAEO,SAASE,oCAAoCA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACnE,IAAIA,MAAM,EAAE;QACXD,KAAK,GAAGA,KAAK,CAACE,KAAK,CAACD,MAAM,CAACE,MAAM,CAAC;QAClC,IAAIH,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACrBA,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;QACvB;IACD;IACA,OAAOF,KAAK;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "file": "parsePhoneNumberCharacter.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/helpers/parsePhoneNumberCharacter.js"], "sourcesContent": ["import { parsePhone<PERSON>umberCharacter } from 'libphonenumber-js/core'\r\n\r\n/**\r\n * Parses next character while parsing phone number digits (including a `+`)\r\n * from text: discards everything except `+` and digits, and `+` is only allowed\r\n * at the start of a phone number.\r\n * For example, is used in `react-phone-number-input` where it uses\r\n * [`input-format`](https://gitlab.com/catamphetamine/input-format).\r\n * @param  {string} character - Yet another character from raw input string.\r\n * @param  {string?} prevParsedCharacters - Previous parsed characters.\r\n * @param  {object?} context - An optional object that could be used by this function to set arbitrary \"flags\". The object should be shared within the parsing of the whole string.\r\n * @return {string?} The parsed character.\r\n */\r\nexport default function parsePhoneNumber<PERSON>haracter_(character, prevParsedCharacters, context) {\r\n\t// `context` argument was added as a third argument of `parse()` function\r\n\t// in `input-format` package on Dec 26th, 2023. So it could potentially be\r\n\t// `undefined` here if a 3rd-party app somehow ends up with this newer version\r\n\t// of `react-phone-number-input` and an older version of `input-format`.\r\n\t// Dunno how, but just in case, it could be `undefined` here and it wouldn't break.\r\n\t// Maybe it's not required to handle `undefined` case here.\r\n\t//\r\n\t// The addition of the `context` argument was to fix the slightly-weird behavior\r\n\t// of parsing an input string when the user inputs something like `\"2+7\"\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/437\r\n\t//\r\n\t// If the parser encounters an unexpected `+` in a string being parsed\r\n\t// then it simply discards that out-of-place `+` and any following characters.\r\n\t//\r\n\tif (context && context.ignoreRest) {\r\n\t\treturn\r\n\t}\r\n\r\n\tconst emitEvent = (eventName) => {\r\n\t\tif (context) {\r\n\t\t\tswitch (eventName) {\r\n\t\t\t\tcase 'end':\r\n\t\t\t\t\tcontext.ignoreRest = true\r\n\t\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn parsePhoneNumberCharacter(character, prevParsedCharacters, emitEvent)\r\n}"], "names": ["parsePhoneNumberCharacter", "parsePhoneNumberCharacter_", "character", "prevParsedCharacters", "context", "ignoreRest", "emitEvent", "eventName"], "mappings": ";;;AAAA,SAASA,yBAAyB,QAAQ,wBAAwB;;AAanD,SAASC,0BAA0BA,CAACC,SAAS,EAAEC,oBAAoB,EAAEC,OAAO,EAAE;IAC5F,yEAAA;IACA,0EAAA;IACA,8EAAA;IACA,wEAAA;IACA,mFAAA;IACA,2DAAA;IACA,EAAA;IACA,gFAAA;IACA,wEAAA;IACA,wEAAA;IACA,EAAA;IACA,sEAAA;IACA,8EAAA;IACA,EAAA;IACA,IAAIA,OAAO,IAAIA,OAAO,CAACC,UAAU,EAAE;QAClC;IACD;IAEA,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIC,SAAS,EAAK;QAChC,IAAIH,OAAO,EAAE;YACZ,OAAQG,SAAS;gBAChB,KAAK,KAAK;oBACTH,OAAO,CAACC,UAAU,GAAG,IAAI;oBACzB;YACF;QACD;IACD,CAAC;IAED,iLAAOL,4BAAAA,AAAyB,EAACE,SAAS,EAAEC,oBAAoB,EAAEG,SAAS,CAAC;AAC7E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "file": "useInputKeyDownHandler.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/useInputKeyDownHandler.js"], "sourcesContent": ["import { useCallback } from 'react'\r\n\r\n// Returns a custom `onKeyDown` handler that works around a Backspace keypress edge case:\r\n// * `<PhoneInputWithCountrySelect international countryCallingCodeEditable={false}/>`\r\n// * When placing the caret before the leading plus character and pressing Backspace,\r\n//   it duplicates the country calling code in the `<input/>`.\r\n// https://github.com/catamphetamine/react-phone-number-input/issues/442\r\nexport default function useInputKeyDownHandler({\r\n\tonKeyDown,\r\n\tinputFormat\r\n}) {\r\n\treturn useCallback((event) => {\r\n\t\t// Usability:\r\n\t\t// Don't allow the user to erase a leading \"+\" character when \"international\" input mode is forced.\r\n\t\t// That indicates to the user that they can't possibly enter the phone number in a non-international format.\r\n\t\tif (event.keyCode === BACKSPACE_KEY_CODE && inputFormat === 'INTERNATIONAL') {\r\n\t\t\t// It checks `event.target` here for being an `<input/>` element\r\n\t\t\t// because \"keydown\" events may bubble from arbitrary child elements\r\n\t\t\t// so there's no guarantee that `event.target` represents an `<input/>` element.\r\n\t\t\t// Also, since `inputComponent` is not neceesarily an `<input/>`, this check is required too.\r\n\t\t\tif (event.target instanceof HTMLInputElement) {\r\n\t\t\t\tif (getCaretPosition(event.target) === LEADING_PLUS.length) {\r\n\t\t\t\t\tevent.preventDefault()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (onKeyDown) {\r\n\t\t\tonKeyDown(event)\r\n\t\t}\r\n\t}, [\r\n\t\tonKeyDown,\r\n\t\tinputFormat\r\n\t])\r\n}\r\n\r\n// Gets the caret position in an `<input/>` field.\r\n// The caret position starts with `0` which means \"before the first character\".\r\nfunction getCaretPosition(element) {\r\n\treturn element.selectionStart\r\n}\r\n\r\nconst BACKSPACE_KEY_CODE = 8\r\n\r\nconst LEADING_PLUS = '+'"], "names": ["useCallback", "useInputKeyDownHandler", "_ref", "onKeyDown", "inputFormat", "event", "keyCode", "BACKSPACE_KEY_CODE", "target", "HTMLInputElement", "getCaretPosition", "LEADING_PLUS", "length", "preventDefault", "element", "selectionStart"], "mappings": ";;;AAAA,SAASA,WAAW,QAAQ,OAAO;;AAOpB,SAASC,sBAAsBA,CAAAC,IAAA,EAG3C;IAAA,IAFFC,SAAS,GAAAD,IAAA,CAATC,SAAS,EACTC,WAAW,GAAAF,IAAA,CAAXE,WAAW;IAEX,sIAAOJ,cAAAA,AAAW;8CAAC,SAACK,KAAK,EAAK;YAC7B,aAAA;YACA,mGAAA;YACA,4GAAA;YACA,IAAIA,KAAK,CAACC,OAAO,KAAKC,kBAAkB,IAAIH,WAAW,KAAK,eAAe,EAAE;gBAC5E,gEAAA;gBACA,oEAAA;gBACA,gFAAA;gBACA,6FAAA;gBACA,IAAIC,KAAK,CAACG,MAAM,YAAYC,gBAAgB,EAAE;oBAC7C,IAAIC,gBAAgB,CAACL,KAAK,CAACG,MAAM,CAAC,KAAKG,YAAY,CAACC,MAAM,EAAE;wBAC3DP,KAAK,CAACQ,cAAc,CAAC,CAAC;wBACtB;oBACD;gBACD;YACD;YACA,IAAIV,SAAS,EAAE;gBACdA,SAAS,CAACE,KAAK,CAAC;YACjB;QACD,CAAC;6CAAE;QACFF,SAAS;QACTC,WAAW;KACX,CAAC;AACH;AAEA,kDAAA;AACA,+EAAA;AACA,SAASM,gBAAgBA,CAACI,OAAO,EAAE;IAClC,OAAOA,OAAO,CAACC,cAAc;AAC9B;AAEA,IAAMR,kBAAkB,GAAG,CAAC;AAE5B,IAAMI,YAAY,GAAG,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "file": "InputSmart.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/InputSmart.js"], "sourcesContent": ["import React, { useCallback } from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport Input from 'input-format/react'\r\nimport { AsYouType } from 'libphonenumber-js/core'\r\n\r\nimport { getPrefixForFormattingValueAsPhoneNumber, removePrefixFromFormattedPhoneNumber } from './helpers/inputValuePrefix.js'\r\nimport parsePhoneNumberCharacter from './helpers/parsePhoneNumberCharacter.js'\r\n\r\nimport useInputKeyDownHandler from './useInputKeyDownHandler.js'\r\n\r\nexport function createInput(defaultMetadata)\r\n{\r\n\t/**\r\n\t * `InputSmart` is a \"smarter\" implementation of a `Component`\r\n\t * that can be passed to `<PhoneInput/>`. It parses and formats\r\n\t * the user's and maintains the caret's position in the process.\r\n\t * The caret positioning is maintained using `input-format` library.\r\n\t * Relies on being run in a DOM environment for calling caret positioning functions.\r\n\t */\r\n\tfunction InputSmart({\r\n\t\tonKeyDown,\r\n\t\tcountry,\r\n\t\tinputFormat,\r\n\t\tmetadata = defaultMetadata,\r\n\t\t//\r\n\t\t// The rest of the properties listed here are just to get the `rest` props\r\n\t\t// that will be passed to the DOM `<input/>` element.\r\n\t\t//\r\n\t\t// `international` property is deprecated and is not used.\r\n\t\tinternational,\r\n\t\t// `withCountryCallingCode` property is deprecated and is not used.\r\n\t\twithCountryCallingCode,\r\n\t\t...rest\r\n\t}, ref) {\r\n\t\tconst format = useCallback((value) => {\r\n\t\t\t// \"As you type\" formatter.\r\n\t\t\tconst formatter = new AsYouType(country, metadata)\r\n\r\n\t\t\tconst prefix = getPrefixForFormattingValueAsPhoneNumber({\r\n\t\t\t\tinputFormat,\r\n\t\t\t\tcountry,\r\n\t\t\t\tmetadata\r\n\t\t\t})\r\n\r\n\t\t\t// Format the number.\r\n\t\t\tlet text = formatter.input(prefix + value)\r\n\t\t\tlet template = formatter.getTemplate()\r\n\r\n\t\t\tif (prefix) {\r\n\t\t\t\ttext = removePrefixFromFormattedPhoneNumber(text, prefix)\r\n\t\t\t\t// `AsYouType.getTemplate()` can be `undefined`.\r\n\t\t\t\tif (template) {\r\n\t\t\t\t\ttemplate = removePrefixFromFormattedPhoneNumber(template, prefix)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn {\r\n\t\t\t\ttext,\r\n\t\t\t\ttemplate\r\n\t\t\t}\r\n\t\t}, [\r\n\t\t\tcountry,\r\n\t\t\tmetadata\r\n\t\t])\r\n\r\n\t\tconst _onKeyDown = useInputKeyDownHandler({\r\n\t\t\tonKeyDown,\r\n\t\t\tinputFormat\r\n\t\t})\r\n\r\n\t\treturn (\r\n\t\t\t<Input\r\n\t\t\t\t{...rest}\r\n\t\t\t\tref={ref}\r\n\t\t\t\tparse={parsePhoneNumberCharacter}\r\n\t\t\t\tformat={format}\r\n\t\t\t\tonKeyDown={_onKeyDown}\r\n\t\t\t/>\r\n\t\t)\r\n\t}\r\n\r\n\tInputSmart = React.forwardRef(InputSmart)\r\n\r\n\tInputSmart.propTypes = {\r\n\t\t/**\r\n\t\t * The parsed phone number.\r\n\t\t * \"Parsed\" not in a sense of \"E.164\"\r\n\t\t * but rather in a sense of \"having only\r\n\t\t * digits and possibly a leading plus character\".\r\n\t\t * Examples: `\"\"`, `\"+\"`, `\"+123\"`, `\"123\"`.\r\n\t\t */\r\n\t\tvalue: PropTypes.string.isRequired,\r\n\r\n\t\t/**\r\n\t\t * A function of `value: string`.\r\n\t\t * Updates the `value` property.\r\n\t\t */\r\n\t\tonChange: PropTypes.func.isRequired,\r\n\r\n\t\t/**\r\n\t\t * A function of `event: Event`.\r\n\t\t * Handles `keydown` events.\r\n\t\t */\r\n\t\tonKeyDown: PropTypes.func,\r\n\r\n\t\t/**\r\n\t\t * A two-letter country code for formatting `value`\r\n\t\t * as a national phone number (e.g. `(800) 555 35 35`).\r\n\t\t * E.g. \"US\", \"RU\", etc.\r\n\t\t * If no `country` is passed then `value`\r\n\t\t * is formatted as an international phone number.\r\n\t\t * (e.g. `****** 555 35 35`)\r\n\t\t * This property should've been called `defaultCountry`\r\n\t\t * because it only applies when the user inputs a phone number in a national format\r\n\t\t * and is completely ignored when the user inputs a phone number in an international format.\r\n\t\t */\r\n\t\tcountry: PropTypes.string,\r\n\r\n\t\t/**\r\n\t\t * The format that the input field value is being input/output in.\r\n\t\t */\r\n\t\tinputFormat : PropTypes.oneOf([\r\n\t\t\t'INTERNATIONAL',\r\n\t\t\t'NATIONAL_PART_OF_INTERNATIONAL',\r\n\t\t\t'NATIONAL',\r\n\t\t\t'INTERNATIONAL_OR_NATIONAL'\r\n\t\t]).isRequired,\r\n\r\n\t\t/**\r\n\t\t * `libphonenumber-js` metadata.\r\n\t\t */\r\n\t\tmetadata: PropTypes.object\r\n\t}\r\n\r\n\treturn InputSmart\r\n}\r\n\r\nexport default createInput()"], "names": ["React", "useCallback", "PropTypes", "Input", "AsYouType", "getPrefixForFormattingValueAsPhoneNumber", "removePrefixFromFormattedPhoneNumber", "parsePhoneNumberCharacter", "useInputKeyDownHandler", "createInput", "defaultMetadata", "InputSmart", "_ref", "ref", "onKeyDown", "country", "inputFormat", "_ref$metadata", "metadata", "international", "withCountryCallingCode", "rest", "_objectWithoutProperties", "_excluded", "format", "value", "formatter", "prefix", "text", "input", "template", "getTemplate", "_onKeyDown", "createElement", "_extends", "parse", "forwardRef", "propTypes", "string", "isRequired", "onChange", "func", "oneOf", "object"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,WAAW,QAAQ,OAAO;AAC1C,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,SAASC,SAAS,QAAQ,wBAAwB;AAElD,SAASC,wCAAwC,EAAEC,oCAAoC,QAAQ,+BAA+B;AAC9H,OAAOC,yBAAyB,MAAM,wCAAwC;AAE9E,OAAOC,sBAAsB,MAAM,6BAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzD,SAASC,WAAWA,CAACC,eAAe,EAC3C;IACC;;;;;;GAMD,GACC,SAASC,UAAUA,CAAAC,IAAA,EAchBC,GAAG,EAAE;QAAA,IAbPC,SAAS,GAAAF,IAAA,CAATE,SAAS,EACTC,OAAO,GAAAH,IAAA,CAAPG,OAAO,EACPC,WAAW,GAAAJ,IAAA,CAAXI,WAAW,EAAAC,aAAA,GAAAL,IAAA,CACXM,QAAQ,EAARA,QAAQ,GAAAD,aAAA,KAAA,KAAA,IAAGP,eAAe,GAAAO,aAAA,EAM1BE,aAAa,GAAAP,IAAA,CAAbO,aAAa,EAEbC,sBAAsB,GAAAR,IAAA,CAAtBQ,sBAAsB,EACnBC,IAAI,GAAAC,wBAAA,CAAAV,IAAA,EAAAW,SAAA;QAEP,IAAMC,MAAM,kIAAGvB,cAAAA,AAAW;0DAAC,SAACwB,KAAK,EAAK;gBACrC,2BAAA;gBACA,IAAMC,SAAS,GAAG,iMAAItB,YAAS,CAACW,OAAO,EAAEG,QAAQ,CAAC;gBAElD,IAAMS,MAAM,IAAGtB,sOAAAA,AAAwC,EAAC;oBACvDW,WAAW,EAAXA,WAAW;oBACXD,OAAO,EAAPA,OAAO;oBACPG,QAAQ,EAARA;gBACD,CAAC,CAAC;gBAEF,qBAAA;gBACA,IAAIU,IAAI,GAAGF,SAAS,CAACG,KAAK,CAACF,MAAM,GAAGF,KAAK,CAAC;gBAC1C,IAAIK,QAAQ,GAAGJ,SAAS,CAACK,WAAW,CAAC,CAAC;gBAEtC,IAAIJ,MAAM,EAAE;oBACXC,IAAI,GAAGtB,mOAAAA,AAAoC,EAACsB,IAAI,EAAED,MAAM,CAAC;oBACzD,gDAAA;oBACA,IAAIG,QAAQ,EAAE;wBACbA,QAAQ,+LAAGxB,uCAAAA,AAAoC,EAACwB,QAAQ,EAAEH,MAAM,CAAC;oBAClE;gBACD;gBAEA,OAAO;oBACNC,IAAI,EAAJA,IAAI;oBACJE,QAAQ,EAARA;gBACD,CAAC;YACF,CAAC;yDAAE;YACFf,OAAO;YACPG,QAAQ;SACR,CAAC;QAEF,IAAMc,UAAU,0LAAGxB,UAAAA,AAAsB,EAAC;YACzCM,SAAS,EAATA,SAAS;YACTE,WAAW,EAAXA;QACD,CAAC,CAAC;QAEF,OAAA,WAAA,GACChB,qIAAA,CAAAiC,aAAA,0JAAC9B,UAAK,EAAA+B,QAAA,CAAA,CAAA,GACDb,IAAI,EAAA;YACRR,GAAG,EAAEA,GAAI;YACTsB,KAAK,mMAAE5B,UAA0B;YACjCiB,MAAM,EAAEA,MAAO;YACfV,SAAS,EAAEkB;QAAW,EACtB,CAAC;IAEJ;IAEArB,UAAU,GAAA,WAAA,8HAAGX,UAAK,CAACoC,UAAU,CAACzB,UAAU,CAAC;IAEzCA,UAAU,CAAC0B,SAAS,GAAG;QACtB;;;;;;KAMF,GACEZ,KAAK,qIAAEvB,UAAS,CAACoC,MAAM,CAACC,UAAU;QAElC;;;KAGF,GACEC,QAAQ,qIAAEtC,UAAS,CAACuC,IAAI,CAACF,UAAU;QAEnC;;;KAGF,GACEzB,SAAS,qIAAEZ,UAAS,CAACuC,IAAI;QAEzB;;;;;;;;;;KAUF,GACE1B,OAAO,qIAAEb,UAAS,CAACoC,MAAM;QAEzB;;KAEF,GACEtB,WAAW,qIAAGd,UAAS,CAACwC,KAAK,CAAC;YAC7B,eAAe;YACf,gCAAgC;YAChC,UAAU;YACV,2BAA2B;SAC3B,CAAC,CAACH,UAAU;QAEb;;KAEF,GACErB,QAAQ,qIAAEhB,UAAS,CAACyC,MAAAA;IACrB,CAAC;IAED,OAAOhC,UAAU;AAClB;uCAEeF,WAAW,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "file": "InputBasic.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/InputBasic.js"], "sourcesContent": ["import React, { useCallback } from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport { parseIncompletePhoneNumber, formatIncompletePhoneNumber } from 'libphonenumber-js/core'\r\n\r\nimport { getPrefixForFormattingValueAsPhoneNumber, removePrefixFromFormattedPhoneNumber } from './helpers/inputValuePrefix.js'\r\n\r\nimport useInput<PERSON>eyDownHandler from './useInputKeyDownHandler.js'\r\n\r\nexport function createInput(defaultMetadata) {\r\n\t/**\r\n\t * `InputBasic` is the most basic implementation of a `Component`\r\n\t * that can be passed to `<PhoneInput/>`. It parses and formats\r\n\t * the user's input but doesn't control the caret in the process:\r\n\t * when erasing or inserting digits in the middle of a phone number\r\n\t * the caret usually jumps to the end (this is the expected behavior).\r\n\t * Why does `InputBasic` exist when there's `InputSmart`?\r\n\t * One reason is working around the [Samsung Galaxy smart caret positioning bug]\r\n\t * (https://github.com/catamphetamine/react-phone-number-input/issues/75).\r\n\t * Another reason is that, unlike `InputSmart`, it doesn't require DOM environment.\r\n\t */\r\n\tfunction InputBasic({\r\n\t\tvalue,\r\n\t\tonChange,\r\n\t\tonKeyDown,\r\n\t\tcountry,\r\n\t\tinputFormat,\r\n\t\tmetadata = defaultMetadata,\r\n\t\tinputComponent: Input = 'input',\r\n\t\t//\r\n\t\t// The rest of the properties listed here are just to get the `rest` props\r\n\t\t// that will be passed to the DOM `<input/>` element.\r\n\t\t//\r\n\t\t// `international` property is deprecated and is not used.\r\n\t\tinternational,\r\n\t\t// `withCountryCallingCode` property is deprecated and is not used.\r\n\t\twithCountryCallingCode,\r\n\t\t...rest\r\n\t}, ref) {\r\n\t\tconst prefix = getPrefixForFormattingValueAsPhoneNumber({\r\n\t\t\tinputFormat,\r\n\t\t\tcountry,\r\n\t\t\tmetadata\r\n\t\t})\r\n\r\n\t\tconst _onChange = useCallback((event) => {\r\n\t\t\tlet newValue = parseIncompletePhoneNumber(event.target.value)\r\n\t\t\t// By default, if a value is something like `\"(123)\"`\r\n\t\t\t// then Backspace would only erase the rightmost brace\r\n\t\t\t// becoming something like `\"(123\"`\r\n\t\t\t// which would give the same `\"123\"` value\r\n\t\t\t// which would then be formatted back to `\"(123)\"`\r\n\t\t\t// and so a user wouldn't be able to erase the phone number.\r\n\t\t\t//\r\n\t\t\t// This issue is worked around with this simple hack:\r\n\t\t\t// when \"old\" and \"new\" parsed values are the same,\r\n\t\t\t// it checks if the \"new\" formatted value could be obtained\r\n\t\t\t// from the \"old\" formatted value by erasing some (or no) characters at the right side.\r\n\t\t\t// If it could then it's likely that the user has hit a Backspace key\r\n\t\t\t// and what they really intended was to erase a rightmost digit rather than\r\n\t\t\t// a rightmost punctuation character.\r\n\t\t\t//\r\n\t\t\tif (newValue === value) {\r\n\t\t\t\tconst newValueFormatted = format(prefix, newValue, country, metadata)\r\n\t\t\t\tif (newValueFormatted.indexOf(event.target.value) === 0) {\r\n\t\t\t\t\t// Trim the last digit (or plus sign).\r\n\t\t\t\t\tnewValue = newValue.slice(0, -1)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tonChange(newValue)\r\n\t\t}, [\r\n\t\t\tprefix,\r\n\t\t\tvalue,\r\n\t\t\tonChange,\r\n\t\t\tcountry,\r\n\t\t\tmetadata\r\n\t\t])\r\n\r\n\t\tconst _onKeyDown = useInputKeyDownHandler({\r\n\t\t\tonKeyDown,\r\n\t\t\tinputFormat\r\n\t\t})\r\n\r\n\t\treturn (\r\n\t\t\t<Input\r\n\t\t\t\t{...rest}\r\n\t\t\t\tref={ref}\r\n\t\t\t\tvalue={format(prefix, value, country, metadata)}\r\n\t\t\t\tonChange={_onChange}\r\n\t\t\t\tonKeyDown={_onKeyDown}/>\r\n\t\t)\r\n\t}\r\n\r\n\tInputBasic = React.forwardRef(InputBasic)\r\n\r\n\tInputBasic.propTypes = {\r\n\t\t/**\r\n\t\t * The parsed phone number.\r\n\t\t * \"Parsed\" not in a sense of \"E.164\"\r\n\t\t * but rather in a sense of \"having only\r\n\t\t * digits and possibly a leading plus character\".\r\n\t\t * Examples: `\"\"`, `\"+\"`, `\"+123\"`, `\"123\"`.\r\n\t\t */\r\n\t\tvalue: PropTypes.string.isRequired,\r\n\r\n\t\t/**\r\n\t\t * A function of `value: string`.\r\n\t\t * Updates the `value` property.\r\n\t\t */\r\n\t\tonChange: PropTypes.func.isRequired,\r\n\r\n\t\t/**\r\n\t\t * A function of `event: Event`.\r\n\t\t * Handles `keydown` events.\r\n\t\t */\r\n\t\tonKeyDown: PropTypes.func,\r\n\r\n\t\t/**\r\n\t\t * A two-letter country code for formatting `value`\r\n\t\t * as a national phone number (e.g. `(800) 555 35 35`).\r\n\t\t * E.g. \"US\", \"RU\", etc.\r\n\t\t * If no `country` is passed then `value`\r\n\t\t * is formatted as an international phone number.\r\n\t\t * (e.g. `****** 555 35 35`)\r\n\t\t * This property should've been called `defaultCountry`\r\n\t\t * because it only applies when the user inputs a phone number in a national format\r\n\t\t * and is completely ignored when the user inputs a phone number in an international format.\r\n\t\t */\r\n\t\tcountry : PropTypes.string,\r\n\r\n\t\t/**\r\n\t\t * The format that the input field value is being input/output in.\r\n\t\t */\r\n\t\tinputFormat : PropTypes.oneOf([\r\n\t\t\t'INTERNATIONAL',\r\n\t\t\t'NATIONAL_PART_OF_INTERNATIONAL',\r\n\t\t\t'NATIONAL',\r\n\t\t\t'INTERNATIONAL_OR_NATIONAL'\r\n\t\t]).isRequired,\r\n\r\n\t\t/**\r\n\t\t * `libphonenumber-js` metadata.\r\n\t\t */\r\n\t\tmetadata: PropTypes.object,\r\n\r\n\t\t/**\r\n\t\t * The `<input/>` component.\r\n\t\t */\r\n\t\tinputComponent: PropTypes.elementType\r\n\t}\r\n\r\n\treturn InputBasic\r\n}\r\n\r\nexport default createInput()\r\n\r\nfunction format(prefix, value, country, metadata) {\r\n\treturn removePrefixFromFormattedPhoneNumber(\r\n\t\tformatIncompletePhoneNumber(\r\n\t\t\tprefix + value,\r\n\t\t\tcountry,\r\n\t\t\tmetadata\r\n\t\t),\r\n\t\tprefix\r\n\t)\r\n}"], "names": ["React", "useCallback", "PropTypes", "parseIncompletePhoneNumber", "formatIncompletePhoneNumber", "getPrefixForFormattingValueAsPhoneNumber", "removePrefixFromFormattedPhoneNumber", "useInputKeyDownHandler", "createInput", "defaultMetadata", "InputBasic", "_ref", "ref", "value", "onChange", "onKeyDown", "country", "inputFormat", "_ref$metadata", "metadata", "_ref$inputComponent", "inputComponent", "Input", "international", "withCountryCallingCode", "rest", "_objectWithoutProperties", "_excluded", "prefix", "_onChange", "event", "newValue", "target", "newValueFormatted", "format", "indexOf", "slice", "_onKeyDown", "createElement", "_extends", "forwardRef", "propTypes", "string", "isRequired", "func", "oneOf", "object", "elementType"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,WAAW,QAAQ,OAAO;AAC1C,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,0BAA0B,EAAEC,2BAA2B,QAAQ,wBAAwB;;AAEhG,SAASC,wCAAwC,EAAEC,oCAAoC,QAAQ,+BAA+B;AAE9H,OAAOC,sBAAsB,MAAM,6BAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzD,SAASC,WAAWA,CAACC,eAAe,EAAE;IAC5C;;;;;;;;;;GAUD,GACC,SAASC,UAAUA,CAAAC,IAAA,EAiBhBC,GAAG,EAAE;QAAA,IAhBPC,KAAK,GAAAF,IAAA,CAALE,KAAK,EACLC,QAAQ,GAAAH,IAAA,CAARG,QAAQ,EACRC,SAAS,GAAAJ,IAAA,CAATI,SAAS,EACTC,OAAO,GAAAL,IAAA,CAAPK,OAAO,EACPC,WAAW,GAAAN,IAAA,CAAXM,WAAW,EAAAC,aAAA,GAAAP,IAAA,CACXQ,QAAQ,EAARA,QAAQ,GAAAD,aAAA,KAAA,KAAA,IAAGT,eAAe,GAAAS,aAAA,EAAAE,mBAAA,GAAAT,IAAA,CAC1BU,cAAc,EAAEC,KAAK,GAAAF,mBAAA,KAAA,KAAA,IAAG,OAAO,GAAAA,mBAAA,EAM/BG,aAAa,GAAAZ,IAAA,CAAbY,aAAa,EAEbC,sBAAsB,GAAAb,IAAA,CAAtBa,sBAAsB,EACnBC,IAAI,GAAAC,wBAAA,CAAAf,IAAA,EAAAgB,SAAA;QAEP,IAAMC,MAAM,GAAGvB,uOAAAA,AAAwC,EAAC;YACvDY,WAAW,EAAXA,WAAW;YACXD,OAAO,EAAPA,OAAO;YACPG,QAAQ,EAARA;QACD,CAAC,CAAC;QAEF,IAAMU,SAAS,kIAAG5B,cAAAA,AAAW;6DAAC,SAAC6B,KAAK,EAAK;gBACxC,IAAIC,QAAQ,IAAG5B,+PAAAA,AAA0B,EAAC2B,KAAK,CAACE,MAAM,CAACnB,KAAK,CAAC;gBAC7D,qDAAA;gBACA,sDAAA;gBACA,mCAAA;gBACA,0CAAA;gBACA,kDAAA;gBACA,4DAAA;gBACA,EAAA;gBACA,qDAAA;gBACA,mDAAA;gBACA,2DAAA;gBACA,uFAAA;gBACA,qEAAA;gBACA,2EAAA;gBACA,qCAAA;gBACA,EAAA;gBACA,IAAIkB,QAAQ,KAAKlB,KAAK,EAAE;oBACvB,IAAMoB,iBAAiB,GAAGC,MAAM,CAACN,MAAM,EAAEG,QAAQ,EAAEf,OAAO,EAAEG,QAAQ,CAAC;oBACrE,IAAIc,iBAAiB,CAACE,OAAO,CAACL,KAAK,CAACE,MAAM,CAACnB,KAAK,CAAC,KAAK,CAAC,EAAE;wBACxD,sCAAA;wBACAkB,QAAQ,GAAGA,QAAQ,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACjC;gBACD;gBACAtB,QAAQ,CAACiB,QAAQ,CAAC;YACnB,CAAC;4DAAE;YACFH,MAAM;YACNf,KAAK;YACLC,QAAQ;YACRE,OAAO;YACPG,QAAQ;SACR,CAAC;QAEF,IAAMkB,UAAU,0LAAG9B,UAAAA,AAAsB,EAAC;YACzCQ,SAAS,EAATA,SAAS;YACTE,WAAW,EAAXA;QACD,CAAC,CAAC;QAEF,OAAA,WAAA,8HACCjB,UAAA,CAAAsC,aAAA,CAAChB,KAAK,EAAAiB,QAAA,CAAA,CAAA,GACDd,IAAI,EAAA;YACRb,GAAG,EAAEA,GAAI;YACTC,KAAK,EAAEqB,MAAM,CAACN,MAAM,EAAEf,KAAK,EAAEG,OAAO,EAAEG,QAAQ,CAAE;YAChDL,QAAQ,EAAEe,SAAU;YACpBd,SAAS,EAAEsB;QAAW,EAAC,CAAC;IAE3B;IAEA3B,UAAU,GAAA,WAAA,8HAAGV,UAAK,CAACwC,UAAU,CAAC9B,UAAU,CAAC;IAEzCA,UAAU,CAAC+B,SAAS,GAAG;QACtB;;;;;;KAMF,GACE5B,KAAK,qIAAEX,UAAS,CAACwC,MAAM,CAACC,UAAU;QAElC;;;KAGF,GACE7B,QAAQ,qIAAEZ,UAAS,CAAC0C,IAAI,CAACD,UAAU;QAEnC;;;KAGF,GACE5B,SAAS,qIAAEb,UAAS,CAAC0C,IAAI;QAEzB;;;;;;;;;;KAUF,GACE5B,OAAO,qIAAGd,UAAS,CAACwC,MAAM;QAE1B;;KAEF,GACEzB,WAAW,qIAAGf,UAAS,CAAC2C,KAAK,CAAC;YAC7B,eAAe;YACf,gCAAgC;YAChC,UAAU;YACV,2BAA2B;SAC3B,CAAC,CAACF,UAAU;QAEb;;KAEF,GACExB,QAAQ,qIAAEjB,UAAS,CAAC4C,MAAM;QAE1B;;KAEF,GACEzB,cAAc,qIAAEnB,UAAS,CAAC6C,WAAAA;IAC3B,CAAC;IAED,OAAOrC,UAAU;AAClB;uCAEeF,WAAW,CAAC,CAAC;AAE5B,SAAS0B,MAAMA,CAACN,MAAM,EAAEf,KAAK,EAAEG,OAAO,EAAEG,QAAQ,EAAE;IACjD,mMAAOb,uCAAAA,AAAoC,uOAC1CF,8BAAAA,AAA2B,EAC1BwB,MAAM,GAAGf,KAAK,EACdG,OAAO,EACPG,QACD,CAAC,EACDS,MACD,CAAC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "file": "CountrySelect.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/CountrySelect.js"], "sourcesContent": ["import React, { useCallback, useMemo } from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport classNames from 'classnames'\r\nimport getUnicodeFlagIcon from 'country-flag-icons/unicode'\r\n\r\nexport default function CountrySelect({\r\n\tvalue,\r\n\tonChange,\r\n\toptions,\r\n\tdisabled,\r\n\treadOnly,\r\n\t...rest\r\n}) {\r\n\tconst onChange_ = useCallback((event) => {\r\n\t\tconst value = event.target.value\r\n\t\tonChange(value === 'ZZ' ? undefined : value)\r\n\t}, [onChange])\r\n\r\n\tconst selectedOption = useMemo(() => {\r\n\t\treturn getSelectedOption(options, value)\r\n\t}, [options, value])\r\n\r\n\t// \"ZZ\" means \"International\".\r\n\t// (HTML requires each `<option/>` have some string `value`).\r\n\treturn (\r\n\t\t<select\r\n\t\t\t{...rest}\r\n\t\t\tdisabled={disabled || readOnly}\r\n\t\t\treadOnly={readOnly}\r\n\t\t\tvalue={value || 'ZZ'}\r\n\t\t\tonChange={onChange_}>\r\n\t\t\t{options.map(({ value, label, divider }) => (\r\n\t\t\t\t<option\r\n\t\t\t\t\tkey={divider ? '|' : value || 'ZZ'}\r\n\t\t\t\t\tvalue={divider ? '|' : value || 'ZZ'}\r\n\t\t\t\t\tdisabled={divider ? true : false}\r\n\t\t\t\t\tstyle={divider ? DIVIDER_STYLE : undefined}>\r\n\t\t\t\t\t{label}\r\n\t\t\t\t</option>\r\n\t\t\t))}\r\n\t\t</select>\r\n\t)\r\n}\r\n\r\nCountrySelect.propTypes = {\r\n\t/**\r\n\t * A two-letter country code.\r\n\t * Example: \"US\", \"RU\", etc.\r\n\t */\r\n\tvalue: PropTypes.string,\r\n\r\n\t/**\r\n\t * A function of `value: string`.\r\n\t * Updates the `value` property.\r\n\t */\r\n\tonChange: PropTypes.func.isRequired,\r\n\r\n\t// `<select/>` options.\r\n\toptions: PropTypes.arrayOf(PropTypes.shape({\r\n\t\tvalue: PropTypes.string,\r\n\t\tlabel: PropTypes.string,\r\n\t\tdivider: PropTypes.bool\r\n\t})).isRequired,\r\n\r\n\t// `readonly` attribute doesn't work on a `<select/>`.\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/419#issuecomment-1764384480\r\n\t// https://www.delftstack.com/howto/html/html-select-readonly/\r\n\t// To work around that, if `readOnly: true` property is passed\r\n\t// to this component, it behaves analogous to `disabled: true`.\r\n\tdisabled: PropTypes.bool,\r\n\treadOnly: PropTypes.bool\r\n}\r\n\r\nconst DIVIDER_STYLE = {\r\n\tfontSize: '1px',\r\n\tbackgroundColor: 'currentColor',\r\n\tcolor: 'inherit'\r\n}\r\n\r\nexport function CountrySelectWithIcon({\r\n\tvalue,\r\n\toptions,\r\n\tclassName,\r\n\ticonComponent: Icon,\r\n\tgetIconAspectRatio,\r\n\tarrowComponent: Arrow = DefaultArrowComponent,\r\n\tunicodeFlags,\r\n\t...rest\r\n}) {\r\n\tconst selectedOption = useMemo(() => {\r\n\t\treturn getSelectedOption(options, value)\r\n\t}, [options, value])\r\n\r\n\treturn (\r\n\t\t<div className=\"PhoneInputCountry\">\r\n\t\t\t<CountrySelect\r\n\t\t\t\t{...rest}\r\n\t\t\t\tvalue={value}\r\n\t\t\t\toptions={options}\r\n\t\t\t\tclassName={classNames('PhoneInputCountrySelect', className)}\r\n\t\t\t/>\r\n\r\n\t\t\t{/* Either a Unicode flag icon or an SVG flag icon. */}\r\n\t\t\t{selectedOption && (\r\n\t\t\t\tunicodeFlags && value ? (\r\n\t\t\t\t\t<div className=\"PhoneInputCountryIconUnicode\">\r\n\t\t\t\t\t\t{getUnicodeFlagIcon(value)}\r\n\t\t\t\t\t</div>\r\n\t\t\t\t) : (\r\n\t\t\t\t\t<Icon\r\n\t\t\t\t\t\taria-hidden\r\n\t\t\t\t\t\tcountry={value}\r\n\t\t\t\t\t\tlabel={selectedOption.label}\r\n\t\t\t\t\t\taspectRatio={unicodeFlags ? 1 : undefined}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)\r\n\t\t\t)}\r\n\r\n\t\t\t<Arrow/>\r\n\t\t</div>\r\n\t)\r\n}\r\n\r\nCountrySelectWithIcon.propTypes = {\r\n\t// Country flag component.\r\n\ticonComponent: PropTypes.elementType,\r\n\r\n\t// Select arrow component.\r\n\tarrowComponent: PropTypes.elementType,\r\n\r\n\t// Set to `true` to render Unicode flag icons instead of SVG images.\r\n\tunicodeFlags: PropTypes.bool\r\n}\r\n\r\nfunction DefaultArrowComponent() {\r\n\treturn <div className=\"PhoneInputCountrySelectArrow\"/>\r\n}\r\n\r\nfunction getSelectedOption(options, value) {\r\n\tfor (const option of options) {\r\n\t\tif (!option.divider) {\r\n\t\t\tif (isSameOptionValue(option.value, value)) {\r\n\t\t\t\treturn option\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction isSameOptionValue(value1, value2) {\r\n\t// `undefined` is identical to `null`: both mean \"no country selected\".\r\n\tif (value1 === undefined || value1 === null) {\r\n\t\treturn value2 === undefined || value2 === null\r\n\t}\r\n\treturn value1 === value2\r\n}"], "names": ["React", "useCallback", "useMemo", "PropTypes", "classNames", "getUnicodeFlagIcon", "CountrySelect", "_ref", "value", "onChange", "options", "disabled", "readOnly", "rest", "_objectWithoutProperties", "_excluded", "onChange_", "event", "target", "undefined", "selectedOption", "getSelectedOption", "createElement", "_extends", "map", "_ref2", "label", "divider", "key", "style", "DIVIDER_STYLE", "propTypes", "string", "func", "isRequired", "arrayOf", "shape", "bool", "fontSize", "backgroundColor", "color", "CountrySelectWithIcon", "_ref3", "className", "Icon", "iconComponent", "getIconAspectRatio", "_ref3$arrowComponent", "arrowComponent", "Arrow", "DefaultArrowComponent", "unicodeFlags", "_excluded2", "country", "aspectRatio", "elementType", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "option", "isSameOptionValue", "value1", "value2"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACnD,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,kBAAkB,MAAM,4BAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5C,SAASC,aAAaA,CAAAC,IAAA,EAOlC;IAAA,IANFC,KAAK,GAAAD,IAAA,CAALC,KAAK,EACLC,QAAQ,GAAAF,IAAA,CAARE,QAAQ,EACRC,OAAO,GAAAH,IAAA,CAAPG,OAAO,EACPC,QAAQ,GAAAJ,IAAA,CAARI,QAAQ,EACRC,QAAQ,GAAAL,IAAA,CAARK,QAAQ,EACLC,IAAI,GAAAC,wBAAA,CAAAP,IAAA,EAAAQ,SAAA;IAEP,IAAMC,SAAS,kIAAGf,cAAAA,AAAW;gDAAC,SAACgB,KAAK,EAAK;YACxC,IAAMT,KAAK,GAAGS,KAAK,CAACC,MAAM,CAACV,KAAK;YAChCC,QAAQ,CAACD,KAAK,KAAK,IAAI,GAAGW,SAAS,GAAGX,KAAK,CAAC;QAC7C,CAAC;+CAAE;QAACC,QAAQ;KAAC,CAAC;IAEd,IAAMW,cAAc,kIAAGlB,UAAAA,AAAO;iDAAC,YAAM;YACpC,OAAOmB,iBAAiB,CAACX,OAAO,EAAEF,KAAK,CAAC;QACzC,CAAC;gDAAE;QAACE,OAAO;QAAEF,KAAK;KAAC,CAAC;IAEpB,8BAAA;IACA,6DAAA;IACA,OAAA,WAAA,8HACCR,UAAA,CAAAsB,aAAA,CAAA,UAAAC,QAAA,CAAA,CAAA,GACKV,IAAI,EAAA;QACRF,QAAQ,EAAEA,QAAQ,IAAIC,QAAS;QAC/BA,QAAQ,EAAEA,QAAS;QACnBJ,KAAK,EAAEA,KAAK,IAAI,IAAK;QACrBC,QAAQ,EAAEO;IAAU,IACnBN,OAAO,CAACc,GAAG,CAAC,SAAAC,KAAA;QAAA,IAAGjB,KAAK,GAAAiB,KAAA,CAALjB,KAAK,EAAEkB,KAAK,GAAAD,KAAA,CAALC,KAAK,EAAEC,OAAO,GAAAF,KAAA,CAAPE,OAAO;QAAA,OAAA,WAAA,8HACpC3B,UAAA,CAAAsB,aAAA,CAAA,UAAA;YACCM,GAAG,EAAED,OAAO,GAAG,GAAG,GAAGnB,KAAK,IAAI,IAAK;YACnCA,KAAK,EAAEmB,OAAO,GAAG,GAAG,GAAGnB,KAAK,IAAI,IAAK;YACrCG,QAAQ,EAAEgB,OAAO,GAAG,IAAI,GAAG,KAAM;YACjCE,KAAK,EAAEF,OAAO,GAAGG,aAAa,GAAGX;QAAU,GAC1CO,KACM,CAAC;IAAA,CACT,CACM,CAAC;AAEX;AAEApB,aAAa,CAACyB,SAAS,GAAG;IACzB;;;GAGD,GACCvB,KAAK,qIAAEL,UAAS,CAAC6B,MAAM;IAEvB;;;GAGD,GACCvB,QAAQ,qIAAEN,UAAS,CAAC8B,IAAI,CAACC,UAAU;IAEnC,uBAAA;IACAxB,OAAO,qIAAEP,UAAS,CAACgC,OAAO,oIAAChC,UAAS,CAACiC,KAAK,CAAC;QAC1C5B,KAAK,qIAAEL,UAAS,CAAC6B,MAAM;QACvBN,KAAK,qIAAEvB,UAAS,CAAC6B,MAAM;QACvBL,OAAO,oIAAExB,WAAS,CAACkC,IAAAA;IACpB,CAAC,CAAC,CAAC,CAACH,UAAU;IAEd,sDAAA;IACA,gGAAA;IACA,8DAAA;IACA,8DAAA;IACA,+DAAA;IACAvB,QAAQ,qIAAER,UAAS,CAACkC,IAAI;IACxBzB,QAAQ,oIAAET,WAAS,CAACkC,IAAAA;AACrB,CAAC;AAED,IAAMP,aAAa,GAAG;IACrBQ,QAAQ,EAAE,KAAK;IACfC,eAAe,EAAE,cAAc;IAC/BC,KAAK,EAAE;AACR,CAAC;AAEM,SAASC,qBAAqBA,CAAAC,KAAA,EASlC;IAAA,IARFlC,KAAK,GAAAkC,KAAA,CAALlC,KAAK,EACLE,OAAO,GAAAgC,KAAA,CAAPhC,OAAO,EACPiC,SAAS,GAAAD,KAAA,CAATC,SAAS,EACMC,IAAI,GAAAF,KAAA,CAAnBG,aAAa,EACbC,kBAAkB,GAAAJ,KAAA,CAAlBI,kBAAkB,EAAAC,oBAAA,GAAAL,KAAA,CAClBM,cAAc,EAAEC,KAAK,GAAAF,oBAAA,KAAA,KAAA,IAAGG,qBAAqB,GAAAH,oBAAA,EAC7CI,YAAY,GAAAT,KAAA,CAAZS,YAAY,EACTtC,IAAI,GAAAC,wBAAA,CAAA4B,KAAA,EAAAU,UAAA;IAEP,IAAMhC,cAAc,kIAAGlB,UAAAA,AAAO;yDAAC,YAAM;YACpC,OAAOmB,iBAAiB,CAACX,OAAO,EAAEF,KAAK,CAAC;QACzC,CAAC;wDAAE;QAACE,OAAO;QAAEF,KAAK;KAAC,CAAC;IAEpB,OAAA,WAAA,GACCR,qIAAA,CAAAsB,aAAA,CAAA,OAAA;QAAKqB,SAAS,EAAC;IAAmB,GAAA,WAAA,8HACjC3C,UAAA,CAAAsB,aAAA,CAAChB,aAAa,EAAAiB,QAAA,CAAA,CAAA,GACTV,IAAI,EAAA;QACRL,KAAK,EAAEA,KAAM;QACbE,OAAO,EAAEA,OAAQ;QACjBiC,SAAS,sIAAEvC,UAAAA,AAAU,EAAC,yBAAyB,EAAEuC,SAAS;IAAE,EAC5D,CAAC,EAGDvB,cAAc,IAAA,CACd+B,YAAY,IAAI3C,KAAK,GAAA,WAAA,GACpBR,qIAAA,CAAAsB,aAAA,CAAA,OAAA;QAAKqB,SAAS,EAAC;IAA8B,kKAC3CtC,UAAAA,AAAkB,EAACG,KAAK,CACrB,CAAC,GAAA,WAAA,8HAENR,UAAA,CAAAsB,aAAA,CAACsB,IAAI,EAAA;QACJ,eAAA,IAAW;QACXS,OAAO,EAAE7C,KAAM;QACfkB,KAAK,EAAEN,cAAc,CAACM,KAAM;QAC5B4B,WAAW,EAAEH,YAAY,GAAG,CAAC,GAAGhC;IAAU,CAC1C,CACD,CACD,EAAA,WAAA,8HAEDnB,UAAA,CAAAsB,aAAA,CAAC2B,KAAK,EAAA,IAAC,CACH,CAAC;AAER;AAEAR,qBAAqB,CAACV,SAAS,GAAG;IACjC,0BAAA;IACAc,aAAa,qIAAE1C,UAAS,CAACoD,WAAW;IAEpC,0BAAA;IACAP,cAAc,oIAAE7C,WAAS,CAACoD,WAAW;IAErC,oEAAA;IACAJ,YAAY,qIAAEhD,UAAS,CAACkC,IAAAA;AACzB,CAAC;AAED,SAASa,qBAAqBA,CAAA,EAAG;IAChC,OAAA,WAAA,6HAAOlD,WAAA,CAAAsB,aAAA,CAAA,OAAA;QAAKqB,SAAS,EAAC;IAA8B,CAAC,CAAC;AACvD;AAEA,SAAStB,iBAAiBA,CAACX,OAAO,EAAEF,KAAK,EAAE;IAC1C,IAAA,IAAAgD,SAAA,GAAAC,+BAAA,CAAqB/C,OAAO,GAAAgD,KAAA,EAAA,CAAA,CAAAA,KAAA,GAAAF,SAAA,EAAA,EAAAG,IAAA,EAAE;QAAA,IAAnBC,MAAM,GAAAF,KAAA,CAAAlD,KAAA;QAChB,IAAI,CAACoD,MAAM,CAACjC,OAAO,EAAE;YACpB,IAAIkC,iBAAiB,CAACD,MAAM,CAACpD,KAAK,EAAEA,KAAK,CAAC,EAAE;gBAC3C,OAAOoD,MAAM;YACd;QACD;IACD;AACD;AAEA,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,MAAM,EAAE;IAC1C,uEAAA;IACA,IAAID,MAAM,KAAK3C,SAAS,IAAI2C,MAAM,KAAK,IAAI,EAAE;QAC5C,OAAOC,MAAM,KAAK5C,SAAS,IAAI4C,MAAM,KAAK,IAAI;IAC/C;IACA,OAAOD,MAAM,KAAKC,MAAM;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "file": "Flag.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/Flag.js"], "sourcesContent": ["import React from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport classNames from 'classnames'\r\n\r\n// Default country flag icon.\r\n// `<img/>` is wrapped in a `<div/>` to prevent SVGs from exploding in size in IE 11.\r\n// https://github.com/catamphetamine/react-phone-number-input/issues/111\r\nexport default function FlagComponent({\r\n\tcountry,\r\n\tcountryName,\r\n\tflags,\r\n\tflagUrl,\r\n\t...rest\r\n}) {\r\n\tif (flags && flags[country]) {\r\n\t\treturn flags[country]({ title: countryName })\r\n\t}\r\n\treturn (\r\n\t\t<img\r\n\t\t\t{...rest}\r\n\t\t\talt={countryName}\r\n\t\t\trole={countryName ? undefined : \"presentation\"}\r\n\t\t\tsrc={flagUrl.replace('{XX}', country).replace('{xx}', country.toLowerCase())}/>\r\n\t)\r\n}\r\n\r\nFlagComponent.propTypes = {\r\n\t// The country to be selected by default.\r\n\t// Two-letter country code (\"ISO 3166-1 alpha-2\").\r\n\tcountry: PropTypes.string.isRequired,\r\n\r\n\t// Will be HTML `title` attribute of the `<img/>`.\r\n\tcountryName: PropTypes.string.isRequired,\r\n\r\n\t// Country flag icon components.\r\n\t// By default flag icons are inserted as `<img/>`s\r\n\t// with their `src` pointed to `country-flag-icons` gitlab pages website.\r\n\t// There might be cases (e.g. an offline application)\r\n\t// where having a large (3 megabyte) `<svg/>` flags\r\n\t// bundle is more appropriate.\r\n\t// `import flags from 'react-phone-number-input/flags'`.\r\n\tflags: PropTypes.objectOf(PropTypes.elementType),\r\n\r\n\t// A URL for a country flag icon.\r\n\t// By default it points to `country-flag-icons` gitlab pages website.\r\n\tflagUrl: PropTypes.string.isRequired\r\n}\r\n"], "names": ["React", "PropTypes", "classNames", "FlagComponent", "_ref", "country", "countryName", "flags", "flagUrl", "rest", "_objectWithoutProperties", "_excluded", "title", "createElement", "_extends", "alt", "role", "undefined", "src", "replace", "toLowerCase", "propTypes", "string", "isRequired", "objectOf", "elementType"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,UAAU,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKpB,SAASC,aAAaA,CAAAC,IAAA,EAMlC;IAAA,IALFC,OAAO,GAAAD,IAAA,CAAPC,OAAO,EACPC,WAAW,GAAAF,IAAA,CAAXE,WAAW,EACXC,KAAK,GAAAH,IAAA,CAALG,KAAK,EACLC,OAAO,GAAAJ,IAAA,CAAPI,OAAO,EACJC,IAAI,GAAAC,wBAAA,CAAAN,IAAA,EAAAO,SAAA;IAEP,IAAIJ,KAAK,IAAIA,KAAK,CAACF,OAAO,CAAC,EAAE;QAC5B,OAAOE,KAAK,CAACF,OAAO,CAAC,CAAC;YAAEO,KAAK,EAAEN;QAAY,CAAC,CAAC;IAC9C;IACA,OAAA,WAAA,6HACCN,WAAA,CAAAa,aAAA,CAAA,OAAAC,QAAA,CAAA,CAAA,GACKL,IAAI,EAAA;QACRM,GAAG,EAAET,WAAY;QACjBU,IAAI,EAAEV,WAAW,GAAGW,SAAS,GAAG,cAAe;QAC/CC,GAAG,EAAEV,OAAO,CAACW,OAAO,CAAC,MAAM,EAAEd,OAAO,CAAC,CAACc,OAAO,CAAC,MAAM,EAAEd,OAAO,CAACe,WAAW,CAAC,CAAC;IAAE,EAAC,CAAC;AAElF;AAEAjB,aAAa,CAACkB,SAAS,GAAG;IACzB,yCAAA;IACA,kDAAA;IACAhB,OAAO,qIAAEJ,UAAS,CAACqB,MAAM,CAACC,UAAU;IAEpC,kDAAA;IACAjB,WAAW,qIAAEL,UAAS,CAACqB,MAAM,CAACC,UAAU;IAExC,gCAAA;IACA,kDAAA;IACA,yEAAA;IACA,qDAAA;IACA,mDAAA;IACA,8BAAA;IACA,wDAAA;IACAhB,KAAK,qIAAEN,UAAS,CAACuB,QAAQ,oIAACvB,UAAS,CAACwB,WAAW,CAAC;IAEhD,iCAAA;IACA,qEAAA;IACAjB,OAAO,qIAAEP,UAAS,CAACqB,MAAM,CAACC,UAAAA;AAC3B,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "file": "InternationalIcon.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/InternationalIcon.js"], "sourcesContent": ["import React from 'react'\r\nimport PropTypes from 'prop-types'\r\n\r\nexport default function InternationalIcon({ aspectRatio, ...rest }) {\r\n\tif (aspectRatio === 1) {\r\n\t\treturn <InternationalIcon1x1 {...rest}/>\r\n\t} else {\r\n\t\treturn <InternationalIcon3x2 {...rest}/>\r\n\t}\r\n}\r\n\r\nInternationalIcon.propTypes = {\r\n\ttitle: PropTypes.string.isRequired,\r\n\taspectRatio: PropTypes.number\r\n}\r\n\r\n// 3x2.\r\n// Using `<title/>` in `<svg/>`s:\r\n// https://developer.mozilla.org/en-US/docs/Web/SVG/Element/title\r\nfunction InternationalIcon3x2({ title, ...rest }) {\r\n\treturn (\r\n\t\t<svg\r\n\t\t\t{...rest}\r\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\tviewBox=\"0 0 75 50\">\r\n\t\t\t<title>{title}</title>\r\n\t\t\t<g\r\n\t\t\t\tclassName=\"PhoneInputInternationalIconGlobe\"\r\n\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\tfill=\"none\"\r\n\t\t\t\tstrokeWidth=\"2\"\r\n\t\t\t\tstrokeMiterlimit=\"10\">\r\n\t\t\t\t<path strokeLinecap=\"round\" d=\"M47.2,36.1C48.1,36,49,36,50,36c7.4,0,14,1.7,18.5,4.3\"/>\r\n\t\t\t\t<path d=\"M68.6,9.6C64.2,12.3,57.5,14,50,14c-7.4,0-14-1.7-18.5-4.3\"/>\r\n\t\t\t\t<line x1=\"26\" y1=\"25\" x2=\"74\" y2=\"25\"/>\r\n\t\t\t\t<line x1=\"50\" y1=\"1\" x2=\"50\" y2=\"49\"/>\r\n\t\t\t\t<path strokeLinecap=\"round\" d=\"M46.3,48.7c1.2,0.2,2.5,0.3,3.7,0.3c13.3,0,24-10.7,24-24S63.3,1,50,1S26,11.7,26,25c0,2,0.3,3.9,0.7,5.8\"/>\r\n\t\t\t\t<path strokeLinecap=\"round\" d=\"M46.8,48.2c1,0.6,2.1,0.8,3.2,0.8c6.6,0,12-10.7,12-24S56.6,1,50,1S38,11.7,38,25c0,1.4,0.1,2.7,0.2,4c0,0.1,0,0.2,0,0.2\"/>\r\n\t\t\t</g>\r\n\t\t\t<path\r\n\t\t\t\tclassName=\"PhoneInputInternationalIconPhone\"\r\n\t\t\t\tstroke=\"none\"\r\n\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\td=\"M12.4,17.9c2.9-2.9,5.4-4.8,0.3-11.2S4.1,5.2,1.3,8.1C-2,11.4,1.1,23.5,13.1,35.6s24.3,15.2,27.5,11.9c2.8-2.8,7.8-6.3,1.4-11.5s-8.3-2.6-11.2,0.3c-2,2-7.2-2.2-11.7-6.7S10.4,19.9,12.4,17.9z\"/>\r\n\t\t</svg>\r\n\t)\r\n}\r\n\r\nInternationalIcon3x2.propTypes = {\r\n\ttitle: PropTypes.string.isRequired\r\n}\r\n\r\n// 1x1.\r\n// Using `<title/>` in `<svg/>`s:\r\n// https://developer.mozilla.org/en-US/docs/Web/SVG/Element/title\r\nfunction InternationalIcon1x1({ title, ...rest }) {\r\n\treturn (\r\n\t\t<svg\r\n\t\t\t{...rest}\r\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\tviewBox=\"0 0 50 50\">\r\n\t\t\t<title>{title}</title>\r\n\t\t\t<g\r\n\t\t\t\tclassName=\"PhoneInputInternationalIconGlobe\"\r\n\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\tfill=\"none\"\r\n\t\t\t\tstrokeWidth=\"2\"\r\n\t\t\t\tstrokeLinecap=\"round\">\r\n\t\t\t\t<path d=\"M8.45,13A21.44,21.44,0,1,1,37.08,41.56\"/>\r\n\t\t\t\t<path d=\"M19.36,35.47a36.9,36.9,0,0,1-2.28-13.24C17.08,10.39,21.88.85,27.8.85s10.72,9.54,10.72,21.38c0,6.48-1.44,12.28-3.71,16.21\"/>\r\n\t\t\t\t<path d=\"M17.41,33.4A39,39,0,0,1,27.8,32.06c6.62,0,12.55,1.5,16.48,3.86\"/>\r\n\t\t\t\t<path d=\"M44.29,8.53c-3.93,2.37-9.86,3.88-16.49,3.88S15.25,10.9,11.31,8.54\"/>\r\n\t\t\t\t<line x1=\"27.8\" y1=\"0.85\" x2=\"27.8\" y2=\"34.61\"/>\r\n\t\t\t\t<line x1=\"15.2\" y1=\"22.23\" x2=\"49.15\" y2=\"22.23\"/>\r\n\t\t\t</g>\r\n\t\t\t<path\r\n\t\t\t\tclassName=\"PhoneInputInternationalIconPhone\"\r\n\t\t\t\tstroke=\"transparent\"\r\n\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\td=\"M9.42,26.64c2.22-2.22,4.15-3.59.22-8.49S3.08,17,.93,19.17c-2.49,2.48-.13,11.74,9,20.89s18.41,11.5,20.89,9c2.15-2.15,5.91-4.77,1-8.71s-6.27-2-8.49.22c-1.55,1.55-5.48-1.69-8.86-5.08S7.87,28.19,9.42,26.64Z\"/>\r\n\t\t</svg>\r\n\t)\r\n}\r\n\r\nInternationalIcon1x1.propTypes = {\r\n\ttitle: PropTypes.string.isRequired\r\n}\r\n"], "names": ["React", "PropTypes", "InternationalIcon", "_ref", "aspectRatio", "rest", "_objectWithoutProperties", "_excluded", "createElement", "InternationalIcon1x1", "InternationalIcon3x2", "propTypes", "title", "string", "isRequired", "number", "_ref2", "_excluded2", "_extends", "xmlns", "viewBox", "className", "stroke", "fill", "strokeWidth", "strokeMiterlimit", "strokeLinecap", "d", "x1", "y1", "x2", "y2", "_ref3", "_excluded3"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnB,SAASC,iBAAiBA,CAAAC,IAAA,EAA2B;IAAA,IAAxBC,WAAW,GAAAD,IAAA,CAAXC,WAAW,EAAKC,IAAI,GAAAC,wBAAA,CAAAH,IAAA,EAAAI,SAAA;IAC/D,IAAIH,WAAW,KAAK,CAAC,EAAE;QACtB,OAAA,WAAA,8HAAOJ,UAAA,CAAAQ,aAAA,CAACC,oBAAoB,EAAKJ,IAAM,CAAC;IACzC,CAAC,MAAM;QACN,OAAA,WAAA,8HAAOL,UAAA,CAAAQ,aAAA,CAACE,oBAAoB,EAAKL,IAAM,CAAC;IACzC;AACD;AAEAH,iBAAiB,CAACS,SAAS,GAAG;IAC7BC,KAAK,EAAEX,6IAAS,CAACY,MAAM,CAACC,UAAU;IAClCV,WAAW,qIAAEH,UAAS,CAACc,MAAAA;AACxB,CAAC;AAED,OAAA;AACA,iCAAA;AACA,iEAAA;AACA,SAASL,oBAAoBA,CAAAM,KAAA,EAAqB;IAAA,IAAlBJ,KAAK,GAAAI,KAAA,CAALJ,KAAK,EAAKP,IAAI,GAAAC,wBAAA,CAAAU,KAAA,EAAAC,UAAA;IAC7C,OAAA,WAAA,8HACCjB,UAAA,CAAAQ,aAAA,CAAA,OAAAU,QAAA,CAAA,CAAA,GACKb,IAAI,EAAA;QACRc,KAAK,EAAC,4BAA4B;QAClCC,OAAO,EAAC;IAAW,IAAA,WAAA,8HACnBpB,UAAA,CAAAQ,aAAA,CAAA,SAAA,MAAQI,KAAa,CAAC,EAAA,WAAA,8HACtBZ,UAAA,CAAAQ,aAAA,CAAA,KAAA;QACCa,SAAS,EAAC,kCAAkC;QAC5CC,MAAM,EAAC,cAAc;QACrBC,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,GAAG;QACfC,gBAAgB,EAAC;IAAI,GAAA,WAAA,8HACrBzB,UAAA,CAAAQ,aAAA,CAAA,QAAA;QAAMkB,aAAa,EAAC,OAAO;QAACC,CAAC,EAAC;IAAsD,CAAC,CAAC,EAAA,WAAA,GACtF3B,qIAAA,CAAAQ,aAAA,CAAA,QAAA;QAAMmB,CAAC,EAAC;IAA0D,CAAC,CAAC,EAAA,WAAA,8HACpE3B,UAAA,CAAAQ,aAAA,CAAA,QAAA;QAAMoB,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC;IAAI,CAAC,CAAC,EAAA,WAAA,GACvC/B,qIAAA,CAAAQ,aAAA,CAAA,QAAA;QAAMoB,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC;IAAI,CAAC,CAAC,EAAA,WAAA,8HACtC/B,UAAA,CAAAQ,aAAA,CAAA,QAAA;QAAMkB,aAAa,EAAC,OAAO;QAACC,CAAC,EAAC;IAAuG,CAAC,CAAC,EAAA,WAAA,8HACvI3B,UAAA,CAAAQ,aAAA,CAAA,QAAA;QAAMkB,aAAa,EAAC,OAAO;QAACC,CAAC,EAAC;IAAsH,CAAC,CACnJ,CAAC,EAAA,WAAA,GACJ3B,qIAAA,CAAAQ,aAAA,CAAA,QAAA;QACCa,SAAS,EAAC,kCAAkC;QAC5CC,MAAM,EAAC,MAAM;QACbC,IAAI,EAAC,cAAc;QACnBI,CAAC,EAAC;IAA0L,CAAC,CAC1L,CAAC;AAER;AAEAjB,oBAAoB,CAACC,SAAS,GAAG;IAChCC,KAAK,qIAAEX,UAAS,CAACY,MAAM,CAACC,UAAAA;AACzB,CAAC;AAED,OAAA;AACA,iCAAA;AACA,iEAAA;AACA,SAASL,oBAAoBA,CAAAuB,KAAA,EAAqB;IAAA,IAAlBpB,KAAK,GAAAoB,KAAA,CAALpB,KAAK,EAAKP,IAAI,GAAAC,wBAAA,CAAA0B,KAAA,EAAAC,UAAA;IAC7C,OAAA,WAAA,GACCjC,qIAAA,CAAAQ,aAAA,CAAA,OAAAU,QAAA,CAAA,CAAA,GACKb,IAAI,EAAA;QACRc,KAAK,EAAC,4BAA4B;QAClCC,OAAO,EAAC;IAAW,IAAA,WAAA,6HACnBpB,WAAA,CAAAQ,aAAA,CAAA,SAAA,MAAQI,KAAa,CAAC,EAAA,WAAA,8HACtBZ,UAAA,CAAAQ,aAAA,CAAA,KAAA;QACCa,SAAS,EAAC,kCAAkC;QAC5CC,MAAM,EAAC,cAAc;QACrBC,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,GAAG;QACfE,aAAa,EAAC;IAAO,GAAA,WAAA,GACrB1B,qIAAA,CAAAQ,aAAA,CAAA,QAAA;QAAMmB,CAAC,EAAC;IAAwC,CAAC,CAAC,EAAA,WAAA,8HAClD3B,UAAA,CAAAQ,aAAA,CAAA,QAAA;QAAMmB,CAAC,EAAC;IAA0H,CAAC,CAAC,EAAA,WAAA,8HACpI3B,UAAA,CAAAQ,aAAA,CAAA,QAAA;QAAMmB,CAAC,EAAC;IAAgE,CAAC,CAAC,EAAA,WAAA,8HAC1E3B,UAAA,CAAAQ,aAAA,CAAA,QAAA;QAAMmB,CAAC,EAAC;IAAmE,CAAC,CAAC,EAAA,WAAA,8HAC7E3B,UAAA,CAAAQ,aAAA,CAAA,QAAA;QAAMoB,EAAE,EAAC,MAAM;QAACC,EAAE,EAAC,MAAM;QAACC,EAAE,EAAC,MAAM;QAACC,EAAE,EAAC;IAAO,CAAC,CAAC,EAAA,WAAA,8HAChD/B,UAAA,CAAAQ,aAAA,CAAA,QAAA;QAAMoB,EAAE,EAAC,MAAM;QAACC,EAAE,EAAC,OAAO;QAACC,EAAE,EAAC,OAAO;QAACC,EAAE,EAAC;IAAO,CAAC,CAC/C,CAAC,EAAA,WAAA,8HACJ/B,UAAA,CAAAQ,aAAA,CAAA,QAAA;QACCa,SAAS,EAAC,kCAAkC;QAC5CC,MAAM,EAAC,aAAa;QACpBC,IAAI,EAAC,cAAc;QACnBI,CAAC,EAAC;IAA4M,CAAC,CAC5M,CAAC;AAER;AAEAlB,oBAAoB,CAACE,SAAS,GAAG;IAChCC,KAAK,qIAAEX,UAAS,CAACY,MAAM,CAACC,UAAAA;AACzB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "file": "isE164Number.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/helpers/isE164Number.js"], "sourcesContent": ["// Tells if `value: string` is an `E.164` phone number.\r\n//\r\n// Returns a boolean.\r\n//\r\n// It doesn't validate that the minimum national (significant) number length\r\n// is at least 2 characters.\r\n//\r\nexport default function isE164Number(value) {\r\n\tif (value.length < 2) {\r\n\t\treturn false\r\n\t}\r\n\tif (value[0] !== '+') {\r\n\t\treturn false\r\n\t}\r\n\tlet i = 1\r\n\twhile (i < value.length) {\r\n\t\tconst character = value.charCodeAt(i)\r\n\t\tif (character >= 48 && character <= 57) {\r\n\t\t\t// Is a digit.\r\n\t\t} else {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\treturn true\r\n}\r\n\r\nexport function validateE164Number(value) {\r\n\tif (!isE164Number(value)) {\r\n\t\tconsole.error('[react-phone-number-input] Expected the initial `value` to be a E.164 phone number. Got', value)\r\n\t}\r\n}"], "names": ["isE164Number", "value", "length", "i", "character", "charCodeAt", "validateE164Number", "console", "error"], "mappings": "AAAA,uDAAA;AACA,EAAA;AACA,qBAAA;AACA,EAAA;AACA,4EAAA;AACA,4BAAA;AACA,EAAA;;;;;AACe,SAASA,YAAYA,CAACC,KAAK,EAAE;IAC3C,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;QACrB,OAAO,KAAK;IACb;IACA,IAAID,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACrB,OAAO,KAAK;IACb;IACA,IAAIE,CAAC,GAAG,CAAC;IACT,MAAOA,CAAC,GAAGF,KAAK,CAACC,MAAM,CAAE;QACxB,IAAME,SAAS,GAAGH,KAAK,CAACI,UAAU,CAACF,CAAC,CAAC;QACrC,IAAIC,SAAS,IAAI,EAAE,IAAIA,SAAS,IAAI,EAAE,EAAE;QACvC,cAAA;QAAA,CACA,MAAM;YACN,OAAO,KAAK;QACb;QACAD,CAAC,EAAE;IACJ;IACA,OAAO,IAAI;AACZ;AAEO,SAASG,kBAAkBA,CAACL,KAAK,EAAE;IACzC,IAAI,CAACD,YAAY,CAACC,KAAK,CAAC,EAAE;QACzBM,OAAO,CAACC,KAAK,CAAC,yFAAyF,EAAEP,KAAK,CAAC;IAChH;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1280, "column": 0}, "map": {"version": 3, "file": "countries.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/helpers/countries.js"], "sourcesContent": ["// Ignores weird istanbul error: \"else path not taken\".\r\nimport { isSupportedCountry } from 'libphonenumber-js/core'\r\nexport { getCountries } from 'libphonenumber-js/core'\r\n\r\n/**\r\n * Sorts country `<select/>` options.\r\n * Can move some country `<select/>` options\r\n * to the top of the list, for example.\r\n * @param  {object[]} countryOptions — Country `<select/>` options.\r\n * @param  {string[]} [countryOptionsOrder] — Country `<select/>` options order. Example: `[\"US\", \"CA\", \"AU\", \"|\", \"...\"]`.\r\n * @return {object[]}\r\n */\r\nexport function sortCountryOptions(options, order) {\r\n\tif (!order) {\r\n\t\treturn options\r\n\t}\r\n\tconst optionsOnTop = []\r\n\tconst optionsOnBottom = []\r\n\tlet appendTo = optionsOnTop\r\n\tfor (const element of order) {\r\n\t\tif (element === '|') {\r\n\t\t\tappendTo.push({ divider: true })\r\n\t\t} else if (element === '...' || element === '…') {\r\n\t\t\tappendTo = optionsOnBottom\r\n\t\t} else {\r\n\t\t\tlet countryCode\r\n\t\t\tif (element === '🌐') {\r\n\t\t\t\tcountryCode = undefined\r\n\t\t\t} else {\r\n\t\t\t\tcountryCode = element\r\n\t\t\t}\r\n\t\t\t// Find the position of the option.\r\n\t\t\tconst index = options.indexOf(options.filter(option => option.value === countryCode)[0])\r\n\t\t\t// Get the option.\r\n\t\t\tconst option = options[index]\r\n\t\t\t// Remove the option from its default position.\r\n\t\t\toptions.splice(index, 1)\r\n\t\t\t// Add the option on top.\r\n\t\t\tappendTo.push(option)\r\n\t\t}\r\n\t}\r\n\treturn optionsOnTop.concat(options).concat(optionsOnBottom)\r\n}\r\n\r\nexport function getSupportedCountryOptions(countryOptions, metadata) {\r\n\tif (countryOptions) {\r\n\t\tcountryOptions = countryOptions.filter((option) => {\r\n\t\t\tswitch (option) {\r\n\t\t\t\tcase '🌐':\r\n\t\t\t\tcase '|':\r\n\t\t\t\tcase '...':\r\n\t\t\t\tcase '…':\r\n\t\t\t\t\treturn true\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn isCountrySupportedWithError(option, metadata)\r\n\t\t\t}\r\n\t\t})\r\n\t\tif (countryOptions.length > 0) {\r\n\t\t\treturn countryOptions\r\n\t\t}\r\n\t}\r\n}\r\n\r\nexport function isCountrySupportedWithError(country, metadata) {\r\n\tif (isSupportedCountry(country, metadata)) {\r\n\t\treturn true\r\n\t} else {\r\n\t\tconsole.error(`Country not found: ${country}`)\r\n\t\treturn false\r\n\t}\r\n}\r\n\r\nexport function getSupportedCountries(countries, metadata) {\r\n\tif (countries) {\r\n\t\tcountries = countries.filter(country => isCountrySupportedWithError(country, metadata))\r\n\t\tif (countries.length === 0) {\r\n\t\t\tcountries = undefined\r\n\t\t}\r\n\t}\r\n\treturn countries\r\n}"], "names": ["isSupportedCountry", "getCountries", "sortCountryOptions", "options", "order", "optionsOnTop", "optionsOnBottom", "appendTo", "_loop", "element", "_step", "value", "push", "divider", "countryCode", "undefined", "index", "indexOf", "filter", "option", "splice", "_iterator", "_createForOfIteratorHelperLoose", "done", "concat", "getSupportedCountryOptions", "countryOptions", "metadata", "isCountrySupportedWithError", "length", "country", "console", "error", "getSupportedCountries", "countries"], "mappings": ";;;;;;AAAA,uDAAA;AACA,SAASA,kBAAkB,QAAQ,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWpD,SAASE,kBAAkBA,CAACC,OAAO,EAAEC,KAAK,EAAE;IAClD,IAAI,CAACA,KAAK,EAAE;QACX,OAAOD,OAAO;IACf;IACA,IAAME,YAAY,GAAG,EAAE;IACvB,IAAMC,eAAe,GAAG,EAAE;IAC1B,IAAIC,QAAQ,GAAGF,YAAY;IAAA,IAAAG,KAAA,GAAA,SAAAA,MAAA,EACE;QAAA,IAAlBC,OAAO,GAAAC,KAAA,CAAAC,KAAA;QACjB,IAAIF,OAAO,KAAK,GAAG,EAAE;YACpBF,QAAQ,CAACK,IAAI,CAAC;gBAAEC,OAAO,EAAE;YAAK,CAAC,CAAC;QACjC,CAAC,MAAM,IAAIJ,OAAO,KAAK,KAAK,IAAIA,OAAO,KAAK,GAAG,EAAE;YAChDF,QAAQ,GAAGD,eAAe;QAC3B,CAAC,MAAM;YACN,IAAIQ,WAAW;YACf,IAAIL,OAAO,KAAK,IAAI,EAAE;gBACrBK,WAAW,GAAGC,SAAS;YACxB,CAAC,MAAM;gBACND,WAAW,GAAGL,OAAO;YACtB;YACA,mCAAA;YACA,IAAMO,KAAK,GAAGb,OAAO,CAACc,OAAO,CAACd,OAAO,CAACe,MAAM,CAAC,SAAAC,MAAM;gBAAA,OAAIA,MAAM,CAACR,KAAK,KAAKG,WAAW;YAAA,EAAC,CAAC,CAAC,CAAC,CAAC;YACxF,kBAAA;YACA,IAAMK,MAAM,GAAGhB,OAAO,CAACa,KAAK,CAAC;YAC7B,+CAAA;YACAb,OAAO,CAACiB,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;YACxB,yBAAA;YACAT,QAAQ,CAACK,IAAI,CAACO,MAAM,CAAC;QACtB;IACD,CAAC;IArBD,IAAA,IAAAE,SAAA,GAAAC,+BAAA,CAAsBlB,KAAK,GAAAM,KAAA,EAAA,CAAA,CAAAA,KAAA,GAAAW,SAAA,EAAA,EAAAE,IAAA,EAAA;QAAAf,KAAA;IAAA;IAsB3B,OAAOH,YAAY,CAACmB,MAAM,CAACrB,OAAO,CAAC,CAACqB,MAAM,CAAClB,eAAe,CAAC;AAC5D;AAEO,SAASmB,0BAA0BA,CAACC,cAAc,EAAEC,QAAQ,EAAE;IACpE,IAAID,cAAc,EAAE;QACnBA,cAAc,GAAGA,cAAc,CAACR,MAAM,CAAC,SAACC,MAAM,EAAK;YAClD,OAAQA,MAAM;gBACb,KAAK,IAAI;gBACT,KAAK,GAAG;gBACR,KAAK,KAAK;gBACV,KAAK,GAAG;oBACP,OAAO,IAAI;gBACZ;oBACC,OAAOS,2BAA2B,CAACT,MAAM,EAAEQ,QAAQ,CAAC;YACtD;QACD,CAAC,CAAC;QACF,IAAID,cAAc,CAACG,MAAM,GAAG,CAAC,EAAE;YAC9B,OAAOH,cAAc;QACtB;IACD;AACD;AAEO,SAASE,2BAA2BA,CAACE,OAAO,EAAEH,QAAQ,EAAE;IAC9D,4JAAI3B,qBAAAA,AAAkB,EAAC8B,OAAO,EAAEH,QAAQ,CAAC,EAAE;QAC1C,OAAO,IAAI;IACZ,CAAC,MAAM;QACNI,OAAO,CAACC,KAAK,CAAA,sBAAAR,MAAA,CAAuBM,OAAO,CAAE,CAAC;QAC9C,OAAO,KAAK;IACb;AACD;AAEO,SAASG,qBAAqBA,CAACC,SAAS,EAAEP,QAAQ,EAAE;IAC1D,IAAIO,SAAS,EAAE;QACdA,SAAS,GAAGA,SAAS,CAAChB,MAAM,CAAC,SAAAY,OAAO;YAAA,OAAIF,2BAA2B,CAACE,OAAO,EAAEH,QAAQ,CAAC;QAAA,EAAC;QACvF,IAAIO,SAAS,CAACL,MAAM,KAAK,CAAC,EAAE;YAC3BK,SAAS,GAAGnB,SAAS;QACtB;IACD;IACA,OAAOmB,SAAS;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1403, "column": 0}, "map": {"version": 3, "file": "CountryIcon.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/CountryIcon.js"], "sourcesContent": ["import React from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport classNames from 'classnames'\r\n\r\nimport DefaultInternationalIcon from './InternationalIcon.js'\r\nimport Flag from './Flag.js'\r\n\r\nexport function createCountryIconComponent({\r\n\tflags,\r\n\tflagUrl,\r\n\tflagComponent: FlagComponent,\r\n\tinternationalIcon: InternationalIcon\r\n}) {\r\n\tfunction CountryIcon({\r\n\t\tcountry,\r\n\t\tlabel,\r\n\t\taspectRatio,\r\n\t\t...rest\r\n\t}) {\r\n\t\t// `aspectRatio` is currently a hack for the default \"International\" icon\r\n\t\t// to render it as a square when Unicode flag icons are used.\r\n\t\t// So `aspectRatio` property is only used with the default \"International\" icon.\r\n\t\tconst _aspectRatio = InternationalIcon === DefaultInternationalIcon ? aspectRatio : undefined\r\n\t\treturn (\r\n\t\t\t<div\r\n\t\t\t\t{...rest}\r\n\t\t\t\tclassName={classNames('PhoneInputCountryIcon', {\r\n\t\t\t\t\t'PhoneInputCountryIcon--square': _aspectRatio === 1,\r\n\t\t\t\t\t'PhoneInputCountryIcon--border': country\r\n\t\t\t\t})}>\r\n\t\t\t\t{\r\n\t\t\t\t\tcountry\r\n\t\t\t\t\t?\r\n\t\t\t\t\t<FlagComponent\r\n\t\t\t\t\t\tcountry={country}\r\n\t\t\t\t\t\tcountryName={label}\r\n\t\t\t\t\t\tflags={flags}\r\n\t\t\t\t\t\tflagUrl={flagUrl}\r\n\t\t\t\t\t\tclassName=\"PhoneInputCountryIconImg\"/>\r\n\t\t\t\t\t:\r\n\t\t\t\t\t<InternationalIcon\r\n\t\t\t\t\t\ttitle={label}\r\n\t\t\t\t\t\taspectRatio={_aspectRatio}\r\n\t\t\t\t\t\tclassName=\"PhoneInputCountryIconImg\"/>\r\n\t\t\t\t}\r\n\t\t\t</div>\r\n\t\t)\r\n\t}\r\n\r\n\tCountryIcon.propTypes = {\r\n\t\tcountry: PropTypes.string,\r\n\t\tlabel: PropTypes.string.isRequired,\r\n\t\taspectRatio: PropTypes.number\r\n\t}\r\n\r\n\treturn CountryIcon\r\n}\r\n\r\nexport default createCountryIconComponent({\r\n\t// Must be equal to `defaultProps.flagUrl` in `./PhoneInputWithCountry.js`.\r\n\tflagUrl: 'https://purecatamphetamine.github.io/country-flag-icons/3x2/{XX}.svg',\r\n\tflagComponent: Flag,\r\n\tinternationalIcon: DefaultInternationalIcon\r\n})"], "names": ["React", "PropTypes", "classNames", "DefaultInternationalIcon", "Flag", "createCountryIconComponent", "_ref", "flags", "flagUrl", "FlagComponent", "flagComponent", "InternationalIcon", "internationalIcon", "CountryIcon", "_ref2", "country", "label", "aspectRatio", "rest", "_objectWithoutProperties", "_excluded", "_aspectRatio", "undefined", "createElement", "_extends", "className", "countryName", "title", "propTypes", "string", "isRequired", "number"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,UAAU,MAAM,YAAY;AAEnC,OAAOC,wBAAwB,MAAM,wBAAwB;AAC7D,OAAOC,IAAI,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,SAASC,0BAA0BA,CAAAC,IAAA,EAKvC;IAAA,IAJFC,KAAK,GAAAD,IAAA,CAALC,KAAK,EACLC,OAAO,GAAAF,IAAA,CAAPE,OAAO,EACQC,aAAa,GAAAH,IAAA,CAA5BI,aAAa,EACMC,iBAAiB,GAAAL,IAAA,CAApCM,iBAAiB;IAEjB,SAASC,WAAWA,CAAAC,KAAA,EAKjB;QAAA,IAJFC,OAAO,GAAAD,KAAA,CAAPC,OAAO,EACPC,KAAK,GAAAF,KAAA,CAALE,KAAK,EACLC,WAAW,GAAAH,KAAA,CAAXG,WAAW,EACRC,IAAI,GAAAC,wBAAA,CAAAL,KAAA,EAAAM,SAAA;QAEP,yEAAA;QACA,6DAAA;QACA,gFAAA;QACA,IAAMC,YAAY,GAAGV,iBAAiB,mLAAKR,UAAwB,GAAGc,WAAW,GAAGK,SAAS;QAC7F,OAAA,WAAA,8HACCtB,UAAA,CAAAuB,aAAA,CAAA,OAAAC,QAAA,CAAA,CAAA,GACKN,IAAI,EAAA;YACRO,SAAS,sIAAEvB,UAAAA,AAAU,EAAC,uBAAuB,EAAE;gBAC9C,+BAA+B,EAAEmB,YAAY,KAAK,CAAC;gBACnD,+BAA+B,EAAEN;YAClC,CAAC;QAAE,IAEFA,OAAO,GAAA,WAAA,8HAEPf,UAAA,CAAAuB,aAAA,CAACd,aAAa,EAAA;YACbM,OAAO,EAAEA,OAAQ;YACjBW,WAAW,EAAEV,KAAM;YACnBT,KAAK,EAAEA,KAAM;YACbC,OAAO,EAAEA,OAAQ;YACjBiB,SAAS,EAAC;QAA0B,CAAC,CAAC,GAAA,WAAA,8HAEvCzB,UAAA,CAAAuB,aAAA,CAACZ,iBAAiB,EAAA;YACjBgB,KAAK,EAAEX,KAAM;YACbC,WAAW,EAAEI,YAAa;YAC1BI,SAAS,EAAC;QAA0B,CAAC,CAEnC,CAAC;IAER;IAEAZ,WAAW,CAACe,SAAS,GAAG;QACvBb,OAAO,qIAAEd,UAAS,CAAC4B,MAAM;QACzBb,KAAK,qIAAEf,UAAS,CAAC4B,MAAM,CAACC,UAAU;QAClCb,WAAW,qIAAEhB,UAAS,CAAC8B,MAAAA;IACxB,CAAC;IAED,OAAOlB,WAAW;AACnB;uCAEeR,0BAA0B,CAAC;IACzC,2EAAA;IACAG,OAAO,EAAE,sEAAsE;IAC/EE,aAAa,mKAAEN,UAAI;IACnBQ,iBAAiB,gLAAET,UAAAA;AACpB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1498, "column": 0}, "map": {"version": 3, "file": "useExternalRef.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/useExternalRef.js"], "sourcesContent": ["import { useRef, useCallback } from 'react'\r\n\r\n/**\r\n * This hook creates an internal copy of a `ref`\r\n * and returns a new `ref`-alike setter function\r\n * that updates both `ref` and the internal copy of it.\r\n * That `ref`-alike setter function could then be passed\r\n * to child elements instead of the original `ref`.\r\n *\r\n * The internal copy of the `ref` can then be used to\r\n * call instance methods like `.focus()`, etc.\r\n *\r\n * One may ask: why create a copy of `ref` for \"internal\" use\r\n * when the code could use the original `ref` for that.\r\n * The answer is: the code would have to dance around the original `ref` anyway\r\n * to figure out whether it exists and to find out the internal implementation of it\r\n * in order to read its value correctly. This hook encapsulates all that \"boilerplate\" code.\r\n * The returned copy of the `ref` is guaranteed to exist and functions as a proper ref \"object\".\r\n * The returned `ref`-alike setter function must be used instead of the original `ref`\r\n * when passing it to child elements.\r\n *\r\n * @param  {(object|function)} [externalRef] — The original `ref` that may have any internal implementation and might not even exist.\r\n * @return {any[]} Returns an array of two elements: a copy of the `ref` for \"internal\" use and a `ref`-alike setter function that should be used in-place of the original `ref` when passing it to child elements.\r\n */\r\nexport default function useExternalRef(externalRef) {\r\n  // Create a copy of the original `ref` (which might not exist).\r\n  // Both refs will point to the same value.\r\n  const refCopy = useRef()\r\n\r\n  // Updates both `ref`s with the same `value`.\r\n  const refSetter = useCallback((value) => {\r\n    setRefsValue([externalRef, refCopy], value)\r\n  }, [\r\n    externalRef,\r\n    refCopy\r\n  ])\r\n\r\n  return [refCopy, refSetter]\r\n}\r\n\r\n// Sets the same `value` of all `ref`s.\r\n// Some of the `ref`s may not exist in which case they'll be skipped.\r\nexport function setRefsValue(refs, value) {\r\n  for (const ref of refs) {\r\n    if (ref) {\r\n      setRefValue(ref, value)\r\n    }\r\n  }\r\n}\r\n\r\n// Sets the value of a `ref`.\r\n// Before React Hooks were introduced, `ref`s used to be functions.\r\n// After React Hooks were introduces, `ref`s became objects with `.current` property.\r\n// This function sets a `ref`'s value regardless of its internal implementation,\r\n// so it supports both types of `ref`s.\r\nfunction setRefValue(ref, value) {\r\n  if (typeof ref === 'function') {\r\n    ref(value)\r\n  } else {\r\n    ref.current = value\r\n  }\r\n}"], "names": ["useRef", "useCallback", "useExternalRef", "externalRef", "refCopy", "refSetter", "value", "setRefsValue", "refs", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "ref", "setRefValue", "current"], "mappings": ";;;;AAAA,SAASA,MAAM,EAAEC,WAAW,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwB5B,SAASC,cAAcA,CAACC,WAAW,EAAE;IAClD,+DAAA;IACA,0CAAA;IACA,IAAMC,OAAO,kIAAGJ,SAAAA,AAAM,CAAC,CAAC;IAExB,6CAAA;IACA,IAAMK,SAAS,kIAAGJ,cAAAA,AAAW;iDAAC,SAACK,KAAK,EAAK;YACvCC,YAAY,CAAC;gBAACJ,WAAW;gBAAEC,OAAO;aAAC,EAAEE,KAAK,CAAC;QAC7C,CAAC;gDAAE;QACDH,WAAW;QACXC,OAAO;KACR,CAAC;IAEF,OAAO;QAACA,OAAO;QAAEC,SAAS;KAAC;AAC7B;AAIO,SAASE,YAAYA,CAACC,IAAI,EAAEF,KAAK,EAAE;IACxC,IAAA,IAAAG,SAAA,GAAAC,+BAAA,CAAkBF,IAAI,GAAAG,KAAA,EAAA,CAAA,CAAAA,KAAA,GAAAF,SAAA,EAAA,EAAAG,IAAA,EAAE;QAAA,IAAbC,GAAG,GAAAF,KAAA,CAAAL,KAAA;QACZ,IAAIO,GAAG,EAAE;YACPC,WAAW,CAACD,GAAG,EAAEP,KAAK,CAAC;QACzB;IACF;AACF;AAEA,6BAAA;AACA,mEAAA;AACA,qFAAA;AACA,gFAAA;AACA,uCAAA;AACA,SAASQ,WAAWA,CAACD,GAAG,EAAEP,KAAK,EAAE;IAC/B,IAAI,OAAOO,GAAG,KAAK,UAAU,EAAE;QAC7BA,GAAG,CAACP,KAAK,CAAC;IACZ,CAAC,MAAM;QACLO,GAAG,CAACE,OAAO,GAAGT,KAAK;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1582, "column": 0}, "map": {"version": 3, "file": "getInternationalPhoneNumberPrefix.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/helpers/getInternationalPhoneNumberPrefix.js"], "sourcesContent": ["import {\r\n\tgetCountryCallingCode,\r\n\tMetadata\r\n} from 'libphonenumber-js/core'\r\n\r\nconst ONLY_DIGITS_REGEXP = /^\\d+$/\r\n\r\nexport default function getInternationalPhoneNumberPrefix(country, metadata) {\r\n\t// Standard international phone number prefix: \"+\" and \"country calling code\".\r\n\tlet prefix = '+' + getCountryCallingCode(country, metadata)\r\n\r\n\t// \"Leading digits\" can't be used to rule out any countries.\r\n\t// So the \"pre-fill with leading digits on country selection\" feature had to be reverted.\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/10#note_1231042367\r\n\t// // Get \"leading digits\" for a phone number of the country.\r\n\t// // If there're \"leading digits\" then they can be part of the prefix too.\r\n\t// // https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/10\r\n\t// metadata = new Metadata(metadata)\r\n\t// metadata.selectNumberingPlan(country)\r\n\t// // \"Leading digits\" patterns are only defined for about 20% of all countries.\r\n\t// // By definition, matching \"leading digits\" is a sufficient but not a necessary\r\n\t// // condition for a phone number to belong to a country.\r\n\t// // The point of \"leading digits\" check is that it's the fastest one to get a match.\r\n\t// // https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md#leading_digits\r\n\t// const leadingDigits = metadata.numberingPlan.leadingDigits()\r\n\t// if (leadingDigits && ONLY_DIGITS_REGEXP.test(leadingDigits)) {\r\n\t// \tprefix += leadingDigits\r\n\t// }\r\n\r\n\treturn prefix\r\n}"], "names": ["getCountryCallingCode", "<PERSON><PERSON><PERSON>", "ONLY_DIGITS_REGEXP", "getInternationalPhoneNumberPrefix", "country", "metadata", "prefix"], "mappings": ";;;AAAA,SACCA,qBAAqB,EACrBC,QAAQ,QACF,wBAAwB;;AAE/B,IAAMC,kBAAkB,GAAG,OAAO;AAEnB,SAASC,iCAAiCA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC5E,8EAAA;IACA,IAAIC,MAAM,GAAG,GAAG,2JAAGN,wBAAAA,AAAqB,EAACI,OAAO,EAAEC,QAAQ,CAAC;IAE3D,4DAAA;IACA,yFAAA;IACA,yFAAA;IACA,6DAAA;IACA,2EAAA;IACA,4EAAA;IACA,oCAAA;IACA,wCAAA;IACA,gFAAA;IACA,kFAAA;IACA,0DAAA;IACA,sFAAA;IACA,gGAAA;IACA,+DAAA;IACA,iEAAA;IACA,2BAAA;IACA,IAAA;IAEA,OAAOC,MAAM;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1616, "column": 0}, "map": {"version": 3, "file": "phoneInputHelpers.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/helpers/phoneInputHelpers.js"], "sourcesContent": ["import parsePhoneNumber_, {\r\n\tgetCountryCallingCode,\r\n\tAsYouType,\r\n\tMetadata\r\n} from 'libphonenumber-js/core'\r\n\r\nimport getInternationalPhoneNumberPrefix from './getInternationalPhoneNumberPrefix.js'\r\n\r\n/**\r\n * Decides which country should be pre-selected\r\n * when the phone number input component is first mounted.\r\n * @param  {object?} phoneNumber - An instance of `PhoneNumber` class.\r\n * @param  {string?} country - Pre-defined country (two-letter code).\r\n * @param  {string[]?} countries - A list of countries available.\r\n * @param  {object} metadata - `libphonenumber-js` metadata\r\n * @return {string?}\r\n */\r\nexport function getPreSelectedCountry({\r\n\tvalue,\r\n\tphoneNumber,\r\n\tdefaultCountry,\r\n\tgetAnyCountry,\r\n\tcountries,\r\n\trequired,\r\n\tmetadata\r\n}) {\r\n\tlet country\r\n\r\n\t// If can get country from E.164 phone number\r\n\t// then it overrides the `country` passed (or not passed).\r\n\tif (phoneNumber && phoneNumber.country) {\r\n\t\t// `country` will be left `undefined` in case of non-detection.\r\n\t\tcountry = phoneNumber.country\r\n\t} else if (defaultCountry) {\r\n\t\tif (!value || couldNumberBelongToCountry(value, defaultCountry, metadata)) {\r\n\t\t\tcountry = defaultCountry\r\n\t\t}\r\n\t}\r\n\r\n\t// Only pre-select a country if it's in the available `countries` list.\r\n\tif (countries && countries.indexOf(country) < 0) {\r\n\t\tcountry = undefined\r\n\t}\r\n\r\n\t// If there will be no \"International\" option\r\n\t// then some `country` must be selected.\r\n\t// It will still be the wrong country though.\r\n\t// But still country `<select/>` can't be left in a broken state.\r\n\tif (!country && required && countries && countries.length > 0) {\r\n\t\tcountry = getAnyCountry()\r\n\t\t// noCountryMatchesTheNumber = true\r\n\t}\r\n\r\n\treturn country\r\n}\r\n\r\n/**\r\n * Generates a sorted list of country `<select/>` options.\r\n * @param  {string[]} countries - A list of two-letter (\"ISO 3166-1 alpha-2\") country codes.\r\n * @param  {object} labels - Custom country labels. E.g. `{ RU: 'Россия', US: 'США', ... }`.\r\n * @param  {boolean} addInternationalOption - Whether should include \"International\" option at the top of the list.\r\n * @return {object[]} A list of objects having shape `{ value : string, label : string }`.\r\n */\r\nexport function getCountrySelectOptions({\r\n\tcountries,\r\n\tcountryNames,\r\n\taddInternationalOption,\r\n\t// `locales` are only used in country name comparator:\r\n\t// depending on locale, string sorting order could be different.\r\n\tcompareStringsLocales,\r\n\tcompareStrings: _compareStrings\r\n}) {\r\n\t// Default country name comparator uses `String.localeCompare()`.\r\n\tif (!_compareStrings) {\r\n\t\t_compareStrings = compareStrings\r\n\t}\r\n\r\n\t// Generates a `<Select/>` option for each country.\r\n\tconst countrySelectOptions = countries.map((country) => ({\r\n\t\tvalue: country,\r\n\t\t// All `locale` country names included in this library\r\n\t\t// include all countries (this is checked at build time).\r\n\t\t// The only case when a country name might be missing\r\n\t\t// is when a developer supplies their own `labels` property.\r\n\t\t// To guard against such cases, a missing country name\r\n\t\t// is substituted by country code.\r\n\t\tlabel: countryNames[country] || country\r\n\t}))\r\n\r\n\t// Sort the list of countries alphabetically.\r\n\tcountrySelectOptions.sort((a, b) => _compareStrings(a.label, b.label, compareStringsLocales))\r\n\r\n\t// Add the \"International\" option to the country list (if suitable)\r\n\tif (addInternationalOption) {\r\n\t\tcountrySelectOptions.unshift({\r\n\t\t\tlabel: countryNames.ZZ\r\n\t\t})\r\n\t}\r\n\r\n\treturn countrySelectOptions\r\n}\r\n\r\n/**\r\n * Parses a E.164 phone number to an instance of `PhoneNumber` class.\r\n * @param {string?} value = E.164 phone number.\r\n * @param  {object} metadata - `libphonenumber-js` metadata\r\n * @return {object} Object having shape `{ country: string?, countryCallingCode: string, number: string }`. `PhoneNumber`: https://gitlab.com/catamphetamine/libphonenumber-js#phonenumber.\r\n * @example\r\n * parsePhoneNumber('+78005553535')\r\n */\r\nexport function parsePhoneNumber(value, metadata) {\r\n\treturn parsePhoneNumber_(value || '', metadata)\r\n}\r\n\r\n/**\r\n * Generates national number digits for a parsed phone.\r\n * May prepend national prefix.\r\n * The phone number must be a complete and valid phone number.\r\n * @param  {object} phoneNumber - An instance of `PhoneNumber` class.\r\n * @param  {object} metadata - `libphonenumber-js` metadata\r\n * @return {string}\r\n * @example\r\n * getNationalNumberDigits({ country: 'RU', phone: '8005553535' })\r\n * // returns '88005553535'\r\n */\r\nexport function generateNationalNumberDigits(phoneNumber) {\r\n\treturn phoneNumber.formatNational().replace(/\\D/g, '')\r\n}\r\n\r\n/**\r\n * Migrates parsed `<input/>` `value` for the newly selected `country`.\r\n * @param {string?} phoneDigits - Phone number digits (and `+`) parsed from phone number `<input/>` (it's not the same as the `value` property).\r\n * @param {string?} prevCountry - Previously selected country.\r\n * @param {string?} newCountry - Newly selected country. Can't be same as previously selected country.\r\n * @param {object} metadata - `libphonenumber-js` metadata.\r\n * @param {boolean} useNationalFormat - whether should attempt to convert from international to national number for the new country.\r\n * @return {string?}\r\n */\r\nexport function getPhoneDigitsForNewCountry(phoneDigits, {\r\n\tprevCountry,\r\n\tnewCountry,\r\n\tmetadata,\r\n\tuseNationalFormat\r\n}) {\r\n\tif (prevCountry === newCountry) {\r\n\t\treturn phoneDigits\r\n\t}\r\n\r\n\t// If `parsed_input` is empty\r\n\t// then no need to migrate anything.\r\n\tif (!phoneDigits) {\r\n\t\tif (useNationalFormat) {\r\n\t\t\treturn ''\r\n\t\t} else {\r\n\t\t\tif (newCountry) {\r\n\t\t\t\t// If `phoneDigits` is empty then set `phoneDigits` to\r\n\t\t\t\t// `+{getCountryCallingCode(newCountry)}`.\r\n\t\t\t\treturn getInternationalPhoneNumberPrefix(newCountry, metadata)\r\n\t\t\t}\r\n\t\t\treturn ''\r\n\t\t}\r\n\t}\r\n\r\n\t// If switching to some country.\r\n\t// (from \"International\" or another country)\r\n\t// If switching from \"International\" then `phoneDigits` starts with a `+`.\r\n\t// Otherwise it may or may not start with a `+`.\r\n\tif (newCountry) {\r\n\t\t// If the phone number was entered in international format\r\n\t\t// then migrate it to the newly selected country.\r\n\t\t// The phone number may be incomplete.\r\n\t\t// The phone number entered not necessarily starts with\r\n\t\t// the previously selected country phone prefix.\r\n\t\tif (phoneDigits[0] === '+') {\r\n\t\t\t// If the international phone number is for the new country\r\n\t\t\t// then convert it to local if required.\r\n\t\t\tif (useNationalFormat) {\r\n\t\t\t\t// // If a phone number is being input in international form\r\n\t\t\t\t// // and the country can already be derived from it,\r\n\t\t\t\t// // and if it is the new country, then format as a national number.\r\n\t\t\t\t// const derived_country = getCountryFromPossiblyIncompleteInternationalPhoneNumber(phoneDigits, metadata)\r\n\t\t\t\t// if (derived_country === newCountry) {\r\n\t\t\t\t// \treturn stripCountryCallingCode(phoneDigits, derived_country, metadata)\r\n\t\t\t\t// }\r\n\r\n\t\t\t\t// Actually, the two countries don't necessarily need to match:\r\n\t\t\t\t// the condition could be looser here, because several countries\r\n\t\t\t\t// might share the same international phone number format\r\n\t\t\t\t// (for example, \"NANPA\" countries like US, Canada, etc).\r\n\t\t\t\t// The looser condition would be just \"same nternational phone number format\"\r\n\t\t\t\t// which would mean \"same country calling code\" in the context of `libphonenumber-js`.\r\n\t\t\t\tif (phoneDigits.indexOf('+' + getCountryCallingCode(newCountry, metadata)) === 0) {\r\n\t\t\t\t\treturn stripCountryCallingCode(phoneDigits, newCountry, metadata)\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Simply discard the previously entered international phone number,\r\n\t\t\t\t// because otherwise any \"smart\" transformation like getting the\r\n\t\t\t\t// \"national (significant) number\" part and then prepending the\r\n\t\t\t\t// newly selected country's \"country calling code\" to it\r\n\t\t\t\t// would just be confusing for a user without being actually useful.\r\n\t\t\t\treturn ''\r\n\r\n\t\t\t\t// // Simply strip the leading `+` character\r\n\t\t\t\t// // therefore simply converting all digits into a \"local\" phone number.\r\n\t\t\t\t// // https://github.com/catamphetamine/react-phone-number-input/issues/287\r\n\t\t\t\t// return phoneDigits.slice(1)\r\n\t\t\t}\r\n\r\n\t\t\tif (prevCountry) {\r\n\t\t\t\tconst newCountryPrefix = getInternationalPhoneNumberPrefix(newCountry, metadata)\r\n\t\t\t\tif (phoneDigits.indexOf(newCountryPrefix) === 0) {\r\n\t\t\t\t\treturn phoneDigits\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn newCountryPrefix\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tconst defaultValue = getInternationalPhoneNumberPrefix(newCountry, metadata)\r\n\t\t\t\t// If `phoneDigits`'s country calling code part is the same\r\n\t\t\t\t// as for the new `country`, then leave `phoneDigits` as is.\r\n\t\t\t\tif (phoneDigits.indexOf(defaultValue) === 0) {\r\n\t\t\t\t\treturn phoneDigits\r\n\t\t\t\t}\r\n\t\t\t\t// If `phoneDigits`'s country calling code part is not the same\r\n\t\t\t\t// as for the new `country`, then set `phoneDigits` to\r\n\t\t\t\t// `+{getCountryCallingCode(newCountry)}`.\r\n\t\t\t\treturn defaultValue\r\n\t\t\t}\r\n\r\n\t\t\t// // If the international phone number already contains\r\n\t\t\t// // any country calling code then trim the country calling code part.\r\n\t\t\t// // (that could also be the newly selected country phone code prefix as well)\r\n\t\t\t// // `phoneDigits` doesn't neccessarily belong to `prevCountry`.\r\n\t\t\t// // (e.g. if a user enters an international number\r\n\t\t\t// //  not belonging to any of the reduced `countries` list).\r\n\t\t\t// phoneDigits = stripCountryCallingCode(phoneDigits, prevCountry, metadata)\r\n\r\n\t\t\t// // Prepend country calling code prefix\r\n\t\t\t// // for the newly selected country.\r\n\t\t\t// return e164(phoneDigits, newCountry, metadata) || `+${getCountryCallingCode(newCountry, metadata)}`\r\n\t\t}\r\n\t}\r\n\t// If switching to \"International\" from a country.\r\n\telse {\r\n\t\t// If the phone number was entered in national format.\r\n\t\tif (phoneDigits[0] !== '+') {\r\n\t\t\t// Format the national phone number as an international one.\r\n\t\t\t// The phone number entered not necessarily even starts with\r\n\t\t\t// the previously selected country phone prefix.\r\n\t\t\t// Even if the phone number belongs to whole another country\r\n\t\t\t// it will still be parsed into some national phone number.\r\n\t\t\t//\r\n\t\t\t// Ignore the now-uncovered `|| ''` code branch:\r\n\t\t\t// previously `e164()` function could return an empty string\r\n\t\t\t// even when `phoneDigits` were not empty.\r\n\t\t\t// Now it always returns some `value` when there're any `phoneDigits`.\r\n\t\t\t// Still, didn't remove the `|| ''` code branch just in case\r\n\t\t\t// that logic changes somehow in some future, so there're no\r\n\t\t\t// possible bugs related to that.\r\n\t\t\t//\r\n\t\t\t// (ignore the `|| ''` code branch)\r\n\t\t\t/* istanbul ignore next */\r\n\t\t\treturn e164(phoneDigits, prevCountry, metadata) || ''\r\n\t\t}\r\n\t}\r\n\r\n\treturn phoneDigits\r\n}\r\n\r\n/**\r\n * Converts phone number digits to a (possibly incomplete) E.164 phone number.\r\n * @param  {string?} number - A possibly incomplete phone number digits string. Can be a possibly incomplete E.164 phone number.\r\n * @param  {string?} country\r\n * @param  {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string?}\r\n */\r\nexport function e164(number, country, metadata) {\r\n\tif (!number) {\r\n\t\treturn\r\n\t}\r\n\t// If the phone number is being input in international format.\r\n\tif (number[0] === '+') {\r\n\t\t// If it's just the `+` sign then return nothing.\r\n\t\tif (number === '+') {\r\n\t\t\treturn\r\n\t\t}\r\n\t\t// Return a E.164 phone number.\r\n\t\t//\r\n\t\t// Could return `number` \"as is\" here, but there's a possibility\r\n\t\t// that some user might incorrectly input an international number\r\n\t\t// with a \"national prefix\". Such numbers aren't considered valid,\r\n\t\t// but `libphonenumber-js` is \"forgiving\" when it comes to parsing\r\n\t\t// user's input, and this input component follows that behavior.\r\n\t\t//\r\n\t\tconst asYouType = new AsYouType(country, metadata)\r\n\t\tasYouType.input(number)\r\n\t\t// This function would return `undefined` only when `number` is `\"+\"`,\r\n\t\t// but at this point it is known that `number` is not `\"+\"`.\r\n\t\treturn asYouType.getNumberValue()\r\n\t}\r\n\t// For non-international phone numbers\r\n\t// an accompanying country code is required.\r\n\t// The situation when `country` is `undefined`\r\n\t// and a non-international phone number is passed\r\n\t// to this function shouldn't happen.\r\n\tif (!country) {\r\n\t\treturn\r\n\t}\r\n\tconst partial_national_significant_number = getNationalSignificantNumberDigits(number, country, metadata)\r\n\t//\r\n\t// Even if no \"national (significant) number\" digits have been input,\r\n\t// still return a non-`undefined` value.\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/113\r\n\t//\r\n\t// For example, if the user has selected country `US` and entered `\"1\"`\r\n\t// then that `\"1\"` is just a \"national prefix\" and no \"national (significant) number\"\r\n\t// digits have been input yet. Still, return `\"+1\"` as `value` in such cases,\r\n\t// because otherwise the app would think that the input is empty and mark it as such\r\n\t// while in reality it isn't empty, which might be thought of as a \"bug\", or just\r\n\t// a \"weird\" behavior.\r\n\t//\r\n\t// if (partial_national_significant_number) {\r\n\t\treturn `+${getCountryCallingCode(country, metadata)}${partial_national_significant_number || ''}`\r\n\t// }\r\n}\r\n\r\n/**\r\n * Trims phone number digits if they exceed the maximum possible length\r\n * for a national (significant) number for the country.\r\n * @param  {string} number - A possibly incomplete phone number digits string. Can be a possibly incomplete E.164 phone number.\r\n * @param  {string} country\r\n * @param  {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string} Can be empty.\r\n */\r\nexport function trimNumber(number, country, metadata) {\r\n\tconst nationalSignificantNumberPart = getNationalSignificantNumberDigits(number, country, metadata)\r\n\tif (nationalSignificantNumberPart) {\r\n\t\tconst overflowDigitsCount = nationalSignificantNumberPart.length - getMaxNumberLength(country, metadata)\r\n\t\tif (overflowDigitsCount > 0) {\r\n\t\t\treturn number.slice(0, number.length - overflowDigitsCount)\r\n\t\t}\r\n\t}\r\n\treturn number\r\n}\r\n\r\nfunction getMaxNumberLength(country, metadata) {\r\n\t// Get \"possible lengths\" for a phone number of the country.\r\n\tmetadata = new Metadata(metadata)\r\n\tmetadata.selectNumberingPlan(country)\r\n\t// Return the last \"possible length\".\r\n\treturn metadata.numberingPlan.possibleLengths()[metadata.numberingPlan.possibleLengths().length - 1]\r\n}\r\n\r\n// If the phone number being input is an international one\r\n// then tries to derive the country from the phone number.\r\n// (regardless of whether there's any country currently selected)\r\n/**\r\n * @param {string} partialE164Number - A possibly incomplete E.164 phone number.\r\n * @param {string?} country - Currently selected country.\r\n * @param {string[]?} countries - A list of available countries. If not passed then \"all countries\" are assumed.\r\n * @param {string?} defaultCountry — Default country.\r\n * @param {string?} latestCountrySelectedByUser — The latest country that has been manually selected by the user.\r\n * @param {boolean?} required — Whether \"International\" option could be selected, meaning \"no country is selected\".\r\n * @param {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string?}\r\n */\r\nexport function getCountryForPartialE164Number(partialE164Number, {\r\n\tcountry,\r\n\tcountries,\r\n\tdefaultCountry,\r\n\tlatestCountrySelectedByUser,\r\n\trequired,\r\n\tmetadata\r\n}) {\r\n\t// `partialE164Number` is supposed to be an E.164 phone number.\r\n\r\n\t// `partialE164Number` is supposed to be non-empty when calling this function\r\n\t// so it doesn't check for `if (!partialE164Number)`.\r\n\r\n\tif (partialE164Number === '+') {\r\n\t\t// Don't change the currently selected country yet.\r\n\t\treturn country\r\n\t}\r\n\r\n\tconst derived_country = getCountryFromPossiblyIncompleteInternationalPhoneNumber(partialE164Number, metadata)\r\n\r\n\t// If a phone number is being input in international form\r\n\t// and the country can already be derived from it,\r\n\t// then select that country.\r\n\tif (derived_country) {\r\n\t\tif (!countries || (countries.indexOf(derived_country) >= 0)) {\r\n\t\t\treturn derived_country\r\n\t\t} else {\r\n\t\t\treturn undefined\r\n\t\t}\r\n\t}\r\n\t// Otherwise, if the phone number doesn't correspond to any particular country.\r\n\t// If some country was previously selected.\r\n\telse if (country) {\r\n\t\t// If the international phone number entered could still correspond to the previously selected country\r\n\t\t// and also to some other country or countries corresponding to the same calling code\r\n\t\t// then it should reset the currently selected country to reflect the ambiguity.\r\n\t\tif (couldNumberBelongToCountry(partialE164Number, country, metadata)) {\r\n\t\t\t// Reset the country either to the latest one that was manually selected by the user\r\n\t\t\t// or to the default country or just reset the country selection.\r\n\t\t\tif (latestCountrySelectedByUser && couldNumberBelongToCountry(partialE164Number, latestCountrySelectedByUser, metadata)) {\r\n\t\t\t\treturn latestCountrySelectedByUser\r\n\t\t\t} else if (defaultCountry && couldNumberBelongToCountry(partialE164Number, defaultCountry, metadata)) {\r\n\t\t\t\treturn defaultCountry\r\n\t\t\t} else {\r\n\t\t\t\tif (!required) {\r\n\t\t\t\t\t// Just reset the currently selected country.\r\n\t\t\t\t\treturn undefined\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// If \"International\" country option has not been disabled\r\n\t\t\t// and the international phone number entered doesn't necessarily correspond to\r\n\t\t\t// the currently selected country and it could not possibly correspond to it\r\n\t\t\t// then reset the currently selected country.\r\n\t\t\tif (!required) {\r\n\t\t\t\treturn undefined\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// Don't change the currently selected country.\r\n\treturn country\r\n}\r\n\r\n/**\r\n * Parses `<input/>` value. Derives `country` from `input`. Derives an E.164 `value`.\r\n * @param  {string?} phoneDigits — Parsed `<input/>` value. Examples: `\"\"`, `\"+\"`, `\"+123\"`, `\"123\"`.\r\n * @param  {string?} prevPhoneDigits — Previous parsed `<input/>` value. Examples: `\"\"`, `\"+\"`, `\"+123\"`, `\"123\"`.\r\n * @param  {string?} country - Currently selected country.\r\n * @param  {string?} defaultCountry - Default country.\r\n * @param  {string?} latestCountrySelectedByUser - The latest country that has been manually selected by the user.\r\n * @param  {boolean} countryRequired - Is selecting some country required.\r\n * @param  {function} getAnyCountry - Can be used to get any country when selecting some country required.\r\n * @param  {string[]?} countries - A list of available countries. If not passed then \"all countries\" are assumed.\r\n * @param  {boolean} international - Set to `true` to force international phone number format (leading `+`). Set to `false` to force \"national\" phone number format. Is `undefined` by default.\r\n * @param  {boolean} limitMaxLength — Whether to enable limiting phone number max length.\r\n * @param  {object} metadata - `libphonenumber-js` metadata.\r\n * @return {object} An object of shape `{ phoneDigits, country, value }`. `phoneDigits` returned here are a \"normalized\" version of the original `phoneDigits`. The returned `phoneDigits` shouldn't be used anywhere except for passing it as `prevPhoneDigits` parameter to this same function on next input change event.\r\n */\r\nexport function onPhoneDigitsChange(phoneDigits, {\r\n\tprevPhoneDigits,\r\n\tcountry,\r\n\tdefaultCountry,\r\n\tlatestCountrySelectedByUser,\r\n\tcountryRequired,\r\n\tgetAnyCountry,\r\n\tcountries,\r\n\tinternational,\r\n\tlimitMaxLength,\r\n\tcountryCallingCodeEditable,\r\n\tmetadata\r\n}) {\r\n\t// When the input is in `international` and `countryCallingCodeEditable={false}` mode,\r\n\t// the `country` should not change. If the user attempted to overwrite the country callling code part,\r\n\t// the component should reset it back to the correct country calling code for the `country`.\r\n\tif (international && countryCallingCodeEditable === false) {\r\n\t\tif (country) {\r\n\t\t\t// For international phone numbers written with non-editable country calling code,\r\n\t\t\t// the `<input/>` value must always start with that non-editable country calling code.\r\n\t\t\tconst prefix = getInternationalPhoneNumberPrefix(country, metadata)\r\n\t\t\t// If the input value doesn't start with the non-editable country calling code,\r\n\t\t\t// it should be fixed.\r\n\t\t\tif (phoneDigits.indexOf(prefix) !== 0) {\r\n\t\t\t\tlet value\r\n\t\t\t\t// If a phone number input is declared as\r\n\t\t\t\t// `international: true` and `countryCallingCodeEditable: false`,\r\n\t\t\t\t// then the value of the `<input/>` is gonna be non-empty at all times,\r\n\t\t\t\t// even before the user has started to input any digits in the input field,\r\n\t\t\t\t// because the country calling code is always there by design.\r\n\t\t\t\t//\r\n\t\t\t\t// The fact that the input value is always non-empty results in a side effect:\r\n\t\t\t\t// whenever a user tabs into such input field, its value gets automatically selected.\r\n\t\t\t\t// If at that moment in time the user starts typing in the national digits of the phone number,\r\n\t\t\t\t// the selected `<input/>` value gets automatically replaced by those typed-in digits\r\n\t\t\t\t// so the value changes from `+xxx` to `y`, because inputting anything while having\r\n\t\t\t\t// the `<input/>` value selected results in erasing that `<input/>` value.\r\n\t\t\t\t//\r\n\t\t\t\t// This component handles such cases by restoring the `<input/>` value to what\r\n\t\t\t\t// it should be in such cases: `+xxxy`.\r\n\t\t\t\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/43\r\n\t\t\t\t//\r\n\t\t\t\tconst hasStartedTypingInNationalNumberDigitsHavingInputValueSelected = phoneDigits && phoneDigits[0] !== '+'\r\n\t\t\t\tif (hasStartedTypingInNationalNumberDigitsHavingInputValueSelected) {\r\n\t\t\t\t\t// Fix the input value to what it should be: `y` → `+xxxy`.\r\n\t\t\t\t\tphoneDigits = prefix + phoneDigits\r\n\t\t\t\t\tvalue = e164(phoneDigits, country, metadata)\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// In other cases, simply reset the `<input/>` value, because there're only two\r\n\t\t\t\t\t// possible cases:\r\n\t\t\t\t\t// * The user has selected the `<input/>` value and then hit Delete/Backspace to erase it.\r\n\t\t\t\t\t// * The user has pasted an international phone number for another country calling code,\r\n\t\t\t\t\t//   which is considered a non-valid value.\r\n\t\t\t\t\tphoneDigits = prefix\r\n\t\t\t\t}\r\n\t\t\t\treturn {\r\n\t\t\t\t\tphoneDigits,\r\n\t\t\t\t\tvalue,\r\n\t\t\t\t\tcountry\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// If `international` property is `false`, then it means\r\n\t// \"enforce national-only format during input\",\r\n\t// so, if that's the case, then remove all `+` characters,\r\n\t// but only if some country is currently selected.\r\n\t// (not if \"International\" country is selected).\r\n\tif (international === false && country && phoneDigits && phoneDigits[0] === '+') {\r\n\t\tphoneDigits = convertInternationalPhoneDigitsToNational(phoneDigits, country, metadata)\r\n\t}\r\n\r\n\t// Trim the input to not exceed the maximum possible number length.\r\n\tif (phoneDigits && country && limitMaxLength) {\r\n\t\tphoneDigits = trimNumber(phoneDigits, country, metadata)\r\n\t}\r\n\r\n\t// If this `onChange()` event was triggered\r\n\t// as a result of selecting \"International\" country,\r\n\t// then force-prepend a `+` sign if the phone number\r\n\t// `<input/>` value isn't in international format.\r\n\t// Also, force-prepend a `+` sign if international\r\n\t// phone number input format is set.\r\n\tif (phoneDigits && phoneDigits[0] !== '+' && (!country || international)) {\r\n\t\tphoneDigits = '+' + phoneDigits\r\n\t}\r\n\r\n\t// If the previously entered phone number\r\n\t// has been entered in international format\r\n\t// and the user decides to erase it,\r\n\t// then also reset the `country`\r\n\t// because it was most likely automatically selected\r\n\t// while the user was typing in the phone number\r\n\t// in international format.\r\n\t// This fixes the issue when a user is presented\r\n\t// with a phone number input with no country selected\r\n\t// and then types in their local phone number\r\n\t// then discovers that the input's messed up\r\n\t// (a `+` has been prepended at the start of their input\r\n\t//  and a random country has been selected),\r\n\t// decides to undo it all by erasing everything\r\n\t// and then types in their local phone number again\r\n\t// resulting in a seemingly correct phone number\r\n\t// but in reality that phone number has incorrect country.\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/273\r\n\tif (!phoneDigits && prevPhoneDigits && prevPhoneDigits[0] === '+') {\r\n\t\tif (international) {\r\n\t\t\tcountry = undefined\r\n\t\t} else {\r\n\t\t\tcountry = defaultCountry\r\n\t\t}\r\n\t}\r\n\t// Also resets such \"randomly\" selected country\r\n\t// as soon as the user erases the number\r\n\t// digit-by-digit up to the leading `+` sign.\r\n\tif (phoneDigits === '+' && prevPhoneDigits && prevPhoneDigits[0] === '+' && prevPhoneDigits.length > '+'.length) {\r\n\t\tcountry = undefined\r\n\t}\r\n\r\n\t// Generate the new `value` property.\r\n\tlet value\r\n\tif (phoneDigits) {\r\n\t\tif (phoneDigits[0] === '+') {\r\n\t\t\tif (phoneDigits === '+') {\r\n\t\t\t\tvalue = undefined\r\n\t\t\t} else if (country && getInternationalPhoneNumberPrefix(country, metadata).indexOf(phoneDigits) === 0) {\r\n\t\t\t\t// Selected a `country` and started inputting an\r\n\t\t\t\t// international phone number for this country\r\n\t\t\t\t// but hasn't input any \"national (significant) number\" digits yet.\r\n\t\t\t\t// In that case, assume `value` be `undefined`.\r\n\t\t\t\t//\r\n\t\t\t\t// For example, if selected `country` `\"US\"`\r\n\t\t\t\t// and started inputting phone number `\"+1\"`\r\n\t\t\t\t// then `value` `undefined` will be returned from this function.\r\n\t\t\t\t//\r\n\t\t\t\tvalue = undefined\r\n\t\t\t} else {\r\n\t\t\t\tvalue = e164(phoneDigits, country, metadata)\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tvalue = e164(phoneDigits, country, metadata)\r\n\t\t}\r\n\t}\r\n\r\n\t// Derive the country from the phone number.\r\n\t// (regardless of whether there's any country currently selected,\r\n\t//  because there could be several countries corresponding to one country calling code)\r\n\tif (value) {\r\n\t\tcountry = getCountryForPartialE164Number(value, {\r\n\t\t\tcountry,\r\n\t\t\tcountries,\r\n\t\t\tdefaultCountry,\r\n\t\t\tlatestCountrySelectedByUser,\r\n\t\t\t// `countryRequired` flag is not passed here.\r\n\t\t\t// Instead, it's explicitly checked a bit later in the code.\r\n\t\t\trequired: false,\r\n\t\t\tmetadata\r\n\t\t})\r\n\t\t// If `international` property is `false`, then it means\r\n\t\t// \"enforce national-only format during input\",\r\n\t\t// so, if that's the case, then remove all `+` characters,\r\n\t\t// but only if some country is currently selected.\r\n\t\t// (not if \"International\" country is selected).\r\n\t\tif (international === false && country && phoneDigits && phoneDigits[0] === '+') {\r\n\t\t\tphoneDigits = convertInternationalPhoneDigitsToNational(phoneDigits, country, metadata)\r\n\t\t\t// Re-calculate `value` because `phoneDigits` has changed.\r\n\t\t\tvalue = e164(phoneDigits, country, metadata)\r\n\t\t}\r\n\t}\r\n\r\n\tif (!country && countryRequired) {\r\n\t\tcountry = defaultCountry || getAnyCountry()\r\n\t}\r\n\r\n\treturn {\r\n\t\t// `phoneDigits` returned here are a \"normalized\" version of the original `phoneDigits`.\r\n\t\t// The returned `phoneDigits` shouldn't be used anywhere except for passing it as\r\n\t\t// `prevPhoneDigits` parameter to this same function on next input change event.\r\n\t\tphoneDigits,\r\n\t\tcountry,\r\n\t\tvalue\r\n\t}\r\n}\r\n\r\nfunction convertInternationalPhoneDigitsToNational(input, country, metadata) {\r\n\t// Handle the case when a user might have pasted\r\n\t// a phone number in international format.\r\n\tif (input.indexOf(getInternationalPhoneNumberPrefix(country, metadata)) === 0) {\r\n\t\t// Create \"as you type\" formatter.\r\n\t\tconst formatter = new AsYouType(country, metadata)\r\n\t\t// Input partial national phone number.\r\n\t\tformatter.input(input)\r\n\t\t// Return the parsed partial national phone number.\r\n\t\tconst phoneNumber = formatter.getNumber()\r\n\t\tif (phoneNumber) {\r\n\t\t\t// Transform the number to a national one,\r\n\t\t\t// and remove all non-digits.\r\n\t\t\treturn phoneNumber.formatNational().replace(/\\D/g, '')\r\n\t\t} else {\r\n\t\t\treturn ''\r\n\t\t}\r\n\t} else {\r\n\t\t// Just remove the `+` sign.\r\n\t\treturn input.replace(/\\D/g, '')\r\n\t}\r\n}\r\n\r\n/**\r\n * Determines the country for a given (possibly incomplete) E.164 phone number.\r\n * @param  {string} number - A possibly incomplete E.164 phone number.\r\n * @param  {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string?}\r\n */\r\nexport function getCountryFromPossiblyIncompleteInternationalPhoneNumber(number, metadata) {\r\n\tconst formatter = new AsYouType(null, metadata)\r\n\tformatter.input(number)\r\n\t// // `001` is a special \"non-geograpical entity\" code\r\n\t// // in Google's `libphonenumber` library.\r\n\t// if (formatter.getCountry() === '001') {\r\n\t// \treturn\r\n\t// }\r\n\treturn formatter.getCountry()\r\n}\r\n\r\n/**\r\n * Compares two strings.\r\n * A helper for `Array.sort()`.\r\n * @param {string} a — First string.\r\n * @param {string} b — Second string.\r\n * @param {(string[]|string)} [locales] — The `locales` argument of `String.localeCompare`.\r\n */\r\nexport function compareStrings(a, b, locales) {\r\n  // Use `String.localeCompare` if it's available.\r\n  // https://developer.mozilla.org/ru/docs/Web/JavaScript/Reference/Global_Objects/String/localeCompare\r\n  // Which means everyone except IE <= 10 and Safari <= 10.\r\n  // `localeCompare()` is available in latest Node.js versions.\r\n  /* istanbul ignore else */\r\n  if (String.prototype.localeCompare) {\r\n    return a.localeCompare(b, locales);\r\n  }\r\n  /* istanbul ignore next */\r\n  return a < b ? -1 : (a > b ? 1 : 0);\r\n}\r\n\r\n/**\r\n * Strips `+${countryCallingCode}` prefix from an E.164 phone number.\r\n * @param {string} number - (possibly incomplete) E.164 phone number.\r\n * @param {string?} country - A possible country for this phone number.\r\n * @param {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string}\r\n */\r\nexport function stripCountryCallingCode(number, country, metadata) {\r\n\t// Just an optimization, so that it\r\n\t// doesn't have to iterate through all country calling codes.\r\n\tif (country) {\r\n\t\tconst countryCallingCodePrefix = '+' + getCountryCallingCode(country, metadata)\r\n\r\n\t\t// If `country` fits the actual `number`.\r\n\t\tif (number.length < countryCallingCodePrefix.length) {\r\n\t\t\tif (countryCallingCodePrefix.indexOf(number) === 0) {\r\n\t\t\t\treturn ''\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (number.indexOf(countryCallingCodePrefix) === 0) {\r\n\t\t\t\treturn number.slice(countryCallingCodePrefix.length)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// If `country` doesn't fit the actual `number`.\r\n\t// Try all available country calling codes.\r\n\tfor (const country_calling_code of Object.keys(metadata.country_calling_codes)) {\r\n\t\tif (number.indexOf(country_calling_code) === '+'.length) {\r\n\t\t\treturn number.slice('+'.length + country_calling_code.length)\r\n\t\t}\r\n\t}\r\n\r\n\treturn ''\r\n}\r\n\r\n/**\r\n * Parses a partially entered national phone number digits\r\n * (or a partially entered E.164 international phone number)\r\n * and returns the national significant number part.\r\n * National significant number returned doesn't come with a national prefix.\r\n * @param {string} number - National number digits. Or possibly incomplete E.164 phone number.\r\n * @param {string?} country\r\n * @param {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string} [result]\r\n */\r\nexport function getNationalSignificantNumberDigits(number, country, metadata) {\r\n\t// Create \"as you type\" formatter.\r\n\tconst formatter = new AsYouType(country, metadata)\r\n\t// Input partial national phone number.\r\n\tformatter.input(number)\r\n\t// Return the parsed partial national phone number.\r\n\tconst phoneNumber = formatter.getNumber()\r\n\treturn phoneNumber && phoneNumber.nationalNumber\r\n}\r\n\r\n/**\r\n * Checks if a partially entered E.164 phone number could belong to a country.\r\n * @param  {string} number\r\n * @param  {string} country\r\n * @return {boolean}\r\n */\r\nexport function couldNumberBelongToCountry(number, country, metadata) {\r\n\tconst intlPhoneNumberPrefix = getInternationalPhoneNumberPrefix(country, metadata)\r\n\tlet i = 0\r\n\twhile (i < number.length && i < intlPhoneNumberPrefix.length) {\r\n\t\tif (number[i] !== intlPhoneNumberPrefix[i]) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\treturn true\r\n}\r\n\r\n/**\r\n * Gets initial \"phone digits\" (including `+`, if using international format).\r\n * @return {string} [phoneDigits] Returns `undefined` if there should be no initial \"phone digits\".\r\n */\r\nexport function getInitialPhoneDigits({\r\n\tvalue,\r\n\tphoneNumber,\r\n\tdefaultCountry,\r\n\tinternational,\r\n\tuseNationalFormat,\r\n\tmetadata\r\n}) {\r\n\t// If the `value` (E.164 phone number)\r\n\t// belongs to the currently selected country\r\n\t// and `useNationalFormat` is `true`\r\n\t// then convert `value` (E.164 phone number)\r\n\t// to a local phone number digits.\r\n\t// E.g. '+78005553535' -> '88005553535'.\r\n\tif ((international === false || useNationalFormat) && phoneNumber && phoneNumber.country) {\r\n\t\treturn generateNationalNumberDigits(phoneNumber)\r\n\t}\r\n\t// If `international` property is `true`,\r\n\t// meaning \"enforce international phone number format\",\r\n\t// then always show country calling code in the input field.\r\n\tif (!value && international && defaultCountry) {\r\n\t\treturn getInternationalPhoneNumberPrefix(defaultCountry, metadata)\r\n\t}\r\n\treturn value\r\n}\r\n\r\n// function doesIncompletePhoneNumberCorrespondToASingleCountry(value, metadata) {\r\n// \t// Create \"as you type\" formatter.\r\n// \tconst formatter = new AsYouType(undefined, metadata)\r\n// \t// Input partial national phone number.\r\n// \tformatter.input(value)\r\n// \t// Return the parsed partial national phone number.\r\n// \tconst phoneNumber = formatter.getNumber()\r\n// \tif (phoneNumber) {\r\n// \t\treturn phoneNumber.getPossibleCountries().length === 1\r\n// \t} else {\r\n// \t\treturn false\r\n// \t}\r\n// }"], "names": ["parsePhoneNumber_", "getCountryCallingCode", "AsYouType", "<PERSON><PERSON><PERSON>", "getInternationalPhoneNumberPrefix", "getPreSelectedCountry", "_ref", "value", "phoneNumber", "defaultCountry", "getAnyCountry", "countries", "required", "metadata", "country", "couldNumberBelongToCountry", "indexOf", "undefined", "length", "getCountrySelectOptions", "_ref2", "countryNames", "addInternationalOption", "compareStringsLocales", "_compareStrings", "compareStrings", "countrySelectOptions", "map", "label", "sort", "a", "b", "unshift", "ZZ", "parsePhoneNumber", "generateNationalNumberDigits", "formatNational", "replace", "getPhoneDigitsForNewCountry", "phoneDigits", "_ref3", "prevCountry", "newCountry", "useNationalFormat", "stripCountryCallingCode", "newCountryPrefix", "defaultValue", "e164", "number", "asYouType", "input", "getNumberValue", "partial_national_significant_number", "getNationalSignificantNumberDigits", "concat", "trimNumber", "nationalSignificantNumberPart", "overflowDigitsCount", "getMaxNumberLength", "slice", "selectNumberingPlan", "numberingPlan", "possibleLengths", "getCountryForPartialE164Number", "partialE164Number", "_ref4", "latestCountrySelectedByUser", "derived_country", "getCountryFromPossiblyIncompleteInternationalPhoneNumber", "onPhoneDigitsChange", "_ref5", "prevPhoneDigits", "countryRequired", "international", "limitMaxLength", "countryCallingCodeEditable", "prefix", "hasStartedTypingInNationalNumberDigitsHavingInputValueSelected", "convertInternationalPhoneDigitsToNational", "formatter", "getNumber", "getCountry", "locales", "String", "prototype", "localeCompare", "countryCallingCodePrefix", "_i", "_Object$keys", "Object", "keys", "country_calling_codes", "country_calling_code", "nationalNumber", "intlPhoneNumberPrefix", "i", "getInitialPhoneDigits", "_ref6"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAOA,iBAAiB,IACvBC,qBAAqB,EACrBC,SAAS,EACTC,QAAQ,QACF,wBAAwB;;;;AAE/B,OAAOC,iCAAiC,MAAM,wCAAwC;;;AAW/E,SAASC,qBAAqBA,CAAAC,IAAA,EAQlC;IAAA,IAPFC,KAAK,GAAAD,IAAA,CAALC,KAAK,EACLC,WAAW,GAAAF,IAAA,CAAXE,WAAW,EACXC,cAAc,GAAAH,IAAA,CAAdG,cAAc,EACdC,aAAa,GAAAJ,IAAA,CAAbI,aAAa,EACbC,SAAS,GAAAL,IAAA,CAATK,SAAS,EACTC,QAAQ,GAAAN,IAAA,CAARM,QAAQ,EACRC,QAAQ,GAAAP,IAAA,CAARO,QAAQ;IAER,IAAIC,OAAO;IAEX,6CAAA;IACA,0DAAA;IACA,IAAIN,WAAW,IAAIA,WAAW,CAACM,OAAO,EAAE;QACvC,+DAAA;QACAA,OAAO,GAAGN,WAAW,CAACM,OAAO;IAC9B,CAAC,MAAM,IAAIL,cAAc,EAAE;QAC1B,IAAI,CAACF,KAAK,IAAIQ,0BAA0B,CAACR,KAAK,EAAEE,cAAc,EAAEI,QAAQ,CAAC,EAAE;YAC1EC,OAAO,GAAGL,cAAc;QACzB;IACD;IAEA,uEAAA;IACA,IAAIE,SAAS,IAAIA,SAAS,CAACK,OAAO,CAACF,OAAO,CAAC,GAAG,CAAC,EAAE;QAChDA,OAAO,GAAGG,SAAS;IACpB;IAEA,6CAAA;IACA,wCAAA;IACA,6CAAA;IACA,iEAAA;IACA,IAAI,CAACH,OAAO,IAAIF,QAAQ,IAAID,SAAS,IAAIA,SAAS,CAACO,MAAM,GAAG,CAAC,EAAE;QAC9DJ,OAAO,GAAGJ,aAAa,CAAC,CAAC;IACzB,mCAAA;IACD;IAEA,OAAOI,OAAO;AACf;AASO,SAASK,uBAAuBA,CAAAC,KAAA,EAQpC;IAAA,IAPFT,SAAS,GAAAS,KAAA,CAATT,SAAS,EACTU,YAAY,GAAAD,KAAA,CAAZC,YAAY,EACZC,sBAAsB,GAAAF,KAAA,CAAtBE,sBAAsB,EAGtBC,qBAAqB,GAAAH,KAAA,CAArBG,qBAAqB,EACLC,eAAe,GAAAJ,KAAA,CAA/BK,cAAc;IAEd,iEAAA;IACA,IAAI,CAACD,eAAe,EAAE;QACrBA,eAAe,GAAGC,cAAc;IACjC;IAEA,mDAAA;IACA,IAAMC,oBAAoB,GAAGf,SAAS,CAACgB,GAAG,CAAC,SAACb,OAAO;QAAA,OAAM;YACxDP,KAAK,EAAEO,OAAO;YACd,sDAAA;YACA,yDAAA;YACA,qDAAA;YACA,4DAAA;YACA,sDAAA;YACA,kCAAA;YACAc,KAAK,EAAEP,YAAY,CAACP,OAAO,CAAC,IAAIA;QACjC,CAAC;IAAA,CAAC,CAAC;IAEH,6CAAA;IACAY,oBAAoB,CAACG,IAAI,CAAC,SAACC,CAAC,EAAEC,CAAC;QAAA,OAAKP,eAAe,CAACM,CAAC,CAACF,KAAK,EAAEG,CAAC,CAACH,KAAK,EAAEL,qBAAqB,CAAC;IAAA,EAAC;IAE7F,mEAAA;IACA,IAAID,sBAAsB,EAAE;QAC3BI,oBAAoB,CAACM,OAAO,CAAC;YAC5BJ,KAAK,EAAEP,YAAY,CAACY,EAAAA;QACrB,CAAC,CAAC;IACH;IAEA,OAAOP,oBAAoB;AAC5B;AAUO,SAASQ,gBAAgBA,CAAC3B,KAAK,EAAEM,QAAQ,EAAE;IACjD,uKAAOb,UAAAA,AAAiB,EAACO,KAAK,IAAI,EAAE,EAAEM,QAAQ,CAAC;AAChD;AAaO,SAASsB,4BAA4BA,CAAC3B,WAAW,EAAE;IACzD,OAAOA,WAAW,CAAC4B,cAAc,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AACvD;AAWO,SAASC,2BAA2BA,CAACC,WAAW,EAAAC,KAAA,EAKpD;IAAA,IAJFC,WAAW,GAAAD,KAAA,CAAXC,WAAW,EACXC,UAAU,GAAAF,KAAA,CAAVE,UAAU,EACV7B,QAAQ,GAAA2B,KAAA,CAAR3B,QAAQ,EACR8B,iBAAiB,GAAAH,KAAA,CAAjBG,iBAAiB;IAEjB,IAAIF,WAAW,KAAKC,UAAU,EAAE;QAC/B,OAAOH,WAAW;IACnB;IAEA,6BAAA;IACA,oCAAA;IACA,IAAI,CAACA,WAAW,EAAE;QACjB,IAAII,iBAAiB,EAAE;YACtB,OAAO,EAAE;QACV,CAAC,MAAM;YACN,IAAID,UAAU,EAAE;gBACf,sDAAA;gBACA,0CAAA;gBACA,mNAAOtC,WAAAA,AAAiC,EAACsC,UAAU,EAAE7B,QAAQ,CAAC;YAC/D;YACA,OAAO,EAAE;QACV;IACD;IAEA,gCAAA;IACA,4CAAA;IACA,0EAAA;IACA,gDAAA;IACA,IAAI6B,UAAU,EAAE;QACf,0DAAA;QACA,iDAAA;QACA,sCAAA;QACA,uDAAA;QACA,gDAAA;QACA,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAC3B,2DAAA;YACA,wCAAA;YACA,IAAII,iBAAiB,EAAE;gBACtB,4DAAA;gBACA,qDAAA;gBACA,qEAAA;gBACA,0GAAA;gBACA,wCAAA;gBACA,0EAAA;gBACA,IAAA;gBAEA,+DAAA;gBACA,gEAAA;gBACA,yDAAA;gBACA,yDAAA;gBACA,6EAAA;gBACA,sFAAA;gBACA,IAAIJ,WAAW,CAACvB,OAAO,CAAC,GAAG,GAAGf,gLAAAA,AAAqB,EAACyC,UAAU,EAAE7B,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;oBACjF,OAAO+B,uBAAuB,CAACL,WAAW,EAAEG,UAAU,EAAE7B,QAAQ,CAAC;gBAClE;gBAEA,oEAAA;gBACA,gEAAA;gBACA,+DAAA;gBACA,wDAAA;gBACA,oEAAA;gBACA,OAAO,EAAE;YAET,4CAAA;YACA,yEAAA;YACA,2EAAA;YACA,8BAAA;YACD;YAEA,IAAI4B,WAAW,EAAE;gBAChB,IAAMI,gBAAgB,gNAAGzC,UAAAA,AAAiC,EAACsC,UAAU,EAAE7B,QAAQ,CAAC;gBAChF,IAAI0B,WAAW,CAACvB,OAAO,CAAC6B,gBAAgB,CAAC,KAAK,CAAC,EAAE;oBAChD,OAAON,WAAW;gBACnB,CAAC,MAAM;oBACN,OAAOM,gBAAgB;gBACxB;YACD,CAAC,MAAM;gBACN,IAAMC,YAAY,gNAAG1C,UAAAA,AAAiC,EAACsC,UAAU,EAAE7B,QAAQ,CAAC;gBAC5E,2DAAA;gBACA,4DAAA;gBACA,IAAI0B,WAAW,CAACvB,OAAO,CAAC8B,YAAY,CAAC,KAAK,CAAC,EAAE;oBAC5C,OAAOP,WAAW;gBACnB;gBACA,+DAAA;gBACA,sDAAA;gBACA,0CAAA;gBACA,OAAOO,YAAY;YACpB;QAEA,wDAAA;QACA,uEAAA;QACA,+EAAA;QACA,iEAAA;QACA,oDAAA;QACA,6DAAA;QACA,4EAAA;QAEA,yCAAA;QACA,qCAAA;QACA,sGAAA;QACD;IACD,OAEK;QACJ,sDAAA;QACA,IAAIP,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAC3B,4DAAA;YACA,4DAAA;YACA,gDAAA;YACA,4DAAA;YACA,2DAAA;YACA,EAAA;YACA,gDAAA;YACA,4DAAA;YACA,0CAAA;YACA,sEAAA;YACA,4DAAA;YACA,4DAAA;YACA,iCAAA;YACA,EAAA;YACA,mCAAA;YACA,wBAAA,GACA,OAAOQ,IAAI,CAACR,WAAW,EAAEE,WAAW,EAAE5B,QAAQ,CAAC,IAAI,EAAE;QACtD;IACD;IAEA,OAAO0B,WAAW;AACnB;AASO,SAASQ,IAAIA,CAACC,MAAM,EAAElC,OAAO,EAAED,QAAQ,EAAE;IAC/C,IAAI,CAACmC,MAAM,EAAE;QACZ;IACD;IACA,8DAAA;IACA,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACtB,iDAAA;QACA,IAAIA,MAAM,KAAK,GAAG,EAAE;YACnB;QACD;QACA,+BAAA;QACA,EAAA;QACA,gEAAA;QACA,iEAAA;QACA,kEAAA;QACA,kEAAA;QACA,gEAAA;QACA,EAAA;QACA,IAAMC,SAAS,GAAG,IAAI/C,yMAAS,CAACY,OAAO,EAAED,QAAQ,CAAC;QAClDoC,SAAS,CAACC,KAAK,CAACF,MAAM,CAAC;QACvB,sEAAA;QACA,4DAAA;QACA,OAAOC,SAAS,CAACE,cAAc,CAAC,CAAC;IAClC;IACA,sCAAA;IACA,4CAAA;IACA,8CAAA;IACA,iDAAA;IACA,qCAAA;IACA,IAAI,CAACrC,OAAO,EAAE;QACb;IACD;IACA,IAAMsC,mCAAmC,GAAGC,kCAAkC,CAACL,MAAM,EAAElC,OAAO,EAAED,QAAQ,CAAC;IACzG,EAAA;IACA,qEAAA;IACA,wCAAA;IACA,0EAAA;IACA,EAAA;IACA,uEAAA;IACA,qFAAA;IACA,6EAAA;IACA,oFAAA;IACA,iFAAA;IACA,sBAAA;IACA,EAAA;IACA,6CAAA;IACC,OAAA,IAAAyC,MAAA,wJAAWrD,yBAAAA,AAAqB,EAACa,OAAO,EAAED,QAAQ,CAAC,EAAAyC,MAAA,CAAGF,mCAAmC,IAAI,EAAE;AAChG,IAAA;AACD;AAUO,SAASG,UAAUA,CAACP,MAAM,EAAElC,OAAO,EAAED,QAAQ,EAAE;IACrD,IAAM2C,6BAA6B,GAAGH,kCAAkC,CAACL,MAAM,EAAElC,OAAO,EAAED,QAAQ,CAAC;IACnG,IAAI2C,6BAA6B,EAAE;QAClC,IAAMC,mBAAmB,GAAGD,6BAA6B,CAACtC,MAAM,GAAGwC,kBAAkB,CAAC5C,OAAO,EAAED,QAAQ,CAAC;QACxG,IAAI4C,mBAAmB,GAAG,CAAC,EAAE;YAC5B,OAAOT,MAAM,CAACW,KAAK,CAAC,CAAC,EAAEX,MAAM,CAAC9B,MAAM,GAAGuC,mBAAmB,CAAC;QAC5D;IACD;IACA,OAAOT,MAAM;AACd;AAEA,SAASU,kBAAkBA,CAAC5C,OAAO,EAAED,QAAQ,EAAE;IAC9C,4DAAA;IACAA,QAAQ,GAAG,+LAAIV,WAAQ,CAACU,QAAQ,CAAC;IACjCA,QAAQ,CAAC+C,mBAAmB,CAAC9C,OAAO,CAAC;IACrC,qCAAA;IACA,OAAOD,QAAQ,CAACgD,aAAa,CAACC,eAAe,CAAC,CAAC,CAACjD,QAAQ,CAACgD,aAAa,CAACC,eAAe,CAAC,CAAC,CAAC5C,MAAM,GAAG,CAAC,CAAC;AACrG;AAeO,SAAS6C,8BAA8BA,CAACC,iBAAiB,EAAAC,KAAA,EAO7D;IAAA,IANFnD,OAAO,GAAAmD,KAAA,CAAPnD,OAAO,EACPH,SAAS,GAAAsD,KAAA,CAATtD,SAAS,EACTF,cAAc,GAAAwD,KAAA,CAAdxD,cAAc,EACdyD,2BAA2B,GAAAD,KAAA,CAA3BC,2BAA2B,EAC3BtD,QAAQ,GAAAqD,KAAA,CAARrD,QAAQ,EACRC,QAAQ,GAAAoD,KAAA,CAARpD,QAAQ;IAER,+DAAA;IAEA,6EAAA;IACA,qDAAA;IAEA,IAAImD,iBAAiB,KAAK,GAAG,EAAE;QAC9B,mDAAA;QACA,OAAOlD,OAAO;IACf;IAEA,IAAMqD,eAAe,GAAGC,wDAAwD,CAACJ,iBAAiB,EAAEnD,QAAQ,CAAC;IAE7G,yDAAA;IACA,kDAAA;IACA,4BAAA;IACA,IAAIsD,eAAe,EAAE;QACpB,IAAI,CAACxD,SAAS,IAAKA,SAAS,CAACK,OAAO,CAACmD,eAAe,CAAC,IAAI,CAAE,EAAE;YAC5D,OAAOA,eAAe;QACvB,CAAC,MAAM;YACN,OAAOlD,SAAS;QACjB;IACD,OAGK,IAAIH,OAAO,EAAE;QACjB,sGAAA;QACA,qFAAA;QACA,gFAAA;QACA,IAAIC,0BAA0B,CAACiD,iBAAiB,EAAElD,OAAO,EAAED,QAAQ,CAAC,EAAE;YACrE,oFAAA;YACA,iEAAA;YACA,IAAIqD,2BAA2B,IAAInD,0BAA0B,CAACiD,iBAAiB,EAAEE,2BAA2B,EAAErD,QAAQ,CAAC,EAAE;gBACxH,OAAOqD,2BAA2B;YACnC,CAAC,MAAM,IAAIzD,cAAc,IAAIM,0BAA0B,CAACiD,iBAAiB,EAAEvD,cAAc,EAAEI,QAAQ,CAAC,EAAE;gBACrG,OAAOJ,cAAc;YACtB,CAAC,MAAM;gBACN,IAAI,CAACG,QAAQ,EAAE;oBACd,6CAAA;oBACA,OAAOK,SAAS;gBACjB;YACD;QACD,CAAC,MAAM;YACN,0DAAA;YACA,+EAAA;YACA,4EAAA;YACA,6CAAA;YACA,IAAI,CAACL,QAAQ,EAAE;gBACd,OAAOK,SAAS;YACjB;QACD;IACD;IAEA,+CAAA;IACA,OAAOH,OAAO;AACf;AAiBO,SAASuD,mBAAmBA,CAAC9B,WAAW,EAAA+B,KAAA,EAY5C;IAAA,IAXFC,eAAe,GAAAD,KAAA,CAAfC,eAAe,EACfzD,OAAO,GAAAwD,KAAA,CAAPxD,OAAO,EACPL,cAAc,GAAA6D,KAAA,CAAd7D,cAAc,EACdyD,2BAA2B,GAAAI,KAAA,CAA3BJ,2BAA2B,EAC3BM,eAAe,GAAAF,KAAA,CAAfE,eAAe,EACf9D,aAAa,GAAA4D,KAAA,CAAb5D,aAAa,EACbC,SAAS,GAAA2D,KAAA,CAAT3D,SAAS,EACT8D,aAAa,GAAAH,KAAA,CAAbG,aAAa,EACbC,cAAc,GAAAJ,KAAA,CAAdI,cAAc,EACdC,0BAA0B,GAAAL,KAAA,CAA1BK,0BAA0B,EAC1B9D,QAAQ,GAAAyD,KAAA,CAARzD,QAAQ;IAER,sFAAA;IACA,sGAAA;IACA,4FAAA;IACA,IAAI4D,aAAa,IAAIE,0BAA0B,KAAK,KAAK,EAAE;QAC1D,IAAI7D,OAAO,EAAE;YACZ,kFAAA;YACA,sFAAA;YACA,IAAM8D,MAAM,gNAAGxE,UAAAA,AAAiC,EAACU,OAAO,EAAED,QAAQ,CAAC;YACnE,+EAAA;YACA,sBAAA;YACA,IAAI0B,WAAW,CAACvB,OAAO,CAAC4D,MAAM,CAAC,KAAK,CAAC,EAAE;gBACtC,IAAIrE,MAAK;gBACT,yCAAA;gBACA,iEAAA;gBACA,uEAAA;gBACA,2EAAA;gBACA,8DAAA;gBACA,EAAA;gBACA,8EAAA;gBACA,qFAAA;gBACA,+FAAA;gBACA,qFAAA;gBACA,mFAAA;gBACA,0EAAA;gBACA,EAAA;gBACA,8EAAA;gBACA,uCAAA;gBACA,yEAAA;gBACA,EAAA;gBACA,IAAMsE,8DAA8D,GAAGtC,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG;gBAC5G,IAAIsC,8DAA8D,EAAE;oBACnE,2DAAA;oBACAtC,WAAW,GAAGqC,MAAM,GAAGrC,WAAW;oBAClChC,MAAK,GAAGwC,IAAI,CAACR,WAAW,EAAEzB,OAAO,EAAED,QAAQ,CAAC;gBAC7C,CAAC,MAAM;oBACN,+EAAA;oBACA,kBAAA;oBACA,0FAAA;oBACA,wFAAA;oBACA,2CAAA;oBACA0B,WAAW,GAAGqC,MAAM;gBACrB;gBACA,OAAO;oBACNrC,WAAW,EAAXA,WAAW;oBACXhC,KAAK,EAALA,MAAK;oBACLO,OAAO,EAAPA;gBACD,CAAC;YACF;QACD;IACD;IAEA,wDAAA;IACA,+CAAA;IACA,0DAAA;IACA,kDAAA;IACA,gDAAA;IACA,IAAI2D,aAAa,KAAK,KAAK,IAAI3D,OAAO,IAAIyB,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAChFA,WAAW,GAAGuC,yCAAyC,CAACvC,WAAW,EAAEzB,OAAO,EAAED,QAAQ,CAAC;IACxF;IAEA,mEAAA;IACA,IAAI0B,WAAW,IAAIzB,OAAO,IAAI4D,cAAc,EAAE;QAC7CnC,WAAW,GAAGgB,UAAU,CAAChB,WAAW,EAAEzB,OAAO,EAAED,QAAQ,CAAC;IACzD;IAEA,2CAAA;IACA,oDAAA;IACA,oDAAA;IACA,kDAAA;IACA,kDAAA;IACA,oCAAA;IACA,IAAI0B,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,IAAA,CAAK,CAACzB,OAAO,IAAI2D,aAAa,CAAC,EAAE;QACzElC,WAAW,GAAG,GAAG,GAAGA,WAAW;IAChC;IAEA,yCAAA;IACA,2CAAA;IACA,oCAAA;IACA,gCAAA;IACA,oDAAA;IACA,gDAAA;IACA,2BAAA;IACA,gDAAA;IACA,qDAAA;IACA,6CAAA;IACA,4CAAA;IACA,wDAAA;IACA,4CAAA;IACA,+CAAA;IACA,mDAAA;IACA,gDAAA;IACA,0DAAA;IACA,wEAAA;IACA,IAAI,CAACA,WAAW,IAAIgC,eAAe,IAAIA,eAAe,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAClE,IAAIE,aAAa,EAAE;YAClB3D,OAAO,GAAGG,SAAS;QACpB,CAAC,MAAM;YACNH,OAAO,GAAGL,cAAc;QACzB;IACD;IACA,+CAAA;IACA,wCAAA;IACA,6CAAA;IACA,IAAI8B,WAAW,KAAK,GAAG,IAAIgC,eAAe,IAAIA,eAAe,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,eAAe,CAACrD,MAAM,GAAG,GAAG,CAACA,MAAM,EAAE;QAChHJ,OAAO,GAAGG,SAAS;IACpB;IAEA,qCAAA;IACA,IAAIV,KAAK;IACT,IAAIgC,WAAW,EAAE;QAChB,IAAIA,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAC3B,IAAIA,WAAW,KAAK,GAAG,EAAE;gBACxBhC,KAAK,GAAGU,SAAS;YAClB,CAAC,MAAM,IAAIH,OAAO,IAAIV,uNAAAA,AAAiC,EAACU,OAAO,EAAED,QAAQ,CAAC,CAACG,OAAO,CAACuB,WAAW,CAAC,KAAK,CAAC,EAAE;gBACtG,gDAAA;gBACA,8CAAA;gBACA,mEAAA;gBACA,+CAAA;gBACA,EAAA;gBACA,4CAAA;gBACA,4CAAA;gBACA,gEAAA;gBACA,EAAA;gBACAhC,KAAK,GAAGU,SAAS;YAClB,CAAC,MAAM;gBACNV,KAAK,GAAGwC,IAAI,CAACR,WAAW,EAAEzB,OAAO,EAAED,QAAQ,CAAC;YAC7C;QACD,CAAC,MAAM;YACNN,KAAK,GAAGwC,IAAI,CAACR,WAAW,EAAEzB,OAAO,EAAED,QAAQ,CAAC;QAC7C;IACD;IAEA,4CAAA;IACA,iEAAA;IACA,uFAAA;IACA,IAAIN,KAAK,EAAE;QACVO,OAAO,GAAGiD,8BAA8B,CAACxD,KAAK,EAAE;YAC/CO,OAAO,EAAPA,OAAO;YACPH,SAAS,EAATA,SAAS;YACTF,cAAc,EAAdA,cAAc;YACdyD,2BAA2B,EAA3BA,2BAA2B;YAC3B,6CAAA;YACA,4DAAA;YACAtD,QAAQ,EAAE,KAAK;YACfC,QAAQ,EAARA;QACD,CAAC,CAAC;QACF,wDAAA;QACA,+CAAA;QACA,0DAAA;QACA,kDAAA;QACA,gDAAA;QACA,IAAI4D,aAAa,KAAK,KAAK,IAAI3D,OAAO,IAAIyB,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAChFA,WAAW,GAAGuC,yCAAyC,CAACvC,WAAW,EAAEzB,OAAO,EAAED,QAAQ,CAAC;YACvF,0DAAA;YACAN,KAAK,GAAGwC,IAAI,CAACR,WAAW,EAAEzB,OAAO,EAAED,QAAQ,CAAC;QAC7C;IACD;IAEA,IAAI,CAACC,OAAO,IAAI0D,eAAe,EAAE;QAChC1D,OAAO,GAAGL,cAAc,IAAIC,aAAa,CAAC,CAAC;IAC5C;IAEA,OAAO;QACN,wFAAA;QACA,iFAAA;QACA,gFAAA;QACA6B,WAAW,EAAXA,WAAW;QACXzB,OAAO,EAAPA,OAAO;QACPP,KAAK,EAALA;IACD,CAAC;AACF;AAEA,SAASuE,yCAAyCA,CAAC5B,KAAK,EAAEpC,OAAO,EAAED,QAAQ,EAAE;IAC5E,gDAAA;IACA,0CAAA;IACA,IAAIqC,KAAK,CAAClC,OAAO,8MAACZ,UAAAA,AAAiC,EAACU,OAAO,EAAED,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;QAC9E,kCAAA;QACA,IAAMkE,SAAS,GAAG,iMAAI7E,YAAS,CAACY,OAAO,EAAED,QAAQ,CAAC;QAClD,uCAAA;QACAkE,SAAS,CAAC7B,KAAK,CAACA,KAAK,CAAC;QACtB,mDAAA;QACA,IAAM1C,WAAW,GAAGuE,SAAS,CAACC,SAAS,CAAC,CAAC;QACzC,IAAIxE,WAAW,EAAE;YAChB,0CAAA;YACA,6BAAA;YACA,OAAOA,WAAW,CAAC4B,cAAc,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;QACvD,CAAC,MAAM;YACN,OAAO,EAAE;QACV;IACD,CAAC,MAAM;QACN,4BAAA;QACA,OAAOa,KAAK,CAACb,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAChC;AACD;AAQO,SAAS+B,wDAAwDA,CAACpB,MAAM,EAAEnC,QAAQ,EAAE;IAC1F,IAAMkE,SAAS,GAAG,iMAAI7E,YAAS,CAAC,IAAI,EAAEW,QAAQ,CAAC;IAC/CkE,SAAS,CAAC7B,KAAK,CAACF,MAAM,CAAC;IACvB,sDAAA;IACA,2CAAA;IACA,0CAAA;IACA,UAAA;IACA,IAAA;IACA,OAAO+B,SAAS,CAACE,UAAU,CAAC,CAAC;AAC9B;AASO,SAASxD,cAAcA,CAACK,CAAC,EAAEC,CAAC,EAAEmD,OAAO,EAAE;IAC5C,gDAAA;IACA,qGAAA;IACA,yDAAA;IACA,6DAAA;IACA,wBAAA,GACA,IAAIC,MAAM,CAACC,SAAS,CAACC,aAAa,EAAE;QAClC,OAAOvD,CAAC,CAACuD,aAAa,CAACtD,CAAC,EAAEmD,OAAO,CAAC;IACpC;IACA,wBAAA,GACA,OAAOpD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAID,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAE;AACrC;AASO,SAASa,uBAAuBA,CAACI,MAAM,EAAElC,OAAO,EAAED,QAAQ,EAAE;IAClE,mCAAA;IACA,6DAAA;IACA,IAAIC,OAAO,EAAE;QACZ,IAAMwE,wBAAwB,GAAG,GAAG,2JAAGrF,wBAAAA,AAAqB,EAACa,OAAO,EAAED,QAAQ,CAAC;QAE/E,yCAAA;QACA,IAAImC,MAAM,CAAC9B,MAAM,GAAGoE,wBAAwB,CAACpE,MAAM,EAAE;YACpD,IAAIoE,wBAAwB,CAACtE,OAAO,CAACgC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACnD,OAAO,EAAE;YACV;QACD,CAAC,MAAM;YACN,IAAIA,MAAM,CAAChC,OAAO,CAACsE,wBAAwB,CAAC,KAAK,CAAC,EAAE;gBACnD,OAAOtC,MAAM,CAACW,KAAK,CAAC2B,wBAAwB,CAACpE,MAAM,CAAC;YACrD;QACD;IACD;IAEA,gDAAA;IACA,2CAAA;IACA,IAAA,IAAAqE,EAAA,GAAA,GAAAC,YAAA,GAAmCC,MAAM,CAACC,IAAI,CAAC7E,QAAQ,CAAC8E,qBAAqB,CAAC,EAAAJ,EAAA,GAAAC,YAAA,CAAAtE,MAAA,EAAAqE,EAAA,GAAE;QAA3E,IAAMK,oBAAoB,GAAAJ,YAAA,CAAAD,EAAA,CAAA;QAC9B,IAAIvC,MAAM,CAAChC,OAAO,CAAC4E,oBAAoB,CAAC,KAAK,GAAG,CAAC1E,MAAM,EAAE;YACxD,OAAO8B,MAAM,CAACW,KAAK,CAAC,GAAG,CAACzC,MAAM,GAAG0E,oBAAoB,CAAC1E,MAAM,CAAC;QAC9D;IACD;IAEA,OAAO,EAAE;AACV;AAYO,SAASmC,kCAAkCA,CAACL,MAAM,EAAElC,OAAO,EAAED,QAAQ,EAAE;IAC7E,kCAAA;IACA,IAAMkE,SAAS,GAAG,iMAAI7E,YAAS,CAACY,OAAO,EAAED,QAAQ,CAAC;IAClD,uCAAA;IACAkE,SAAS,CAAC7B,KAAK,CAACF,MAAM,CAAC;IACvB,mDAAA;IACA,IAAMxC,WAAW,GAAGuE,SAAS,CAACC,SAAS,CAAC,CAAC;IACzC,OAAOxE,WAAW,IAAIA,WAAW,CAACqF,cAAc;AACjD;AAQO,SAAS9E,0BAA0BA,CAACiC,MAAM,EAAElC,OAAO,EAAED,QAAQ,EAAE;IACrE,IAAMiF,qBAAqB,gNAAG1F,UAAAA,AAAiC,EAACU,OAAO,EAAED,QAAQ,CAAC;IAClF,IAAIkF,CAAC,GAAG,CAAC;IACT,MAAOA,CAAC,GAAG/C,MAAM,CAAC9B,MAAM,IAAI6E,CAAC,GAAGD,qBAAqB,CAAC5E,MAAM,CAAE;QAC7D,IAAI8B,MAAM,CAAC+C,CAAC,CAAC,KAAKD,qBAAqB,CAACC,CAAC,CAAC,EAAE;YAC3C,OAAO,KAAK;QACb;QACAA,CAAC,EAAE;IACJ;IACA,OAAO,IAAI;AACZ;AAMO,SAASC,qBAAqBA,CAAAC,KAAA,EAOlC;IAAA,IANF1F,KAAK,GAAA0F,KAAA,CAAL1F,KAAK,EACLC,WAAW,GAAAyF,KAAA,CAAXzF,WAAW,EACXC,cAAc,GAAAwF,KAAA,CAAdxF,cAAc,EACdgE,aAAa,GAAAwB,KAAA,CAAbxB,aAAa,EACb9B,iBAAiB,GAAAsD,KAAA,CAAjBtD,iBAAiB,EACjB9B,QAAQ,GAAAoF,KAAA,CAARpF,QAAQ;IAER,sCAAA;IACA,4CAAA;IACA,oCAAA;IACA,4CAAA;IACA,kCAAA;IACA,wCAAA;IACA,IAAI,CAAC4D,aAAa,KAAK,KAAK,IAAI9B,iBAAiB,KAAKnC,WAAW,IAAIA,WAAW,CAACM,OAAO,EAAE;QACzF,OAAOqB,4BAA4B,CAAC3B,WAAW,CAAC;IACjD;IACA,yCAAA;IACA,uDAAA;IACA,4DAAA;IACA,IAAI,CAACD,KAAK,IAAIkE,aAAa,IAAIhE,cAAc,EAAE;QAC9C,oNAAOL,UAAAA,AAAiC,EAACK,cAAc,EAAEI,QAAQ,CAAC;IACnE;IACA,OAAON,KAAK;AACb,EAEA,kFAAA;CACA,sCAAA;CACA,wDAAA;CACA,2CAAA;CACA,0BAAA;CACA,uDAAA;CACA,6CAAA;CACA,sBAAA;CACA,2DAAA;CACA,YAAA;CACA,iBAAA;CACA,KAAA;CACA,IAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2223, "column": 0}, "map": {"version": 3, "file": "getPhoneInputWithCountryStateUpdateFromNewProps.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/helpers/getPhoneInputWithCountryStateUpdateFromNewProps.js"], "sourcesContent": ["import {\r\n\tgetInitialPhoneDigits,\r\n\tgetCountryForPartialE164Number,\r\n\tparsePhoneNumber,\r\n\tcouldNumberBelongToCountry\r\n} from './phoneInputHelpers.js'\r\n\r\nimport { validateE164Number } from './isE164Number.js'\r\n\r\nimport getInternationalPhoneNumberPrefix from './getInternationalPhoneNumberPrefix.js'\r\n\r\nimport {\r\n\tisCountrySupportedWithError,\r\n\tgetSupportedCountries\r\n} from './countries.js'\r\n\r\nexport default function getPhoneInputWithCountryStateUpdateFromNewProps(props, prevProps, state) {\r\n\tconst {\r\n\t\tmetadata,\r\n\t\tcountries,\r\n\t\tdefaultCountry: newDefaultCountry,\r\n\t\tvalue: newValue,\r\n\t\treset: newReset,\r\n\t\tinternational,\r\n\t\t// `displayInitialValueAsLocalNumber` property has been\r\n\t\t// superceded by `initialValueFormat` property.\r\n\t\tdisplayInitialValueAsLocalNumber,\r\n\t\tinitialValueFormat\r\n\t} = props\r\n\r\n\tconst {\r\n\t\tdefaultCountry: prevDefaultCountry,\r\n\t\tvalue: prevValue,\r\n\t\treset: prevReset\r\n\t} = prevProps\r\n\r\n\tconst {\r\n\t\tcountry,\r\n\t\tvalue,\r\n\t\t// If the user has already manually selected a country\r\n\t\t// then don't override that already selected country\r\n\t\t// if the `defaultCountry` property changes.\r\n\t\t// That's what `hasUserSelectedACountry` flag is for.\r\n\t\thasUserSelectedACountry,\r\n\t\tlatestCountrySelectedByUser\r\n\t} = state\r\n\r\n\tconst _getInitialPhoneDigits = (parameters) => getInitialPhoneDigits({\r\n\t\t...parameters,\r\n\t\tinternational,\r\n\t\tuseNationalFormat: displayInitialValueAsLocalNumber || initialValueFormat === 'national',\r\n\t\tmetadata\r\n\t})\r\n\r\n\t// Some users requested a way to reset the component\r\n\t// (both number `<input/>` and country `<select/>`).\r\n\t// Whenever `reset` property changes both number `<input/>`\r\n\t// and country `<select/>` are reset.\r\n\t// It's not implemented as some instance `.reset()` method\r\n\t// because `ref` is forwarded to `<input/>`.\r\n\t// It's also not replaced with just resetting `country` on\r\n\t// external `value` reset, because a user could select a country\r\n\t// and then not input any `value`, and so the selected country\r\n\t// would be \"stuck\", if not using this `reset` property.\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/300\r\n\tif (newReset !== prevReset) {\r\n\t\treturn {\r\n\t\t\tphoneDigits: _getInitialPhoneDigits({\r\n\t\t\t\tvalue: undefined,\r\n\t\t\t\tdefaultCountry: newDefaultCountry\r\n\t\t\t}),\r\n\t\t\tvalue: undefined,\r\n\t\t\tcountry: newDefaultCountry,\r\n\t\t\tlatestCountrySelectedByUser: undefined,\r\n\t\t\thasUserSelectedACountry: undefined\r\n\t\t}\r\n\t}\r\n\r\n\t// `value` is the value currently shown in the component:\r\n\t// it's stored in the component's `state`, and it's not the `value` property.\r\n\t// `prevValue` is \"previous `value` property\".\r\n\t// `newValue` is \"new `value` property\".\r\n\r\n\t// If the default country changed\r\n\t// (e.g. in case of ajax GeoIP detection after page loaded)\r\n\t// then select it, but only if the user hasn't already manually\r\n\t// selected a country, and no phone number has been manually entered so far.\r\n\t// Because if the user has already started inputting a phone number\r\n\t// then they're okay with no country being selected at all (\"International\")\r\n\t// and they don't want to be disturbed, don't want their input to be screwed, etc.\r\n\tif (newDefaultCountry !== prevDefaultCountry) {\r\n\t\tconst isNewDefaultCountrySupported = !newDefaultCountry || isCountrySupportedWithError(newDefaultCountry, metadata)\r\n\t\tconst noValueHasBeenEnteredByTheUser = (\r\n\t\t\t// By default, \"no value has been entered\" means `value` is `undefined`.\r\n\t\t\t!value ||\r\n\t\t\t// When `international` is `true`, and some country has been pre-selected,\r\n\t\t\t// then the `<input/>` contains a pre-filled value of `+${countryCallingCode}${leadingDigits}`,\r\n\t\t\t// so in case of `international` being `true`, \"the user hasn't entered anything\" situation\r\n\t\t\t// doesn't just mean `value` is `undefined`, but could also mean `value` is `+${countryCallingCode}`.\r\n\t\t\t(international && value === _getInitialPhoneDigits({\r\n\t\t\t\tvalue: undefined,\r\n\t\t\t\tdefaultCountry: prevDefaultCountry\r\n\t\t\t}))\r\n\t\t)\r\n\t\t// Only update the `defaultCountry` property if no phone number\r\n\t\t// has been entered by the user or pre-set by the application.\r\n\t\tconst noValueHasBeenEntered = !newValue && noValueHasBeenEnteredByTheUser\r\n\t\tif (!hasUserSelectedACountry && isNewDefaultCountrySupported && noValueHasBeenEntered) {\r\n\t\t\treturn {\r\n\t\t\t\tcountry: newDefaultCountry,\r\n\t\t\t\t// If `phoneDigits` is empty, then automatically select the new `country`\r\n\t\t\t\t// and set `phoneDigits` to `+{getCountryCallingCode(newCountry)}`.\r\n\t\t\t\t// The code assumes that \"no phone number has been entered by the user\",\r\n\t\t\t\t// and no `value` property has been passed, so the `phoneNumber` parameter\r\n\t\t\t\t// of `_getInitialPhoneDigits({ value, phoneNumber, ... })` is `undefined`.\r\n\t\t\t\tphoneDigits: _getInitialPhoneDigits({\r\n\t\t\t\t\tvalue: undefined,\r\n\t\t\t\t\tdefaultCountry: newDefaultCountry\r\n\t\t\t\t}),\r\n\t\t\t\t// `value` is `undefined` and it stays so.\r\n\t\t\t\tvalue: undefined\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// If a new `value` is set externally.\r\n\t// (e.g. as a result of an ajax API request\r\n\t//  to get user's phone after page loaded)\r\n\t// The first part — `newValue !== prevValue` —\r\n\t// is basically `props.value !== prevProps.value`\r\n\t// so it means \"if value property was changed externally\".\r\n\t// The second part — `newValue !== value` —\r\n\t// is for ignoring the `getDerivedStateFromProps()` call\r\n\t// which happens in `this.onChange()` right after `this.setState()`.\r\n\t// If this `getDerivedStateFromProps()` call isn't ignored\r\n\t// then the country flag would reset on each input.\r\n\tif (!valuesAreEqual(newValue, prevValue) && !valuesAreEqual(newValue, value)) {\r\n\t\tlet phoneNumber\r\n\t\tlet parsedCountry\r\n\t\tif (newValue) {\r\n\t\t\t// Validate that the newly-supplied `value` is in `E.164` format.\r\n\t\t\t// Because sometimes people attempt to supply a `value` like \"+****************\".\r\n\t\t\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/231#note_2016334796\r\n\t\t\tif (newValue) {\r\n\t\t\t\tvalidateE164Number(newValue)\r\n\t\t\t}\r\n\t\t\tphoneNumber = parsePhoneNumber(newValue, metadata)\r\n\t\t\tconst supportedCountries = getSupportedCountries(countries, metadata)\r\n\t\t\tif (phoneNumber && phoneNumber.country) {\r\n\t\t\t\t// Ignore `else` because all countries are supported in metadata.\r\n\t\t\t\t/* istanbul ignore next */\r\n\t\t\t\tif (!supportedCountries || supportedCountries.indexOf(phoneNumber.country) >= 0) {\r\n\t\t\t\t\tparsedCountry = phoneNumber.country\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tparsedCountry = getCountryForPartialE164Number(newValue, {\r\n\t\t\t\t\tcountry: undefined,\r\n\t\t\t\t\tcountries: supportedCountries,\r\n\t\t\t\t\tmetadata\r\n\t\t\t\t})\r\n\r\n\t\t\t\t// In cases when multiple countries correspond to the same country calling code,\r\n\t\t\t\t// the phone number digits of `newValue` have to be matched against country-specific\r\n\t\t\t\t// regular expressions in order to determine the exact country.\r\n\t\t\t\t// Sometimes, that algorithm can't decide for sure which country does the phone number belong to,\r\n\t\t\t\t// for example when the digits of `newValue` don't match any of those regular expressions.\r\n\t\t\t\t// and the country of the phone number couldn't be determined.\r\n\t\t\t\t// In those cases, people prefer the component to show the flag of the `defaultCountry`\r\n\t\t\t\t// if the phone number could potentially belong to that `defaultCountry`.\r\n\t\t\t\t// At least that's how the component behaves when a user pastes an international\r\n\t\t\t\t// phone number into the input field: for example, when `defaultCountry` is `\"US\"`\r\n\t\t\t\t// and the user pastes value \"****** 555 5555\" into the input field, it keep showing \"US\" flag.\r\n\t\t\t\t// So when setting new `value` property externally, the component should behave the same way:\r\n\t\t\t\t// it should select the `defaultCountry` when the new `value` could potentially belong\r\n\t\t\t\t// to that country in cases when the exact country can't be determined.\r\n\t\t\t\t// https://github.com/catamphetamine/react-phone-number-input/issues/413#issuecomment-1536219404\r\n\t\t\t\tif (!parsedCountry) {\r\n\t\t\t\t\tif (newDefaultCountry) {\r\n\t\t\t\t\t\tif (newValue.indexOf(getInternationalPhoneNumberPrefix(newDefaultCountry, metadata)) === 0) {\r\n\t\t\t\t\t\t\tparsedCountry = newDefaultCountry\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet userCountrySelectionHistoryStateUpdate\r\n\t\tif (newValue) {\r\n\t\t\t// If the latest country that has been manually selected by the user\r\n\t\t\t// no longer corresponds to the new value then reset it.\r\n\t\t\tif (latestCountrySelectedByUser) {\r\n\t\t\t\tconst couldNewValueCorrespondToLatestCountrySelectedByUser =\r\n\t\t\t\t\tparsedCountry\r\n\t\t\t\t\t\t? latestCountrySelectedByUser === parsedCountry\r\n\t\t\t\t\t\t: couldNumberBelongToCountry(newValue, latestCountrySelectedByUser, metadata)\r\n\r\n\t\t\t\tif (couldNewValueCorrespondToLatestCountrySelectedByUser) {\r\n\t\t\t\t\tif (!parsedCountry) {\r\n\t\t\t\t\t\tparsedCountry = latestCountrySelectedByUser\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuserCountrySelectionHistoryStateUpdate = {\r\n\t\t\t\t\t\tlatestCountrySelectedByUser: undefined\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// When the `value` property is being reset \"externally\",\r\n\t\t\t// reset any tracking of the country that the user has previously selected.\r\n\t\t\tuserCountrySelectionHistoryStateUpdate = {\r\n\t\t\t\tlatestCountrySelectedByUser: undefined,\r\n\t\t\t\thasUserSelectedACountry: undefined\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn {\r\n\t\t\t...userCountrySelectionHistoryStateUpdate,\r\n\t\t\tphoneDigits: _getInitialPhoneDigits({\r\n\t\t\t\tphoneNumber,\r\n\t\t\t\tvalue: newValue,\r\n\t\t\t\tdefaultCountry: newDefaultCountry\r\n\t\t\t}),\r\n\t\t\tvalue: newValue,\r\n\t\t\tcountry: newValue ? parsedCountry : newDefaultCountry\r\n\t\t}\r\n\t}\r\n\r\n\t// `defaultCountry` didn't change.\r\n\t// `value` didn't change.\r\n\t// `phoneDigits` didn't change, because `value` didn't change.\r\n\t//\r\n\t// So no need to update state.\r\n}\r\n\r\nexport function valuesAreEqual(value1, value2) {\r\n\t// If `value` has been set to `null` externally then convert it to `undefined`.\r\n\t//\r\n\t// For example, `react-hook-form` sets `value` to `null` when the user clears the input.\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/164\r\n\t// In that case, without this conversion of `null` to `undefined`, it would reset\r\n\t// the selected country to `defaultCountry` because in that case `newValue !== value`\r\n\t// because `null !== undefined`.\r\n\t//\r\n\t// Historically, empty `value` is encoded as `undefined`.\r\n\t// Perhaps empty `value` would be better encoded as `null` instead.\r\n\t// But because that would be a potentially breaking change for some people,\r\n\t// it's left as is for the current \"major\" version of this library.\r\n\t//\r\n\tif (value1 === null) {\r\n\t\tvalue1 = undefined\r\n\t}\r\n\tif (value2 === null) {\r\n\t\tvalue2 = undefined\r\n\t}\r\n\treturn value1 === value2\r\n}"], "names": ["getInitialPhoneDigits", "getCountryForPartialE164Number", "parsePhoneNumber", "couldNumberBelongToCountry", "validateE164Number", "getInternationalPhoneNumberPrefix", "isCountrySupportedWithError", "getSupportedCountries", "getPhoneInputWithCountryStateUpdateFromNewProps", "props", "prevProps", "state", "metadata", "countries", "newDefaultCountry", "defaultCountry", "newValue", "value", "newReset", "reset", "international", "displayInitialValueAsLocalNumber", "initialValueFormat", "prevDefaultCountry", "prevValue", "prevReset", "country", "hasUserSelectedACountry", "latestCountrySelectedByUser", "_getInitialPhoneDigits", "parameters", "_objectSpread", "useNationalFormat", "phoneDigits", "undefined", "isNewDefaultCountrySupported", "noValueHasBeenEnteredByTheUser", "noValueHasBeenEntered", "valuesAreEqual", "phoneNumber", "parsedCountry", "supportedCountries", "indexOf", "userCountrySelectionHistoryStateUpdate", "couldNewValueCorrespondToLatestCountrySelectedByUser", "value1", "value2"], "mappings": ";;;;AAAA,SACCA,qBAAqB,EACrBC,8BAA8B,EAC9BC,gBAAgB,EAChBC,0BAA0B,QACpB,wBAAwB;AAE/B,SAASC,kBAAkB,QAAQ,mBAAmB;AAEtD,OAAOC,iCAAiC,MAAM,wCAAwC;AAEtF,SACCC,2BAA2B,EAC3BC,qBAAqB,QACf,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAER,SAASC,+CAA+CA,CAACC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAE;IAChG,IACCC,QAAQ,GAULH,KAAK,CAVRG,QAAQ,EACRC,SAAS,GASNJ,KAAK,CATRI,SAAS,EACOC,iBAAiB,GAQ9BL,KAAK,CARRM,cAAc,EACPC,QAAQ,GAOZP,KAAK,CAPRQ,KAAK,EACEC,QAAQ,GAMZT,KAAK,CANRU,KAAK,EACLC,aAAa,GAKVX,KAAK,CALRW,aAAa,EAGbC,gCAAgC,GAE7BZ,KAAK,CAFRY,gCAAgC,EAChCC,kBAAkB,GACfb,KAAK,CADRa,kBAAkB;IAGnB,IACiBC,kBAAkB,GAG/Bb,SAAS,CAHZK,cAAc,EACPS,SAAS,GAEbd,SAAS,CAFZO,KAAK,EACEQ,SAAS,GACbf,SAAS,CADZS,KAAK;IAGN,IACCO,OAAO,GAQJf,KAAK,CARRe,OAAO,EACPT,KAAK,GAOFN,KAAK,CAPRM,KAAK,EAKLU,uBAAuB,GAEpBhB,KAAK,CAFRgB,uBAAuB,EACvBC,2BAA2B,GACxBjB,KAAK,CADRiB,2BAA2B;IAG5B,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAIC,UAAU;QAAA,oMAAK9B,wBAAAA,AAAqB,EAAA+B,aAAA,CAAAA,aAAA,CAAA,CAAA,GAChED,UAAU,GAAA,CAAA,GAAA;YACbV,aAAa,EAAbA,aAAa;YACbY,iBAAiB,EAAEX,gCAAgC,IAAIC,kBAAkB,KAAK,UAAU;YACxFV,QAAQ,EAARA;QAAQ,EACR,CAAC;IAAA;IAEF,oDAAA;IACA,oDAAA;IACA,2DAAA;IACA,qCAAA;IACA,0DAAA;IACA,4CAAA;IACA,0DAAA;IACA,gEAAA;IACA,8DAAA;IACA,wDAAA;IACA,wEAAA;IACA,IAAIM,QAAQ,KAAKO,SAAS,EAAE;QAC3B,OAAO;YACNQ,WAAW,EAAEJ,sBAAsB,CAAC;gBACnCZ,KAAK,EAAEiB,SAAS;gBAChBnB,cAAc,EAAED;YACjB,CAAC,CAAC;YACFG,KAAK,EAAEiB,SAAS;YAChBR,OAAO,EAAEZ,iBAAiB;YAC1Bc,2BAA2B,EAAEM,SAAS;YACtCP,uBAAuB,EAAEO;QAC1B,CAAC;IACF;IAEA,yDAAA;IACA,6EAAA;IACA,8CAAA;IACA,wCAAA;IAEA,iCAAA;IACA,2DAAA;IACA,+DAAA;IACA,4EAAA;IACA,mEAAA;IACA,4EAAA;IACA,kFAAA;IACA,IAAIpB,iBAAiB,KAAKS,kBAAkB,EAAE;QAC7C,IAAMY,4BAA4B,GAAG,CAACrB,iBAAiB,yMAAIR,8BAAAA,AAA2B,EAACQ,iBAAiB,EAAEF,QAAQ,CAAC;QACnH,IAAMwB,8BAA8B,GACnC,wEAAA;QACA,CAACnB,KAAK,IACN,0EAAA;QACA,+FAAA;QACA,2FAAA;QACA,qGAAA;QACCG,aAAa,IAAIH,KAAK,KAAKY,sBAAsB,CAAC;YAClDZ,KAAK,EAAEiB,SAAS;YAChBnB,cAAc,EAAEQ;QACjB,CAAC,CACD;QACD,+DAAA;QACA,8DAAA;QACA,IAAMc,qBAAqB,GAAG,CAACrB,QAAQ,IAAIoB,8BAA8B;QACzE,IAAI,CAACT,uBAAuB,IAAIQ,4BAA4B,IAAIE,qBAAqB,EAAE;YACtF,OAAO;gBACNX,OAAO,EAAEZ,iBAAiB;gBAC1B,yEAAA;gBACA,mEAAA;gBACA,wEAAA;gBACA,0EAAA;gBACA,2EAAA;gBACAmB,WAAW,EAAEJ,sBAAsB,CAAC;oBACnCZ,KAAK,EAAEiB,SAAS;oBAChBnB,cAAc,EAAED;gBACjB,CAAC,CAAC;gBACF,0CAAA;gBACAG,KAAK,EAAEiB;YACR,CAAC;QACF;IACD;IAEA,sCAAA;IACA,2CAAA;IACA,0CAAA;IACA,8CAAA;IACA,iDAAA;IACA,0DAAA;IACA,2CAAA;IACA,wDAAA;IACA,oEAAA;IACA,0DAAA;IACA,mDAAA;IACA,IAAI,CAACI,cAAc,CAACtB,QAAQ,EAAEQ,SAAS,CAAC,IAAI,CAACc,cAAc,CAACtB,QAAQ,EAAEC,KAAK,CAAC,EAAE;QAC7E,IAAIsB,WAAW;QACf,IAAIC,aAAa;QACjB,IAAIxB,QAAQ,EAAE;YACb,iEAAA;YACA,iFAAA;YACA,0FAAA;YACA,IAAIA,QAAQ,EAAE;wMACbZ,qBAAAA,AAAkB,EAACY,QAAQ,CAAC;YAC7B;YACAuB,WAAW,gMAAGrC,mBAAAA,AAAgB,EAACc,QAAQ,EAAEJ,QAAQ,CAAC;YAClD,IAAM6B,kBAAkB,GAAGlC,6NAAAA,AAAqB,EAACM,SAAS,EAAED,QAAQ,CAAC;YACrE,IAAI2B,WAAW,IAAIA,WAAW,CAACb,OAAO,EAAE;gBACvC,iEAAA;gBACA,wBAAA,GACA,IAAI,CAACe,kBAAkB,IAAIA,kBAAkB,CAACC,OAAO,CAACH,WAAW,CAACb,OAAO,CAAC,IAAI,CAAC,EAAE;oBAChFc,aAAa,GAAGD,WAAW,CAACb,OAAO;gBACpC;YACD,CAAC,MAAM;gBACNc,aAAa,gMAAGvC,iCAAAA,AAA8B,EAACe,QAAQ,EAAE;oBACxDU,OAAO,EAAEQ,SAAS;oBAClBrB,SAAS,EAAE4B,kBAAkB;oBAC7B7B,QAAQ,EAARA;gBACD,CAAC,CAAC;gBAEF,gFAAA;gBACA,oFAAA;gBACA,+DAAA;gBACA,iGAAA;gBACA,0FAAA;gBACA,8DAAA;gBACA,uFAAA;gBACA,yEAAA;gBACA,gFAAA;gBACA,kFAAA;gBACA,+FAAA;gBACA,6FAAA;gBACA,sFAAA;gBACA,uEAAA;gBACA,gGAAA;gBACA,IAAI,CAAC4B,aAAa,EAAE;oBACnB,IAAI1B,iBAAiB,EAAE;wBACtB,IAAIE,QAAQ,CAAC0B,OAAO,CAACrC,uNAAAA,AAAiC,EAACS,iBAAiB,EAAEF,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;4BAC3F4B,aAAa,GAAG1B,iBAAiB;wBAClC;oBACD;gBACD;YACD;QACD;QAEA,IAAI6B,sCAAsC;QAC1C,IAAI3B,QAAQ,EAAE;YACb,oEAAA;YACA,wDAAA;YACA,IAAIY,2BAA2B,EAAE;gBAChC,IAAMgB,oDAAoD,GACzDJ,aAAa,GACVZ,2BAA2B,KAAKY,aAAa,gMAC7CrC,6BAAAA,AAA0B,EAACa,QAAQ,EAAEY,2BAA2B,EAAEhB,QAAQ,CAAC;gBAE/E,IAAIgC,oDAAoD,EAAE;oBACzD,IAAI,CAACJ,aAAa,EAAE;wBACnBA,aAAa,GAAGZ,2BAA2B;oBAC5C;gBACD,CAAC,MAAM;oBACNe,sCAAsC,GAAG;wBACxCf,2BAA2B,EAAEM;oBAC9B,CAAC;gBACF;YACD;QACD,CAAC,MAAM;YACN,yDAAA;YACA,2EAAA;YACAS,sCAAsC,GAAG;gBACxCf,2BAA2B,EAAEM,SAAS;gBACtCP,uBAAuB,EAAEO;YAC1B,CAAC;QACF;QAEA,OAAAH,aAAA,CAAAA,aAAA,CAAA,CAAA,GACIY,sCAAsC,GAAA,CAAA,GAAA;YACzCV,WAAW,EAAEJ,sBAAsB,CAAC;gBACnCU,WAAW,EAAXA,WAAW;gBACXtB,KAAK,EAAED,QAAQ;gBACfD,cAAc,EAAED;YACjB,CAAC,CAAC;YACFG,KAAK,EAAED,QAAQ;YACfU,OAAO,EAAEV,QAAQ,GAAGwB,aAAa,GAAG1B;QAAiB;IAEvD;AAEA,kCAAA;AACA,yBAAA;AACA,8DAAA;AACA,EAAA;AACA,8BAAA;AACD;AAEO,SAASwB,cAAcA,CAACO,MAAM,EAAEC,MAAM,EAAE;IAC9C,+EAAA;IACA,EAAA;IACA,wFAAA;IACA,0EAAA;IACA,iFAAA;IACA,qFAAA;IACA,gCAAA;IACA,EAAA;IACA,yDAAA;IACA,mEAAA;IACA,2EAAA;IACA,mEAAA;IACA,EAAA;IACA,IAAID,MAAM,KAAK,IAAI,EAAE;QACpBA,MAAM,GAAGX,SAAS;IACnB;IACA,IAAIY,MAAM,KAAK,IAAI,EAAE;QACpBA,MAAM,GAAGZ,SAAS;IACnB;IACA,OAAOW,MAAM,KAAKC,MAAM;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2494, "column": 0}, "map": {"version": 3, "file": "PhoneInputWithCountry.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/PhoneInputWithCountry.js"], "sourcesContent": ["import React from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport classNames from 'classnames'\r\n\r\nimport InputSmart from './InputSmart.js'\r\nimport InputBasic from './InputBasic.js'\r\n\r\nimport { CountrySelectWithIcon as CountrySelect } from './CountrySelect.js'\r\n\r\nimport Flag from './Flag.js'\r\nimport InternationalIcon from './InternationalIcon.js'\r\n\r\nimport { validateE164Number } from './helpers/isE164Number.js'\r\n\r\nimport {\r\n\tsortCountryOptions,\r\n\tisCountrySupportedWithError,\r\n\tgetSupportedCountries,\r\n\tgetSupportedCountryOptions,\r\n\tgetCountries\r\n} from './helpers/countries.js'\r\n\r\nimport { createCountryIconComponent } from './CountryIcon.js'\r\n\r\nimport { setRefsValue } from './useExternalRef.js'\r\n\r\nimport {\r\n\tmetadata as metadataPropType,\r\n\tlabels as labelsPropType\r\n} from './PropTypes.js'\r\n\r\nimport {\r\n\tgetPreSelectedCountry,\r\n\tgetCountrySelectOptions,\r\n\tcouldNumberBelongToCountry,\r\n\tparsePhoneNumber,\r\n\tgenerateNationalNumberDigits,\r\n\tgetPhoneDigitsForNewCountry,\r\n\tgetInitialPhoneDigits,\r\n\tonPhoneDigitsChange,\r\n\te164\r\n} from './helpers/phoneInputHelpers.js'\r\n\r\nimport getPhoneInputWithCountryStateUpdateFromNewProps from './helpers/getPhoneInputWithCountryStateUpdateFromNewProps.js'\r\n\r\nclass PhoneNumberInput_ extends React.PureComponent {\r\n\tconstructor(props) {\r\n\t\tsuper(props)\r\n\r\n\t\tthis.inputRef = React.createRef()\r\n\r\n\t\tconst {\r\n\t\t\tvalue,\r\n\t\t\tlabels,\r\n\t\t\tinternational,\r\n\t\t\taddInternationalOption,\r\n\t\t\t// `displayInitialValueAsLocalNumber` property has been\r\n\t\t\t// superceded by `initialValueFormat` property.\r\n\t\t\tdisplayInitialValueAsLocalNumber,\r\n\t\t\tinitialValueFormat,\r\n\t\t\tmetadata\r\n\t\t} = this.props\r\n\r\n\t\tlet {\r\n\t\t\tdefaultCountry,\r\n\t\t\tcountries\r\n\t\t} = this.props\r\n\r\n\t\t// Validate `defaultCountry`.\r\n\t\tif (defaultCountry) {\r\n\t\t\tif (!this.isCountrySupportedWithError(defaultCountry)) {\r\n\t\t\t\tdefaultCountry = undefined\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Validate that the initially-supplied `value` is in `E.164` format.\r\n\t\t// Because sometimes people attempt to supply a `value` like \"+****************\".\r\n\t\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/231#note_2016334796\r\n\t\tif (value) {\r\n\t\t\tvalidateE164Number(value)\r\n\t\t}\r\n\r\n\t\t// Validate `countries`.\r\n\t\tcountries = getSupportedCountries(countries, metadata)\r\n\r\n\t\tconst phoneNumber = parsePhoneNumber(value, metadata)\r\n\r\n\t\tthis.CountryIcon = createCountryIconComponent(this.props)\r\n\r\n\t\tconst preSelectedCountry = getPreSelectedCountry({\r\n\t\t\tvalue,\r\n\t\t\tphoneNumber,\r\n\t\t\tdefaultCountry,\r\n\t\t\trequired: !addInternationalOption,\r\n\t\t\tcountries: countries || getCountries(metadata),\r\n\t\t\tgetAnyCountry: () => this.getFirstSupportedCountry({ countries }),\r\n\t\t\tmetadata\r\n\t\t})\r\n\r\n\t\tthis.state = {\r\n\t\t\t// Workaround for `this.props` inside `getDerivedStateFromProps()`.\r\n\t\t\tprops: this.props,\r\n\r\n\t\t\t// The country selected.\r\n\t\t\tcountry: preSelectedCountry,\r\n\r\n\t\t\t// `countries` are stored in `this.state` because they're filtered.\r\n\t\t\t// For example, a developer might theoretically pass some unsupported\r\n\t\t\t// countries as part of the `countries` property, and because of that\r\n\t\t\t// the component uses `this.state.countries` (which are filtered)\r\n\t\t\t// instead of `this.props.countries`\r\n\t\t\t// (which could potentially contain unsupported countries).\r\n\t\t\tcountries,\r\n\r\n\t\t\t// `phoneDigits` state property holds non-formatted user's input.\r\n\t\t\t// The reason is that there's no way of finding out\r\n\t\t\t// in which form should `value` be displayed: international or national.\r\n\t\t\t// E.g. if `value` is `+78005553535` then it could be input\r\n\t\t\t// by a user both as `8 (800) 555-35-35` and `****** 555 35 35`.\r\n\t\t\t// Hence storing just `value` is not sufficient for correct formatting.\r\n\t\t\t// E.g. if a user entered `8 (800) 555-35-35`\r\n\t\t\t// then value is `+78005553535` and `phoneDigits` are `88005553535`\r\n\t\t\t// and if a user entered `****** 555 35 35`\r\n\t\t\t// then value is `+78005553535` and `phoneDigits` are `+78005553535`.\r\n\t\t\tphoneDigits: getInitialPhoneDigits({\r\n\t\t\t\tvalue,\r\n\t\t\t\tphoneNumber,\r\n\t\t\t\tdefaultCountry,\r\n\t\t\t\tinternational,\r\n\t\t\t\tuseNationalFormat: displayInitialValueAsLocalNumber || initialValueFormat === 'national',\r\n\t\t\t\tmetadata\r\n\t\t\t}),\r\n\r\n\t\t\t// `value` property is duplicated in state.\r\n\t\t\t// The reason is that `getDerivedStateFromProps()`\r\n\t\t\t// needs this `value` to compare to the new `value` property\r\n\t\t\t// to find out if `phoneDigits` needs updating:\r\n\t\t\t// If the `value` property was changed externally\r\n\t\t\t// then it won't be equal to `state.value`\r\n\t\t\t// in which case `phoneDigits` and `country` should be updated.\r\n\t\t\tvalue\r\n\t\t}\r\n\t}\r\n\r\n\tcomponentDidMount() {\r\n\t\tconst { onCountryChange } = this.props\r\n\t\tlet { defaultCountry } = this.props\r\n\t\tconst { country: selectedCountry } = this.state\r\n\t\tif (onCountryChange) {\r\n\t\t\tif (defaultCountry) {\r\n\t\t\t\tif (!this.isCountrySupportedWithError(defaultCountry)) {\r\n\t\t\t\t\tdefaultCountry = undefined\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (selectedCountry !== defaultCountry) {\r\n\t\t\t\tonCountryChange(selectedCountry)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tcomponentDidUpdate(prevProps, prevState) {\r\n\t\tconst { onCountryChange } = this.props\r\n\t\tconst { country } = this.state\r\n\t\t// Call `onCountryChange` when user selects another country.\r\n\t\tif (onCountryChange && country !== prevState.country) {\r\n\t\t\tonCountryChange(country)\r\n\t\t}\r\n\t}\r\n\r\n\t// This function mimicks `refSetter` function returned from `useExternalRef()` hook\r\n\t// because this class-like React component can't use the `useExternalRef()` hook.\r\n\tsetInputRef = (instance) => {\r\n\t\tsetRefsValue([this.props.inputRef, this.inputRef], instance)\r\n\t}\r\n\r\n\tgetCountrySelectOptions({ countries }) {\r\n\t\tconst {\r\n\t\t\tinternational,\r\n\t\t\tcountryCallingCodeEditable,\r\n\t\t\tcountryOptionsOrder,\r\n\t\t\taddInternationalOption,\r\n\t\t\tlabels,\r\n\t\t\tlocales,\r\n\t\t\tmetadata\r\n\t\t} = this.props\r\n\r\n\t\treturn this.useMemoCountrySelectOptions(() => {\r\n\t\t\treturn sortCountryOptions(\r\n\t\t\t\tgetCountrySelectOptions({\r\n\t\t\t\t\tcountries: countries || getCountries(metadata),\r\n\t\t\t\t\tcountryNames: labels,\r\n\t\t\t\t\taddInternationalOption: (international && countryCallingCodeEditable === false) ? false : addInternationalOption,\r\n\t\t\t\t\tcompareStringsLocales: locales,\r\n\t\t\t\t\t// compareStrings\r\n\t\t\t\t}),\r\n\t\t\t\tgetSupportedCountryOptions(countryOptionsOrder, metadata)\r\n\t\t\t)\r\n\t\t}, [\r\n\t\t\tcountries,\r\n\t\t\tcountryOptionsOrder,\r\n\t\t\taddInternationalOption,\r\n\t\t\tlabels,\r\n\t\t\tmetadata\r\n\t\t])\r\n\t}\r\n\r\n\tuseMemoCountrySelectOptions(generator, dependencies) {\r\n\t\tif (\r\n\t\t\t!this.countrySelectOptionsMemoDependencies ||\r\n\t\t\t!areEqualArrays(dependencies, this.countrySelectOptionsMemoDependencies)\r\n\t\t) {\r\n\t\t\tthis.countrySelectOptionsMemo = generator()\r\n\t\t\tthis.countrySelectOptionsMemoDependencies = dependencies\r\n\t\t}\r\n\t\treturn this.countrySelectOptionsMemo\r\n\t}\r\n\r\n\tgetFirstSupportedCountry({ countries }) {\r\n\t\tconst countryOptions = this.getCountrySelectOptions({ countries })\r\n\t\treturn countryOptions[0].value\r\n\t}\r\n\r\n\t// A shorthand for not passing `metadata` as a second argument.\r\n\tisCountrySupportedWithError = (country) => {\r\n\t\tconst { metadata } = this.props\r\n\t\treturn isCountrySupportedWithError(country, metadata)\r\n\t}\r\n\r\n\t// Country `<select/>` `onChange` handler.\r\n\tonCountryChange = (newCountry) => {\r\n\t\tconst {\r\n\t\t\tinternational,\r\n\t\t\tmetadata,\r\n\t\t\tonChange,\r\n\t\t\tfocusInputOnCountrySelection\r\n\t\t} = this.props\r\n\r\n\t\tconst {\r\n\t\t\tphoneDigits: prevPhoneDigits,\r\n\t\t\tcountry: prevCountry\r\n\t\t} = this.state\r\n\r\n\t\t// After the new `country` has been selected,\r\n\t\t// if the phone number `<input/>` holds any digits\r\n\t\t// then migrate those digits for the new `country`.\r\n\t\tconst newPhoneDigits = getPhoneDigitsForNewCountry(prevPhoneDigits, {\r\n\t\t\tprevCountry,\r\n\t\t\tnewCountry,\r\n\t\t\tmetadata,\r\n\t\t\t// Convert the phone number to \"national\" format\r\n\t\t\t// when the user changes the selected country by hand.\r\n\t\t\tuseNationalFormat: !international\r\n\t\t})\r\n\r\n\t\tconst newValue = e164(newPhoneDigits, newCountry, metadata)\r\n\r\n\t\t// Focus phone number `<input/>` upon country selection.\r\n\t\tif (focusInputOnCountrySelection) {\r\n\t\t\tthis.inputRef.current.focus()\r\n\t\t}\r\n\r\n\t\t// If the user has already manually selected a country\r\n\t\t// then don't override that already selected country\r\n\t\t// if the `defaultCountry` property changes.\r\n\t\t// That's what `hasUserSelectedACountry` flag is for.\r\n\r\n\t\tthis.setState({\r\n\t\t\tcountry: newCountry,\r\n\t\t\tlatestCountrySelectedByUser: newCountry,\r\n\t\t\thasUserSelectedACountry: true,\r\n\t\t\tphoneDigits: newPhoneDigits,\r\n\t\t\tvalue: newValue\r\n\t\t},\r\n\t\t() => {\r\n\t\t\t// Update the new `value` property.\r\n\t\t\t// Doing it after the `state` has been updated\r\n\t\t\t// because `onChange()` will trigger `getDerivedStateFromProps()`\r\n\t\t\t// with the new `value` which will be compared to `state.value` there.\r\n\t\t\tonChange(newValue)\r\n\t\t})\r\n\t}\r\n\r\n\t/**\r\n\t * `<input/>` `onChange()` handler.\r\n\t * Updates `value` property accordingly (so that they are kept in sync).\r\n\t * @param {string?} input — Either a parsed phone number or an empty string. Examples: `\"\"`, `\"+\"`, `\"+123\"`, `\"123\"`.\r\n\t */\r\n\tonChange = (_phoneDigits) => {\r\n\t\tconst {\r\n\t\t\tdefaultCountry,\r\n\t\t\tonChange,\r\n\t\t\taddInternationalOption,\r\n\t\t\tinternational,\r\n\t\t\tlimitMaxLength,\r\n\t\t\tcountryCallingCodeEditable,\r\n\t\t\tmetadata\r\n\t\t} = this.props\r\n\r\n\t\tconst {\r\n\t\t\tcountries,\r\n\t\t\tphoneDigits: prevPhoneDigits,\r\n\t\t\tcountry: currentlySelectedCountry,\r\n\t\t\tlatestCountrySelectedByUser\r\n\t\t} = this.state\r\n\r\n\t\tconst {\r\n\t\t\t// `phoneDigits` returned here are a \"normalized\" version of the original `phoneDigits`.\r\n\t\t\t// The returned `phoneDigits` shouldn't be used anywhere except for passing it as\r\n\t\t\t// `prevPhoneDigits` parameter to the same `onPhoneDigitsChange()` function\r\n\t\t\t// on next input change event.\r\n\t\t\tphoneDigits,\r\n\t\t\tcountry,\r\n\t\t\tvalue\r\n\t\t} = onPhoneDigitsChange(_phoneDigits, {\r\n\t\t\tprevPhoneDigits,\r\n\t\t\tcountry: currentlySelectedCountry,\r\n\t\t\tcountryRequired: !addInternationalOption,\r\n\t\t\tdefaultCountry,\r\n\t\t\tlatestCountrySelectedByUser,\r\n\t\t\tgetAnyCountry: () => this.getFirstSupportedCountry({ countries }),\r\n\t\t\tcountries,\r\n\t\t\tinternational,\r\n\t\t\tlimitMaxLength,\r\n\t\t\tcountryCallingCodeEditable,\r\n\t\t\tmetadata\r\n\t\t})\r\n\r\n\t\tconst stateUpdate = {\r\n\t\t\tphoneDigits,\r\n\t\t\tvalue,\r\n\t\t\tcountry\r\n\t\t}\r\n\r\n\t\t// Reset `latestCountrySelectedByUser` if it no longer fits the `value`.\r\n\t\tif (latestCountrySelectedByUser && value && !couldNumberBelongToCountry(value, latestCountrySelectedByUser, metadata)) {\r\n\t\t\tstateUpdate.latestCountrySelectedByUser = undefined\r\n\t\t}\r\n\r\n\t\tif (countryCallingCodeEditable === false) {\r\n\t\t\t// If it simply did `setState({ phoneDigits: intlPrefix })` here,\r\n\t\t\t// then it would have no effect when erasing an inital international prefix\r\n\t\t\t// via Backspace, because `phoneDigits` in `state` wouldn't change\r\n\t\t\t// as a result, because it was `prefix` and it became `prefix`,\r\n\t\t\t// so the component wouldn't rerender, and the user would be able\r\n\t\t\t// to erase the country calling code part, and that part is\r\n\t\t\t// assumed to be non-eraseable. That's why the component is\r\n\t\t\t// forcefully rerendered here.\r\n\t\t\t// https://github.com/catamphetamine/react-phone-number-input/issues/367#issuecomment-721703501\r\n\t\t\tif (!value && phoneDigits === this.state.phoneDigits) {\r\n\t\t\t\t// Force a re-render of the `<input/>` in order to reset its value.\r\n\t\t\t\tstateUpdate.forceRerender = {}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tthis.setState(\r\n\t\t\tstateUpdate,\r\n\t\t\t// Update the new `value` property.\r\n\t\t\t// Doing it after the `state` has been updated\r\n\t\t\t// because `onChange()` will trigger `getDerivedStateFromProps()`\r\n\t\t\t// with the new `value` which will be compared to `state.value` there.\r\n\t\t\t() => onChange(value)\r\n\t\t)\r\n\t}\r\n\r\n\t// Toggles the `--focus` CSS class.\r\n\t_onFocus = () => this.setState({ isFocused: true })\r\n\r\n\t// Toggles the `--focus` CSS class.\r\n\t_onBlur = () => this.setState({ isFocused: false })\r\n\r\n\tonFocus = (event) => {\r\n\t\tthis._onFocus()\r\n\t\tconst { onFocus } = this.props\r\n\t\tif (onFocus) {\r\n\t\t\tonFocus(event)\r\n\t\t}\r\n\t}\r\n\r\n\tonBlur = (event) => {\r\n\t\tconst { onBlur } = this.props\r\n\t\tthis._onBlur()\r\n\t\tif (onBlur) {\r\n\t\t\tonBlur(event)\r\n\t\t}\r\n\t}\r\n\r\n\tonCountryFocus = (event) => {\r\n\t\tthis._onFocus()\r\n\t\t// this.setState({ countrySelectFocused: true })\r\n\t\tconst { countrySelectProps } = this.props\r\n\t\tif (countrySelectProps) {\r\n\t\t\tconst { onFocus } = countrySelectProps\r\n\t\t\tif (onFocus) {\r\n\t\t\t\tonFocus(event)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tonCountryBlur = (event) => {\r\n\t\tthis._onBlur()\r\n\t\t// this.setState({ countrySelectFocused: false })\r\n\t\tconst { countrySelectProps } = this.props\r\n\t\tif (countrySelectProps) {\r\n\t\t\tconst { onBlur } = countrySelectProps\r\n\t\t\tif (onBlur) {\r\n\t\t\t\tonBlur(event)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// `state` holds previous props as `props`, and also:\r\n\t// * `country` — The currently selected country, e.g. `\"RU\"`.\r\n\t// * `value` — The currently entered phone number (E.164), e.g. `+78005553535`.\r\n\t// * `phoneDigits` — The parsed `<input/>` value, e.g. `8005553535`.\r\n\t// (and a couple of other less significant properties)\r\n\tstatic getDerivedStateFromProps(props, state) {\r\n\t\treturn {\r\n\t\t\t// Emulate `prevProps` via `state.props`.\r\n\t\t\tprops,\r\n\t\t\t...getPhoneInputWithCountryStateUpdateFromNewProps(props, state.props, state)\r\n\t\t}\r\n\t}\r\n\r\n\trender() {\r\n\t\tconst {\r\n\t\t\t// Generic HTML attributes.\r\n\t\t\tname,\r\n\t\t\tdisabled,\r\n\t\t\treadOnly,\r\n\t\t\tautoComplete,\r\n\t\t\tstyle,\r\n\t\t\tclassName,\r\n\r\n\t\t\t// Number `<input/>` properties.\r\n\t\t\tinputRef,\r\n\t\t\tinputComponent,\r\n\t\t\tnumberInputProps,\r\n\t\t\tsmartCaret,\r\n\r\n\t\t\t// Country `<select/>` properties.\r\n\t\t\tcountrySelectComponent: CountrySelectComponent,\r\n\t\t\tcountrySelectProps,\r\n\r\n\t\t\t// Container `<div/>` properties.\r\n\t\t\tcontainerComponent: ContainerComponent,\r\n\t\t\tcontainerComponentProps,\r\n\r\n\t\t\t// Get \"rest\" properties (passed through to number `<input/>`).\r\n\t\t\tdefaultCountry,\r\n\t\t\tcountries: countriesProperty,\r\n\t\t\tcountryOptionsOrder,\r\n\t\t\tlabels,\r\n\t\t\tflags,\r\n\t\t\tflagComponent,\r\n\t\t\tflagUrl,\r\n\t\t\taddInternationalOption,\r\n\t\t\tinternationalIcon,\r\n\t\t\t// `displayInitialValueAsLocalNumber` property has been\r\n\t\t\t// superceded by `initialValueFormat` property.\r\n\t\t\tdisplayInitialValueAsLocalNumber,\r\n\t\t\tinitialValueFormat,\r\n\t\t\tonCountryChange,\r\n\t\t\tlimitMaxLength,\r\n\t\t\tcountryCallingCodeEditable,\r\n\t\t\tfocusInputOnCountrySelection,\r\n\t\t\treset,\r\n\t\t\tmetadata,\r\n\t\t\tinternational,\r\n\t\t\tlocales,\r\n\t\t\t// compareStrings,\r\n\t\t\t...rest\r\n\t\t} = this.props\r\n\r\n\t\tconst {\r\n\t\t\tcountry,\r\n\t\t\tcountries,\r\n\t\t\tphoneDigits,\r\n\t\t\tisFocused\r\n\t\t} = this.state\r\n\r\n\t\tconst InputComponent = smartCaret ? InputSmart : InputBasic\r\n\r\n\t\tconst countrySelectOptions = this.getCountrySelectOptions({ countries })\r\n\r\n\t\treturn (\r\n\t\t\t<ContainerComponent\r\n\t\t\t\tstyle={style}\r\n\t\t\t\tclassName={classNames(className, 'PhoneInput', {\r\n\t\t\t\t\t'PhoneInput--focus': isFocused,\r\n\t\t\t\t\t'PhoneInput--disabled': disabled,\r\n\t\t\t\t\t'PhoneInput--readOnly': readOnly\r\n\t\t\t\t})}\r\n\t\t\t\t{...containerComponentProps}>\r\n\r\n\t\t\t\t{/* Country `<select/>` */}\r\n\t\t\t\t<CountrySelectComponent\r\n\t\t\t\t\tname={name ? `${name}Country` : undefined}\r\n\t\t\t\t\taria-label={labels.country}\r\n\t\t\t\t\t{...countrySelectProps}\r\n\t\t\t\t\tvalue={country}\r\n\t\t\t\t\toptions={countrySelectOptions}\r\n\t\t\t\t\tonChange={this.onCountryChange}\r\n\t\t\t\t\tonFocus={this.onCountryFocus}\r\n\t\t\t\t\tonBlur={this.onCountryBlur}\r\n\t\t\t\t\tdisabled={disabled || (countrySelectProps && countrySelectProps.disabled)}\r\n\t\t\t\t\treadOnly={readOnly || (countrySelectProps && countrySelectProps.readOnly)}\r\n\t\t\t\t\ticonComponent={this.CountryIcon}/>\r\n\r\n\t\t\t\t{/* Phone number `<input/>` */}\r\n\t\t\t\t<InputComponent\r\n\t\t\t\t\tref={this.setInputRef}\r\n\t\t\t\t\ttype=\"tel\"\r\n\t\t\t\t\tautoComplete={autoComplete}\r\n\t\t\t\t\t{...numberInputProps}\r\n\t\t\t\t\t{...rest}\r\n\t\t\t\t\tinputFormat={international === true ? 'INTERNATIONAL' : (international === false ? 'NATIONAL' : 'INTERNATIONAL_OR_NATIONAL')}\r\n\t\t\t\t\tinternational={international ? true : undefined}\r\n\t\t\t\t\twithCountryCallingCode={international ? true : undefined}\r\n\t\t\t\t\tname={name}\r\n\t\t\t\t\tmetadata={metadata}\r\n\t\t\t\t\tcountry={country}\r\n\t\t\t\t\tvalue={phoneDigits || ''}\r\n\t\t\t\t\tonChange={this.onChange}\r\n\t\t\t\t\tonFocus={this.onFocus}\r\n\t\t\t\t\tonBlur={this.onBlur}\r\n\t\t\t\t\tdisabled={disabled}\r\n\t\t\t\t\treadOnly={readOnly}\r\n\t\t\t\t\tinputComponent={inputComponent}\r\n\t\t\t\t\tclassName={classNames(\r\n\t\t\t\t\t\t'PhoneInputInput',\r\n\t\t\t\t\t\tnumberInputProps && numberInputProps.className,\r\n\t\t\t\t\t\trest.className\r\n\t\t\t\t\t)}/>\r\n\t\t\t</ContainerComponent>\r\n\t\t)\r\n\t}\r\n}\r\n\r\n// This wrapper is only to `.forwardRef()` to the `<input/>`.\r\nconst PhoneNumberInput = React.forwardRef((props, ref) => (\r\n\t<PhoneNumberInput_ {...withDefaultProps(props)} inputRef={ref}/>\r\n))\r\n\r\nPhoneNumberInput.propTypes = {\r\n\t/**\r\n\t * Phone number in `E.164` format.\r\n\t *\r\n\t * Example:\r\n\t *\r\n\t * `\"+12223333333\"`\r\n\t *\r\n\t * Any \"falsy\" value like `undefined`, `null` or an empty string `\"\"` is treated like \"empty\".\r\n\t */\r\n\tvalue: PropTypes.string,\r\n\r\n\t/**\r\n\t * A function of `value: string?`.\r\n\t *\r\n\t * Updates the `value` property as the user inputs a phone number.\r\n\t *\r\n\t * If the user erases the input value, the argument is `undefined`.\r\n\t */\r\n\tonChange: PropTypes.func.isRequired,\r\n\r\n\t/**\r\n\t * Toggles the `--focus` CSS class.\r\n\t * @ignore\r\n\t */\r\n\tonFocus: PropTypes.func,\r\n\r\n\t/**\r\n\t * `onBlur` is usually passed by `redux-form`.\r\n\t * @ignore\r\n\t */\r\n\tonBlur: PropTypes.func,\r\n\r\n\t/**\r\n\t * Set to `true` to mark both the phone number `<input/>`\r\n\t * and the country `<select/>` as `disabled`.\r\n\t */\r\n\tdisabled: PropTypes.bool,\r\n\r\n\t/**\r\n\t * Set to `true` to mark both the phone number `<input/>`\r\n\t * and the country `<select/>` as `readonly`.\r\n\t */\r\n\treadOnly: PropTypes.bool,\r\n\r\n\t/**\r\n\t * Sets `autoComplete` property for phone number `<input/>`.\r\n\t *\r\n\t * Web browser's \"autocomplete\" feature\r\n\t * remembers the phone number being input\r\n\t * and can also autofill the `<input/>`\r\n\t * with previously remembered phone numbers.\r\n\t *\r\n\t * https://developers.google.com\r\n\t * /web/updates/2015/06/checkout-faster-with-autofill\r\n\t *\r\n\t * For example, can be used to turn it off:\r\n\t *\r\n\t * \"So when should you use `autocomplete=\"off\"`?\r\n\t *  One example is when you've implemented your own version\r\n\t *  of autocomplete for search. Another example is any form field\r\n\t *  where users will input and submit different kinds of information\r\n\t *  where it would not be useful to have the browser remember\r\n\t *  what was submitted previously\".\r\n\t */\r\n\t// (is `\"tel\"` by default)\r\n\tautoComplete: PropTypes.string,\r\n\r\n\t/**\r\n\t * Set to `\"national\"` to show the initial `value` in\r\n\t * \"national\" format rather than \"international\".\r\n\t *\r\n\t * For example, if `initialValueFormat` is `\"national\"`\r\n\t * and the initial `value=\"+12133734253\"` is passed\r\n\t * then the `<input/>` value will be `\"(*************\"`.\r\n\t *\r\n\t * By default, `initialValueFormat` is `undefined`,\r\n\t * meaning that if the initial `value=\"+12133734253\"` is passed\r\n\t * then the `<input/>` value will be `\"****** 373 4253\"`.\r\n\t *\r\n\t * The reason for such default behaviour is that\r\n\t * the newer generation grows up when there are no stationary phones\r\n\t * and therefore everyone inputs phone numbers in international format\r\n\t * in their smartphones so people gradually get more accustomed to\r\n\t * writing phone numbers in international format rather than in local format.\r\n\t * Future people won't be using \"national\" format, only \"international\".\r\n\t */\r\n\t// (is `undefined` by default)\r\n\tinitialValueFormat: PropTypes.oneOf(['national']),\r\n\r\n\t// `displayInitialValueAsLocalNumber` property has been\r\n\t// superceded by `initialValueFormat` property.\r\n\tdisplayInitialValueAsLocalNumber: PropTypes.bool,\r\n\r\n\t/**\r\n\t * The country to be selected by default.\r\n\t * For example, can be set after a GeoIP lookup.\r\n\t *\r\n\t * Example: `\"US\"`.\r\n\t */\r\n\t// A two-letter country code (\"ISO 3166-1 alpha-2\").\r\n\tdefaultCountry: PropTypes.string,\r\n\r\n\t/**\r\n\t * If specified, only these countries will be available for selection.\r\n\t *\r\n\t * Example:\r\n\t *\r\n\t * `[\"RU\", \"UA\", \"KZ\"]`\r\n\t */\r\n\tcountries: PropTypes.arrayOf(PropTypes.string),\r\n\r\n\t/**\r\n\t * Custom country `<select/>` option names.\r\n\t * Also some labels like \"ext\" and country `<select/>` `aria-label`.\r\n\t *\r\n\t * Example:\r\n\t *\r\n\t * `{ \"ZZ\": \"Международный\", RU: \"Россия\", US: \"США\", ... }`\r\n\t *\r\n\t * See the `locales` directory for examples.\r\n\t */\r\n\tlabels: labelsPropType,\r\n\r\n\t/**\r\n\t * Country `<select/>` options are sorted by their labels.\r\n\t * The default sorting function uses `a.localeCompare(b, locales)`,\r\n\t * and, if that's not available, falls back to simple `a > b` / `a < b`.\r\n\t * Some languages, like Chinese, support multiple sorting variants\r\n\t * (called \"collations\"), and the user might prefer one or another.\r\n\t * Also, sometimes the Operating System language is not always\r\n\t * the preferred language for a person using a website or an application,\r\n\t * so there should be a way to specify custom locale.\r\n\t * This `locales` property mimicks the `locales` argument of `Intl` constructors,\r\n\t * and can be either a Unicode BCP 47 locale identifier or an array of such locale identifiers.\r\n\t * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl#locales_argument\r\n\t */\r\n\tlocales: PropTypes.oneOfType([\r\n\t\tPropTypes.string,\r\n\t\tPropTypes.arrayOf(PropTypes.string)\r\n\t]),\r\n\r\n\t/*\r\n\t * Custom country `<select/>` options sorting function.\r\n\t * The default one uses `a.localeCompare(b)`, and,\r\n\t * if that's not available, falls back to simple `a > b`/`a < b`.\r\n\t * There have been requests to add custom sorter for cases\r\n\t * like Chinese language and \"pinyin\" (non-default) sorting order.\r\n\t * https://stackoverflow.com/questions/22907288/chinese-sorting-by-pinyin-in-javascript-with-localecompare\r\n\tcompareStrings: PropTypes.func,\r\n\t */\r\n\r\n\t/**\r\n\t * A URL template of a country flag, where\r\n\t * \"{XX}\" is a two-letter country code in upper case,\r\n\t * or where \"{xx}\" is a two-letter country code in lower case.\r\n\t * By default it points to `country-flag-icons` gitlab pages website.\r\n\t * I imagine someone might want to download those country flag icons\r\n\t * and host them on their own servers instead\r\n\t * (all flags are available in the `country-flag-icons` library).\r\n\t * There's a catch though: new countries may be added in future,\r\n\t * so when hosting country flag icons on your own server\r\n\t * one should check the `CHANGELOG.md` every time before updating this library,\r\n\t * otherwise there's a possibility that some new country flag would be missing.\r\n\t */\r\n\tflagUrl: PropTypes.string,\r\n\r\n\t/**\r\n\t * Custom country flag icon components.\r\n\t * These flags will be used instead of the default ones.\r\n\t * The the \"Flags\" section of the readme for more info.\r\n\t *\r\n\t * The shape is an object where keys are country codes\r\n\t * and values are flag icon components.\r\n\t * Flag icon components receive the same properties\r\n\t * as `flagComponent` (see below).\r\n\t *\r\n\t * Example:\r\n\t *\r\n\t * `{ \"RU\": (props) => <img src=\"...\"/> }`\r\n\t *\r\n\t * Example:\r\n\t *\r\n\t * `import flags from 'country-flag-icons/react/3x2'`\r\n\t *\r\n\t * `import PhoneInput from 'react-phone-number-input'`\r\n\t *\r\n\t * `<PhoneInput flags={flags} .../>`\r\n\t */\r\n\tflags: PropTypes.objectOf(PropTypes.elementType),\r\n\r\n\t/**\r\n\t * Country flag icon component.\r\n\t *\r\n\t * Takes properties:\r\n\t *\r\n\t * * `country: string` — The country code.\r\n\t * * `countryName: string` — The country name.\r\n\t * * `flagUrl: string` — The `flagUrl` property (see above).\r\n\t * * `flags: object` — The `flags` property (see above).\r\n\t */\r\n\tflagComponent: PropTypes.elementType,\r\n\r\n\t/**\r\n\t * Set to `false` to remove the \"International\" option from country `<select/>`.\r\n\t */\r\n\taddInternationalOption: PropTypes.bool,\r\n\r\n\t/**\r\n\t * \"International\" icon component.\r\n\t * Should have the same aspect ratio.\r\n\t *\r\n\t * Receives properties:\r\n\t *\r\n\t * * `title: string` — \"International\" country option label.\r\n\t */\r\n\tinternationalIcon: PropTypes.elementType,\r\n\r\n\t/**\r\n\t * Can be used to place some countries on top of the list of country `<select/>` options.\r\n\t *\r\n\t * * `\"XX\"` — inserts an option for \"XX\" country.\r\n\t * * `\"🌐\"` — inserts \"International\" option.\r\n\t * * `\"|\"` — inserts a separator.\r\n\t * * `\"...\"` — inserts options for the rest of the countries (can be omitted, in which case it will be automatically added at the end).\r\n\t *\r\n\t * Example:\r\n\t *\r\n\t * `[\"US\", \"CA\", \"AU\", \"|\", \"...\"]`\r\n\t */\r\n\tcountryOptionsOrder: PropTypes.arrayOf(PropTypes.string),\r\n\r\n\t/**\r\n\t * `<Phone/>` component CSS style object.\r\n\t */\r\n\tstyle: PropTypes.object,\r\n\r\n\t/**\r\n\t * `<Phone/>` component CSS class.\r\n\t */\r\n\tclassName: PropTypes.string,\r\n\r\n\t/**\r\n\t * Country `<select/>` component.\r\n\t *\r\n\t * Receives properties:\r\n\t *\r\n\t * * `name: string?` — HTML `name` attribute.\r\n\t * * `value: string?` — The currently selected country code.\r\n\t * * `onChange(value: string?)` — Updates the `value`.\r\n\t * * `onFocus()` — Is used to toggle the `--focus` CSS class.\r\n\t * * `onBlur()` — Is used to toggle the `--focus` CSS class.\r\n\t * * `options: object[]` — The list of all selectable countries (including \"International\") each being an object of shape `{ value: string?, label: string }`.\r\n\t * * `iconComponent: PropTypes.elementType` — React component that renders a country icon: `<Icon country={value}/>`. If `country` is `undefined` then it renders an \"International\" icon.\r\n\t * * `disabled: boolean?` — HTML `disabled` attribute.\r\n\t * * `readOnly: boolean?` — HTML `readOnly` attribute.\r\n\t * * `tabIndex: (number|string)?` — HTML `tabIndex` attribute.\r\n\t * * `className: string` — CSS class name.\r\n\t */\r\n\tcountrySelectComponent: PropTypes.elementType,\r\n\r\n\t/**\r\n\t * Country `<select/>` component props.\r\n\t * Along with the usual DOM properties such as `aria-label` and `tabIndex`,\r\n\t * some custom properties are supported, such as `arrowComponent` and `unicodeFlags`.\r\n\t */\r\n\tcountrySelectProps: PropTypes.object,\r\n\r\n\t/**\r\n\t * Phone number `<input/>` component.\r\n\t *\r\n\t * Receives properties:\r\n\t *\r\n\t * * `value: string` — The formatted `value`.\r\n\t * * `onChange(event: Event)` — Updates the formatted `value` from `event.target.value`.\r\n\t * * `onFocus()` — Is used to toggle the `--focus` CSS class.\r\n\t * * `onBlur()` — Is used to toggle the `--focus` CSS class.\r\n\t * * Other properties like `type=\"tel\"` or `autoComplete=\"tel\"` that should be passed through to the DOM `<input/>`.\r\n\t *\r\n\t * Must also either use `React.forwardRef()` to \"forward\" `ref` to the `<input/>` or implement `.focus()` method.\r\n\t */\r\n\tinputComponent: PropTypes.elementType,\r\n\r\n\t/**\r\n\t * Phone number `<input/>` component props.\r\n\t */\r\n\tnumberInputProps: PropTypes.object,\r\n\r\n\t/**\r\n\t * Wrapping `<div/>` component.\r\n\t *\r\n\t * Receives properties:\r\n\t *\r\n\t * * `style: object` — A component CSS style object.\r\n\t * * `className: string` — Classes to attach to the component, typically changes when component focuses or blurs.\r\n\t */\r\n\tcontainerComponent: PropTypes.elementType,\r\n\r\n\t/**\r\n\t * Wrapping `<div/>` component props.\r\n\t */\r\n\tcontainerComponentProps: PropTypes.object,\r\n\r\n\t/**\r\n\t * When the user attempts to insert a digit somewhere in the middle of a phone number,\r\n\t * the caret position is moved right before the next available digit skipping\r\n\t * any punctuation in between. This is called \"smart\" caret positioning.\r\n\t * Another case would be the phone number format changing as a result of\r\n\t * the user inserting the digit somewhere in the middle, which would require\r\n\t * re-positioning the caret because all digit positions have changed.\r\n\t * This \"smart\" caret positioning feature can be turned off by passing\r\n\t * `smartCaret={false}` property: use it in case of any possible issues\r\n\t * with caret position during phone number input.\r\n\t */\r\n\t// Is `true` by default.\r\n\tsmartCaret: PropTypes.bool,\r\n\r\n\t/**\r\n\t * Set to `true` to force \"international\" phone number format.\r\n\t * Set to `false` to force \"national\" phone number format.\r\n\t * By default it's `undefined` meaning that it doesn't enforce any phone number format:\r\n\t * the user can input their phone number in either \"national\" or \"international\" format.\r\n\t */\r\n\tinternational: PropTypes.bool,\r\n\r\n\t/**\r\n\t * If set to `true`, the phone number input will get trimmed\r\n\t * if it exceeds the maximum length for the country.\r\n\t */\r\n\tlimitMaxLength: PropTypes.bool,\r\n\r\n\t/**\r\n\t * If set to `false`, and `international` is `true`, then\r\n\t * users won't be able to erase the \"country calling part\"\r\n\t * of a phone number in the `<input/>`.\r\n\t */\r\n\tcountryCallingCodeEditable: PropTypes.bool,\r\n\r\n\t/**\r\n\t * `libphonenumber-js` metadata.\r\n\t *\r\n\t * Can be used to pass custom `libphonenumber-js` metadata\r\n\t * to reduce the overall bundle size for those who compile \"custom\" metadata.\r\n\t */\r\n\tmetadata: metadataPropType,\r\n\r\n\t/**\r\n\t * Is called every time the selected country changes:\r\n\t * either programmatically or when user selects it manually from the list.\r\n\t */\r\n\t// People have been asking for a way to get the selected country.\r\n\t// @see  https://github.com/catamphetamine/react-phone-number-input/issues/128\r\n\t// For some it's just a \"business requirement\".\r\n\t// I guess it's about gathering as much info on the user as a website can\r\n\t// without introducing any addional fields that would complicate the form\r\n\t// therefore reducing \"conversion\" (that's a marketing term).\r\n\t// Assuming that the phone number's country is the user's country\r\n\t// is not 100% correct but in most cases I guess it's valid.\r\n\tonCountryChange: PropTypes.func,\r\n\r\n\t/**\r\n\t * If set to `false`, will not focus the `<input/>` component\r\n\t * when the user selects a country from the list of countries.\r\n\t * This can be used to conform to the Web Content Accessibility Guidelines (WCAG).\r\n\t * Quote:\r\n\t * \"On input: Changing the setting of any user interface component\r\n\t *  does not automatically cause a change of context unless the user\r\n\t *  has been advised of the behaviour before using the component.\"\r\n\t */\r\n\tfocusInputOnCountrySelection: PropTypes.bool\r\n}\r\n\r\nconst defaultProps = {\r\n\t/**\r\n\t * Remember (and autofill) the value as a phone number.\r\n\t */\r\n\tautoComplete: 'tel',\r\n\r\n\t/**\r\n\t * Country `<select/>` component.\r\n\t */\r\n\tcountrySelectComponent: CountrySelect,\r\n\r\n\t/**\r\n\t * Flag icon component.\r\n\t */\r\n\tflagComponent: Flag,\r\n\r\n\t/**\r\n\t * By default, uses icons from `country-flag-icons` gitlab pages website.\r\n\t */\r\n\t// Must be equal to `flagUrl` in `./CountryIcon.js`.\r\n\tflagUrl: 'https://purecatamphetamine.github.io/country-flag-icons/3x2/{XX}.svg',\r\n\r\n\t/**\r\n\t * Default \"International\" country `<select/>` option icon.\r\n\t */\r\n\tinternationalIcon: InternationalIcon,\r\n\r\n\t/**\r\n\t * Phone number `<input/>` component.\r\n\t */\r\n\tinputComponent: 'input',\r\n\r\n\t/**\r\n\t * Wrapping `<div/>` component.\r\n\t */\r\n\tcontainerComponent: 'div',\r\n\r\n\t/**\r\n\t * Some users requested a way to reset the component:\r\n\t * both number `<input/>` and country `<select/>`.\r\n\t * Whenever `reset` property changes both number `<input/>`\r\n\t * and country `<select/>` are reset.\r\n\t * It's not implemented as some instance `.reset()` method\r\n\t * because `ref` is forwarded to `<input/>`.\r\n\t * It's also not replaced with just resetting `country` on\r\n\t * external `value` reset, because a user could select a country\r\n\t * and then not input any `value`, and so the selected country\r\n\t * would be \"stuck\", if not using this `reset` property.\r\n\t */\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/300\r\n\treset: PropTypes.any,\r\n\r\n\t/**\r\n\t *\r\n\t */\r\n\r\n\t/**\r\n\t * Set to `false` to use \"basic\" caret instead of the \"smart\" one.\r\n\t */\r\n\tsmartCaret: true,\r\n\r\n\t/**\r\n\t * Whether to add the \"International\" option\r\n\t * to the list of countries.\r\n\t */\r\n\taddInternationalOption: true,\r\n\r\n\t/**\r\n\t * If set to `false`, and `international` is `true`, then\r\n\t * users won't be able to erase the \"country calling part\"\r\n\t * of a phone number in the `<input/>`.\r\n\t */\r\n\tcountryCallingCodeEditable: true,\r\n\r\n\t/**\r\n\t * If set to `false`, will not focus the `<input/>` component\r\n\t * when the user selects a country from the list of countries.\r\n\t * This can be used to conform to the Web Content Accessibility Guidelines (WCAG).\r\n\t * Quote:\r\n\t * \"On input: Changing the setting of any user interface component\r\n\t *  does not automatically cause a change of context unless the user\r\n\t *  has been advised of the behaviour before using the component.\"\r\n\t */\r\n\tfocusInputOnCountrySelection: true\r\n}\r\n\r\nfunction withDefaultProps(props) {\r\n\tprops = { ...props }\r\n\r\n\tfor (const key in defaultProps) {\r\n\t\tif (props[key] === undefined) {\r\n\t\t\tprops[key] = defaultProps[key]\r\n\t\t}\r\n\t}\r\n\r\n\treturn props\r\n}\r\n\r\nexport default PhoneNumberInput\r\n\r\nfunction areEqualArrays(a, b) {\r\n\tif (a.length !== b.length) {\r\n\t\treturn false\r\n\t}\r\n\tlet i = 0\r\n\twhile (i < a.length) {\r\n\t\tif (a[i] !== b[i]) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\treturn true\r\n}\r\n"], "names": ["React", "PropTypes", "classNames", "InputSmart", "InputBasic", "CountrySelectWithIcon", "CountrySelect", "Flag", "InternationalIcon", "validateE164Number", "sortCountryOptions", "isCountrySupportedWithError", "getSupportedCountries", "getSupportedCountryOptions", "getCountries", "createCountryIconComponent", "setRefsValue", "metadata", "metadataPropType", "labels", "labelsPropType", "getPreSelectedCountry", "getCountrySelectOptions", "couldNumberBelongToCountry", "parsePhoneNumber", "generateNationalNumberDigits", "getPhoneDigitsForNewCountry", "getInitialPhoneDigits", "onPhoneDigitsChange", "e164", "getPhoneInputWithCountryStateUpdateFromNewProps", "PhoneNumberInput_", "_React$PureComponent", "props", "_this", "_classCallCheck", "_callSuper", "_defineProperty", "instance", "inputRef", "country", "newCountry", "_this$props", "international", "onChange", "focusInputOnCountrySelection", "_this$state", "state", "prevPhoneDigits", "phoneDigits", "prevCountry", "newPhoneDigits", "useNationalFormat", "newValue", "current", "focus", "setState", "latestCountrySelectedByUser", "hasUserSelectedACountry", "value", "_phoneDigits", "_this$props2", "defaultCountry", "addInternationalOption", "limitMaxLength", "countryCallingCodeEditable", "_this$state2", "countries", "currentlySelectedCountry", "_onPhoneDigitsChange", "countryRequired", "getAnyCountry", "getFirstSupportedCountry", "stateUpdate", "undefined", "force<PERSON><PERSON>nder", "isFocused", "event", "_onFocus", "onFocus", "onBlur", "_onBlur", "countrySelectProps", "createRef", "_this$props3", "displayInitialValueAsLocalNumber", "initialValueFormat", "_this$props4", "phoneNumber", "CountryIcon", "preSelectedCountry", "required", "_inherits", "_createClass", "key", "componentDidMount", "onCountryChange", "selectedCountry", "componentDidUpdate", "prevProps", "prevState", "_ref", "_this$props5", "countryOptionsOrder", "locales", "useMemoCountrySelectOptions", "countryNames", "compareStringsLocales", "generator", "dependencies", "countrySelectOptionsMemoDependencies", "areEqualArrays", "countrySelectOptionsMemo", "_ref2", "countryOptions", "render", "_this$props6", "name", "disabled", "readOnly", "autoComplete", "style", "className", "inputComponent", "numberInputProps", "smartCaret", "CountrySelectComponent", "countrySelectComponent", "ContainerComponent", "containerComponent", "containerComponentProps", "countriesProperty", "flags", "flagComponent", "flagUrl", "internationalIcon", "reset", "rest", "_objectWithoutProperties", "_excluded", "_this$state3", "InputComponent", "countrySelectOptions", "createElement", "_extends", "concat", "options", "onCountryFocus", "onCountryBlur", "iconComponent", "ref", "setInputRef", "type", "inputFormat", "withCountryCallingCode", "getDerivedStateFromProps", "_objectSpread", "PureComponent", "PhoneNumberInput", "forwardRef", "withDefaultProps", "propTypes", "string", "func", "isRequired", "bool", "oneOf", "arrayOf", "oneOfType", "objectOf", "elementType", "object", "defaultProps", "any", "a", "b", "length", "i"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,UAAU,MAAM,YAAY;AAEnC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,UAAU,MAAM,iBAAiB;AAExC,SAASC,qBAAqB,IAAIC,aAAa,QAAQ,oBAAoB;AAE3E,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,iBAAiB,MAAM,wBAAwB;AAEtD,SAASC,kBAAkB,QAAQ,2BAA2B;;AAE9D,SACCC,kBAAkB,EAClBC,2BAA2B,EAC3BC,qBAAqB,EACrBC,0BAA0B,EAC1BC,YAAY,QACN,wBAAwB;AAE/B,SAASC,0BAA0B,QAAQ,kBAAkB;AAE7D,SAASC,YAAY,QAAQ,qBAAqB;AAElD,SACCC,QAAQ,IAAIC,gBAAgB,EAC5BC,MAAM,IAAIC,cAAc,QAClB,gBAAgB;AAEvB,SACCC,qBAAqB,EACrBC,uBAAuB,IAAvBA,wBAAuB,EACvBC,0BAA0B,EAC1BC,gBAAgB,EAChBC,4BAA4B,EAC5BC,2BAA2B,EAC3BC,qBAAqB,EACrBC,mBAAmB,EACnBC,IAAI,QACE,gCAAgC;AAEvC,OAAOC,+CAA+C,MAAM,8DAA8D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAEpHC,iBAAiB,GAAA,WAAA,GAAA,SAAAC,oBAAA;IACtB,SAAAD,kBAAYE,KAAK,EAAE;QAAA,IAAAC,KAAA;QAAAC,eAAA,CAAA,IAAA,EAAAJ,iBAAA;QAClBG,KAAA,GAAAE,UAAA,CAAA,IAAA,EAAAL,iBAAA,EAAA;YAAME,KAAK;SAAA;QA0HZ,mFAAA;QACA,iFAAA;QAAAI,eAAA,CAAAH,KAAA,EAAA,eACc,SAACI,QAAQ,EAAK;2LAC3BtB,eAAAA,AAAY,EAAC;gBAACkB,KAAA,CAAKD,KAAK,CAACM,QAAQ;gBAAEL,KAAA,CAAKK,QAAQ;aAAC,EAAED,QAAQ,CAAC;QAC7D,CAAC;QAiDD,+DAAA;QAAAD,eAAA,CAAAH,KAAA,EAAA,+BAC8B,SAACM,OAAO,EAAK;YAC1C,IAAQvB,QAAQ,GAAKiB,KAAA,CAAKD,KAAK,CAAvBhB,QAAQ;YAChB,4MAAON,8BAAAA,AAA2B,EAAC6B,OAAO,EAAEvB,QAAQ,CAAC;QACtD,CAAC;QAED,0CAAA;QAAAoB,eAAA,CAAAH,KAAA,EAAA,mBACkB,SAACO,UAAU,EAAK;YACjC,IAAAC,WAAA,GAKIR,KAAA,CAAKD,KAAK,EAJbU,aAAa,GAAAD,WAAA,CAAbC,aAAa,EACb1B,QAAQ,GAAAyB,WAAA,CAARzB,QAAQ,EACR2B,QAAQ,GAAAF,WAAA,CAARE,QAAQ,EACRC,4BAA4B,GAAAH,WAAA,CAA5BG,4BAA4B;YAG7B,IAAAC,WAAA,GAGIZ,KAAA,CAAKa,KAAK,EAFAC,eAAe,GAAAF,WAAA,CAA5BG,WAAW,EACFC,WAAW,GAAAJ,WAAA,CAApBN,OAAO;YAGR,6CAAA;YACA,kDAAA;YACA,mDAAA;YACA,IAAMW,cAAc,+LAAGzB,+BAAAA,AAA2B,EAACsB,eAAe,EAAE;gBACnEE,WAAW,EAAXA,WAAW;gBACXT,UAAU,EAAVA,UAAU;gBACVxB,QAAQ,EAARA,QAAQ;gBACR,gDAAA;gBACA,sDAAA;gBACAmC,iBAAiB,EAAE,CAACT;YACrB,CAAC,CAAC;YAEF,IAAMU,QAAQ,gMAAGxB,OAAAA,AAAI,EAACsB,cAAc,EAAEV,UAAU,EAAExB,QAAQ,CAAC;YAE3D,wDAAA;YACA,IAAI4B,4BAA4B,EAAE;gBACjCX,KAAA,CAAKK,QAAQ,CAACe,OAAO,CAACC,KAAK,CAAC,CAAC;YAC9B;YAEA,sDAAA;YACA,oDAAA;YACA,4CAAA;YACA,qDAAA;YAEArB,KAAA,CAAKsB,QAAQ,CAAC;gBACbhB,OAAO,EAAEC,UAAU;gBACnBgB,2BAA2B,EAAEhB,UAAU;gBACvCiB,uBAAuB,EAAE,IAAI;gBAC7BT,WAAW,EAAEE,cAAc;gBAC3BQ,KAAK,EAAEN;YACR,CAAC,EACD,YAAM;gBACL,mCAAA;gBACA,8CAAA;gBACA,iEAAA;gBACA,sEAAA;gBACAT,QAAQ,CAACS,QAAQ,CAAC;YACnB,CAAC,CAAC;QACH,CAAC;QAED;;;;KAID,GAJChB,eAAA,CAAAH,KAAA,EAAA,YAKW,SAAC0B,YAAY,EAAK;YAC5B,IAAAC,YAAA,GAQI3B,KAAA,CAAKD,KAAK,EAPb6B,cAAc,GAAAD,YAAA,CAAdC,cAAc,EACdlB,QAAQ,GAAAiB,YAAA,CAARjB,QAAQ,EACRmB,sBAAsB,GAAAF,YAAA,CAAtBE,sBAAsB,EACtBpB,aAAa,GAAAkB,YAAA,CAAblB,aAAa,EACbqB,cAAc,GAAAH,YAAA,CAAdG,cAAc,EACdC,0BAA0B,GAAAJ,YAAA,CAA1BI,0BAA0B,EAC1BhD,QAAQ,GAAA4C,YAAA,CAAR5C,QAAQ;YAGT,IAAAiD,YAAA,GAKIhC,KAAA,CAAKa,KAAK,EAJboB,SAAS,GAAAD,YAAA,CAATC,SAAS,EACInB,eAAe,GAAAkB,YAAA,CAA5BjB,WAAW,EACFmB,wBAAwB,GAAAF,YAAA,CAAjC1B,OAAO,EACPiB,2BAA2B,GAAAS,YAAA,CAA3BT,2BAA2B;YAG5B,IAAAY,oBAAA,OAQIzC,+MAAAA,AAAmB,EAACgC,YAAY,EAAE;gBACrCZ,eAAe,EAAfA,eAAe;gBACfR,OAAO,EAAE4B,wBAAwB;gBACjCE,eAAe,EAAE,CAACP,sBAAsB;gBACxCD,cAAc,EAAdA,cAAc;gBACdL,2BAA2B,EAA3BA,2BAA2B;gBAC3Bc,aAAa,EAAE,SAAAA,cAAA;oBAAA,OAAMrC,KAAA,CAAKsC,wBAAwB,CAAC;wBAAEL,SAAS,EAATA;oBAAU,CAAC,CAAC;gBAAA;gBACjEA,SAAS,EAATA,SAAS;gBACTxB,aAAa,EAAbA,aAAa;gBACbqB,cAAc,EAAdA,cAAc;gBACdC,0BAA0B,EAA1BA,0BAA0B;gBAC1BhD,QAAQ,EAARA;YACD,CAAC,CAAC,EAfDgC,WAAW,GAAAoB,oBAAA,CAAXpB,WAAW,EACXT,OAAO,GAAA6B,oBAAA,CAAP7B,OAAO,EACPmB,KAAK,GAAAU,oBAAA,CAALV,KAAK;YAeN,IAAMc,WAAW,GAAG;gBACnBxB,WAAW,EAAXA,WAAW;gBACXU,KAAK,EAALA,KAAK;gBACLnB,OAAO,EAAPA;YACD,CAAC;YAED,wEAAA;YACA,IAAIiB,2BAA2B,IAAIE,KAAK,IAAI,KAACpC,sNAAAA,AAA0B,EAACoC,KAAK,EAAEF,2BAA2B,EAAExC,QAAQ,CAAC,EAAE;gBACtHwD,WAAW,CAAChB,2BAA2B,GAAGiB,SAAS;YACpD;YAEA,IAAIT,0BAA0B,KAAK,KAAK,EAAE;gBACzC,iEAAA;gBACA,2EAAA;gBACA,kEAAA;gBACA,+DAAA;gBACA,iEAAA;gBACA,2DAAA;gBACA,2DAAA;gBACA,8BAAA;gBACA,+FAAA;gBACA,IAAI,CAACN,KAAK,IAAIV,WAAW,KAAKf,KAAA,CAAKa,KAAK,CAACE,WAAW,EAAE;oBACrD,mEAAA;oBACAwB,WAAW,CAACE,aAAa,GAAG,CAAC,CAAC;gBAC/B;YACD;YAEAzC,KAAA,CAAKsB,QAAQ,CACZiB,WAAW,EACX,mCAAA;YACA,8CAAA;YACA,iEAAA;YACA,sEAAA;YACA;gBAAA,OAAM7B,QAAQ,CAACe,KAAK,CAAC;YAAA,CACtB,CAAC;QACF,CAAC;QAED,mCAAA;QAAAtB,eAAA,CAAAH,KAAA,EAAA,YACW;YAAA,OAAMA,KAAA,CAAKsB,QAAQ,CAAC;gBAAEoB,SAAS,EAAE;YAAK,CAAC,CAAC;QAAA;QAEnD,mCAAA;QAAAvC,eAAA,CAAAH,KAAA,EAAA,WACU;YAAA,OAAMA,KAAA,CAAKsB,QAAQ,CAAC;gBAAEoB,SAAS,EAAE;YAAM,CAAC,CAAC;QAAA;QAAAvC,eAAA,CAAAH,KAAA,EAAA,WAEzC,SAAC2C,KAAK,EAAK;YACpB3C,KAAA,CAAK4C,QAAQ,CAAC,CAAC;YACf,IAAQC,OAAO,GAAK7C,KAAA,CAAKD,KAAK,CAAtB8C,OAAO;YACf,IAAIA,OAAO,EAAE;gBACZA,OAAO,CAACF,KAAK,CAAC;YACf;QACD,CAAC;QAAAxC,eAAA,CAAAH,KAAA,EAAA,UAEQ,SAAC2C,KAAK,EAAK;YACnB,IAAQG,MAAM,GAAK9C,KAAA,CAAKD,KAAK,CAArB+C,MAAM;YACd9C,KAAA,CAAK+C,OAAO,CAAC,CAAC;YACd,IAAID,MAAM,EAAE;gBACXA,MAAM,CAACH,KAAK,CAAC;YACd;QACD,CAAC;QAAAxC,eAAA,CAAAH,KAAA,EAAA,kBAEgB,SAAC2C,KAAK,EAAK;YAC3B3C,KAAA,CAAK4C,QAAQ,CAAC,CAAC;YACf,gDAAA;YACA,IAAQI,kBAAkB,GAAKhD,KAAA,CAAKD,KAAK,CAAjCiD,kBAAkB;YAC1B,IAAIA,kBAAkB,EAAE;gBACvB,IAAQH,OAAO,GAAKG,kBAAkB,CAA9BH,OAAO;gBACf,IAAIA,OAAO,EAAE;oBACZA,OAAO,CAACF,KAAK,CAAC;gBACf;YACD;QACD,CAAC;QAAAxC,eAAA,CAAAH,KAAA,EAAA,iBAEe,SAAC2C,KAAK,EAAK;YAC1B3C,KAAA,CAAK+C,OAAO,CAAC,CAAC;YACd,iDAAA;YACA,IAAQC,kBAAkB,GAAKhD,KAAA,CAAKD,KAAK,CAAjCiD,kBAAkB;YAC1B,IAAIA,kBAAkB,EAAE;gBACvB,IAAQF,MAAM,GAAKE,kBAAkB,CAA7BF,MAAM;gBACd,IAAIA,MAAM,EAAE;oBACXA,MAAM,CAACH,KAAK,CAAC;gBACd;YACD;QACD,CAAC;QAvWA3C,KAAA,CAAKK,QAAQ,GAAA,WAAA,8HAAGvC,UAAK,CAACmF,SAAS,CAAC,CAAC;QAEjC,IAAAC,YAAA,GAUIlD,KAAA,CAAKD,KAAK,EATb0B,MAAK,GAAAyB,YAAA,CAALzB,KAAK,EACLxC,MAAM,GAAAiE,YAAA,CAANjE,MAAM,EACNwB,cAAa,GAAAyC,YAAA,CAAbzC,aAAa,EACboB,uBAAsB,GAAAqB,YAAA,CAAtBrB,sBAAsB,EAGtBsB,gCAAgC,GAAAD,YAAA,CAAhCC,gCAAgC,EAChCC,kBAAkB,GAAAF,YAAA,CAAlBE,kBAAkB,EAClBrE,SAAQ,GAAAmE,YAAA,CAARnE,QAAQ;QAGT,IAAAsE,YAAA,GAGIrD,KAAA,CAAKD,KAAK,EAFb6B,eAAc,GAAAyB,YAAA,CAAdzB,cAAc,EACdK,UAAS,GAAAoB,YAAA,CAATpB,SAAS;QAGV,6BAAA;QACA,IAAIL,eAAc,EAAE;YACnB,IAAI,CAAC5B,KAAA,CAAKvB,2BAA2B,CAACmD,eAAc,CAAC,EAAE;gBACtDA,eAAc,GAAGY,SAAS;YAC3B;QACD;QAEA,qEAAA;QACA,iFAAA;QACA,0FAAA;QACA,IAAIf,MAAK,EAAE;aACVlD,4MAAAA,AAAkB,EAACkD,MAAK,CAAC;QAC1B;QAEA,wBAAA;QACAQ,UAAS,wMAAGvD,wBAAAA,AAAqB,EAACuD,UAAS,EAAElD,SAAQ,CAAC;QAEtD,IAAMuE,WAAW,gMAAGhE,mBAAAA,AAAgB,EAACmC,MAAK,EAAE1C,SAAQ,CAAC;QAErDiB,KAAA,CAAKuD,WAAW,GAAG1E,yMAAAA,AAA0B,EAACmB,KAAA,CAAKD,KAAK,CAAC;QAEzD,IAAMyD,kBAAkB,gMAAGrE,wBAAAA,AAAqB,EAAC;YAChDsC,KAAK,EAALA,MAAK;YACL6B,WAAW,EAAXA,WAAW;YACX1B,cAAc,EAAdA,eAAc;YACd6B,QAAQ,EAAE,CAAC5B,uBAAsB;YACjCI,SAAS,EAAEA,UAAS,2MAAIrD,eAAAA,AAAY,EAACG,SAAQ,CAAC;YAC9CsD,aAAa,EAAE,SAAAA,cAAA;gBAAA,OAAMrC,KAAA,CAAKsC,wBAAwB,CAAC;oBAAEL,SAAS,EAATA;gBAAU,CAAC,CAAC;YAAA;YACjElD,QAAQ,EAARA;QACD,CAAC,CAAC;QAEFiB,KAAA,CAAKa,KAAK,GAAG;YACZ,mEAAA;YACAd,KAAK,EAAEC,KAAA,CAAKD,KAAK;YAEjB,wBAAA;YACAO,OAAO,EAAEkD,kBAAkB;YAE3B,mEAAA;YACA,qEAAA;YACA,qEAAA;YACA,iEAAA;YACA,oCAAA;YACA,2DAAA;YACAvB,SAAS,EAATA,UAAS;YAET,iEAAA;YACA,mDAAA;YACA,wEAAA;YACA,2DAAA;YACA,gEAAA;YACA,uEAAA;YACA,6CAAA;YACA,mEAAA;YACA,2CAAA;YACA,qEAAA;YACAlB,WAAW,+LAAEtB,wBAAAA,AAAqB,EAAC;gBAClCgC,KAAK,EAALA,MAAK;gBACL6B,WAAW,EAAXA,WAAW;gBACX1B,cAAc,EAAdA,eAAc;gBACdnB,aAAa,EAAbA,cAAa;gBACbS,iBAAiB,EAAEiC,gCAAgC,IAAIC,kBAAkB,KAAK,UAAU;gBACxFrE,QAAQ,EAARA;YACD,CAAC,CAAC;YAEF,2CAAA;YACA,kDAAA;YACA,4DAAA;YACA,+CAAA;YACA,iDAAA;YACA,0CAAA;YACA,+DAAA;YACA0C,KAAK,EAALA;QACD,CAAC;QAAA,OAAAzB,KAAA;IACF;IAAC0D,SAAA,CAAA7D,iBAAA,EAAAC,oBAAA;IAAA,OAAA6D,YAAA,CAAA9D,iBAAA,EAAA;QAAA;YAAA+D,GAAA,EAAA;YAAAnC,KAAA,EAED,SAAAoC,kBAAA,EAAoB;gBACnB,IAAQC,eAAe,GAAK,IAAI,CAAC/D,KAAK,CAA9B+D,eAAe;gBACvB,IAAMlC,cAAc,GAAK,IAAI,CAAC7B,KAAK,CAA7B6B,cAAc;gBACpB,IAAiBmC,eAAe,GAAK,IAAI,CAAClD,KAAK,CAAvCP,OAAO;gBACf,IAAIwD,eAAe,EAAE;oBACpB,IAAIlC,cAAc,EAAE;wBACnB,IAAI,CAAC,IAAI,CAACnD,2BAA2B,CAACmD,cAAc,CAAC,EAAE;4BACtDA,cAAc,GAAGY,SAAS;wBAC3B;oBACD;oBACA,IAAIuB,eAAe,KAAKnC,cAAc,EAAE;wBACvCkC,eAAe,CAACC,eAAe,CAAC;oBACjC;gBACD;YACD;QAAC;QAAA;YAAAH,GAAA,EAAA;YAAAnC,KAAA,EAED,SAAAuC,mBAAmBC,SAAS,EAAEC,SAAS,EAAE;gBACxC,IAAQJ,eAAe,GAAK,IAAI,CAAC/D,KAAK,CAA9B+D,eAAe;gBACvB,IAAQxD,OAAO,GAAK,IAAI,CAACO,KAAK,CAAtBP,OAAO;gBACf,4DAAA;gBACA,IAAIwD,eAAe,IAAIxD,OAAO,KAAK4D,SAAS,CAAC5D,OAAO,EAAE;oBACrDwD,eAAe,CAACxD,OAAO,CAAC;gBACzB;YACD;QAAC;QAAA;YAAAsD,GAAA,EAAA;YAAAnC,KAAA,EAQD,SAAArC,wBAAA+E,IAAA,EAAuC;gBAAA,IAAblC,SAAS,GAAAkC,IAAA,CAATlC,SAAS;gBAClC,IAAAmC,YAAA,GAQI,IAAI,CAACrE,KAAK,EAPbU,aAAa,GAAA2D,YAAA,CAAb3D,aAAa,EACbsB,0BAA0B,GAAAqC,YAAA,CAA1BrC,0BAA0B,EAC1BsC,mBAAmB,GAAAD,YAAA,CAAnBC,mBAAmB,EACnBxC,sBAAsB,GAAAuC,YAAA,CAAtBvC,sBAAsB,EACtB5C,MAAM,GAAAmF,YAAA,CAANnF,MAAM,EACNqF,OAAO,GAAAF,YAAA,CAAPE,OAAO,EACPvF,QAAQ,GAAAqF,YAAA,CAARrF,QAAQ;gBAGT,OAAO,IAAI,CAACwF,2BAA2B;6FAAC,YAAM;wBAC7C,4MAAO/F,qBAAAA,AAAkB,+LACxBY,0BAAAA,AAAuB,EAAC;4BACvB6C,SAAS,EAAEA,SAAS,2MAAIrD,eAAAA,AAAY,EAACG,QAAQ,CAAC;4BAC9CyF,YAAY,EAAEvF,MAAM;4BACpB4C,sBAAsB,EAAGpB,aAAa,IAAIsB,0BAA0B,KAAK,KAAK,GAAI,KAAK,GAAGF,sBAAsB;4BAChH4C,qBAAqB,EAAEH;wBAExB,CAAC,CAAC,EACF3F,kOAAAA,AAA0B,EAAC0F,mBAAmB,EAAEtF,QAAQ,CACzD,CAAC;oBACF,CAAC;4FAAE;oBACFkD,SAAS;oBACToC,mBAAmB;oBACnBxC,sBAAsB;oBACtB5C,MAAM;oBACNF,QAAQ;iBACR,CAAC;YACH;QAAC;QAAA;YAAA6E,GAAA,EAAA;YAAAnC,KAAA,EAED,SAAA8C,4BAA4BG,SAAS,EAAEC,YAAY,EAAE;gBACpD,IACC,CAAC,IAAI,CAACC,oCAAoC,IAC1C,CAACC,cAAc,CAACF,YAAY,EAAE,IAAI,CAACC,oCAAoC,CAAC,EACvE;oBACD,IAAI,CAACE,wBAAwB,GAAGJ,SAAS,CAAC,CAAC;oBAC3C,IAAI,CAACE,oCAAoC,GAAGD,YAAY;gBACzD;gBACA,OAAO,IAAI,CAACG,wBAAwB;YACrC;QAAC;QAAA;YAAAlB,GAAA,EAAA;YAAAnC,KAAA,EAED,SAAAa,yBAAAyC,KAAA,EAAwC;gBAAA,IAAb9C,SAAS,GAAA8C,KAAA,CAAT9C,SAAS;gBACnC,IAAM+C,cAAc,GAAG,IAAI,CAAC5F,uBAAuB,CAAC;oBAAE6C,SAAS,EAATA;gBAAU,CAAC,CAAC;gBAClE,OAAO+C,cAAc,CAAC,CAAC,CAAC,CAACvD,KAAK;YAC/B;QAAC;QAAA;YAAAmC,GAAA,EAAA;YAAAnC,KAAA,EA2MD,SAAAwD,OAAA,EAAS;gBACR,IAAAC,YAAA,GA+CI,IAAI,CAACnF,KAAK,EA7CboF,IAAI,GAAAD,YAAA,CAAJC,IAAI,EACJC,QAAQ,GAAAF,YAAA,CAARE,QAAQ,EACRC,QAAQ,GAAAH,YAAA,CAARG,QAAQ,EACRC,YAAY,GAAAJ,YAAA,CAAZI,YAAY,EACZC,KAAK,GAAAL,YAAA,CAALK,KAAK,EACLC,SAAS,GAAAN,YAAA,CAATM,SAAS,EAGTnF,QAAQ,GAAA6E,YAAA,CAAR7E,QAAQ,EACRoF,cAAc,GAAAP,YAAA,CAAdO,cAAc,EACdC,gBAAgB,GAAAR,YAAA,CAAhBQ,gBAAgB,EAChBC,UAAU,GAAAT,YAAA,CAAVS,UAAU,EAGcC,sBAAsB,GAAAV,YAAA,CAA9CW,sBAAsB,EACtB7C,kBAAkB,GAAAkC,YAAA,CAAlBlC,kBAAkB,EAGE8C,kBAAkB,GAAAZ,YAAA,CAAtCa,kBAAkB,EAClBC,uBAAuB,GAAAd,YAAA,CAAvBc,uBAAuB,EAGvBpE,cAAc,GAAAsD,YAAA,CAAdtD,cAAc,EACHqE,iBAAiB,GAAAf,YAAA,CAA5BjD,SAAS,EACToC,mBAAmB,GAAAa,YAAA,CAAnBb,mBAAmB,EACnBpF,MAAM,GAAAiG,YAAA,CAANjG,MAAM,EACNiH,KAAK,GAAAhB,YAAA,CAALgB,KAAK,EACLC,aAAa,GAAAjB,YAAA,CAAbiB,aAAa,EACbC,OAAO,GAAAlB,YAAA,CAAPkB,OAAO,EACPvE,sBAAsB,GAAAqD,YAAA,CAAtBrD,sBAAsB,EACtBwE,iBAAiB,GAAAnB,YAAA,CAAjBmB,iBAAiB,EAGjBlD,gCAAgC,GAAA+B,YAAA,CAAhC/B,gCAAgC,EAChCC,kBAAkB,GAAA8B,YAAA,CAAlB9B,kBAAkB,EAClBU,eAAe,GAAAoB,YAAA,CAAfpB,eAAe,EACfhC,cAAc,GAAAoD,YAAA,CAAdpD,cAAc,EACdC,0BAA0B,GAAAmD,YAAA,CAA1BnD,0BAA0B,EAC1BpB,4BAA4B,GAAAuE,YAAA,CAA5BvE,4BAA4B,EAC5B2F,KAAK,GAAApB,YAAA,CAALoB,KAAK,EACLvH,QAAQ,GAAAmG,YAAA,CAARnG,QAAQ,EACR0B,aAAa,GAAAyE,YAAA,CAAbzE,aAAa,EACb6D,OAAO,GAAAY,YAAA,CAAPZ,OAAO,EAEJiC,IAAI,GAAAC,wBAAA,CAAAtB,YAAA,EAAAuB,SAAA;gBAGR,IAAAC,YAAA,GAKI,IAAI,CAAC7F,KAAK,EAJbP,OAAO,GAAAoG,YAAA,CAAPpG,OAAO,EACP2B,SAAS,GAAAyE,YAAA,CAATzE,SAAS,EACTlB,WAAW,GAAA2F,YAAA,CAAX3F,WAAW,EACX2B,SAAS,GAAAgE,YAAA,CAAThE,SAAS;gBAGV,IAAMiE,cAAc,GAAGhB,UAAU,0KAAG1H,UAAU,0KAAGC,UAAU;gBAE3D,IAAM0I,oBAAoB,GAAG,IAAI,CAACxH,uBAAuB,CAAC;oBAAE6C,SAAS,EAATA;gBAAU,CAAC,CAAC;gBAExE,OAAA,WAAA,8HACCnE,UAAA,CAAA+I,aAAA,CAACf,kBAAkB,EAAAgB,QAAA,CAAA;oBAClBvB,KAAK,EAAEA,KAAM;oBACbC,SAAS,EAAExH,8IAAAA,AAAU,EAACwH,SAAS,EAAE,YAAY,EAAE;wBAC9C,mBAAmB,EAAE9C,SAAS;wBAC9B,sBAAsB,EAAE0C,QAAQ;wBAChC,sBAAsB,EAAEC;oBACzB,CAAC;gBAAE,GACCW,uBAAuB,GAAA,WAAA,8HAG3BlI,UAAA,CAAA+I,aAAA,CAACjB,sBAAsB,EAAAkB,QAAA,CAAA;oBACtB3B,IAAI,EAAEA,IAAI,GAAA,GAAA4B,MAAA,CAAM5B,IAAI,EAAA,aAAY3C,SAAU;oBAC1C,cAAYvD,MAAM,CAACqB,OAAAA;gBAAQ,GACvB0C,kBAAkB,EAAA;oBACtBvB,KAAK,EAAEnB,OAAQ;oBACf0G,OAAO,EAAEJ,oBAAqB;oBAC9BlG,QAAQ,EAAE,IAAI,CAACoD,eAAgB;oBAC/BjB,OAAO,EAAE,IAAI,CAACoE,cAAe;oBAC7BnE,MAAM,EAAE,IAAI,CAACoE,aAAc;oBAC3B9B,QAAQ,EAAEA,QAAQ,IAAKpC,kBAAkB,IAAIA,kBAAkB,CAACoC,QAAU;oBAC1EC,QAAQ,EAAEA,QAAQ,IAAKrC,kBAAkB,IAAIA,kBAAkB,CAACqC,QAAU;oBAC1E8B,aAAa,EAAE,IAAI,CAAC5D,WAAAA;gBAAY,EAAC,CAAC,EAAA,WAAA,8HAGnCzF,UAAA,CAAA+I,aAAA,CAACF,cAAc,EAAAG,QAAA,CAAA;oBACdM,GAAG,EAAE,IAAI,CAACC,WAAY;oBACtBC,IAAI,EAAC,KAAK;oBACVhC,YAAY,EAAEA;gBAAa,GACvBI,gBAAgB,EAChBa,IAAI,EAAA;oBACRgB,WAAW,EAAE9G,aAAa,KAAK,IAAI,GAAG,eAAe,GAAIA,aAAa,KAAK,KAAK,GAAG,UAAU,GAAG,2BAA6B;oBAC7HA,aAAa,EAAEA,aAAa,GAAG,IAAI,GAAG+B,SAAU;oBAChDgF,sBAAsB,EAAE/G,aAAa,GAAG,IAAI,GAAG+B,SAAU;oBACzD2C,IAAI,EAAEA,IAAK;oBACXpG,QAAQ,EAAEA,QAAS;oBACnBuB,OAAO,EAAEA,OAAQ;oBACjBmB,KAAK,EAAEV,WAAW,IAAI,EAAG;oBACzBL,QAAQ,EAAE,IAAI,CAACA,QAAS;oBACxBmC,OAAO,EAAE,IAAI,CAACA,OAAQ;oBACtBC,MAAM,EAAE,IAAI,CAACA,MAAO;oBACpBsC,QAAQ,EAAEA,QAAS;oBACnBC,QAAQ,EAAEA,QAAS;oBACnBI,cAAc,EAAEA,cAAe;oBAC/BD,SAAS,sIAAExH,UAAAA,AAAU,EACpB,iBAAiB,EACjB0H,gBAAgB,IAAIA,gBAAgB,CAACF,SAAS,EAC9Ce,IAAI,CAACf,SACN;gBAAE,EAAC,CACe,CAAC;YAEvB;QAAC;KAAA,EAAA;QAAA;YAAA5B,GAAA,EAAA;YAAAnC,KAAA,EA7HD,qDAAA;YACA,6DAAA;YACA,+EAAA;YACA,oEAAA;YACA,sDAAA;YACA,SAAAgG,yBAAgC1H,KAAK,EAAEc,KAAK,EAAE;gBAC7C,OAAA6G,aAAA,CAAA;oBACC,yCAAA;oBACA3H,KAAK,EAALA;gBAAK,IACFH,oOAAAA,AAA+C,EAACG,KAAK,EAAEc,KAAK,CAACd,KAAK,EAAEc,KAAK,CAAC;YAE/E;QAAC;KAAA;AAAA,4HAxX8B/C,WAAK,CAAC6J,aAAa,GA6enD,6DAAA;AACA,IAAMC,gBAAgB,GAAA,WAAA,8HAAG9J,UAAK,CAAC+J,UAAU,CAAC,SAAC9H,KAAK,EAAEqH,GAAG;IAAA,OAAA,WAAA,GACpDtJ,qIAAA,CAAA+I,aAAA,CAAChH,iBAAiB,EAAAiH,QAAA,CAAA,CAAA,GAAKgB,gBAAgB,CAAC/H,KAAK,CAAC,EAAA;QAAEM,QAAQ,EAAE+G;IAAI,EAAC,CAAC;AAAA,CAChE,CAAC;AAEFQ,gBAAgB,CAACG,SAAS,GAAG;IAC5B;;;;;;;;GAQD,GACCtG,KAAK,qIAAE1D,UAAS,CAACiK,MAAM;IAEvB;;;;;;GAMD,GACCtH,QAAQ,qIAAE3C,UAAS,CAACkK,IAAI,CAACC,UAAU;IAEnC;;;GAGD,GACCrF,OAAO,qIAAE9E,UAAS,CAACkK,IAAI;IAEvB;;;GAGD,GACCnF,MAAM,qIAAE/E,UAAS,CAACkK,IAAI;IAEtB;;;GAGD,GACC7C,QAAQ,qIAAErH,UAAS,CAACoK,IAAI;IAExB;;;GAGD,GACC9C,QAAQ,qIAAEtH,UAAS,CAACoK,IAAI;IAExB;;;;;;;;;;;;;;;;;;;GAmBD,GACC,0BAAA;IACA7C,YAAY,qIAAEvH,UAAS,CAACiK,MAAM;IAE9B;;;;;;;;;;;;;;;;;;GAkBD,GACC,8BAAA;IACA5E,kBAAkB,EAAErF,6IAAS,CAACqK,KAAK,CAAC;QAAC,UAAU;KAAC,CAAC;IAEjD,uDAAA;IACA,+CAAA;IACAjF,gCAAgC,qIAAEpF,UAAS,CAACoK,IAAI;IAEhD;;;;;GAKD,GACC,oDAAA;IACAvG,cAAc,qIAAE7D,UAAS,CAACiK,MAAM;IAEhC;;;;;;GAMD,GACC/F,SAAS,qIAAElE,UAAS,CAACsK,OAAO,oIAACtK,UAAS,CAACiK,MAAM,CAAC;IAE9C;;;;;;;;;GASD,GACC/I,MAAM,wKAAEC,SAAc;IAEtB;;;;;;;;;;;;GAYD,GACCoF,OAAO,qIAAEvG,UAAS,CAACuK,SAAS,CAAC;2IAC5BvK,UAAS,CAACiK,MAAM;2IAChBjK,UAAS,CAACsK,OAAO,CAACtK,6IAAS,CAACiK,MAAM,CAAC;KACnC,CAAC;IAEF;;;;;;;;GAQD,GAEC;;;;;;;;;;;;GAYD,GACC5B,OAAO,qIAAErI,UAAS,CAACiK,MAAM;IAEzB;;;;;;;;;;;;;;;;;;;;;GAqBD,GACC9B,KAAK,qIAAEnI,UAAS,CAACwK,QAAQ,CAACxK,6IAAS,CAACyK,WAAW,CAAC;IAEhD;;;;;;;;;GASD,GACCrC,aAAa,qIAAEpI,UAAS,CAACyK,WAAW;IAEpC;;GAED,GACC3G,sBAAsB,EAAE9D,6IAAS,CAACoK,IAAI;IAEtC;;;;;;;GAOD,GACC9B,iBAAiB,qIAAEtI,UAAS,CAACyK,WAAW;IAExC;;;;;;;;;;;GAWD,GACCnE,mBAAmB,oIAAEtG,WAAS,CAACsK,OAAO,oIAACtK,UAAS,CAACiK,MAAM,CAAC;IAExD;;GAED,GACCzC,KAAK,oIAAExH,WAAS,CAAC0K,MAAM;IAEvB;;GAED,GACCjD,SAAS,qIAAEzH,UAAS,CAACiK,MAAM;IAE3B;;;;;;;;;;;;;;;;GAgBD,GACCnC,sBAAsB,qIAAE9H,UAAS,CAACyK,WAAW;IAE7C;;;;GAID,GACCxF,kBAAkB,qIAAEjF,UAAS,CAAC0K,MAAM;IAEpC;;;;;;;;;;;;GAYD,GACChD,cAAc,qIAAE1H,UAAS,CAACyK,WAAW;IAErC;;GAED,GACC9C,gBAAgB,qIAAE3H,UAAS,CAAC0K,MAAM;IAElC;;;;;;;GAOD,GACC1C,kBAAkB,qIAAEhI,UAAS,CAACyK,WAAW;IAEzC;;GAED,GACCxC,uBAAuB,EAAEjI,6IAAS,CAAC0K,MAAM;IAEzC;;;;;;;;;;GAUD,GACC,wBAAA;IACA9C,UAAU,qIAAE5H,UAAS,CAACoK,IAAI;IAE1B;;;;;GAKD,GACC1H,aAAa,qIAAE1C,UAAS,CAACoK,IAAI;IAE7B;;;GAGD,GACCrG,cAAc,qIAAE/D,UAAS,CAACoK,IAAI;IAE9B;;;;GAID,GACCpG,0BAA0B,qIAAEhE,UAAS,CAACoK,IAAI;IAE1C;;;;;GAKD,GACCpJ,QAAQ,wKAAEC,WAAgB;IAE1B;;;GAGD,GACC,iEAAA;IACA,8EAAA;IACA,+CAAA;IACA,yEAAA;IACA,yEAAA;IACA,6DAAA;IACA,iEAAA;IACA,4DAAA;IACA8E,eAAe,qIAAE/F,UAAS,CAACkK,IAAI;IAE/B;;;;;;;;GAQD,GACCtH,4BAA4B,qIAAE5C,UAAS,CAACoK,IAAAA;AACzC,CAAC;AAED,IAAMO,YAAY,GAAG;IACpB;;GAED,GACCpD,YAAY,EAAE,KAAK;IAEnB;;GAED,GACCO,sBAAsB,4KAAEzH,wBAAa;IAErC;;GAED,GACC+H,aAAa,mKAAE9H,UAAI;IAEnB;;GAED,GACC,oDAAA;IACA+H,OAAO,EAAE,sEAAsE;IAE/E;;GAED,GACCC,iBAAiB,gLAAE/H,UAAiB;IAEpC;;GAED,GACCmH,cAAc,EAAE,OAAO;IAEvB;;GAED,GACCM,kBAAkB,EAAE,KAAK;IAEzB;;;;;;;;;;;GAWD,GACC,wEAAA;IACAO,KAAK,qIAAEvI,UAAS,CAAC4K,GAAG;IAEpB;;GAED,GAEC;;GAED,GACChD,UAAU,EAAE,IAAI;IAEhB;;;GAGD,GACC9D,sBAAsB,EAAE,IAAI;IAE5B;;;;GAID,GACCE,0BAA0B,EAAE,IAAI;IAEhC;;;;;;;;GAQD,GACCpB,4BAA4B,EAAE;AAC/B,CAAC;AAED,SAASmH,gBAAgBA,CAAC/H,KAAK,EAAE;IAChCA,KAAK,GAAA2H,aAAA,CAAA,CAAA,GAAQ3H,KAAK,CAAE;IAEpB,IAAK,IAAM6D,GAAG,IAAI8E,YAAY,CAAE;QAC/B,IAAI3I,KAAK,CAAC6D,GAAG,CAAC,KAAKpB,SAAS,EAAE;YAC7BzC,KAAK,CAAC6D,GAAG,CAAC,GAAG8E,YAAY,CAAC9E,GAAG,CAAC;QAC/B;IACD;IAEA,OAAO7D,KAAK;AACb;uCAEe6H,gBAAgB;AAE/B,SAAS/C,cAAcA,CAAC+D,CAAC,EAAEC,CAAC,EAAE;IAC7B,IAAID,CAAC,CAACE,MAAM,KAAKD,CAAC,CAACC,MAAM,EAAE;QAC1B,OAAO,KAAK;IACb;IACA,IAAIC,CAAC,GAAG,CAAC;IACT,MAAOA,CAAC,GAAGH,CAAC,CAACE,MAAM,CAAE;QACpB,IAAIF,CAAC,CAACG,CAAC,CAAC,KAAKF,CAAC,CAACE,CAAC,CAAC,EAAE;YAClB,OAAO,KAAK;QACb;QACAA,CAAC,EAAE;IACJ;IACA,OAAO,IAAI;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3502, "column": 0}, "map": {"version": 3, "file": "PhoneInputWithCountryDefault.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/source/PhoneInputWithCountryDefault.js"], "sourcesContent": ["import React from 'react'\r\nimport PropTypes from 'prop-types'\r\n\r\nimport defaultLabels from '../locale/en.json.js'\r\n\r\nimport {\r\n\tmetadata as metadataPropType,\r\n\tlabels as labelsPropType\r\n} from './PropTypes.js'\r\n\r\nimport PhoneInput from './PhoneInputWithCountry.js'\r\n\r\nexport function createPhoneInput(defaultMetadata) {\r\n\tconst PhoneInputDefault = React.forwardRef(({\r\n\t\tmetadata = defaultMetadata,\r\n\t\tlabels = defaultLabels,\r\n\t\t...rest\r\n\t}, ref) => (\r\n\t\t<PhoneInput\r\n\t\t\t{...rest}\r\n\t\t\tref={ref}\r\n\t\t\tmetadata={metadata}\r\n\t\t\tlabels={labels}\r\n\t\t/>\r\n\t))\r\n\r\n\tPhoneInputDefault.propTypes = {\r\n\t\tmetadata: metadataPropType,\r\n\t\tlabels: labelsPropType\r\n\t}\r\n\r\n\treturn PhoneInputDefault\r\n}\r\n\r\nexport default createPhoneInput()"], "names": ["React", "PropTypes", "defaultLabels", "metadata", "metadataPropType", "labels", "labelsPropType", "PhoneInput", "createPhoneInput", "defaultMetadata", "PhoneInputDefault", "forwardRef", "_ref", "ref", "_ref$metadata", "_ref$labels", "rest", "_objectWithoutProperties", "_excluded", "createElement", "_extends", "propTypes"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAGzB,OAAOE,aAAa,MAAM,sBAAsB;AAEhD,SACCC,QAAQ,IAAIC,gBAAgB,EAC5BC,MAAM,IAAIC,cAAc,QAClB,gBAAgB;AAEvB,OAAOC,UAAU,MAAM,4BAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5C,SAASC,gBAAgBA,CAACC,eAAe,EAAE;IACjD,IAAMC,iBAAiB,GAAA,WAAA,6HAAGV,WAAK,CAACW,UAAU,CAAC,SAAAC,IAAA,EAIxCC,GAAG;QAAA,IAAAC,aAAA,GAAAF,IAAA,CAHLT,QAAQ,EAARA,QAAQ,GAAAW,aAAA,KAAA,KAAA,IAAGL,eAAe,GAAAK,aAAA,EAAAC,WAAA,GAAAH,IAAA,CAC1BP,MAAM,EAANA,MAAM,GAAAU,WAAA,KAAA,KAAA,IAAGb,gLAAa,GAAAa,WAAA,EACnBC,IAAI,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;QAAA,OAAA,WAAA,8HAEPlB,UAAA,CAAAmB,aAAA,mLAACZ,UAAU,EAAAa,QAAA,CAAA,CAAA,GACNJ,IAAI,EAAA;YACRH,GAAG,EAAEA,GAAI;YACTV,QAAQ,EAAEA,QAAS;YACnBE,MAAM,EAAEA;QAAO,EACf,CAAC;IAAA,CACF,CAAC;IAEFK,iBAAiB,CAACW,SAAS,GAAG;QAC7BlB,QAAQ,wKAAEC,WAAgB;QAC1BC,MAAM,wKAAEC,SAAAA;IACT,CAAC;IAED,OAAOI,iBAAiB;AACzB;uCAEeF,gBAAgB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3573, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/node_modules/react-phone-number-input/min/index.js"], "sourcesContent": ["import metadata from 'libphonenumber-js/min/metadata'\r\n\r\nimport {\r\n\tparsePhoneNumber as _parsePhoneNumber,\r\n\tformatPhoneNumber as _formatPhoneNumber,\r\n\tformatPhoneNumberIntl as _formatPhoneNumberIntl,\r\n\tisValidPhoneNumber as _isValidPhoneNumber,\r\n\tisPossiblePhoneNumber as _isPossiblePhoneNumber,\r\n\tgetCountries as _getCountries,\r\n\tgetCountryCallingCode as _getCountryCallingCode,\r\n\tisSupportedCountry as _isSupportedCountry\r\n} from '../core/index.js'\r\n\r\nimport { createPhoneInput } from '../modules/PhoneInputWithCountryDefault.js'\r\n\r\nfunction call(func, _arguments) {\r\n\tvar args = Array.prototype.slice.call(_arguments)\r\n\targs.push(metadata)\r\n\treturn func.apply(this, args)\r\n}\r\n\r\nexport default createPhoneInput(metadata)\r\n\r\nexport function parsePhoneNumber() {\r\n\treturn call(_parsePhoneNumber, arguments)\r\n}\r\n\r\nexport function formatPhoneNumber() {\r\n\treturn call(_formatPhoneNumber, arguments)\r\n}\r\n\r\nexport function formatPhoneNumberIntl() {\r\n\treturn call(_formatPhoneNumberIntl, arguments)\r\n}\r\n\r\nexport function isValidPhoneNumber() {\r\n\treturn call(_isValidPhoneNumber, arguments)\r\n}\r\n\r\nexport function isPossiblePhoneNumber() {\r\n\treturn call(_isPossiblePhoneNumber, arguments)\r\n}\r\n\r\nexport function getCountries() {\r\n\treturn call(_getCountries, arguments)\r\n}\r\n\r\nexport function getCountryCallingCode() {\r\n\treturn call(_getCountryCallingCode, arguments)\r\n}\r\n\r\nexport function isSupportedCountry() {\r\n\treturn call(_isSupportedCountry, arguments)\r\n}"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;;AAEA,SAAS,KAAK,IAAI,EAAE,UAAU;IAC7B,IAAI,OAAO,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;IACtC,KAAK,IAAI,CAAC,2JAAA,CAAA,UAAQ;IAClB,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE;AACzB;uCAEe,CAAA,GAAA,wLAAA,CAAA,mBAAgB,AAAD,EAAE,2JAAA,CAAA,UAAQ;AAEjC,SAAS;IACf,OAAO,KAAK,0MAAA,CAAA,mBAAiB,EAAE;AAChC;AAEO,SAAS;IACf,OAAO,KAAK,+OAAA,CAAA,oBAAkB,EAAE;AACjC;AAEO,SAAS;IACf,OAAO,KAAK,+LAAA,CAAA,wBAAsB,EAAE;AACrC;AAEO,SAAS;IACf,OAAO,KAAK,8MAAA,CAAA,qBAAmB,EAAE;AAClC;AAEO,SAAS;IACf,OAAO,KAAK,oNAAA,CAAA,wBAAsB,EAAE;AACrC;AAEO,SAAS;IACf,OAAO,KAAK,kMAAA,CAAA,eAAa,EAAE;AAC5B;AAEO,SAAS;IACf,OAAO,KAAK,mJAAA,CAAA,wBAAsB,EAAE;AACrC;AAEO,SAAS;IACf,OAAO,KAAK,mJAAA,CAAA,qBAAmB,EAAE;AAClC", "ignoreList": [0], "debugId": null}}]}