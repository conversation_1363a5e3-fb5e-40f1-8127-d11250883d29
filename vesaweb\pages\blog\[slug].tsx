// pages/blog/[slug].tsx - Individual blog post page
import React from 'react'
import { GetStaticProps, GetStaticPaths } from 'next'
import Head from 'next/head'
import Header from '@/components/global/Header'
import Footer from '@/components/global/Footer'
import {
  BlogPostHero,
  BlogPostContent,
  BlogPostNavigation,
  BlogPostSocial,
  RelatedPosts
} from '@/components/blog/single'
import { getBlogPost, getBlogPostSlugs, getRelatedBlogPosts, urlForImage } from '@/lib/sanity-blog'
import { BlogPost, BlogListItem } from '@/types/blog'

interface BlogPostPageProps {
  post: BlogPost
  relatedPosts: BlogListItem[]
}

const BlogPostPage: React.FC<BlogPostPageProps> = ({ post, relatedPosts }) => {
  const seoTitle = post.seo?.metaTitle || `${post.title} | VESA Solutions Blog`
  const seoDescription = post.seo?.metaDescription || post.excerpt
  const seoImage = post.seo?.ogImage || post.featuredImage
  const canonicalUrl = `https://vesasolutions.com/blog/${post.slug.current}`

  return (
    <>
      <Head>
        <title>{seoTitle}</title>
        <meta name="description" content={seoDescription} />
        {post.seo?.keywords && (
          <meta name="keywords" content={post.seo.keywords.join(', ')} />
        )}
        
        {/* Open Graph */}
        <meta property="og:title" content={seoTitle} />
        <meta property="og:description" content={seoDescription} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={canonicalUrl} />
        <meta property="og:image" content={seoImage ? urlForImage(seoImage).width(1200).height(630).url() : ''} />
        
        {/* Article specific */}
        <meta property="article:published_time" content={post.publishedAt} />
        {post.categories.map((category) => (
          <meta key={category._id} property="article:section" content={category.title} />
        ))}
        {post.tags?.map((tag) => (
          <meta key={tag._id} property="article:tag" content={tag.title} />
        ))}
        
        {/* Twitter Card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={seoTitle} />
        <meta name="twitter:description" content={seoDescription} />
        <meta name="twitter:image" content={seoImage ? urlForImage(seoImage).width(1200).height(630).url() : ''} />
        
        {/* Canonical URL */}
        <link rel="canonical" href={canonicalUrl} />
        
        {/* JSON-LD Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "BlogPosting",
              "headline": post.title,
              "description": post.excerpt,
              ...(post.featuredImage && { "image": urlForImage(post.featuredImage).width(1200).height(630).url() }),
              "author": {
                "@type": "Organization",
                "name": "VESA Solutions"
              },
              "publisher": {
                "@type": "Organization",
                "name": "VESA Solutions",
                "logo": {
                  "@type": "ImageObject",
                  "url": "https://vesasolutions.com/VesaLogo.svg"
                }
              },
              "datePublished": post.publishedAt,
              "dateModified": post.publishedAt,
              "mainEntityOfPage": {
                "@type": "WebPage",
                "@id": canonicalUrl
              }
            })
          }}
        />
      </Head>

      <Header isVisible={true} />
      
      <main>
        {/* Hero Section */}
        <BlogPostHero post={post} />

        {/* Main Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-3">
              <BlogPostContent post={post} />

              {/* Social Sharing */}
              <BlogPostSocial post={post} />
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-24 space-y-8">
                {/* Table of Contents could go here */}
                
                {/* Related Posts Preview */}
                {relatedPosts.length > 0 && (
                  <div className="bg-gray-50 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Articles</h3>
                    <div className="space-y-4">
                      {relatedPosts.slice(0, 3).map((relatedPost) => (
                        <a
                          key={relatedPost._id}
                          href={`/blog/${relatedPost.slug.current}`}
                          className="block group"
                        >
                          <h4 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2">
                            {relatedPost.title}
                          </h4>
                          <p className="text-xs text-gray-500 mt-1">
                            {new Date(relatedPost.publishedAt).toLocaleDateString()}
                          </p>
                        </a>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <BlogPostNavigation currentSlug={post.slug.current} />

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <RelatedPosts posts={relatedPosts} />
        )}
      </main>

      <Footer />
    </>
  )
}

export const getStaticPaths: GetStaticPaths = async () => {
  try {
    const slugs = await getBlogPostSlugs()
    
    const paths = slugs.map((slug) => ({
      params: { slug }
    }))

    return {
      paths,
      fallback: 'blocking'
    }
  } catch (error) {
    console.error('Error fetching blog post slugs:', error)
    return {
      paths: [],
      fallback: 'blocking'
    }
  }
}

export const getStaticProps: GetStaticProps = async ({ params, preview = false }) => {
  const slug = params?.slug as string

  if (!slug) {
    return { notFound: true }
  }

  try {
    const post = await getBlogPost(slug)
    
    if (!post) {
      console.log(`❌ Blog post not found for slug: ${slug}`)
      return { notFound: true }
    }

    // Get related posts
    const categoryIds = post.categories.map(cat => cat.slug.current)
    const relatedPosts = await getRelatedBlogPosts(post._id, categoryIds, 6)

    console.log(`✅ Successfully loaded blog post: ${post.title}`)

    return {
      props: { 
        post,
        relatedPosts
      },
      // Only use revalidate in production to prevent ISR manifest HMR errors
      ...(process.env.NODE_ENV === 'production' && { 
        revalidate: preview ? 1 : 300 
      }),
    }
  } catch (error) {
    console.error(`❌ Error fetching blog post for slug ${slug}:`, error)
    return { notFound: true }
  }
}

export default BlogPostPage
