import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Header from '@/components/global/Header';
import Footer from '@/components/global/Footer';

const TermsOfServicePage: React.FC = () => {
  return (
    <>
      <Head>
        <title>Terms of Service | VESA Solutions</title>
        <meta name="description" content="Terms of Service for VESA Solutions digital marketing and web development services. Read our terms and conditions for using our website and services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="robots" content="index, follow" />
      </Head>
      
      <Header />
      
      {/* Breadcrumbs */}
      <div className="bg-gray-50 py-4">
        <div className="max-w-7xl mx-auto px-6">
          <nav className="text-sm" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              <li>
                <Link href="/" className="text-gray-500 hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-gray-900 font-medium" aria-current="page">
                Terms of Service
              </li>
            </ol>
          </nav>
        </div>
      </div>
      
      <main className="min-h-screen bg-white">
        {/* Header Section */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-800">
          <div className="max-w-4xl mx-auto px-6 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Terms of Service
            </h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Please read these terms and conditions carefully before using our services.
            </p>
            <p className="text-blue-200 mt-4">
              Last updated: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
            </p>
          </div>
        </section>

        {/* Terms Content */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-6">
            <div className="prose prose-lg max-w-none">
              
              <h2 className="text-2xl font-bold text-gray-900 mb-4">1. Acceptance of Terms</h2>
              <p className="text-gray-600 mb-6">
                By accessing and using the VESA Solutions website and services, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">2. Description of Service</h2>
              <p className="text-gray-600 mb-6">
                VESA Solutions provides digital marketing services including but not limited to SEO (Search Engine Optimization), web development, PPC advertising, social media marketing, content creation, and related digital marketing services. We reserve the right to modify, suspend, or discontinue any aspect of our services at any time.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">3. User Responsibilities</h2>
              <p className="text-gray-600 mb-4">You agree to:</p>
              <ul className="list-disc list-inside text-gray-600 mb-6 space-y-2">
                <li>Provide accurate and complete information when requested</li>
                <li>Maintain the confidentiality of your account credentials</li>
                <li>Use our services in compliance with all applicable laws and regulations</li>
                <li>Not engage in any activity that could harm our systems or other users</li>
                <li>Respect intellectual property rights</li>
              </ul>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">4. Payment Terms</h2>
              <p className="text-gray-600 mb-6">
                Payment terms will be specified in individual service agreements. Generally, payments are due according to the agreed schedule. Late payments may result in service suspension. All fees are non-refundable unless otherwise specified in writing.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">5. Intellectual Property</h2>
              <p className="text-gray-600 mb-6">
                All content, trademarks, and intellectual property on this website remain the property of VESA Solutions unless otherwise specified. Work products created for clients become the property of the client upon full payment, unless otherwise agreed in writing.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">6. Privacy and Data Protection</h2>
              <p className="text-gray-600 mb-6">
                Your privacy is important to us. Please review our Privacy Policy, which also governs your use of our services, to understand our practices regarding the collection and use of your information.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">7. Limitation of Liability</h2>
              <p className="text-gray-600 mb-6">
                VESA Solutions shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your use of our services.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">8. Service Availability</h2>
              <p className="text-gray-600 mb-6">
                While we strive to maintain continuous service availability, we do not guarantee uninterrupted access to our website or services. We may perform maintenance, updates, or experience technical difficulties that temporarily affect service availability.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">9. Termination</h2>
              <p className="text-gray-600 mb-6">
                Either party may terminate services with appropriate notice as specified in individual service agreements. Upon termination, your right to use our services ceases immediately, though certain provisions of these terms may survive termination.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">10. Changes to Terms</h2>
              <p className="text-gray-600 mb-6">
                We reserve the right to modify these terms at any time. Changes will be posted on this page with an updated revision date. Continued use of our services after changes constitutes acceptance of the new terms.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">11. Governing Law</h2>
              <p className="text-gray-600 mb-6">
                These terms shall be governed by and construed in accordance with applicable laws. Any disputes arising from these terms or our services shall be resolved through appropriate legal channels.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">12. Contact Information</h2>
              <p className="text-gray-600 mb-6">
                If you have any questions about these Terms of Service, please contact us:
              </p>
              <div className="bg-gray-50 p-6 rounded-lg">
                <p className="text-gray-700 mb-2"><strong>VESA Solutions</strong></p>
                <p className="text-gray-600 mb-2">Email: <EMAIL></p>
                <p className="text-gray-600 mb-2">Phone: +355 69 404 6408</p>
                <p className="text-gray-600">Website: www.vesasolutions.com</p>
              </div>

            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </>
  );
};

export default TermsOfServicePage;
