// Migration to update sub-service colors based on parent service
const { createClient } = require('@sanity/client')

const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

// Color schemes based on parent service
const COLOR_SCHEMES = {
  'seo': {
    heroGradient: 'from-green-600 to-green-800',
    lightBackground: 'from-green-50 to-green-100'
  },
  'web-development': {
    heroGradient: 'from-blue-600 to-blue-800',
    lightBackground: 'from-blue-50 to-blue-100'
  },
  'digital-marketing': {
    heroGradient: 'from-purple-600 to-purple-800',
    lightBackground: 'from-purple-50 to-purple-100'
  }
}

async function updateSubServiceColors() {
  try {
    console.log('🎨 Starting sub-service color update...')
    
    // Get all sub-services
    const subServices = await client.fetch('*[_type == "subService"]')
    
    console.log(`Found ${subServices.length} sub-services to update`)
    
    for (const subService of subServices) {
      const parentService = subService.parentService
      const colorScheme = COLOR_SCHEMES[parentService]
      
      if (!colorScheme) {
        console.log(`⚠️  No color scheme found for parent service: ${parentService} (${subService.title})`)
        continue
      }
      
      const updates = {}
      
      // Update hero background gradient if it's using default blue
      if (subService.hero && (!subService.hero.backgroundGradient || subService.hero.backgroundGradient.includes('blue'))) {
        updates['hero.backgroundGradient'] = colorScheme.heroGradient
        console.log(`🎨 Updating hero gradient for: ${subService.title} (${parentService})`)
      }
      
      // Update market intelligence background if it's using default blue
      if (subService.marketIntelligence && (!subService.marketIntelligence.backgroundGradient || subService.marketIntelligence.backgroundGradient.includes('blue'))) {
        updates['marketIntelligence.backgroundGradient'] = colorScheme.lightBackground
        console.log(`🎨 Updating market intelligence background for: ${subService.title} (${parentService})`)
      }
      
      // Apply updates if any
      if (Object.keys(updates).length > 0) {
        await client.patch(subService._id).set(updates).commit()
        console.log(`✅ Updated colors for: ${subService.title}`)
      } else {
        console.log(`⏭️  No color updates needed for: ${subService.title}`)
      }
    }
    
    console.log('✅ Sub-service color update completed successfully!')
    
  } catch (error) {
    console.error('❌ Error during color update:', error)
  }
}

// Run the update
updateSubServiceColors()
