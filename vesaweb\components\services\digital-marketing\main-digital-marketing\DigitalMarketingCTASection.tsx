import React from 'react';
import { Phone, Mail } from 'lucide-react';

const DigitalMarketingCTASection: React.FC = () => {
  return (
    <section className="py-24 bg-gradient-to-r from-purple-700 to-purple-900">
      <div className="max-w-4xl mx-auto px-6 text-center">
        <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
          Ready to Accelerate Your Digital Growth?
        </h2>
        <p className="text-xl text-purple-100 mb-12 leading-relaxed">
          Let&apos;s discuss your business goals and create a digital marketing strategy that drives real results. Our team is ready to help you dominate your market and achieve sustainable growth.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
          <a
            href="tel:+***********"
            className="group bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-bold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-xl shadow-purple-500/25 inline-flex items-center justify-center text-lg"
          >
            <Phone className="mr-3" size={24} />
            Start Growing Today
          </a>

          <a
            href="mailto:<EMAIL>"
            className="group bg-white hover:bg-gray-50 text-purple-600 font-bold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-xl border-2 border-purple-500 hover:border-purple-600 inline-flex items-center justify-center text-lg"
          >
            <Mail className="mr-3" size={24} />
            Get Your Strategy
          </a>
        </div>
        
        <p className="text-purple-200 mt-8 text-sm">
          Free consultation • Custom strategy • Proven results guaranteed
        </p>
      </div>
    </section>
  );
};

export default DigitalMarketingCTASection;
