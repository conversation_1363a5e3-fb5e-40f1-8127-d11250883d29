// ServiceProcess.tsx - Fixed with IconRenderer and CaseStudy integration
import React from 'react'
import { Icon<PERSON><PERSON><PERSON> } from '@/components/global/IconRender'
import { CaseStudy } from './CaseStudy'
import { ServiceProcess as ServiceProcessType, CaseStudy as CaseStudyType } from '@/types/subService'

interface ServiceProcessProps {
  data?: ServiceProcessType
  caseStudy?: CaseStudyType
}

export const ServiceProcess: React.FC<ServiceProcessProps> = ({ data, caseStudy }) => {
  return (
    <>
      {/* Process Section */}
      {data && data.steps && data.steps.length > 0 && (
        <section className="py-24 bg-white">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
                {data.title || 'Our Proven Process'}
              </h2>
              {data.description && (
                <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                  {data.description}
                </p>
              )}
            </div>

            <div className="space-y-16">
              {data.steps.map((step, index) => (
                <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${index % 2 === 0 ? '' : 'lg:grid-cols-2'}`}>
                  <div className={`${index % 2 === 0 ? 'lg:order-1' : 'lg:order-2'}`}>
                    <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mb-6">
                      {step.step}
                    </div>
                    <h3 className="text-3xl font-bold text-gray-800 mb-4">
                      {step.title}
                    </h3>
                    <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                      {step.description}
                    </p>
                    {step.details && step.details.length > 0 && (
                      <ul className="space-y-3">
                        {step.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-center text-gray-700">
                            <IconRenderer 
                              iconName={step.icon} 
                              size={20} 
                              className="text-blue-500 mr-3 flex-shrink-0" 
                            />
                            {detail}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                  <div className={`${index % 2 === 0 ? 'lg:order-2' : 'lg:order-1'}`}>
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-3xl">
                      <div className="flex justify-center mb-4">
                        <IconRenderer 
                          iconName={step.icon} 
                          size={80} 
                          className="text-blue-500" 
                        />
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-gray-800 mb-2">
                          Step {step.step}
                        </div>
                        <div className="text-blue-600 font-semibold">
                          {step.title}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Case Study Section */}
      <CaseStudy data={caseStudy} />
    </>
  )
}