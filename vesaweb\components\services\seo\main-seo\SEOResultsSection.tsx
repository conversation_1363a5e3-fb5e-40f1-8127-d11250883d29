import React from 'react';
import Image from 'next/image';

const SEOResultsSection: React.FC = () => {
  return (
    <>
      {/* Case Study Section */}
      <section className="py-24 bg-green-600">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-8">
                Real Results: E-commerce Client Achieves 340% Traffic Growth
              </h2>
              <p className="text-xl text-green-100 mb-8 leading-relaxed">
                See how our comprehensive SEO strategy helped a fashion e-commerce store dominate competitive keywords and achieve remarkable growth in organic traffic and revenue.
              </p>
              <div className="grid grid-cols-2 gap-8 mb-8">
                <div className="text-center">
                  <div className="text-4xl font-bold text-green-300 mb-2">340%</div>
                  <div className="text-green-100">Organic Traffic Increase</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-green-300 mb-2">280%</div>
                  <div className="text-green-100">Keyword Rankings Improvement</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-green-300 mb-2">190%</div>
                  <div className="text-green-100">Revenue Growth</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-green-300 mb-2">6</div>
                  <div className="text-green-100">Month Timeline</div>
                </div>
              </div>
              <button className="bg-white text-green-600 font-semibold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300">
                View Full Case Study
              </button>
            </div>
            <div>
              <Image
                src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop"
                alt="E-commerce SEO Success Story"
                width={600}
                height={400}
                className="w-full rounded-3xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
              What Our Clients Say About Vesa Solutions
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              Don&apos;t just take our word for it. Here&apos;s what business owners say about working with our SEO experts.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="bg-gray-50 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="text-6xl text-blue-500 mb-4">&quot;</div>
              <p className="text-gray-700 mb-6 leading-relaxed">
                Vesa Solutions completely transformed our online presence. Their SEO strategy increased our organic traffic by 340% and our lead quality improved dramatically. Best investment we&apos;ve made in digital marketing.
              </p>
              <div className="flex items-center">
                <Image
                  src="https://images.unsplash.com/photo-1494790108755-2616b25c1b0c?w=60&h=60&fit=crop&crop=face"
                  alt="Sarah Johnson"
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full mr-4 object-cover"
                />
                <div>
                  <div className="font-bold text-gray-800">Sarah Johnson</div>
                  <div className="text-gray-600">CEO, TechFlow Solutions</div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="text-6xl text-blue-500 mb-4">&quot;</div>
              <p className="text-gray-700 mb-6 leading-relaxed">
                The ROI from our SEO investment with Vesa has been incredible. We&apos;re now ranking #1 for our most important keywords and generating 3x more qualified leads than before.
              </p>
              <div className="flex items-center">
                <Image
                  src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face"
                  alt="Michael Chen"
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full mr-4 object-cover"
                />
                <div>
                  <div className="font-bold text-gray-800">Michael Chen</div>
                  <div className="text-gray-600">Founder, GreenLeaf Consulting</div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="text-6xl text-blue-500 mb-4">&quot;</div>
              <p className="text-gray-700 mb-6 leading-relaxed">
                Their local SEO strategy helped us become the go-to service provider in our area. Our phone calls and walk-ins have both doubled since working with Vesa Solutions.
              </p>
              <div className="flex items-center">
                <Image
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face"
                  alt="David Martinez"
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full mr-4 object-cover"
                />
                <div>
                  <div className="font-bold text-gray-800">David Martinez</div>
                  <div className="text-gray-600">Owner, Martinez Auto Repair</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default SEOResultsSection;