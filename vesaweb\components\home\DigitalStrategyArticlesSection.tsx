// components/home/<USER>
import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { BlogListItem } from '@/types/blog';
import { urlForImage } from '@/lib/sanity-blog';

interface DigitalStrategyArticlesSectionProps {
  className?: string;
  blogPosts?: BlogListItem[];
}

const DigitalStrategyArticlesSection: React.FC<DigitalStrategyArticlesSectionProps> = ({
  className = '',
  blogPosts = []
}) => {
  // Newsletter subscription state
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState('');

  // Get the featured post (first post) and sidebar posts (remaining posts)
  const featuredPost = blogPosts[0];
  const sidebarPosts = blogPosts.slice(1, 4); // Get up to 3 sidebar posts

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Newsletter subscription handler
  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !email.includes('@')) {
      setSubmitStatus('error');
      setStatusMessage('Please enter a valid email address');
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');
    setStatusMessage('');

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setSubmitStatus('success');
        setStatusMessage(result.message || 'Successfully subscribed to newsletter!');
        setEmail(''); // Clear the form
      } else {
        setSubmitStatus('error');
        setStatusMessage(result.message || 'Failed to subscribe. Please try again.');
      }
    } catch (error) {
      setSubmitStatus('error');
      setStatusMessage('Network error. Please check your connection and try again.');
      console.error('Newsletter subscription error:', error);
    } finally {
      setIsSubmitting(false);

      // Clear status message after 5 seconds
      setTimeout(() => {
        setSubmitStatus('idle');
        setStatusMessage('');
      }, 5000);
    }
  };

  // Show loading state if no blog posts are available
  if (!blogPosts || blogPosts.length === 0) {
    return (
      <section className={`w-full min-h-screen py-8 sm:py-12 lg:py-16 ${className}`} style={{
        backgroundColor: '#f9fafb'
      }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8 sm:mb-12 lg:mb-16">
            <h3 className="text-sm font-semibold tracking-wider uppercase mb-3 sm:mb-4" style={{
              color: '#1d4ed8'
            }}>
              TAILORED DIGITAL STRATEGY FOR SUCCESS
            </h3>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold leading-tight" style={{
              color: '#1f2937'
            }}>
              BUILDING A DIGITAL PRESENCE STRATEGY<br className="hidden sm:block" />
              <span className="sm:hidden"> </span>TAILORED TO YOUR BUSINESS
            </h2>
          </div>

          {/* Loading/No Content State */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10">
            <div className="lg:col-span-2">
              <div
                className="rounded-2xl h-64 sm:h-80 md:h-96 lg:h-[480px] flex items-center justify-center"
                style={{
                  backgroundColor: '#ffffff',
                  border: '1px solid #e5e7eb',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
                }}
              >
                <div className="text-center">
                  <div
                    className="animate-spin rounded-full h-12 w-12 mx-auto mb-4"
                    style={{
                      borderWidth: '2px',
                      borderStyle: 'solid',
                      borderColor: '#e5e7eb',
                      borderBottomColor: '#3b82f6'
                    }}
                  ></div>
                  <p style={{ color: '#4b5563' }}>Loading latest insights...</p>
                </div>
              </div>
            </div>
            <div className="space-y-4 sm:space-y-6">
              {[1, 2, 3].map((i) => (
                <div
                  key={i}
                  className="rounded-xl p-4 sm:p-6"
                  style={{
                    backgroundColor: '#ffffff',
                    border: '1px solid #e5e7eb',
                    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
                  }}
                >
                  <div className="animate-pulse">
                    <div className="h-4 rounded w-3/4 mb-2" style={{ backgroundColor: '#e5e7eb' }}></div>
                    <div className="h-3 rounded w-1/2" style={{ backgroundColor: '#e5e7eb' }}></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={`w-full min-h-screen py-8 sm:py-12 lg:py-16 ${className}`} style={{
      backgroundColor: '#f9fafb'
    }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8 sm:mb-12 lg:mb-16">
          <h3 className="text-sm font-semibold tracking-wider uppercase mb-3 sm:mb-4" style={{
            color: '#1d4ed8'
          }}>
            TAILORED DIGITAL STRATEGY FOR SUCCESS
          </h3>
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold leading-tight" style={{
            color: '#1f2937'
          }}>
            BUILDING A DIGITAL PRESENCE STRATEGY<br className="hidden sm:block" />
            <span className="sm:hidden"> </span>TAILORED TO YOUR BUSINESS
          </h2>
        </div>
        
        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10">
          {/* Featured Article - Large Card */}
          <div className="lg:col-span-2">
            {featuredPost ? (
              <Link href={`/blog/${featuredPost.slug.current}`}>
                <div
                  className="relative rounded-2xl overflow-hidden h-64 sm:h-80 md:h-96 lg:h-[480px] group cursor-pointer transition-all duration-300"
                  style={{
                    backgroundColor: '#ffffff',
                    border: '1px solid #e5e7eb',
                    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
                  }}
                >
                  {/* Main Article Image */}
                  {featuredPost.featuredImage ? (
                    <Image
                      src={urlForImage(featuredPost.featuredImage).width(800).height(480).url()}
                      alt={featuredPost.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div
                      className="absolute inset-0 flex items-center justify-center group-hover:scale-105 transition-transform duration-300"
                      style={{
                        background: 'linear-gradient(to bottom right, #2563eb, #1d4ed8)'
                      }}
                    >
                      <div className="text-center" style={{ color: '#ffffff' }}>
                        <div className="text-4xl font-bold mb-2">VESA</div>
                        <div className="text-lg">Digital Strategy</div>
                      </div>
                    </div>
                  )}

                  {/* Article Info Overlay */}
                  <div
                    className="absolute bottom-0 left-0 right-0 p-4 sm:p-6"
                    style={{
                      background: 'linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.4), transparent)'
                    }}
                  >
                    <h3 className="text-lg sm:text-xl md:text-2xl font-bold mb-2 leading-tight" style={{
                      color: '#ffffff'
                    }}>
                      {featuredPost.title}
                    </h3>
                    <div className="flex items-center space-x-4">
                      <p className="text-sm font-medium" style={{
                        color: '#93c5fd'
                      }}>
                        {featuredPost.readingTime ? `${featuredPost.readingTime} min read` : 'Quick read'}
                      </p>
                      <div className="hidden sm:flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full" style={{
                          backgroundColor: '#93c5fd'
                        }}></div>
                        <p className="text-sm" style={{
                          color: '#93c5fd'
                        }}>{formatDate(featuredPost.publishedAt)}</p>
                      </div>
                    </div>
                  </div>

                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </Link>
            ) : (
              <div
                className="relative rounded-2xl overflow-hidden h-64 sm:h-80 md:h-96 lg:h-[480px] group"
                style={{
                  backgroundColor: '#ffffff',
                  border: '1px solid #e5e7eb',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
                }}
              >
                <div
                  className="absolute inset-0 flex items-center justify-center"
                  style={{
                    background: 'linear-gradient(to bottom right, #2563eb, #1d4ed8)'
                  }}
                >
                  <div className="text-center" style={{ color: '#ffffff' }}>
                    <div className="text-4xl font-bold mb-2">VESA</div>
                    <div className="text-lg">Digital Strategy</div>
                    <div className="text-sm mt-2" style={{ opacity: 0.75 }}>No featured articles yet</div>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {/* Sidebar Articles */}
          <div className="space-y-4 sm:space-y-6 lg:space-y-8">
            {sidebarPosts.length > 0 ? (
              sidebarPosts.map((post) => (
                <Link key={post._id} href={`/blog/${post.slug.current}`}>
                  <div
                    className="group cursor-pointer rounded-xl transition-all duration-300"
                    style={{
                      backgroundColor: '#ffffff',
                      border: '1px solid #f3f4f6'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
                      e.currentTarget.style.borderColor = '#bfdbfe';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.boxShadow = 'none';
                      e.currentTarget.style.borderColor = '#f3f4f6';
                    }}
                  >
                    <div className="flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4">
                      {/* Article Image */}
                      <div className="flex-shrink-0 group-hover:scale-105 transition-transform duration-300">
                        {post.featuredImage ? (
                          <Image
                            src={urlForImage(post.featuredImage).width(112).height(80).url()}
                            alt={post.title}
                            width={112}
                            height={80}
                            className="w-20 h-14 sm:w-24 sm:h-16 md:w-28 md:h-20 rounded-lg object-cover shadow-md"
                          />
                        ) : (
                          <div
                            className="w-20 h-14 sm:w-24 sm:h-16 md:w-28 md:h-20 rounded-lg flex items-center justify-center shadow-md"
                            style={{
                              background: 'linear-gradient(to bottom right, #dbeafe, #bfdbfe)'
                            }}
                          >
                            <div className="text-center" style={{ color: '#2563eb' }}>
                              <div className="text-xs font-bold">VESA</div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <h4
                          className="font-bold text-base sm:text-lg md:text-xl leading-tight mb-2 sm:mb-3 transition-colors duration-300 line-clamp-2"
                          style={{ color: '#1f2937' }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.color = '#2563eb';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.color = '#1f2937';
                          }}
                        >
                          {post.title}
                        </h4>
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium" style={{ color: '#3b82f6' }}>
                            {post.readingTime ? `${post.readingTime} min read` : formatDate(post.publishedAt)}
                          </p>
                          <div
                            className="transition-colors duration-300"
                            style={{ color: '#60a5fa' }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.color = '#2563eb';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.color = '#60a5fa';
                            }}
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))
            ) : (
              // Fallback when no blog posts are available
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="bg-white rounded-xl border border-gray-100">
                  <div className="flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4">
                    <div className="w-20 h-14 sm:w-24 sm:h-16 md:w-28 md:h-20 rounded-lg bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center shadow-md">
                      <div className="text-blue-600 text-center">
                        <div className="text-xs font-bold">VESA</div>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-bold text-base sm:text-lg md:text-xl leading-tight mb-2 sm:mb-3" style={{
                        color: '#1f2937'
                      }}>
                        Coming Soon: Digital Marketing Insights
                      </h4>
                      <p className="text-sm font-medium" style={{
                        color: '#3b82f6'
                      }}>
                        Stay tuned
                      </p>
                    </div>
                  </div>
                </div>
              ))
            )}

            {/* View All Articles Link */}
            <div className="mt-6 lg:mt-8">
              <Link href="/blog">
                <button
                  className="w-full font-medium rounded-xl py-3 sm:py-4 px-4 sm:px-6 transition-all duration-300 group"
                  style={{
                    backgroundColor: '#eff6ff',
                    color: '#2563eb',
                    border: '1px solid #bfdbfe'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#dbeafe';
                    e.currentTarget.style.borderColor = '#93c5fd';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#eff6ff';
                    e.currentTarget.style.borderColor = '#bfdbfe';
                  }}
                >
                  <div className="flex items-center justify-center space-x-2">
                    <span>View All Articles</span>
                    <svg className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
              </Link>
            </div>
          </div>
        </div>

        {/* Bottom Call to Action */}
        <div className="mt-12 sm:mt-16 lg:mt-20 text-center">
          <div className="rounded-2xl p-6 sm:p-8 lg:p-12 shadow-lg" style={{
            backgroundColor: '#ffffff',
            border: '1px solid #e5e7eb'
          }}>
            <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-3 sm:mb-4" style={{
              color: '#1f2937'
            }}>
              Stay Updated with Digital Marketing Insights
            </h3>
            <p className="text-base sm:text-lg mb-6 sm:mb-8 max-w-2xl mx-auto" style={{
              color: '#4b5563'
            }}>
              Get the latest strategies, trends, and expert insights delivered to your inbox.
            </p>

            {/* Newsletter Subscription Form */}
            <form onSubmit={handleNewsletterSubmit} className="max-w-md mx-auto">
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Your email address"
                  required
                  disabled={isSubmitting}
                  className="flex-1 px-4 py-3 rounded-lg focus:outline-none transition-all duration-300 whitespace-nowrap min-w-[120px] flex items-center justify-center"
                  style={{
                    border: '1px solid #d1d5db',
                    backgroundColor: '#ffffff',
                    color: '#374151'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.outline = '2px solid #3b82f6';
                    e.currentTarget.style.outlineOffset = '2px';
                    e.currentTarget.style.borderColor = 'transparent';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.outline = 'none';
                    e.currentTarget.style.borderColor = '#d1d5db';
                  }}
                />
                <button
                  type="submit"
                  disabled={isSubmitting || !email}
                  className="font-medium px-6 py-3 rounded-lg transition-all duration-300 whitespace-nowrap min-w-[120px] flex items-center justify-center"
                  style={{
                    backgroundColor: isSubmitting || !email ? '#9ca3af' : '#3b82f6',
                    color: '#ffffff',
                    cursor: isSubmitting || !email ? 'not-allowed' : 'pointer',
                    opacity: isSubmitting || !email ? 0.5 : 1
                  }}
                  onMouseEnter={(e) => {
                    if (!isSubmitting && email) {
                      e.currentTarget.style.backgroundColor = '#2563eb';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isSubmitting && email) {
                      e.currentTarget.style.backgroundColor = '#3b82f6';
                    }
                  }}
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Subscribing...
                    </>
                  ) : (
                    'Subscribe'
                  )}
                </button>
              </div>

              {/* Status Message */}
              {statusMessage && (
                <div
                  className="mt-4 p-3 rounded-lg text-sm"
                  style={{
                    backgroundColor: submitStatus === 'success' ? '#f0fdf4' : submitStatus === 'error' ? '#fef2f2' : '#f9fafb',
                    color: submitStatus === 'success' ? '#15803d' : submitStatus === 'error' ? '#dc2626' : '#374151',
                    border: `1px solid ${submitStatus === 'success' ? '#bbf7d0' : submitStatus === 'error' ? '#fecaca' : '#e5e7eb'}`
                  }}
                >
                  {statusMessage}
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DigitalStrategyArticlesSection;