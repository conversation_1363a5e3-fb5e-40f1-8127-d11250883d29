{"name": "vesaweb", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.1.1", "@mui/material": "^7.2.0", "@mui/x-date-pickers": "^8.7.0", "@portabletext/react": "^3.2.1", "@sanity/client": "^7.6.0", "@sanity/image-url": "^1.1.0", "@types/nodemailer": "^6.4.17", "@upstash/redis": "^1.35.1", "@vercel/kv": "^3.0.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "framer-motion": "^12.16.0", "gsap": "^3.13.0", "lucide-react": "^0.513.0", "next": "15.3.3", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-clock": "^6.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.0", "react-phone-number-input": "^3.4.12", "react-time-picker": "^7.0.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}