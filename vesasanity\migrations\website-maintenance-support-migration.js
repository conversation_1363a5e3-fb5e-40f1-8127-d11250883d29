// scripts/migrate-website-maintenance-support.js - Working migration with proper schema-compatible icons and BLUE colors
const { createClient } = require('@sanity/client')
const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

const maintenanceSupportData = {
  _type: 'subService',
  parentService: 'web-development',
  title: 'Website Maintenance & Support Services',
  slug: {
    _type: 'slug',
    current: 'website-maintenance-support'
  },
  
  // Hero Section
  hero: {
    badgeText: 'Website Maintenance & Support',
    badgeIcon: 'Settings',
    title: 'Keep Your Website Running Perfectly',
    subtitle: 'Ensure your website stays secure, fast, and up-to-date with comprehensive maintenance and support services. Our proactive approach prevents issues before they impact your business and keeps your site performing at its best.',
    stats: [
      { _key: 'stat1', value: '99.9%', label: 'Website Uptime Guarantee' },
      { _key: 'stat2', value: '24/7', label: 'Monitoring & Support' },
      { _key: 'stat3', value: '95%', label: 'Issue Prevention Rate' },
      { _key: 'stat4', value: '<2hr', label: 'Average Response Time' }
    ],
    backgroundGradient: 'from-blue-600 to-blue-800'
  },

  // Why Service Matters
  whyMatters: {
    title: 'Why Professional Website Maintenance is Essential',
    description: [
      {
        _type: 'block',
        _key: 'desc1',
        children: [
          {
            _type: 'span',
            _key: 'span1',
            text: "Websites require ongoing maintenance to stay secure, perform well, and remain competitive. Without regular updates, security patches, and performance optimization, websites become vulnerable to security threats, experience declining performance, and lose search engine rankings."
          }
        ],
        markDefs: [],
        style: 'normal'
      },
      {
        _type: 'block',
        _key: 'desc2',
        children: [
          {
            _type: 'span',
            _key: 'span2',
            text: "VESA Solutions provides comprehensive website maintenance and support services that keep your website secure, fast, and up-to-date. Our proactive approach prevents problems before they occur and ensures your website continues to drive business results."
          }
        ],
        markDefs: [],
        style: 'normal'
      }
    ],
    features: [
      { _key: 'feature1', text: 'Proactive security monitoring and updates' },
      { _key: 'feature2', text: 'Performance optimization and speed monitoring' },
      { _key: 'feature3', text: 'Regular backups and disaster recovery' },
      { _key: 'feature4', text: '24/7 technical support and issue resolution' }
    ],
    stats: [
      {
        _key: 'stat1',
        value: '43%',
        label: 'of websites are attacked by hackers',
        color: 'from-blue-50 to-blue-100'
      },
      {
        _key: 'stat2',
        value: '60%',
        label: 'of small businesses close after cyber attacks',
        color: 'from-blue-100 to-blue-200'
      },
      {
        _key: 'stat3',
        value: '94%',
        label: 'of malware is delivered via email',
        color: 'from-blue-200 to-blue-300'
      },
      {
        _key: 'stat4',
        value: '3.5x',
        label: 'more likely to rank well with regular updates',
        color: 'from-blue-300 to-blue-400'
      }
    ],
    ctaButton: {
      text: 'Secure Your Website Today'
    }
  },

  // All Services (with schema-compatible icons)
  services: [
    {
      _key: 'service1',
      icon: 'Shield',
      title: 'Security Monitoring & Updates',
      description: 'Comprehensive security monitoring, threat detection, and regular updates to protect your website from vulnerabilities and cyber attacks.',
      fullDescription: 'Website security requires constant vigilance and proactive measures. We provide comprehensive security monitoring and regular updates to protect your website from evolving threats and vulnerabilities.',
      features: [
        'Real-time security monitoring and threat detection',
        'Regular security updates and patch management',
        'Malware scanning and removal services',
        'Firewall configuration and management',
        'SSL certificate management and renewal',
        'Security audit and vulnerability assessments',
        'Backup verification and disaster recovery testing',
        'Security incident response and resolution'
      ],
      benefits: 'Security monitoring prevents 95% of potential attacks, reduces security incidents by 90%, and ensures your website remains protected against evolving cyber threats.',
      result: '+95% Attack Prevention'
    },
    {
      _key: 'service2',
      icon: 'Zap',
      title: 'Performance Monitoring & Optimization',
      description: 'Continuous performance monitoring and optimization to ensure your website loads quickly and provides excellent user experiences.',
      fullDescription: 'Website performance directly impacts user experience, conversion rates, and search rankings. We provide ongoing performance monitoring and optimization to maintain peak website speeds.',
      features: [
        'Real-time performance monitoring and alerts',
        'Page speed optimization and improvements',
        'Database optimization and cleanup',
        'Image optimization and compression',
        'Caching configuration and management',
        'CDN setup and optimization',
        'Core Web Vitals monitoring and improvement',
        'Mobile performance optimization'
      ],
      benefits: 'Performance monitoring maintains optimal speeds, prevents performance degradation, and ensures your website continues to provide excellent user experiences and search rankings.',
      result: '+40% Faster Loading'
    },
    {
      _key: 'service3',
      icon: 'Database',
      title: 'Backup & Disaster Recovery',
      description: 'Automated backup systems and disaster recovery planning to protect your website data and ensure quick recovery from any issues.',
      fullDescription: 'Data loss can be catastrophic for businesses. We implement comprehensive backup and disaster recovery systems that protect your website data and ensure quick recovery from any issues or disasters.',
      features: [
        'Automated daily website backups',
        'Multiple backup location storage',
        'Database backup and verification',
        'File system and content backups',
        'Disaster recovery planning and testing',
        'Quick restoration and recovery services',
        'Backup monitoring and verification',
        'Emergency response and support'
      ],
      benefits: 'Comprehensive backup systems prevent 100% of data loss scenarios, enable quick recovery from issues, and provide peace of mind knowing your website data is always protected.',
      result: '+100% Data Protection'
    },
    {
      _key: 'service4',
      icon: 'RefreshCw',
      title: 'Content & Software Updates',
      description: 'Regular content updates, software maintenance, and system upgrades to keep your website current, secure, and functioning optimally.',
      fullDescription: 'Websites require regular updates to stay current, secure, and competitive. We provide comprehensive update services that keep your website\'s content, software, and systems current and optimized.',
      features: [
        'Content management system updates',
        'Plugin and extension updates',
        'Security patch installation',
        'Software version upgrades',
        'Content updates and modifications',
        'Feature enhancements and improvements',
        'Compatibility testing and validation',
        'Update documentation and reporting'
      ],
      benefits: 'Regular updates improve security by 85%, enhance performance by 30%, and ensure your website remains current with the latest features and capabilities.',
      result: '+85% Security Improvement'
    },
    {
      _key: 'service5',
      icon: 'Headphones',
      title: '24/7 Technical Support',
      description: 'Round-the-clock technical support and issue resolution to ensure your website problems are addressed quickly and effectively.',
      fullDescription: 'Technical issues can occur at any time and need immediate attention. We provide 24/7 technical support with fast response times to ensure your website problems are resolved quickly.',
      features: [
        '24/7 technical support availability',
        'Emergency issue response and resolution',
        'Website troubleshooting and debugging',
        'Performance issue diagnosis and fixes',
        'User support and training assistance',
        'Priority support for critical issues',
        'Detailed issue tracking and reporting',
        'Proactive monitoring and alerts'
      ],
      benefits: '24/7 support reduces downtime by 90%, provides peace of mind, and ensures technical issues are resolved quickly to minimize business impact.',
      result: '+90% Uptime Reliability'
    },
    {
      _key: 'service6',
      icon: 'BarChart3',
      title: 'Analytics & Reporting',
      description: 'Comprehensive website analytics, performance reporting, and insights to help you understand your website\'s performance and opportunities.',
      fullDescription: 'Understanding your website\'s performance is crucial for making informed decisions. We provide comprehensive analytics and reporting that give you insights into your website\'s performance and opportunities for improvement.',
      features: [
        'Website traffic analysis and reporting',
        'Performance metrics tracking and insights',
        'Security incident reporting and analysis',
        'Uptime monitoring and availability reports',
        'User behavior analysis and recommendations',
        'SEO performance tracking and optimization',
        'Monthly maintenance reports and summaries',
        'Custom reporting and dashboard creation'
      ],
      benefits: 'Comprehensive reporting provides valuable insights, improves decision-making by 75%, and helps identify optimization opportunities that drive better results.',
      result: '+75% Better Decisions'
    }
  ],

  // Strategic Implementation Section (with schema-compatible icons and colors)
  strategicImplementation: {
    title: 'Comprehensive Website Maintenance & Support Process',
    description: 'Successful website maintenance requires proactive monitoring, regular updates, and responsive support. Our integrated approach ensures your website remains secure, fast, and reliable at all times.',
    secondaryDescription: 'From initial setup to ongoing optimization, we provide comprehensive maintenance and support services that keep your website performing at its best and protect your business investment.',
    features: [
      'Proactive monitoring and maintenance',
      'Regular updates and optimization',
      'Responsive support and issue resolution'
    ],
    sections: [
      {
        _key: 'section1',
        icon: 'Shield',
        title: 'Security & Monitoring',
        description: 'Comprehensive security monitoring, threat detection, and proactive measures to protect your website from vulnerabilities and attacks.',
        color: 'from-blue-50 to-blue-100'
      },
      {
        _key: 'section2',
        icon: 'RefreshCw',
        title: 'Updates & Optimization',
        description: 'Regular updates, performance optimization, and system maintenance to keep your website current, fast, and functioning optimally.',
        color: 'from-blue-100 to-blue-200'
      },
      {
        _key: 'section3',
        icon: 'Headphones',
        title: 'Support & Response',
        description: '24/7 technical support, issue resolution, and proactive assistance to ensure your website problems are addressed quickly and effectively.',
        color: 'from-blue-200 to-blue-300'
      }
    ]
  },

  // Market Intelligence Section (with schema-compatible background)
  marketIntelligence: {
    title: 'Advanced Website Maintenance & Support Intelligence',
    description: 'Our maintenance and support strategies are powered by industry expertise, proven methodologies, and advanced monitoring technologies that deliver exceptional reliability and performance for websites across all industries.',
    secondaryDescription: 'Through years of experience and hundreds of successful maintenance partnerships, we\'ve developed the expertise and processes needed to keep websites secure, fast, and reliable while preventing issues before they impact your business.',
    stats: [
      {
        _key: 'stat1',
        value: '500+',
        label: 'Websites Maintained'
      },
      {
        _key: 'stat2',
        value: '99.9%',
        label: 'Uptime Guarantee'
      },
      {
        _key: 'stat3',
        value: '95%',
        label: 'Issue Prevention Rate'
      },
      {
        _key: 'stat4',
        value: '24/7',
        label: 'Monitoring & Support'
      }
    ],
    ctaButton: {
      text: 'Secure Your Website Today'
    },
    backgroundGradient: 'from-blue-50 to-blue-100'
  },

  // Process (with schema-compatible icons)
  process: {
    title: 'Our Proven Website Maintenance & Support Process',
    description: 'VESA Solutions follows a systematic approach that has kept hundreds of websites secure, fast, and reliable, ensuring maximum uptime and performance at all times.',
    steps: [
      {
        _key: 'step1',
        step: 1,
        title: 'Initial Assessment & Setup',
        description: 'Comprehensive website assessment and monitoring system setup to establish baseline performance and security.',
        icon: 'Search',
        details: [
          'Complete website security and performance audit',
          'Monitoring system installation and configuration',
          'Backup system setup and verification',
          'Performance baseline establishment',
          'Maintenance schedule and plan development'
        ]
      },
      {
        _key: 'step2',
        step: 2,
        title: 'Proactive Monitoring',
        description: 'Continuous monitoring of security, performance, and uptime with automated alerts and proactive issue detection.',
        icon: 'Shield',
        details: [
          '24/7 security monitoring and threat detection',
          'Performance monitoring and optimization',
          'Uptime monitoring and availability tracking',
          'Automated backup verification and testing',
          'Proactive issue identification and prevention'
        ]
      },
      {
        _key: 'step3',
        step: 3,
        title: 'Regular Maintenance',
        description: 'Scheduled maintenance activities including updates, optimization, and preventive measures.',
        icon: 'RefreshCw',
        details: [
          'Regular security updates and patch installation',
          'Performance optimization and improvements',
          'Content management system maintenance',
          'Database optimization and cleanup',
          'Software updates and compatibility testing'
        ]
      },
      {
        _key: 'step4',
        step: 4,
        title: 'Support & Reporting',
        description: 'Responsive technical support and comprehensive reporting on website performance and maintenance activities.',
        icon: 'Headphones',
        details: [
          '24/7 technical support and issue resolution',
          'Monthly maintenance reports and insights',
          'Performance analytics and recommendations',
          'Security incident response and resolution',
          'Ongoing optimization and improvement suggestions'
        ]
      }
    ]
  },

  // Case Study (with schema-compatible background)
  caseStudy: {
    title: 'Law Firm Achieves 99.9% Uptime & Zero Security Incidents',
    description: 'Discover how our comprehensive maintenance and support services helped Johnson & Associates Law maintain perfect website reliability and security over 3 years of partnership.',
    results: [
      {
        _key: 'result1',
        value: '99.9%',
        label: 'Website Uptime'
      },
      {
        _key: 'result2',
        value: '0',
        label: 'Security Incidents'
      },
      {
        _key: 'result3',
        value: '50%',
        label: 'Performance Improvement'
      },
      {
        _key: 'result4',
        value: '100%',
        label: 'Data Protection'
      }
    ],
    ctaButton: {
      text: 'View Complete Case Study'
    },
    backgroundGradient: 'from-blue-600 to-blue-800'
  },

  // CTA
  cta: {
    title: 'Get Your Free Website Maintenance Assessment',
    description: 'Protect your website investment with professional maintenance and support services. Get a comprehensive assessment and maintenance plan that keeps your site secure and performing optimally.',
    benefits: [
      'Complete website security & performance assessment',
      'Custom maintenance plan & service recommendations',
      'Backup system setup & disaster recovery planning',
      'Ongoing support strategy & response time guarantees'
    ],
    phoneNumber: '(*************',
    formSettings: {
      ctaText: 'Get My Free Maintenance Assessment',
      messagePlaceholder: 'Tell us about your website maintenance needs and concerns*'
    }
  },

  // All Testimonials (without image references)
  testimonials: [
    {
      _key: 'testimonial1',
      name: 'Attorney Sarah Johnson',
      business: 'Johnson & Associates Law',
      location: 'Chicago, IL',
      quote: 'VESA\'s maintenance services have been incredible. We\'ve had 99.9% uptime for over 3 years with zero security incidents. Their proactive approach gives us complete peace of mind to focus on our legal practice.',
      result: '+99.9% Uptime',
      rating: 5
    },
    {
      _key: 'testimonial2',
      name: 'Dr. Michael Rodriguez',
      business: 'Rodriguez Medical Practice',
      location: 'Phoenix, AZ',
      quote: 'The 24/7 support and proactive monitoring have been game-changing for our practice. When we had an emergency issue, they resolved it within an hour. Our website has never been more reliable.',
      result: '+1hr Emergency Response',
      rating: 5
    },
    {
      _key: 'testimonial3',
      name: 'Jennifer Chen',
      business: 'TechStart Solutions',
      location: 'San Francisco, CA',
      quote: 'Their maintenance service has improved our website performance by 50% and we haven\'t had a single security issue. The monthly reports help us understand our website\'s health and make informed decisions.',
      result: '+50% Performance',
      rating: 5
    }
  ],

  // All FAQs
  faqs: [
    {
      _key: 'faq1',
      question: 'What is included in website maintenance services?',
      answer: 'Our maintenance services include security monitoring, regular updates, performance optimization, backups, 24/7 support, uptime monitoring, and monthly reporting. We provide comprehensive care to keep your website secure, fast, and reliable.'
    },
    {
      _key: 'faq2',
      question: 'How often do you perform website maintenance?',
      answer: 'We perform daily monitoring, weekly security scans, monthly updates and optimization, and immediate response to any issues. Critical security updates are applied as soon as they become available to ensure maximum protection.'
    },
    {
      _key: 'faq3',
      question: 'What is your response time for technical issues?',
      answer: 'We provide 24/7 support with average response times under 2 hours for standard issues and immediate response for critical emergencies. Our goal is to resolve most issues within 4-6 hours of detection.'
    },
    {
      _key: 'faq4',
      question: 'Do you provide website backups?',
      answer: 'Yes, we perform automated daily backups stored in multiple secure locations. We also test backup integrity regularly and can quickly restore your website from backups if needed. All backup and restoration services are included.'
    },
    {
      _key: 'faq5',
      question: 'Can you maintain websites built by other developers?',
      answer: 'Absolutely! We maintain websites built on all major platforms and technologies. We\'ll perform an initial assessment to understand your website\'s architecture and create a customized maintenance plan.'
    },
    {
      _key: 'faq6',
      question: 'What happens if my website gets hacked?',
      answer: 'We provide immediate incident response including malware removal, security hardening, and website restoration. Our proactive security monitoring prevents most attacks, but if issues occur, we handle complete cleanup and recovery.'
    },
    {
      _key: 'faq7',
      question: 'Do you provide performance optimization as part of maintenance?',
      answer: 'Yes, performance optimization is included in our maintenance services. We continuously monitor website speed, optimize images, manage caching, and implement improvements to maintain fast loading times.'
    },
    {
      _key: 'faq8',
      question: 'How much do website maintenance services cost?',
      answer: 'Maintenance costs vary based on website complexity and service level requirements. Plans typically start at $99/month for basic sites and range up to $499/month for complex enterprise websites. We provide custom quotes based on your specific needs.'
    }
  ],

  // Footer CTA (with schema-compatible background)
  footerCta: {
    title: 'Ready to Protect Your Website Investment?',
    description: 'Join hundreds of businesses that trust VESA Solutions to keep their websites secure, fast, and reliable with comprehensive maintenance and support services.',
    primaryButton: {
      text: 'Schedule Free Consultation',
      icon: 'Calendar'
    },
    secondaryButton: {
      text: 'Call (*************',
      icon: 'Phone',
      phoneNumber: '(*************'
    },
    trustSignals: {
      title: 'Trusted by Businesses & Certified Partners',
      ratings: [
        {
          _key: 'rating1',
          rating: '4.9/5 Rating',
          source: 'Google Reviews',
          description: 'Google Reviews'
        },
        {
          _key: 'rating2',
          rating: '4.8/5 Rating',
          source: 'Clutch Reviews',
          description: 'Clutch Reviews'
        },
        {
          _key: 'rating3',
          rating: '500+ Sites',
          source: 'Successfully Maintained',
          description: 'Successfully Maintained'
        }
      ]
    },
    backgroundGradient: 'from-blue-700 to-blue-900'
  },

  // SEO (without image reference)
  seo: {
    metaTitle: 'Website Maintenance & Support Services | 24/7 Website Care | VESA Solutions',
    metaDescription: 'Professional website maintenance and support services with 24/7 monitoring, security updates, and technical support. Keep your website secure and performing optimally. Free assessment included.',
    keywords: [
      'website maintenance services',
      'website support services',
      'website security monitoring',
      'website backup services',
      '24/7 website support'
    ]
  }
}

async function migrateMaintenanceSupportData() {
  try {
    console.log('🚀 Starting Website Maintenance & Support migration...')
    console.log('📊 Project ID: zleti5e4')
    console.log('🗃️  Dataset: production')
    console.log('')
    
    // Check if document already exists
    const existing = await client.fetch(`*[_type == "subService" && slug.current == "website-maintenance-support"][0]`)
    
    if (existing) {
      console.log('📄 Website Maintenance & Support document already exists:', existing._id)
      console.log('🔄 Updating existing document...')
      
      const result = await client
        .patch(existing._id)
        .set(maintenanceSupportData)
        .commit()
      
      console.log('✅ Website Maintenance & Support page updated successfully!')
      console.log(`📄 Document ID: ${result._id}`)
      console.log(`🔗 Edit in Studio: https://vesasanity.sanity.studio/structure/subService;${result._id}`)
      return result
    } else {
      const result = await client.create(maintenanceSupportData)
      console.log('✅ Website Maintenance & Support page created successfully!')
      console.log(`📄 Document ID: ${result._id}`)
      console.log(`🔗 Edit in Studio: https://vesasanity.sanity.studio/structure/subService;${result._id}`)
      return result
    }
    
  } catch (error) {
    console.error('')
    console.error('❌ Migration failed:')
    console.error(error)
    
    if (error.message && error.message.includes('token')) {
      console.log('')
      console.log('💡 Make sure your token is correctly set in this file')
      console.log('   Get your token from: https://sanity.io/manage/personal/tokens')
    }
    
    if (error.message && error.message.includes('Insufficient permissions')) {
      console.log('')
      console.log('💡 Make sure your token has "Editor" or "Administrator" permissions')
    }
    
    process.exit(1)
  }
}

// Export the function instead of auto-running
module.exports = { migrateMaintenanceSupportData }

// Run the migration
migrateMaintenanceSupportData()
  .then(() => {
    console.log('')
    console.log('🎉 Your Website Maintenance & Support page is now managed by Sanity!')
    console.log('📝 You can now edit all content in Sanity Studio')
    console.log('🔄 Remember to update your Next.js page to fetch from Sanity')
    console.log('')
    console.log('🎯 The page structure is complete and functional!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Migration failed:', error)
    process.exit(1)
  })
