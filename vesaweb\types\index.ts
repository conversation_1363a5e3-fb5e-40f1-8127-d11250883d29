// types/index.ts
export interface Partner {
  id: number;
  name: string;
  logo: string;
}

export interface Slide {
  number: string;
  topTitle: string;
  title: string;
  content: string[];
  imageDesc: string;
}

export interface Testimonial {
  id: number;
  name: string;
  title: string;
  image: string;
  heading: string;
  content: string;
  additionalContent: string;
  stars: number;
  signature: string;
  position: string;
}

export interface WhyChooseStep {
  id: string;
  title: string;
  description: string;
  image: string;
}

export interface CalendarDay {
  date: number;
  available: boolean;
  fullDate?: Date;
  isNextMonth?: boolean;
  isToday?: boolean;
}

export interface Service {
  id: number;
  name: string;
  title: string;
  description: string[];
  images: string[];
}

export interface Platform {
  name: string;
  logo: string;
}

export interface Article {
  id: number;
  title: string;
  readTime: string;
  image: string;
}