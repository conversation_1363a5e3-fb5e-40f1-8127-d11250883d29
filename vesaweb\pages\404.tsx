// pages/404.tsx
import React from 'react'
import Head from 'next/head'
import Link from 'next/link'
import Header from '@/components/global/Header'
import Footer from '@/components/global/Footer'

const Custom404: React.FC = () => {
  return (
    <>
      <Head>
        <title>Page Not Found | VESA Solutions</title>
        <meta name="description" content="The page you&apos;re looking for could not be found." />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Header isVisible={true} />

      <main className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center px-4">
          <div className="mb-8">
            <h1 className="text-6xl font-bold text-gray-800 mb-4">404</h1>
            <h2 className="text-2xl font-semibold text-gray-700 mb-4">
              Service Not Found
            </h2>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              The service you&apos;re looking for doesn&apos;t exist or may have been moved.
              Let&apos;s get you back on track.
            </p>
          </div>

          <div className="space-y-4">
            <Link 
              href="/services"
              className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              View All Services
            </Link>
            
            <div className="block">
              <Link 
                href="/"
                className="inline-block px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
              >
                Return Home
              </Link>
            </div>
          </div>

          {/* Popular services quick links */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <h3 className="text-lg font-medium text-gray-800 mb-6">
              Popular Services
            </h3>
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              <Link href="/seo-search-engine-optimization" className="text-blue-600 hover:text-blue-800 transition-colors">
                SEO Services
              </Link>
              <span className="text-gray-400">•</span>
              <Link href="/web-development" className="text-blue-600 hover:text-blue-800 transition-colors">
                Web Development
              </Link>
              <span className="text-gray-400">•</span>
              <Link href="/digital-marketing" className="text-blue-600 hover:text-blue-800 transition-colors">
                Digital Marketing
              </Link>
            </div>

            {/* Additional helpful links */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <h4 className="font-medium text-gray-800 mb-2">Company</h4>
                <div className="space-y-1">
                  <Link href="/about" className="block text-gray-600 hover:text-blue-600 transition-colors">
                    About Us
                  </Link>
                  <Link href="/case-studies" className="block text-gray-600 hover:text-blue-600 transition-colors">
                    Case Studies
                  </Link>
                  <Link href="/blog" className="block text-gray-600 hover:text-blue-600 transition-colors">
                    Blog
                  </Link>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-800 mb-2">SEO Services</h4>
                <div className="space-y-1">
                  <Link href="/on-page-seo" className="block text-gray-600 hover:text-blue-600 transition-colors">
                    On-Page SEO
                  </Link>
                  <Link href="/local-seo" className="block text-gray-600 hover:text-blue-600 transition-colors">
                    Local SEO
                  </Link>
                  <Link href="/technical-seo" className="block text-gray-600 hover:text-blue-600 transition-colors">
                    Technical SEO
                  </Link>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-800 mb-2">Web Development</h4>
                <div className="space-y-1">
                  <Link href="/custom-website-development" className="block text-gray-600 hover:text-blue-600 transition-colors">
                    Custom Websites
                  </Link>
                  <Link href="/ecommerce-development" className="block text-gray-600 hover:text-blue-600 transition-colors">
                    E-commerce
                  </Link>
                  <Link href="/website-speed-optimization" className="block text-gray-600 hover:text-blue-600 transition-colors">
                    Speed Optimization
                  </Link>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-800 mb-2">Digital Marketing</h4>
                <div className="space-y-1">
                  <Link href="/ppc" className="block text-gray-600 hover:text-blue-600 transition-colors">
                    PPC Advertising
                  </Link>
                  <Link href="/social-media-marketing" className="block text-gray-600 hover:text-blue-600 transition-colors">
                    Social Media
                  </Link>
                  <Link href="/email-marketing" className="block text-gray-600 hover:text-blue-600 transition-colors">
                    Email Marketing
                  </Link>
                </div>
              </div>
            </div>

            {/* Contact section */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <p className="text-gray-600 mb-4">
                Can&apos;t find what you&apos;re looking for?
              </p>
              <Link
                href="/contact-us"
                className="inline-flex items-center px-4 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
              >
                Contact Us
              </Link>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </>
  )
}

export default Custom404