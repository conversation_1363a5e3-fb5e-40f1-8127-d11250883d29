// Migration to revert all sub-service colors back to original blue
const { createClient } = require('@sanity/client')

const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

// Original blue color scheme for all services
const ORIGINAL_COLORS = {
  heroGradient: 'from-blue-600 to-blue-800',
  footerCtaGradient: 'from-blue-700 to-blue-900',
  marketIntelligenceGradient: 'from-blue-50 to-blue-100',
  statsColor: 'from-blue-50 to-blue-100'
}

async function revertSubServiceColors() {
  try {
    console.log('🔄 Starting sub-service color revert to original blue...')
    
    // Get all sub-services with their current data
    const subServices = await client.fetch(`
      *[_type == "subService"] {
        _id,
        title,
        hero,
        whyMatters,
        strategicImplementation,
        marketIntelligence,
        footerCta
      }
    `)
    
    console.log(`Found ${subServices.length} sub-services to revert`)
    
    for (const subService of subServices) {
      console.log(`\n🔄 Reverting colors for: ${subService.title}`)
      
      const updates = {}
      
      // 1. Revert Hero Section background gradient to blue
      if (subService.hero) {
        updates['hero.backgroundGradient'] = ORIGINAL_COLORS.heroGradient
        console.log(`  ✅ Hero gradient: ${ORIGINAL_COLORS.heroGradient}`)
      }
      
      // 2. Revert WhyMatters stats colors to blue
      if (subService.whyMatters && subService.whyMatters.stats) {
        subService.whyMatters.stats.forEach((stat, index) => {
          updates[`whyMatters.stats[${index}].color`] = ORIGINAL_COLORS.statsColor
        })
        console.log(`  ✅ WhyMatters stats colors reverted to blue`)
      }
      
      // 3. Revert Strategic Implementation section colors to blue
      if (subService.strategicImplementation && subService.strategicImplementation.sections) {
        subService.strategicImplementation.sections.forEach((section, index) => {
          updates[`strategicImplementation.sections[${index}].color`] = ORIGINAL_COLORS.statsColor
        })
        console.log(`  ✅ Strategic Implementation section colors reverted to blue`)
      }
      
      // 4. Revert Market Intelligence background gradient to blue
      if (subService.marketIntelligence) {
        updates['marketIntelligence.backgroundGradient'] = ORIGINAL_COLORS.marketIntelligenceGradient
        console.log(`  ✅ Market Intelligence gradient: ${ORIGINAL_COLORS.marketIntelligenceGradient}`)
      }
      
      // 5. Revert Footer CTA background gradient to blue
      if (subService.footerCta) {
        updates['footerCta.backgroundGradient'] = ORIGINAL_COLORS.footerCtaGradient
        console.log(`  ✅ Footer CTA gradient: ${ORIGINAL_COLORS.footerCtaGradient}`)
      }
      
      // Apply all updates
      if (Object.keys(updates).length > 0) {
        await client.patch(subService._id).set(updates).commit()
        console.log(`  🎉 Successfully reverted ${Object.keys(updates).length} color fields`)
      } else {
        console.log(`  ⏭️  No color updates needed`)
      }
    }
    
    console.log('\n✅ Sub-service color revert completed successfully!')
    console.log('\n📊 All services now use the original blue color scheme')
    
  } catch (error) {
    console.error('❌ Error during color revert:', error)
    throw error
  }
}

// Run the revert
if (require.main === module) {
  revertSubServiceColors()
    .then(() => {
      console.log('🎉 Revert completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Revert failed:', error)
      process.exit(1)
    })
}

module.exports = { revertSubServiceColors }
