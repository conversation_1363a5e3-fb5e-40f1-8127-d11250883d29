// schemas/blogTag.ts - Blog tag schema for VESA blog system
import { defineType, defineField } from 'sanity'

export const blogTag = defineType({
  name: 'blogTag',
  title: 'Blog Tag',
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Tag Title',
      type: 'string',
      validation: Rule => Rule.required().max(30)
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'description',
      title: 'Tag Description',
      type: 'text',
      rows: 2,
      validation: Rule => Rule.max(150)
    })
  ],
  preview: {
    select: {
      title: 'title',
      description: 'description'
    },
    prepare({ title, description }) {
      return {
        title,
        subtitle: description || 'No description'
      }
    }
  }
})
