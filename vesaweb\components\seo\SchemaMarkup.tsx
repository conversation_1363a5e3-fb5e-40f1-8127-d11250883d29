import React from 'react';
import Head from 'next/head';

interface SchemaMarkupProps {
  pageType?: 'homepage' | 'service' | 'about' | 'contact' | 'blog' | 'case-study';
  serviceData?: {
    name: string;
    description: string;
    price?: string;
    category: string;
  };
  articleData?: {
    title: string;
    description: string;
    publishedDate: string;
    modifiedDate?: string;
    author: string;
    image?: string;
  };
}

const SchemaMarkup: React.FC<SchemaMarkupProps> = ({ 
  pageType = 'homepage', 
  serviceData,
  articleData 
}) => {
  // Organization Schema (appears on all pages)
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Vesa Solutions",
    "alternateName": "VesaSolutions",
    "url": "https://vesasolutions.com",
    "logo": "https://vesasolutions.com/VesaLogo.svg",
    "description": "Full-service digital marketing agency helping businesses attract, impress, and convert more leads online since 2015.",
    "foundingDate": "2015",
    "email": "<EMAIL>",
    "telephone": ["+***********", "+355694046408"],
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Bulevardi Dyrrah, Pallati 394, Kati 4-t",
      "addressLocality": "Durrës",
      "postalCode": "2001",
      "addressCountry": "AL"
    },
    "location": {
      "@type": "Place",
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "41.3317",
        "longitude": "19.4414"
      }
    },
    "sameAs": [
      "https://www.facebook.com/vesasolutions",
      "https://www.linkedin.com/company/vesasolutions",
      "https://twitter.com/vesasolutions"
    ],
    "serviceArea": {
      "@type": "Place",
      "name": "Worldwide"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Digital Marketing Services",
      "itemListElement": [
        {
          "@type": "OfferCatalog",
          "name": "SEO Services",
          "itemListElement": [
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "On-Page SEO Optimization",
                "description": "Comprehensive optimization of website content, meta tags, headers, and internal structure for maximum search engine visibility."
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Local SEO Services",
                "description": "Optimize your business for local search results and Google My Business to attract nearby customers."
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Technical SEO Services",
                "description": "Advanced technical optimization to ensure search engines can properly crawl, index, and understand your website."
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Content Writing",
                "description": "Professional SEO content writing services to engage your audience and improve search rankings."
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "SEO Analytics",
                "description": "Comprehensive SEO analytics and reporting to track performance and optimize strategies."
              }
            }
          ]
        },
        {
          "@type": "OfferCatalog",
          "name": "Web Development Services",
          "itemListElement": [
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Custom Website Development",
                "description": "Professional custom website development tailored to your business needs and goals."
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "E-commerce Development",
                "description": "Complete e-commerce solutions to help you sell products and services online effectively."
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Website Speed Optimization",
                "description": "Optimize your website's loading speed and performance for better user experience and SEO."
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Website Maintenance & Support",
                "description": "Comprehensive website maintenance and support services with 24/7 monitoring and updates."
              }
            }
          ]
        },
        {
          "@type": "OfferCatalog",
          "name": "Digital Marketing Services",
          "itemListElement": [
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "PPC Advertising",
                "description": "Drive immediate results with targeted pay-per-click campaigns across Google, Bing, and social platforms."
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Social Media Marketing",
                "description": "Engage your audience and build brand communities with strategic social media marketing and management."
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Email Marketing",
                "description": "Build lasting relationships and drive conversions with strategic email marketing campaigns."
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Branding Services",
                "description": "Create memorable brand identities that resonate with your target audience and drive business growth."
              }
            }
          ]
        }
      ]
    }
  };

  // Local Business Schema
  const localBusinessSchema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Vesa Solutions",
    "image": "https://vesasolutions.com/VesaLogo.svg",
    "url": "https://vesasolutions.com",
    "telephone": "+***********",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Bulevardi Dyrrah, Pallati 394, Kati 4-t",
      "addressLocality": "Durrës",
      "postalCode": "2001",
      "addressCountry": "AL"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "41.3317",
      "longitude": "19.4414"
    },
    "openingHoursSpecification": {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": [
        "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"
      ],
      "opens": "00:00",
      "closes": "23:59"
    },
    "priceRange": "$$",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "127",
      "bestRating": "5",
      "worstRating": "1"
    }
  };

  // Website Schema
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Vesa Solutions",
    "url": "https://vesasolutions.com",
    "description": "Full-service digital marketing agency helping businesses attract, impress, and convert more leads online since 2015.",
    "publisher": {
      "@type": "Organization",
      "name": "Vesa Solutions"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://vesasolutions.com/search?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    }
  };

  // Professional Service Schema
  const professionalServiceSchema = {
    "@context": "https://schema.org",
    "@type": "ProfessionalService",
    "name": "Vesa Solutions",
    "url": "https://vesasolutions.com",
    "logo": "https://vesasolutions.com/VesaLogo.svg",
    "description": "Professional digital marketing agency specializing in SEO, web development, and digital marketing services.",
    "serviceType": "Digital Marketing Agency",
    "provider": {
      "@type": "Organization",
      "name": "Vesa Solutions"
    },
    "areaServed": "Worldwide",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Digital Marketing Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Search Engine Optimization (SEO)",
            "serviceType": "SEO Services"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Web Development",
            "serviceType": "Web Development Services"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Digital Marketing",
            "serviceType": "Digital Marketing Services"
          }
        }
      ]
    }
  };

  // Service Schema (for service pages)
  const getServiceSchema = () => {
    if (!serviceData) return null;

    return {
      "@context": "https://schema.org",
      "@type": "Service",
      "name": serviceData.name,
      "description": serviceData.description,
      "provider": {
        "@type": "Organization",
        "name": "Vesa Solutions",
        "url": "https://vesasolutions.com"
      },
      "serviceType": serviceData.category,
      "areaServed": "Worldwide",
      "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": serviceData.name,
        "itemListElement": [
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": serviceData.name,
              "description": serviceData.description
            },
            "price": serviceData.price || "Contact for pricing",
            "priceCurrency": "USD"
          }
        ]
      }
    };
  };

  // Article Schema (for blog posts)
  const getArticleSchema = () => {
    if (!articleData) return null;

    return {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": articleData.title,
      "description": articleData.description,
      "image": articleData.image || "https://vesasolutions.com/VesaLogo.svg",
      "author": {
        "@type": "Person",
        "name": articleData.author,
        "url": "https://vesasolutions.com/about"
      },
      "publisher": {
        "@type": "Organization",
        "name": "Vesa Solutions",
        "logo": {
          "@type": "ImageObject",
          "url": "https://vesasolutions.com/VesaLogo.svg"
        }
      },
      "datePublished": articleData.publishedDate,
      "dateModified": articleData.modifiedDate || articleData.publishedDate,
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "https://vesasolutions.com"
      }
    };
  };

  // FAQ Schema (for pages with FAQs) - Available for future use
  // const getFAQSchema = (faqs: Array<{question: string; answer: string}>) => {
  //   return {
  //     "@context": "https://schema.org",
  //     "@type": "FAQPage",
  //     "mainEntity": faqs.map(faq => ({
  //       "@type": "Question",
  //       "name": faq.question,
  //       "acceptedAnswer": {
  //         "@type": "Answer",
  //         "text": faq.answer
  //       }
  //     }))
  //   };
  // };

  // Breadcrumb Schema - Available for future use
  // const getBreadcrumbSchema = (breadcrumbs: Array<{name: string; url: string}>) => {
  //   return {
  //     "@context": "https://schema.org",
  //     "@type": "BreadcrumbList",
  //     "itemListElement": breadcrumbs.map((crumb, index) => ({
  //       "@type": "ListItem",
  //       "position": index + 1,
  //       "name": crumb.name,
  //       "item": crumb.url
  //     }))
  //   };
  // };

  return (
    <Head>
      {/* Organization Schema - Always present */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />

      {/* Homepage specific schemas */}
      {pageType === 'homepage' && (
        <>
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(localBusinessSchema)
            }}
          />
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(websiteSchema)
            }}
          />
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(professionalServiceSchema)
            }}
          />
        </>
      )}

      {/* Service page schema */}
      {pageType === 'service' && serviceData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(getServiceSchema())
          }}
        />
      )}

      {/* Blog post schema */}
      {pageType === 'blog' && articleData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(getArticleSchema())
          }}
        />
      )}
    </Head>
  );
};

export default SchemaMarkup;
