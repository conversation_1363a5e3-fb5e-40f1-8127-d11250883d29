# Sanity Migrations

This directory contains migration scripts for populating the Sanity CMS with demo data.

## Available Migrations

### Case Studies Migration (`case-studies-migration.js`)

Creates demo case studies data including:
- **5 comprehensive case studies** across different industries
- **3 featured case studies** for homepage display
- **Complete data structure** including client info, project details, results, and testimonials
- **SEO metadata** for each case study
- **Realistic metrics** and success stories

#### Industries Covered:
- E-commerce & Retail
- Technology (SaaS)
- Food & Beverage
- Healthcare
- Professional Services (Legal)

#### Services Covered:
- Full Digital Strategy
- SEO Services
- Web Development
- Content Marketing
- Local SEO

## Running Migrations

### Prerequisites

1. **Environment Variables**: Make sure you have the following environment variables set in your `.env.local` file:
   ```
   SANITY_STUDIO_PROJECT_ID=your-project-id
   SANITY_STUDIO_DATASET=production
   SANITY_API_TOKEN=your-api-token
   ```

2. **API Token**: You need a Sanity API token with write permissions:
   - Go to your Sanity project dashboard
   - Navigate to API → Tokens
   - Create a new token with "Editor" permissions
   - Copy the token to your environment variables

### Running the Case Studies Migration

```bash
# Navigate to the vesasanity directory
cd vesasanity

# Install dependencies (if not already done)
npm install

# Test the connection first (optional but recommended)
npm run test:connection

# Run the case studies migration
npm run migrate:case-studies
```

### Testing Connection First

Before running the migration, test your connection:

```bash
npm run test:connection
```

**Expected output:**
```
Testing Sanity connection...
✅ Connection successful!
Found 0 existing case studies
```

### Expected Migration Output

```
Starting case studies migration...
Creating case study: E-commerce Fashion Retailer Achieves 450% Revenue Growth
✅ Created case study: case-study-id-1
Creating case study: SaaS Startup Generates 340% More Qualified Leads
✅ Created case study: case-study-id-2
Creating case study: Local Restaurant Chain Doubles Online Orders
✅ Created case study: case-study-id-3
Creating case study: Medical Practice Increases New Patients by 280%
✅ Created case study: case-study-id-4
Creating case study: Law Firm Generates 400% More Qualified Leads
✅ Created case study: case-study-id-5
🎉 Successfully created 5 case studies!
```

## Data Structure

Each case study includes:

### Basic Information
- Title and slug
- Short description
- Featured status
- Publication date

### Client Details
- Company name and industry
- Location and website
- Company size
- Logo (placeholder)

### Project Information
- Service type
- Project duration
- Challenge description
- Project goals

### Solution Details
- Approach description
- Implementation steps
- Tools and technologies used

### Results & Metrics
- Overview of results
- Key performance metrics with before/after values
- Improvement percentages
- Timeline for results

### Client Testimonial
- Quote from client
- Author name and position
- Star rating

### SEO Metadata
- Meta title and description
- Focus keywords
- Social sharing optimization

## Customizing the Data

To modify the demo data:

1. **Edit the migration file**: Open `case-studies-migration.js`
2. **Update case study objects**: Modify the data in the `caseStudiesData` and `additionalCaseStudies` arrays
3. **Add new case studies**: Add new objects to the arrays following the same structure
4. **Run the migration**: Execute `npm run migrate:case-studies`

## Troubleshooting

### Common Issues

1. **Authentication Error**
   - Verify your `SANITY_API_TOKEN` is correct
   - Ensure the token has write permissions

2. **Project Not Found**
   - Check your `SANITY_STUDIO_PROJECT_ID` is correct
   - Verify the dataset name

3. **Schema Validation Errors**
   - Ensure the `caseStudy` schema is deployed to your Sanity project
   - Check that all required fields are included in the migration data

### Clearing Existing Data

If you need to clear existing case studies before running the migration:

```bash
# This will delete ALL case studies - use with caution!
sanity dataset delete production --force
sanity dataset create production
```

## Next Steps

After running the migration:

1. **Verify in Sanity Studio**: Check that the case studies appear in your Sanity Studio
2. **Test the Frontend**: Visit `/case-studies` on your website to see the data
3. **Customize Content**: Edit the case studies in Sanity Studio to match your actual projects
4. **Add Images**: Upload real images for featured images, client logos, and before/after shots

## Support

If you encounter issues with the migration:

1. Check the console output for specific error messages
2. Verify your environment variables are set correctly
3. Ensure your Sanity schema is up to date
4. Check that your API token has the necessary permissions
