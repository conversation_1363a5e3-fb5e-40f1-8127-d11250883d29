import React from 'react';
import { GetStaticProps, GetStaticPaths } from 'next';
import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import Header from '@/components/global/Header';
import Footer from '@/components/global/Footer';
import { getCaseStudyBySlug, getAllCaseStudies, urlFor, CaseStudy } from '@/lib/sanity-case-studies';
import { ArrowLeft, TrendingUp, Star } from 'lucide-react';

// Add proper types for Sanity content
interface SanityTextChild {
  text: string;
  _key?: string;
  _type?: string;
  marks?: string[];
}

interface SanityBlock {
  _key?: string;
  _type?: string;
  children?: SanityTextChild[];
  style?: string;
  markDefs?: unknown[];
}

interface CaseStudyPageProps {
  caseStudy: CaseStudy;
}

const CaseStudyPage: React.FC<CaseStudyPageProps> = ({ caseStudy }) => {
  if (!caseStudy) {
    return <div>Case study not found</div>;
  }

  const {
    title,
    shortDescription,
    client,
    project,
    solution,
    results,
    testimonial,
    featuredImage,
    beforeAfterImages,
    publishedAt,
    seo
  } = caseStudy;

  return (
    <>
      <Head>
        <title>{seo?.metaTitle || `${title} | VESA Solutions Case Study`}</title>
        <meta name="description" content={seo?.metaDescription || shortDescription} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        {seo?.keywords && (
          <meta name="keywords" content={seo.keywords.join(', ')} />
        )}
        
        {/* Open Graph */}
        <meta property="og:title" content={seo?.metaTitle || title} />
        <meta property="og:description" content={seo?.metaDescription || shortDescription} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={`https://vesasolutions.com/case-studies/${caseStudy.slug.current}`} />
        {(seo?.ogImage || featuredImage) && (
          <meta property="og:image" content={urlFor(seo?.ogImage || featuredImage).width(1200).height(630).url()} />
        )}
        
        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Article",
              "headline": title,
              "description": shortDescription,
              "image": featuredImage ? urlFor(featuredImage).url() : undefined,
              "datePublished": publishedAt,
              "author": {
                "@type": "Organization",
                "name": "VESA Solutions"
              },
              "publisher": {
                "@type": "Organization",
                "name": "VESA Solutions",
                "url": "https://vesasolutions.com"
              }
            })
          }}
        />
      </Head>
      
      <Header />
      
      {/* Breadcrumbs */}
      <div className="bg-gray-50 py-4">
        <div className="max-w-7xl mx-auto px-6">
          <nav className="text-sm" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              <li>
                <Link href="/" className="text-gray-500 hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li>
                <Link href="/case-studies" className="text-gray-500 hover:text-blue-600 transition-colors">
                  Case Studies
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-gray-900 font-medium" aria-current="page">
                {client.name}
              </li>
            </ol>
          </nav>
        </div>
      </div>

      <main className="min-h-screen bg-white">
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-br from-blue-50 to-blue-100">
          <div className="max-w-4xl mx-auto px-6">
            <Link 
              href="/case-studies"
              className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-8 font-semibold"
            >
              <ArrowLeft size={20} className="mr-2" />
              Back to Case Studies
            </Link>

            <div className="flex items-center mb-6">
              {client.logo && (
                <Image
                  src={urlFor(client.logo).height(60).url()}
                  alt={client.name}
                  width={80}
                  height={48}
                  className="h-12 mr-4"
                />
              )}
              <div>
                <h2 className="text-2xl font-bold text-gray-800">{client.name}</h2>
                <p className="text-gray-600">{client.industry}</p>
              </div>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              {title}
            </h1>

            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              {shortDescription}
            </p>

            <div className="flex flex-wrap gap-4 mb-8">
              <div className="bg-blue-100 px-4 py-2 rounded-full">
                <span className="text-blue-600 font-semibold">{project.serviceType}</span>
              </div>
              {project.duration && (
                <div className="bg-gray-100 px-4 py-2 rounded-full">
                  <span className="text-gray-600">{project.duration}</span>
                </div>
              )}
              <div className="bg-green-100 px-4 py-2 rounded-full">
                <span className="text-green-600 font-semibold">
                  {results.metrics[0]?.improvement || results.metrics[0]?.afterValue}
                </span>
              </div>
            </div>

            {featuredImage && (
              <Image
                src={urlFor(featuredImage).width(800).height(500).url()}
                alt={title}
                width={800}
                height={500}
                className="w-full rounded-2xl shadow-2xl"
              />
            )}
          </div>
        </section>

        {/* Key Metrics */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-6">
            <h2 className="text-3xl font-bold text-gray-800 mb-12 text-center">
              Key Results Achieved
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {results.metrics.slice(0, 3).map((metric, index) => (
                <div key={index} className="text-center p-8 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl">
                  <div className="text-4xl font-bold text-blue-600 mb-2">
                    {metric.improvement || metric.afterValue}
                  </div>
                  <div className="text-lg font-semibold text-gray-800 mb-2">
                    {metric.label}
                  </div>
                  {metric.beforeValue && (
                    <div className="text-sm text-gray-600">
                      From {metric.beforeValue}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Challenge & Solution */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
              {/* Challenge */}
              <div>
                <h2 className="text-3xl font-bold text-gray-800 mb-6">
                  The Challenge
                </h2>
                <div className="prose prose-lg text-gray-600">
                  {/* Render rich text content from Sanity - FIXED: No more 'any' type */}
                  <div dangerouslySetInnerHTML={{ 
                    __html: project.challenge.map((block: SanityBlock) => 
                      block.children?.map((child: SanityTextChild) => child.text).join('') || ''
                    ).join('<br/><br/>') 
                  }} />
                </div>
                
                {project.goals && project.goals.length > 0 && (
                  <div className="mt-8">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">
                      Project Goals:
                    </h3>
                    <ul className="space-y-2">
                      {project.goals.map((goal, index) => (
                        <li key={index} className="flex items-start">
                          <TrendingUp className="text-blue-600 mr-2 mt-1 flex-shrink-0" size={16} />
                          <span className="text-gray-600">{goal}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              {/* Solution */}
              <div>
                <h2 className="text-3xl font-bold text-gray-800 mb-6">
                  Our Solution
                </h2>
                <div className="prose prose-lg text-gray-600">
                  {/* FIXED: No more 'any' type */}
                  <div dangerouslySetInnerHTML={{ 
                    __html: solution.approach.map((block: SanityBlock) => 
                      block.children?.map((child: SanityTextChild) => child.text).join('') || ''
                    ).join('<br/><br/>') 
                  }} />
                </div>

                {solution.tools && solution.tools.length > 0 && (
                  <div className="mt-8">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">
                      Tools & Technologies:
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {solution.tools.map((tool, index) => (
                        <span key={index} className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm">
                          {tool}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* Before/After Images */}
        {beforeAfterImages && (beforeAfterImages.before || beforeAfterImages.after) && (
          <section className="py-16">
            <div className="max-w-6xl mx-auto px-6">
              <h2 className="text-3xl font-bold text-gray-800 mb-12 text-center">
                Visual Results
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {beforeAfterImages.before && (
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800 mb-4 text-center">
                      Before
                    </h3>
                    <Image
                      src={urlFor(beforeAfterImages.before).width(600).height(400).url()}
                      alt="Before"
                      width={600}
                      height={400}
                      className="w-full rounded-xl shadow-lg"
                    />
                  </div>
                )}
                
                {beforeAfterImages.after && (
                  <div>
                    <h3 className="text-xl font-semibel text-gray-800 mb-4 text-center">
                      After
                    </h3>
                    <Image
                      src={urlFor(beforeAfterImages.after).width(600).height(400).url()}
                      alt="After"
                      width={600}
                      height={400}
                      className="w-full rounded-xl shadow-lg"
                    />
                  </div>
                )}
              </div>
            </div>
          </section>
        )}

        {/* Client Testimonial */}
        {testimonial && (
          <section className="py-16 bg-blue-600">
            <div className="max-w-4xl mx-auto px-6 text-center">
              <div className="text-6xl text-blue-300 mb-6">&quot;</div>
              
              <blockquote className="text-2xl md:text-3xl text-white mb-8 leading-relaxed">
                {testimonial.quote}
              </blockquote>
              
              <div className="flex items-center justify-center">
                {testimonial.authorImage && (
                  <Image
                    src={urlFor(testimonial.authorImage).width(80).height(80).url()}
                    alt={testimonial.author}
                    width={64}
                    height={64}
                    className="w-16 h-16 rounded-full mr-4"
                  />
                )}
                <div className="text-left">
                  <div className="text-xl font-semibold text-white">
                    {testimonial.author}
                  </div>
                  <div className="text-blue-200">
                    {testimonial.position}
                  </div>
                  {testimonial.rating && (
                    <div className="flex mt-2">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} size={16} className="text-yellow-400 fill-current" />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </section>
        )}

        {/* CTA Section */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-6 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-6">
              Ready to Achieve Similar Results?
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Let&apos;s discuss how we can help your business achieve the same level of success.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact-us"
                className="bg-blue-600 text-white font-bold px-8 py-4 rounded-full hover:bg-blue-700 transition-all duration-300 transform hover:scale-105"
              >
                Start Your Success Story
              </Link>
              <Link 
                href="/case-studies"
                className="border-2 border-blue-600 text-blue-600 font-bold px-8 py-4 rounded-full hover:bg-blue-600 hover:text-white transition-all duration-300"
              >
                View More Case Studies
              </Link>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </>
  );
};

export const getStaticPaths: GetStaticPaths = async () => {
  const caseStudies = await getAllCaseStudies();
  
  const paths = caseStudies.map((caseStudy) => ({
    params: { slug: caseStudy.slug.current },
  }));

  return {
    paths,
    fallback: 'blocking',
  };
};

export const getStaticProps: GetStaticProps = async ({ params }) => {
  const slug = params?.slug as string;
  const caseStudy = await getCaseStudyBySlug(slug);

  if (!caseStudy) {
    return {
      notFound: true,
    };
  }

  return {
    props: {
      caseStudy,
    },
    revalidate: 60, // Revalidate every minute
  };
};

export default CaseStudyPage;