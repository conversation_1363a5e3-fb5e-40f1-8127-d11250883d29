import React from 'react';
import <PERSON> from 'next/link';
import { Target, Mail, Share2, <PERSON>lette, BarChart3, Star, CheckCircle } from 'lucide-react';

const DigitalMarketingServicesSection: React.FC = () => {
  const services = [
    {
      icon: Target,
      title: 'PPC Advertising',
      slug: 'ppc',
      description: 'Drive immediate results with targeted pay-per-click campaigns across Google, Bing, and social platforms. Maximize ROI with data-driven ad strategies.',
      features: ['Google Ads Management', 'Bing Ads Campaigns', 'Facebook & Instagram Ads', 'LinkedIn Advertising', 'Shopping Campaigns', 'Remarketing Strategies'],
      benefits: 'Immediate traffic, qualified leads, measurable ROI, and complete campaign transparency.',
      results: '+250% ROI'
    },
    {
      icon: Mail,
      title: 'Email Marketing',
      slug: 'email-marketing',
      description: 'Build relationships and drive conversions with strategic email campaigns and automation that nurture leads into loyal customers.',
      features: ['Email Campaign Design', 'Marketing Automation', 'List Segmentation', 'A/B Testing', 'Drip Campaigns', 'Performance Analytics'],
      benefits: 'Higher engagement rates, improved customer retention, and increased lifetime value.',
      results: '+180% Open Rate'
    },
    {
      icon: Share2,
      title: 'Social Media Marketing',
      slug: 'social-media',
      description: 'Engage your audience and build brand awareness across all major social media platforms with strategic content and community management.',
      features: ['Content Strategy', 'Community Management', 'Social Media Advertising', 'Influencer Partnerships', 'Brand Monitoring', 'Analytics & Reporting'],
      benefits: 'Increased brand awareness, higher engagement, and stronger customer relationships.',
      results: '+320% Engagement'
    },
    {
      icon: Palette,
      title: 'Branding Services',
      slug: 'branding-services',
      description: 'Build a powerful brand identity that resonates with your target audience and differentiates you from competitors across all digital channels.',
      features: ['Brand Strategy Development', 'Logo & Visual Identity', 'Brand Guidelines', 'Brand Messaging', 'Brand Positioning', 'Brand Asset Creation'],
      benefits: 'Stronger brand recognition, improved customer loyalty, and enhanced market positioning.',
      results: '+180% Brand Recognition'
    },
    {
      icon: BarChart3,
      title: 'Conversion Optimization',
      slug: 'conversion-optimization',
      description: 'Maximize your website\'s potential with data-driven conversion rate optimization strategies that turn more visitors into customers.',
      features: ['A/B Testing', 'Landing Page Optimization', 'User Experience Analysis', 'Heat Map Analysis', 'Form Optimization', 'Checkout Optimization'],
      benefits: 'Higher conversion rates, increased revenue per visitor, and better user experience.',
      results: '+150% Conversions'
    },
    {
      icon: Star,
      title: 'Reputation Management',
      slug: 'reputation-management',
      description: 'Build and protect your online reputation with strategic review management and brand monitoring across all digital platforms.',
      features: ['Review Management', 'Brand Monitoring', 'Crisis Management', 'Online PR', 'Review Generation', 'Sentiment Analysis'],
      benefits: 'Improved brand perception, higher trust scores, and better customer acquisition.',
      results: '+95% Positive Reviews'
    }
  ];

  return (
    <section className="py-20 lg:py-32 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Comprehensive Digital Marketing Services
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            VESA Solutions offers end-to-end digital marketing services designed to increase leads, boost sales, and grow your business across all digital channels.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const Icon = service.icon;
            return (
              <Link href={`/${service.slug}`} key={index}>
                <div className="bg-white border border-gray-200 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer group h-full flex flex-col">
                  <div className="flex items-start justify-between mb-6">
                    <div className="bg-purple-600 p-4 rounded-2xl group-hover:bg-purple-700 transition-colors">
                      <Icon size={32} className="text-white" />
                    </div>
                    <div className="bg-green-50 px-4 py-2 rounded-full">
                      <span className="text-green-600 font-bold text-sm">{service.results}</span>
                    </div>
                  </div>
                  
                  <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-purple-600 transition-colors">
                    {service.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-6 leading-relaxed flex-1">
                    {service.description}
                  </p>
                  
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-800 mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {service.features.slice(0, 4).map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-gray-700 text-sm">
                          <CheckCircle size={16} className="text-green-500 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                      {service.features.length > 4 && (
                        <li className="text-purple-600 text-sm font-medium">
                          +{service.features.length - 4} more features
                        </li>
                      )}
                    </ul>
                  </div>
                  
                  <div className="bg-purple-50 p-4 rounded-xl mb-6">
                    <h4 className="font-semibold text-purple-800 mb-2">Benefits:</h4>
                    <p className="text-purple-700 text-sm">{service.benefits}</p>
                  </div>
                  
                  <div className="bg-purple-600 hover:bg-purple-700 text-white font-semibold px-6 py-3 rounded-lg transition-colors w-full text-center group-hover:bg-purple-700 mt-auto">
                    Learn More About {service.title}
                  </div>
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default DigitalMarketingServicesSection;
