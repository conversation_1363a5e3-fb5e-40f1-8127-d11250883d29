import React from 'react';
import Image from 'next/image';

const DigitalMarketingResultsSection: React.FC = () => {
  return (
    <>
      {/* Case Study Section */}
      <section className="py-24 bg-purple-600">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-8">
                Real Results: SaaS Client Achieves 420% Lead Growth
              </h2>
              <p className="text-xl text-purple-100 mb-8 leading-relaxed">
                See how our comprehensive digital marketing strategy helped a B2B SaaS company dominate their market and achieve remarkable growth in qualified leads and revenue.
              </p>
              <div className="grid grid-cols-2 gap-8 mb-8">
                <div className="text-center">
                  <div className="text-4xl font-bold text-purple-300 mb-2">420%</div>
                  <div className="text-purple-100">Lead Generation Increase</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-purple-300 mb-2">350%</div>
                  <div className="text-purple-100">ROI Improvement</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-purple-300 mb-2">280%</div>
                  <div className="text-purple-100">Revenue Growth</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-purple-300 mb-2">4</div>
                  <div className="text-purple-100">Month Timeline</div>
                </div>
              </div>
              <button className="bg-white text-purple-600 font-semibold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300">
                View Full Case Study
              </button>
            </div>
            <div>
              <Image
                src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop"
                alt="Digital Marketing Success Story"
                width={600}
                height={400}
                className="w-full rounded-3xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
              What Our Clients Say About VESA Solutions
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              Don&apos;t just take our word for it. Here&apos;s what business owners say about working with our digital marketing experts.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="bg-gray-50 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="text-6xl text-blue-500 mb-4">&quot;</div>
              <p className="text-gray-700 mb-6 leading-relaxed">
                Vesa Solutions completely transformed our digital presence. Their multi-channel approach increased our leads by 420% and our conversion rates improved dramatically. Best investment we&apos;ve made in marketing.
              </p>
              <div className="flex items-center">
                <Image
                  src="https://images.unsplash.com/photo-1494790108755-2616b25c1b0c?w=60&h=60&fit=crop&crop=face"
                  alt="Sarah Johnson"
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full mr-4 object-cover"
                />
                <div>
                  <div className="font-bold text-gray-800">Sarah Johnson</div>
                  <div className="text-gray-600">CEO, TechFlow Solutions</div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="text-6xl text-blue-500 mb-4">&quot;</div>
              <p className="text-gray-700 mb-6 leading-relaxed">
                The ROI from our digital marketing investment with VESA has been incredible. Our PPC campaigns are generating 3x more qualified leads and our social media engagement has skyrocketed.
              </p>
              <div className="flex items-center">
                <Image
                  src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face"
                  alt="Michael Chen"
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full mr-4 object-cover"
                />
                <div>
                  <div className="font-bold text-gray-800">Michael Chen</div>
                  <div className="text-gray-600">Founder, GreenLeaf Consulting</div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="text-6xl text-blue-500 mb-4">&quot;</div>
              <p className="text-gray-700 mb-6 leading-relaxed">
                Their email marketing automation and social media strategy helped us build a loyal customer base. Our repeat purchases have doubled since working with VESA Solutions.
              </p>
              <div className="flex items-center">
                <Image
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face"
                  alt="David Martinez"
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full mr-4 object-cover"
                />
                <div>
                  <div className="font-bold text-gray-800">David Martinez</div>
                  <div className="text-gray-600">Owner, Martinez E-commerce</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default DigitalMarketingResultsSection;
