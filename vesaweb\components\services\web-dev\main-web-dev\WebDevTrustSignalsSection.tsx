import React from 'react';
import Image from 'next/image';
import { Star } from 'lucide-react';

const WebDevTrustSignalsSection: React.FC = () => {
  const partnerLogos = [
    { name: 'React Partner' },
    { name: 'Next.js Expert' },
    { name: 'WordPress Expert' },
    { name: 'Shopify Partner' }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6">
        {/* Partners */}
        <div className="text-center mb-12">
          <h3 className="text-lg font-semibold text-gray-600 mb-8">Certified Partners & Technology Experts</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center justify-items-center">
            {partnerLogos.map((partner, index) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-sm">
                <div className="h-12 w-full bg-gradient-to-r from-purple-100 to-purple-200 rounded flex items-center justify-center text-purple-700 font-semibold text-sm">
                  {partner.name}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Reviews */}
        <div className="border-t border-gray-200 pt-12">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">Trusted by Businesses Worldwide</h3>
            <p className="text-gray-600">See what our clients say about VESA Solutions web development services</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-white p-6 rounded-2xl shadow-lg">
                <Image src="https://via.placeholder.com/120x60/4285f4/ffffff?text=Google" alt="Google" width={120} height={60} className="mx-auto mb-4" />
                <div className="flex justify-center mb-2">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} size={20} className="text-yellow-400 fill-current" />
                  ))}
                </div>
                <div className="font-semibold text-gray-800">4.9/5 Rating</div>
                <div className="text-sm text-gray-600">150+ reviews</div>
              </div>
            </div>
            <div className="text-center">
              <div className="bg-white p-6 rounded-2xl shadow-lg">
                <Image src="https://via.placeholder.com/120x60/ff6154/ffffff?text=Clutch" alt="Clutch" width={120} height={60} className="mx-auto mb-4" />
                <div className="flex justify-center mb-2">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} size={20} className="text-yellow-400 fill-current" />
                  ))}
                </div>
                <div className="font-semibold text-gray-800">4.8/5 Rating</div>
                <div className="text-sm text-gray-600">85+ reviews</div>
              </div>
            </div>
            <div className="text-center">
              <div className="bg-white p-6 rounded-2xl shadow-lg">
                <Image src="https://via.placeholder.com/120x60/0077b5/ffffff?text=LinkedIn" alt="LinkedIn" width={120} height={60} className="mx-auto mb-4" />
                <div className="flex justify-center mb-2">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} size={20} className="text-yellow-400 fill-current" />
                  ))}
                </div>
                <div className="font-semibold text-gray-800">4.9/5 Rating</div>
                <div className="text-sm text-gray-600">60+ reviews</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WebDevTrustSignalsSection;
