// components/home/<USER>
import React, { useState } from 'react';
import Image from 'next/image';

import { Service } from '@/types';

interface DigitalMarketingServicesSectionProps {
  className?: string;
}

const DigitalMarketingServicesSection: React.FC<DigitalMarketingServicesSectionProps> = ({ className = '' }) => {
  const [activeService, setActiveService] = useState<number>(1);
  const [hoverIndex, setHoverIndex] = useState<number | null>(null);
  
  // Digital Marketing Services Data
  const services: Service[] = [
    {
      id: 0,
      name: "<PERSON>AR<PERSON> ENGINE OPTIMIZATION",
      title: "BOOST YOUR SEARCH RANKINGS WITH EXPERT SEO",
      description: [
        "Our comprehensive SEO strategies help your website rank higher in search engine results, driving more organic traffic to your business. We use proven techniques including keyword research, on-page optimization, and technical SEO audits.",
        "From local SEO to enterprise-level optimization, we create customized strategies that align with your business goals. Our data-driven approach ensures measurable results and improved online visibility.",
        "Partner with our SEO experts to dominate search results and stay ahead of your competition. We provide detailed reporting and continuous optimization to maintain your competitive edge."
      ],
      images: [
        "/homepage/seo.jpeg",
      ]
    },
    {
      id: 1,
      name: "WEBSITE DESIGN",
      title: "STUNNING WEBSITE DESIGN TO ELEVATE YOUR BRAND",
      description: [
        "Our professional web design team creates beautiful, functional websites that capture your brand's essence and convert visitors into customers. We understand that your website is often the first interaction potential customers have with your brand.",
        "We take a comprehensive approach to web design, considering everything from user experience and interface design to search engine optimization. Our designs are responsive across all devices, from desktop to mobile.",
        "At Vesa Solutions, we don't just design websites - we create experiences. By incorporating the latest design trends and technologies, we ensure your site stays ahead of the competition."
      ],
      images: [
        "/homepage/web design.webp",
      ]
    },
    {
      id: 2,
      name: "SOCIAL MEDIA MARKETING",
      title: "AMPLIFY YOUR BRAND THROUGH SOCIAL MEDIA",
      description: [
        "Our social media marketing strategies help you connect with your audience across all major platforms. We create engaging content that builds brand awareness and drives meaningful engagement with your customers.",
        "From content creation to community management, we handle every aspect of your social media presence. Our data-driven approach ensures your campaigns reach the right audience at the right time.",
        "Transform your social media channels into powerful marketing tools that generate leads and build lasting customer relationships. We provide comprehensive analytics and optimization for maximum ROI."
      ],
      images: [
        "/homepage/social media marketing.jpg",
      ]
    },
    {
      id: 3,
      name: "PAY PER CLICK - PPC",
      title: "MAXIMIZE ROI WITH TARGETED PPC CAMPAIGNS",
      description: [
        "Our PPC experts create and manage high-converting ad campaigns across Google Ads, Facebook, and other platforms. We focus on maximizing your return on investment through strategic targeting and optimization.",
        "From keyword research to ad copy creation, we handle every aspect of your PPC campaigns. Our continuous monitoring and optimization ensure your ads perform at their peak potential.",
        "Drive immediate traffic and conversions with our data-driven PPC strategies. We provide transparent reporting and regular campaign optimization to ensure your advertising budget delivers maximum results."
      ],
      images: [
        "/homepage/ppc.jpg",
      ]
    },
    {
      id: 4,
      name: "CONTENT WRITING",
      title: "ENGAGING CONTENT THAT CONVERTS READERS",
      description: [
        "Our expert content writers create compelling, SEO-optimized content that engages your audience and drives action. From blog posts to website copy, we craft content that tells your brand story effectively.",
        "Every piece of content is strategically designed to support your marketing goals, whether that's building brand awareness, generating leads, or improving search rankings. We maintain your brand voice across all content.",
        "Transform your content marketing with professionally written material that resonates with your target audience. Our content drives engagement, builds trust, and converts readers into customers."
      ],
      images: [
        "/homepage/content writing.avif",
      ]
    },
    {
      id: 5,
      name: "CONVERSION OPTIMIZATION",
      title: "OPTIMIZE YOUR FUNNEL FOR MAXIMUM CONVERSIONS",
      description: [
        "Our conversion optimization specialists analyze your website and marketing funnels to identify opportunities for improvement. We use A/B testing and data analysis to increase your conversion rates.",
        "From landing page optimization to checkout process improvement, we focus on every touchpoint in your customer journey. Our systematic approach ensures sustainable growth in your conversion rates.",
        "Transform more visitors into customers with our proven conversion optimization strategies. We provide detailed insights and continuous testing to maximize the value of your existing traffic."
      ],
      images: [
        "/homepage/conversion optimzation.webp",
      ]
    }
  ];

  const currentService = services[activeService];

  // Digital Marketing Handlers
  const handleServiceClick = (index: number): void => {
    setActiveService(index);
  };

  return (
    <div className={`relative min-h-screen bg-cover bg-center bg-no-repeat overflow-hidden ${className}`} 
         style={{backgroundImage: 'url(https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=1920&h=1080&fit=crop)'}}>
      {/* Light Blue Overlay */}
      <div className="absolute inset-0 bg-blue-900/70 backdrop-blur-[1px]"></div>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 border border-white/20 rounded-lg transform rotate-12"></div>
        <div className="absolute top-40 right-20 w-24 h-24 border border-white/20 rounded-lg transform -rotate-12"></div>
        <div className="absolute bottom-32 left-32 w-16 h-16 border border-white/20 rounded-lg transform rotate-45"></div>
      </div>

      <div className="relative z-10 h-full">
        {/* Title Section */}
        <div className="text-center py-8 sm:py-12 lg:py-16 px-4 sm:px-8">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-2 sm:mb-4 tracking-wide">
            TAILORED DIGITAL STRATEGY FOR SUCCESS
          </h2>
          <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-blue-100 font-light tracking-wide">
            BUILDING A DIGITAL PRESENCE STRATEGY TAILORED TO YOUR BUSINESS
          </p>
        </div>

        {/* Mobile Layout */}
        <div className="lg:hidden">
          {/* Mobile Services Menu */}
          <div className="px-4 mb-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {services.map((service, index) => (
                  <button
                    key={service.id}
                    onClick={() => handleServiceClick(index)}
                    className={`p-3 rounded-xl text-left transition-all duration-300 ${
                      activeService === index
                        ? 'bg-white text-blue-900 shadow-lg transform scale-105'
                        : 'bg-white/10 text-white hover:bg-white/20'
                    }`}
                  >
                    <h3 className="font-semibold text-sm leading-tight">
                      {service.name}
                    </h3>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Mobile Content */}
          <div className="px-4 pb-8">
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-2xl">
              {/* Title */}
              <h2 className="text-xl sm:text-2xl font-bold text-blue-900 text-center mb-4 leading-tight">
                {currentService.title}
              </h2>

              {/* Single Image */}
              <div className="relative mb-6">
                <div className="relative h-48 sm:h-56 rounded-lg overflow-hidden">
                  <Image
                    src={currentService.images[0]}
                    alt={`${currentService.name} example`}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>

              {/* Description */}
              <div className="space-y-3 text-gray-700 leading-relaxed text-sm">
                {currentService.description.map((paragraph, index) => (
                  <p key={index} className="transition-all duration-700 ease-out"
                     style={{ transitionDelay: `${index * 100}ms` }}>
                    {paragraph}
                  </p>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Desktop Layout - Unchanged */}
        <div className="hidden lg:grid lg:grid-cols-2 gap-0 h-screen">
          {/* Left Column - Services Menu */}
          <div className="relative flex items-center justify-end p-8 lg:p-16">
            <div className="w-full max-w-2xl space-y-8 text-right">
              {services.map((service, index) => (
                <div 
                  key={service.id} 
                  className="relative h-20 flex items-center justify-end"
                  onMouseEnter={() => setHoverIndex(index)}
                  onMouseLeave={() => setHoverIndex(null)}
                >
                  {/* White line under text only */}
                  <div className="absolute bottom-4 left-0 right-0 flex items-center z-0">
                    <div 
                      className={`h-0.5 transition-all duration-700 ease-out ml-auto ${
                        activeService === index ? 'bg-white' : 'bg-transparent'
                      }`}
                      style={{
                        width: activeService === index ? '100%' : '0%'
                      }}
                    />
                  </div>
                  
                  <div 
                    onClick={() => handleServiceClick(index)}
                    className={`cursor-pointer transition-all duration-300 p-6 block relative z-20 ${
                      activeService === index
                        ? "text-white transform -translate-x-4" 
                        : "text-blue-200 hover:text-white"
                    }`}
                  >
                    <div className="relative overflow-hidden">
                      <h3 className={`font-semibold text-xl lg:text-2xl xl:text-3xl tracking-wide transition-colors duration-200 ${
                        activeService === index ? 'text-white' : 
                        hoverIndex === index ? 'text-white' : 'text-blue-200'
                      }`}>
                        {service.name}
                      </h3>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Column - Full White Content Area */}
          <div className="relative pl-6 pb-12">
            <div className="h-full bg-white/95 backdrop-blur-sm shadow-2xl p-4 sm:p-6 lg:p-8 xl:p-12 relative overflow-hidden rounded-l-[2rem]">
              {/* Fixed Title Area - More Responsive */}
              <div className="absolute top-4 sm:top-6 lg:top-8 xl:top-12 left-4 right-4 sm:left-6 sm:right-6 lg:left-8 lg:right-8 xl:left-12 xl:right-12 h-16 sm:h-18 lg:h-20 xl:h-24 flex items-center justify-center">
                <h2 className={`text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-3xl 2xl:text-4xl font-bold text-blue-900 text-center leading-tight transition-opacity duration-500 ${activeService !== null ? 'opacity-100' : 'opacity-0'}`}>
                  {currentService.title}
                </h2>
              </div>
              
              {/* Fixed Description Area - More Responsive */}
              <div className="absolute top-24 sm:top-28 lg:top-32 xl:top-40 left-4 right-4 sm:left-6 sm:right-6 lg:left-8 lg:right-8 xl:left-12 xl:right-12 h-48 sm:h-52 md:h-56 lg:h-60 xl:h-64 overflow-hidden">
                <div className={`space-y-3 sm:space-y-4 lg:space-y-5 xl:space-y-6 text-gray-700 leading-relaxed text-sm sm:text-base lg:text-lg text-center transition-opacity duration-500 ${activeService !== null ? 'opacity-100' : 'opacity-0'}`}>
                  {currentService.description.map((paragraph, index) => (
                    <p key={index} className="transition-all duration-700 ease-out"
                       style={{ transitionDelay: `${index * 100}ms` }}>
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>

              {/* Fixed Single Image Area - More Responsive */}
              <div className="absolute bottom-4 sm:bottom-6 lg:bottom-8 xl:bottom-12 left-4 right-4 sm:left-6 sm:right-6 lg:left-8 lg:right-8 xl:left-12 xl:right-12">
                <div className={`relative transition-opacity duration-500 ${activeService !== null ? 'opacity-100' : 'opacity-0'}`}>
                  <div className="relative h-48 sm:h-56 md:h-64 lg:h-72 xl:h-80 rounded-lg overflow-hidden">
                    <Image
                      src={currentService.images[0]}
                      alt={`${currentService.name} example`}
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DigitalMarketingServicesSection;