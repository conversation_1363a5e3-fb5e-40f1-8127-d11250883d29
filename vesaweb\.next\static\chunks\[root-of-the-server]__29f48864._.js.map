{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\r\n\r\ntype SendMessage = (msg: any) => void;\r\nexport type WebSocketMessage =\r\n  | {\r\n      type: \"turbopack-connected\";\r\n    }\r\n  | {\r\n      type: \"turbopack-message\";\r\n      data: Record<string, any>;\r\n    };\r\n\r\n\r\nexport type ClientOptions = {\r\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void;\r\n  sendMessage: SendMessage;\r\n  onUpdateError: (err: unknown) => void;\r\n};\r\n\r\nexport function connect({\r\n  addMessageListener,\r\n  sendMessage,\r\n  onUpdateError = console.error,\r\n}: ClientOptions) {\r\n  addMessageListener((msg) => {\r\n    switch (msg.type) {\r\n      case \"turbopack-connected\":\r\n        handleSocketConnected(sendMessage);\r\n        break;\r\n      default:\r\n        try {\r\n          if (Array.isArray(msg.data)) {\r\n            for (let i = 0; i < msg.data.length; i++) {\r\n              handleSocketMessage(msg.data[i] as ServerMessage);\r\n            }\r\n          } else {\r\n            handleSocketMessage(msg.data as ServerMessage);\r\n          }\r\n          applyAggregatedUpdates();\r\n        } catch (e: unknown) {\r\n          console.warn(\r\n            \"[Fast Refresh] performing full reload\\n\\n\" +\r\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\r\n              \"You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n\" +\r\n              \"Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n\" +\r\n              \"It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n\" +\r\n              \"Fast Refresh requires at least one parent function component in your React tree.\"\r\n          );\r\n          onUpdateError(e);\r\n          location.reload();\r\n        }\r\n        break;\r\n    }\r\n  });\r\n\r\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS;\r\n  if (queued != null && !Array.isArray(queued)) {\r\n    throw new Error(\"A separate HMR handler was already registered\");\r\n  }\r\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\r\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    },\r\n  };\r\n\r\n  if (Array.isArray(queued)) {\r\n    for (const [chunkPath, callback] of queued) {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    }\r\n  }\r\n}\r\n\r\ntype UpdateCallbackSet = {\r\n  callbacks: Set<UpdateCallback>;\r\n  unsubscribe: () => void;\r\n};\r\n\r\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map();\r\n\r\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\r\n  sendMessage(JSON.stringify(message));\r\n}\r\n\r\ntype ResourceKey = string;\r\n\r\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\r\n  return JSON.stringify({\r\n    path: resource.path,\r\n    headers: resource.headers || null,\r\n  });\r\n}\r\n\r\nfunction subscribeToUpdates(\r\n  sendMessage: SendMessage,\r\n  resource: ResourceIdentifier\r\n): () => void {\r\n  sendJSON(sendMessage, {\r\n    type: \"turbopack-subscribe\",\r\n    ...resource,\r\n  });\r\n\r\n  return () => {\r\n    sendJSON(sendMessage, {\r\n      type: \"turbopack-unsubscribe\",\r\n      ...resource,\r\n    });\r\n  };\r\n}\r\n\r\nfunction handleSocketConnected(sendMessage: SendMessage) {\r\n  for (const key of updateCallbackSets.keys()) {\r\n    subscribeToUpdates(sendMessage, JSON.parse(key));\r\n  }\r\n}\r\n\r\n// we aggregate all pending updates until the issues are resolved\r\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\r\n  new Map();\r\n\r\nfunction aggregateUpdates(msg: PartialServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  let aggregated = chunkListsWithPendingUpdates.get(key);\r\n\r\n  if (aggregated) {\r\n    aggregated.instruction = mergeChunkListUpdates(\r\n      aggregated.instruction,\r\n      msg.instruction\r\n    );\r\n  } else {\r\n    chunkListsWithPendingUpdates.set(key, msg);\r\n  }\r\n}\r\n\r\nfunction applyAggregatedUpdates() {\r\n  if (chunkListsWithPendingUpdates.size === 0) return;\r\n  hooks.beforeRefresh();\r\n  for (const msg of chunkListsWithPendingUpdates.values()) {\r\n    triggerUpdate(msg);\r\n  }\r\n  chunkListsWithPendingUpdates.clear();\r\n  finalizeUpdate();\r\n}\r\n\r\nfunction mergeChunkListUpdates(\r\n  updateA: ChunkListUpdate,\r\n  updateB: ChunkListUpdate\r\n): ChunkListUpdate {\r\n  let chunks;\r\n  if (updateA.chunks != null) {\r\n    if (updateB.chunks == null) {\r\n      chunks = updateA.chunks;\r\n    } else {\r\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks);\r\n    }\r\n  } else if (updateB.chunks != null) {\r\n    chunks = updateB.chunks;\r\n  }\r\n\r\n  let merged;\r\n  if (updateA.merged != null) {\r\n    if (updateB.merged == null) {\r\n      merged = updateA.merged;\r\n    } else {\r\n      // Since `merged` is an array of updates, we need to merge them all into\r\n      // one, consistent update.\r\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\r\n      // no need to key on the `type` field.\r\n      let update = updateA.merged[0];\r\n      for (let i = 1; i < updateA.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateA.merged[i]\r\n        );\r\n      }\r\n\r\n      for (let i = 0; i < updateB.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateB.merged[i]\r\n        );\r\n      }\r\n\r\n      merged = [update];\r\n    }\r\n  } else if (updateB.merged != null) {\r\n    merged = updateB.merged;\r\n  }\r\n\r\n  return {\r\n    type: \"ChunkListUpdate\",\r\n    chunks,\r\n    merged,\r\n  };\r\n}\r\n\r\nfunction mergeChunkListChunks(\r\n  chunksA: Record<ChunkPath, ChunkUpdate>,\r\n  chunksB: Record<ChunkPath, ChunkUpdate>\r\n): Record<ChunkPath, ChunkUpdate> {\r\n  const chunks: Record<ChunkPath, ChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB);\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeChunkUpdates(\r\n  updateA: ChunkUpdate,\r\n  updateB: ChunkUpdate\r\n): ChunkUpdate | undefined {\r\n  if (\r\n    (updateA.type === \"added\" && updateB.type === \"deleted\") ||\r\n    (updateA.type === \"deleted\" && updateB.type === \"added\")\r\n  ) {\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"partial\") {\r\n    invariant(updateA.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  if (updateB.type === \"partial\") {\r\n    invariant(updateB.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction mergeChunkListEcmascriptMergedUpdates(\r\n  mergedA: EcmascriptMergedUpdate,\r\n  mergedB: EcmascriptMergedUpdate\r\n): EcmascriptMergedUpdate {\r\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries);\r\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks);\r\n\r\n  return {\r\n    type: \"EcmascriptMergedUpdate\",\r\n    entries,\r\n    chunks,\r\n  };\r\n}\r\n\r\nfunction mergeEcmascriptChunkEntries(\r\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\r\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\r\n): Record<ModuleId, EcmascriptModuleEntry> {\r\n  return { ...entriesA, ...entriesB };\r\n}\r\n\r\nfunction mergeEcmascriptChunksUpdates(\r\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\r\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\r\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\r\n  if (chunksA == null) {\r\n    return chunksB;\r\n  }\r\n\r\n  if (chunksB == null) {\r\n    return chunksA;\r\n  }\r\n\r\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\r\n        chunkUpdateA,\r\n        chunkUpdateB\r\n      );\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  if (Object.keys(chunks).length === 0) {\r\n    return undefined;\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeEcmascriptChunkUpdates(\r\n  updateA: EcmascriptMergedChunkUpdate,\r\n  updateB: EcmascriptMergedChunkUpdate\r\n): EcmascriptMergedChunkUpdate | undefined {\r\n  if (updateA.type === \"added\" && updateB.type === \"deleted\") {\r\n    // These two completely cancel each other out.\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"deleted\" && updateB.type === \"added\") {\r\n    const added = [];\r\n    const deleted = [];\r\n    const deletedModules = new Set(updateA.modules ?? []);\r\n    const addedModules = new Set(updateB.modules ?? []);\r\n\r\n    for (const moduleId of addedModules) {\r\n      if (!deletedModules.has(moduleId)) {\r\n        added.push(moduleId);\r\n      }\r\n    }\r\n\r\n    for (const moduleId of deletedModules) {\r\n      if (!addedModules.has(moduleId)) {\r\n        deleted.push(moduleId);\r\n      }\r\n    }\r\n\r\n    if (added.length === 0 && deleted.length === 0) {\r\n      return undefined;\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added,\r\n      deleted,\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"partial\") {\r\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])]);\r\n    const deleted = new Set([\r\n      ...(updateA.deleted ?? []),\r\n      ...(updateB.deleted ?? []),\r\n    ]);\r\n\r\n    if (updateB.added != null) {\r\n      for (const moduleId of updateB.added) {\r\n        deleted.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    if (updateB.deleted != null) {\r\n      for (const moduleId of updateB.deleted) {\r\n        added.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added: [...added],\r\n      deleted: [...deleted],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"added\" && updateB.type === \"partial\") {\r\n    const modules = new Set([\r\n      ...(updateA.modules ?? []),\r\n      ...(updateB.added ?? []),\r\n    ]);\r\n\r\n    for (const moduleId of updateB.deleted ?? []) {\r\n      modules.delete(moduleId);\r\n    }\r\n\r\n    return {\r\n      type: \"added\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"deleted\") {\r\n    // We could eagerly return `updateB` here, but this would potentially be\r\n    // incorrect if `updateA` has added modules.\r\n\r\n    const modules = new Set(updateB.modules ?? []);\r\n\r\n    if (updateA.added != null) {\r\n      for (const moduleId of updateA.added) {\r\n        modules.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"deleted\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  // Any other update combination is invalid.\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction invariant(_: never, message: string): never {\r\n  throw new Error(`Invariant: ${message}`);\r\n}\r\n\r\nconst CRITICAL = [\"bug\", \"error\", \"fatal\"];\r\n\r\nfunction compareByList(list: any[], a: any, b: any) {\r\n  const aI = list.indexOf(a) + 1 || list.length;\r\n  const bI = list.indexOf(b) + 1 || list.length;\r\n  return aI - bI;\r\n}\r\n\r\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map();\r\n\r\nfunction emitIssues() {\r\n  const issues = [];\r\n  const deduplicationSet = new Set();\r\n\r\n  for (const [_, chunkIssues] of chunksWithIssues) {\r\n    for (const chunkIssue of chunkIssues) {\r\n      if (deduplicationSet.has(chunkIssue.formatted)) continue;\r\n\r\n      issues.push(chunkIssue);\r\n      deduplicationSet.add(chunkIssue.formatted);\r\n    }\r\n  }\r\n\r\n  sortIssues(issues);\r\n\r\n  hooks.issues(issues);\r\n}\r\n\r\nfunction handleIssues(msg: ServerMessage): boolean {\r\n  const key = resourceKey(msg.resource);\r\n  let hasCriticalIssues = false;\r\n\r\n  for (const issue of msg.issues) {\r\n    if (CRITICAL.includes(issue.severity)) {\r\n      hasCriticalIssues = true;\r\n    }\r\n  }\r\n\r\n  if (msg.issues.length > 0) {\r\n    chunksWithIssues.set(key, msg.issues);\r\n  } else if (chunksWithIssues.has(key)) {\r\n    chunksWithIssues.delete(key);\r\n  }\r\n\r\n  emitIssues();\r\n\r\n  return hasCriticalIssues;\r\n}\r\n\r\nconst SEVERITY_ORDER = [\"bug\", \"fatal\", \"error\", \"warning\", \"info\", \"log\"];\r\nconst CATEGORY_ORDER = [\r\n  \"parse\",\r\n  \"resolve\",\r\n  \"code generation\",\r\n  \"rendering\",\r\n  \"typescript\",\r\n  \"other\",\r\n];\r\n\r\nfunction sortIssues(issues: Issue[]) {\r\n  issues.sort((a, b) => {\r\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity);\r\n    if (first !== 0) return first;\r\n    return compareByList(CATEGORY_ORDER, a.category, b.category);\r\n  });\r\n}\r\n\r\nconst hooks = {\r\n  beforeRefresh: () => {},\r\n  refresh: () => {},\r\n  buildOk: () => {},\r\n  issues: (_issues: Issue[]) => {},\r\n};\r\n\r\nexport function setHooks(newHooks: typeof hooks) {\r\n  Object.assign(hooks, newHooks);\r\n}\r\n\r\nfunction handleSocketMessage(msg: ServerMessage) {\r\n  sortIssues(msg.issues);\r\n\r\n  handleIssues(msg);\r\n\r\n  switch (msg.type) {\r\n    case \"issues\":\r\n      // issues are already handled\r\n      break;\r\n    case \"partial\":\r\n      // aggregate updates\r\n      aggregateUpdates(msg);\r\n      break;\r\n    default:\r\n      // run single update\r\n      const runHooks = chunkListsWithPendingUpdates.size === 0;\r\n      if (runHooks) hooks.beforeRefresh();\r\n      triggerUpdate(msg);\r\n      if (runHooks) finalizeUpdate();\r\n      break;\r\n  }\r\n}\r\n\r\nfunction finalizeUpdate() {\r\n  hooks.refresh();\r\n  hooks.buildOk();\r\n\r\n  // This is used by the Next.js integration test suite to notify it when HMR\r\n  // updates have been completed.\r\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\r\n  if (globalThis.__NEXT_HMR_CB) {\r\n    globalThis.__NEXT_HMR_CB();\r\n    globalThis.__NEXT_HMR_CB = null;\r\n  }\r\n}\r\n\r\nfunction subscribeToChunkUpdate(\r\n  chunkListPath: ChunkListPath,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n): () => void {\r\n  return subscribeToUpdate(\r\n    {\r\n      path: chunkListPath,\r\n    },\r\n    sendMessage,\r\n    callback\r\n  );\r\n}\r\n\r\nexport function subscribeToUpdate(\r\n  resource: ResourceIdentifier,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n) {\r\n  const key = resourceKey(resource);\r\n  let callbackSet: UpdateCallbackSet;\r\n  const existingCallbackSet = updateCallbackSets.get(key);\r\n  if (!existingCallbackSet) {\r\n    callbackSet = {\r\n      callbacks: new Set([callback]),\r\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\r\n    };\r\n    updateCallbackSets.set(key, callbackSet);\r\n  } else {\r\n    existingCallbackSet.callbacks.add(callback);\r\n    callbackSet = existingCallbackSet;\r\n  }\r\n\r\n  return () => {\r\n    callbackSet.callbacks.delete(callback);\r\n\r\n    if (callbackSet.callbacks.size === 0) {\r\n      callbackSet.unsubscribe();\r\n      updateCallbackSets.delete(key);\r\n    }\r\n  };\r\n}\r\n\r\nfunction triggerUpdate(msg: ServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  const callbackSet = updateCallbackSets.get(key);\r\n  if (!callbackSet) {\r\n    return;\r\n  }\r\n\r\n  for (const callback of callbackSet.callbacks) {\r\n    callback(msg);\r\n  }\r\n\r\n  if (msg.type === \"notFound\") {\r\n    // This indicates that the resource which we subscribed to either does not exist or\r\n    // has been deleted. In either case, we should clear all update callbacks, so if a\r\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\r\n    // message to the server.\r\n    // No need to send an \"unsubscribe\" message to the server, it will have already\r\n    // dropped the update stream before sending the \"notFound\" message.\r\n    updateCallbackSets.delete(key);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAmBtD,SAAS,QAAQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf;IACd,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM,CAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;QAClB,MAAM,iBAAiB,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QACpD,MAAM,eAAe,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,MAAM,QAAQ,IAAI,IAAI;eAAK,QAAQ,KAAK,IAAI,EAAE;eAAO,QAAQ,KAAK,IAAI,EAAE;SAAE;QAC1E,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,OAAO,IAAI,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,KAAK,IAAI,EAAE;SACxB;QAED,KAAK,MAAM,YAAY,QAAQ,OAAO,IAAI,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS;AACzC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { ChevronDown } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\n\r\ninterface HeaderProps {\r\n  isVisible?: boolean;\r\n}\r\n\r\nconst Header: React.FC<HeaderProps> = ({ isVisible = true }) => {\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);\r\n  const [seoDropdownOpen, setSeoDropdownOpen] = useState<boolean>(false);\r\n  const [webDevDropdownOpen, setWebDevDropdownOpen] = useState<boolean>(false);\r\n  const [digitalMarketingDropdownOpen, setDigitalMarketingDropdownOpen] = useState<boolean>(false);\r\n  const [aboutDropdownOpen, setAboutDropdownOpen] = useState<boolean>(false);\r\n\r\n  const [mounted, setMounted] = useState<boolean>(false);\r\n\r\n  // Ensure component is hydrated before rendering state-dependent UI\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Handle body scroll when mobile menu is open\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) {\r\n      document.body.style.overflow = 'hidden';\r\n    } else {\r\n      document.body.style.overflow = 'unset';\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [mobileMenuOpen]);\r\n\r\n  // Close mobile menu on escape key\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (e.key === 'Escape' && mobileMenuOpen) {\r\n        setMobileMenuOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleEscape);\r\n    return () => document.removeEventListener('keydown', handleEscape);\r\n  }, [mobileMenuOpen]);\r\n\r\n  // SEO Sub-services\r\n  const seoSubServices = [\r\n    { name: 'On-Page SEO Optimization', href: '/on-page-seo', description: 'Optimize content and structure' },\r\n    { name: 'Off-Page SEO & Link Building', href: '/off-page-seo', description: 'Build authority and backlinks' },\r\n    { name: 'Technical SEO Services', href: '/technical-seo', description: 'Optimize technical performance' },\r\n    { name: 'Local SEO Marketing', href: '/local-seo', description: 'Dominate local search results' },\r\n    { name: 'Content Writing', href: '/content-writing', description: 'SEO-optimized content creation' },\r\n    { name: 'SEO Analytics', href: '/seo-analytics', description: 'Comprehensive SEO analysis' }\r\n  ];\r\n\r\n  // Web Development Sub-services\r\n  const webDevSubServices = [\r\n    { name: 'Custom Website Development', href: '/custom-website-development', description: 'Unique, tailored websites' },\r\n    { name: 'E-commerce Development', href: '/ecommerce-development', description: 'Online store solutions' },\r\n    { name: 'Mobile App Development', href: '/mobile-app-development', description: 'iOS & Android apps' },\r\n    { name: 'Website Speed Optimization', href: '/website-speed-optimization', description: 'Lightning-fast websites' },\r\n    { name: 'Web Application Development', href: '/web-application-development', description: 'Custom web applications' },\r\n    { name: 'Website Maintenance & Support', href: '/website-maintenance-support', description: '24/7 website care' }\r\n  ];\r\n\r\n  // Digital Marketing Sub-services\r\n  const digitalMarketingSubServices = [\r\n    { name: 'PPC Advertising', href: '/ppc', description: 'Targeted pay-per-click campaigns' },\r\n    { name: 'Email Marketing', href: '/email-marketing', description: 'Automated campaigns that convert' },\r\n    { name: 'Social Media Marketing', href: '/social-media', description: 'Engage your audience effectively' },\r\n    { name: 'Branding Services', href: '/branding-services', description: 'Build powerful brand identity' },\r\n    { name: 'Conversion Optimization', href: '/conversion-optimization', description: 'Turn visitors into customers' },\r\n    { name: 'Reputation Management', href: '/reputation-management', description: 'Protect and enhance your brand' }\r\n  ];\r\n\r\n  // About Sub-services\r\n  const aboutSubServices = [\r\n    { name: 'Case Studies', href: '/case-studies', description: 'View our successful projects' },\r\n    { name: 'Blog', href: '/blog', description: 'Latest insights and industry news' }\r\n  ];\r\n\r\n  const toggleMobileMenu = () => {\r\n    setMobileMenuOpen(!mobileMenuOpen);\r\n  };\r\n\r\n  const closeMobileMenu = () => {\r\n    setMobileMenuOpen(false);\r\n    // Reset all dropdown states when closing mobile menu\r\n    setSeoDropdownOpen(false);\r\n    setWebDevDropdownOpen(false);\r\n    setDigitalMarketingDropdownOpen(false);\r\n    setAboutDropdownOpen(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <header className={`w-full bg-white/95 backdrop-blur-md border-b border-gray-100 z-50 transition-all duration-500 ease-in-out ${\r\n        isVisible \r\n          ? 'sticky top-0 translate-y-0 opacity-100' \r\n          : 'fixed top-0 -translate-y-full opacity-0 pointer-events-none'\r\n      }`}>\r\n        <div className=\"max-w-[1440px] mx-auto px-4 sm:px-6 py-3 sm:py-4\">\r\n          <div className=\"flex justify-between items-center\">\r\n            {/* Logo */}\r\n            <div className=\"flex items-center group cursor-pointer\">\r\n              <div className=\"relative\">\r\n                <Link href=\"/\">\r\n                  <Image \r\n                    src=\"/VesaLogo.svg\" \r\n                    alt=\"VESA Solutions Logo\" \r\n                    width={94}\r\n                    height={40}\r\n                    priority\r\n                    className=\"transition-transform duration-300 group-hover:scale-105 w-20 h-auto sm:w-[94px]\"\r\n                  />\r\n                </Link>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Desktop Navigation */}\r\n            <nav className=\"hidden lg:flex items-center space-x-12\">\r\n              {/* SEO Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setSeoDropdownOpen(true)}\r\n                onMouseLeave={() => setSeoDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/seo-search-engine-optimization\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-green-600 transition-all duration-300 group\">\r\n                  SEO\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-green-500 to-green-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  seoDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">SEO Services</div>\r\n                      {seoSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100/50 transition-all duration-200 ease-out border border-transparent hover:border-green-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-green-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Web Development Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setWebDevDropdownOpen(true)}\r\n                onMouseLeave={() => setWebDevDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/web-development\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group\">\r\n                  WEB DEVELOPMENT\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  webDevDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">Web Development</div>\r\n                      {webDevSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Digital Marketing Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setDigitalMarketingDropdownOpen(true)}\r\n                onMouseLeave={() => setDigitalMarketingDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/digital-marketing\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-purple-600 transition-all duration-300 group\">\r\n                  DIGITAL MARKETING\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-purple-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  digitalMarketingDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">Digital Marketing</div>\r\n                      {digitalMarketingSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50/50 transition-all duration-200 ease-out border border-transparent hover:border-indigo-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-purple-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-purple-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Services */}\r\n              <Link href=\"/services\" className=\"relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300\">\r\n                SERVICES\r\n                <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n              </Link>\r\n\r\n              {/* About Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setAboutDropdownOpen(true)}\r\n                onMouseLeave={() => setAboutDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/about\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group\">\r\n                  ABOUT\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  aboutDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">About</div>\r\n                      {aboutSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Contact */}\r\n              <Link href=\"/contact-us\" className=\"relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300\">\r\n                CONTACT\r\n                <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n              </Link>\r\n            </nav>\r\n\r\n\r\n            {/* CTA Button and Mobile Menu */}\r\n            <div className=\"flex items-center space-x-3 sm:space-x-4\">\r\n              {/* Desktop CTA */}\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                className=\"hidden sm:block group relative bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-sm font-semibold px-6 lg:px-8 py-2.5 lg:py-3 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-blue-500/25\"\r\n              >\r\n                <span className=\"relative z-10\">GET FREE PROPOSAL</span>\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n              </Link>\r\n\r\n              {/* Mobile Menu Button */}\r\n              <button\r\n                className=\"lg:hidden relative p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-all duration-300 group\"\r\n                onClick={toggleMobileMenu}\r\n                aria-label=\"Toggle mobile menu\"\r\n              >\r\n                <div className=\"w-6 h-6 relative\">\r\n                  <span className={`absolute top-1 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${\r\n                    mobileMenuOpen ? 'rotate-45 translate-y-2' : ''\r\n                  }`}></span>\r\n                  <span className={`absolute top-2.5 left-0 w-6 h-0.5 bg-current transition-all duration-300 ${\r\n                    mobileMenuOpen ? 'opacity-0' : ''\r\n                  }`}></span>\r\n                  <span className={`absolute top-4 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${\r\n                    mobileMenuOpen ? '-rotate-45 -translate-y-2' : ''\r\n                  }`}></span>\r\n                </div>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Mobile Menu */}\r\n      {mounted && mobileMenuOpen && (\r\n        <div className=\"lg:hidden fixed inset-0 z-40 bg-white overflow-y-auto\">\r\n          {/* Mobile Header */}\r\n          <div className=\"flex justify-between items-center p-4 border-b border-gray-200\">\r\n            <Link href=\"/\" onClick={closeMobileMenu}>\r\n              <Image\r\n                src=\"/VesaLogo.svg\"\r\n                alt=\"VESA Solutions Logo\"\r\n                width={80}\r\n                height={34}\r\n                priority\r\n                className=\"w-20 h-auto\"\r\n              />\r\n            </Link>\r\n            <button\r\n              onClick={closeMobileMenu}\r\n              className=\"p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-colors\"\r\n              aria-label=\"Close mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 relative\">\r\n                <span className=\"absolute top-2.5 left-0 w-6 h-0.5 bg-current transform rotate-45\"></span>\r\n                <span className=\"absolute top-2.5 left-0 w-6 h-0.5 bg-current transform -rotate-45\"></span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Mobile Navigation */}\r\n          <div className=\"p-4 space-y-6\">\r\n            {/* SEO Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setSeoDropdownOpen(!seoDropdownOpen)}\r\n              >\r\n                <Link href=\"/seo-search-engine-optimization\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-green-600 transition-colors\">\r\n                  SEO\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {seoDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {seoSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-green-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Web Development Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setWebDevDropdownOpen(!webDevDropdownOpen)}\r\n              >\r\n                <Link href=\"/web-development\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\r\n                  WEB DEVELOPMENT\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {webDevDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {webDevSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-blue-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Digital Marketing Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setDigitalMarketingDropdownOpen(!digitalMarketingDropdownOpen)}\r\n              >\r\n                <Link href=\"/digital-marketing\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-purple-600 transition-colors\">\r\n                  DIGITAL MARKETING\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {digitalMarketingDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {digitalMarketingSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-purple-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Services */}\r\n            <div>\r\n              <Link\r\n                href=\"/services\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\"\r\n              >\r\n                SERVICES\r\n              </Link>\r\n            </div>\r\n\r\n            {/* About Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setAboutDropdownOpen(!aboutDropdownOpen)}\r\n              >\r\n                <Link href=\"/about\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\r\n                  ABOUT\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {aboutDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {aboutSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-blue-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Contact */}\r\n            <div>\r\n              <Link\r\n                href=\"/contact-us\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\"\r\n              >\r\n                CONTACT\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Mobile CTA Button */}\r\n            <div className=\"pt-4\">\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block w-full text-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-6 py-4 rounded-full transition-all duration-300 transform hover:scale-105\"\r\n              >\r\n                GET FREE PROPOSAL\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWA,MAAM,SAAgC,CAAC,EAAE,YAAY,IAAI,EAAE;;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAC1F,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAEpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,mEAAmE;IACnE,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;4BAAE;YACR,WAAW;QACb;2BAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,gBAAgB;gBAClB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA,qBAAqB;YACrB;oCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;2BAAG;QAAC;KAAe;IAEnB,kCAAkC;IAClC,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,YAAY,gBAAgB;wBACxC,kBAAkB;oBACpB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;oCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;2BAAG;QAAC;KAAe;IAEnB,mBAAmB;IACnB,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAA4B,MAAM;YAAgB,aAAa;QAAiC;QACxG;YAAE,MAAM;YAAgC,MAAM;YAAiB,aAAa;QAAgC;QAC5G;YAAE,MAAM;YAA0B,MAAM;YAAkB,aAAa;QAAiC;QACxG;YAAE,MAAM;YAAuB,MAAM;YAAc,aAAa;QAAgC;QAChG;YAAE,MAAM;YAAmB,MAAM;YAAoB,aAAa;QAAiC;QACnG;YAAE,MAAM;YAAiB,MAAM;YAAkB,aAAa;QAA6B;KAC5F;IAED,+BAA+B;IAC/B,MAAM,oBAAoB;QACxB;YAAE,MAAM;YAA8B,MAAM;YAA+B,aAAa;QAA4B;QACpH;YAAE,MAAM;YAA0B,MAAM;YAA0B,aAAa;QAAyB;QACxG;YAAE,MAAM;YAA0B,MAAM;YAA2B,aAAa;QAAqB;QACrG;YAAE,MAAM;YAA8B,MAAM;YAA+B,aAAa;QAA0B;QAClH;YAAE,MAAM;YAA+B,MAAM;YAAgC,aAAa;QAA0B;QACpH;YAAE,MAAM;YAAiC,MAAM;YAAgC,aAAa;QAAoB;KACjH;IAED,iCAAiC;IACjC,MAAM,8BAA8B;QAClC;YAAE,MAAM;YAAmB,MAAM;YAAQ,aAAa;QAAmC;QACzF;YAAE,MAAM;YAAmB,MAAM;YAAoB,aAAa;QAAmC;QACrG;YAAE,MAAM;YAA0B,MAAM;YAAiB,aAAa;QAAmC;QACzG;YAAE,MAAM;YAAqB,MAAM;YAAsB,aAAa;QAAgC;QACtG;YAAE,MAAM;YAA2B,MAAM;YAA4B,aAAa;QAA+B;QACjH;YAAE,MAAM;YAAyB,MAAM;YAA0B,aAAa;QAAiC;KAChH;IAED,qBAAqB;IACrB,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAgB,MAAM;YAAiB,aAAa;QAA+B;QAC3F;YAAE,MAAM;YAAQ,MAAM;YAAS,aAAa;QAAoC;KACjF;IAED,MAAM,mBAAmB;QACvB,kBAAkB,CAAC;IACrB;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,qDAAqD;QACrD,mBAAmB;QACnB,sBAAsB;QACtB,gCAAgC;QAChC,qBAAqB;IACvB;IAEA,qBACE;;0BACE,0JAAC;gBAAO,WAAW,CAAC,0GAA0G,EAC5H,YACI,2CACA,+DACJ;0BACA,cAAA,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,0JAAC,yHAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;;;;;0CAOlB,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC;wCACC,WAAU;wCACV,cAAc,IAAM,mBAAmB;wCACvC,cAAc,IAAM,mBAAmB;;0DAEvC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,WAAU;;oDAA6H;kEAElL,0JAAC,gNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,kBAAkB,eAAe,IAAI;;;;;;kEACjH,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,0JAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,kBAAkB,sCAAsC,sCACxD;0DACA,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,0JAAC,wHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,0JAAC;wCACC,WAAU;wCACV,cAAc,IAAM,sBAAsB;wCAC1C,cAAc,IAAM,sBAAsB;;0DAE1C,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;;oDAA4H;kEAElK,0JAAC,gNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,qBAAqB,eAAe,IAAI;;;;;;kEACpH,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,0JAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,qBAAqB,sCAAsC,sCAC3D;0DACA,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,0JAAC,wHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,0JAAC;wCACC,WAAU;wCACV,cAAc,IAAM,gCAAgC;wCACpD,cAAc,IAAM,gCAAgC;;0DAEpD,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;;oDAA8H;kEAEtK,0JAAC,gNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,+BAA+B,eAAe,IAAI;;;;;;kEAC9H,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,0JAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,+BAA+B,sCAAsC,sCACrE;0DACA,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,4BAA4B,GAAG,CAAC,CAAC,SAAS,sBACzC,0JAAC,wHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;;4CAAmH;0DAElJ,0JAAC;gDAAK,WAAU;;;;;;;;;;;;kDAIlB,0JAAC;wCACC,WAAU;wCACV,cAAc,IAAM,qBAAqB;wCACzC,cAAc,IAAM,qBAAqB;;0DAEzC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;;oDAA4H;kEAExJ,0JAAC,gNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,oBAAoB,eAAe,IAAI;;;;;;kEACnH,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,0JAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,oBAAoB,sCAAsC,sCAC1D;0DACA,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,0JAAC,wHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;;4CAAmH;0DAEpJ,0JAAC;gDAAK,WAAU;;;;;;;;;;;;;;;;;;0CAMpB,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC,wHAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,0JAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,0JAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,0JAAC;wCACC,WAAU;wCACV,SAAS;wCACT,cAAW;kDAEX,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAK,WAAW,CAAC,+FAA+F,EAC/G,iBAAiB,4BAA4B,IAC7C;;;;;;8DACF,0JAAC;oDAAK,WAAW,CAAC,yEAAyE,EACzF,iBAAiB,cAAc,IAC/B;;;;;;8DACF,0JAAC;oDAAK,WAAW,CAAC,+FAA+F,EAC/G,iBAAiB,8BAA8B,IAC/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASb,WAAW,gCACV,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;;0CACb,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,SAAS;0CACtB,cAAA,0JAAC,yHAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAGd,0JAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAK,WAAU;;;;;;sDAChB,0JAAC;4CAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMtB,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;;kDACC,0JAAC;wCACC,WAAU;wCACV,SAAS,IAAM,mBAAmB,CAAC;;0DAEnC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,SAAS;gDAAiB,WAAU;0DAA6E;;;;;;0DAG9J,0JAAC,gNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,kBAAkB,eAAe,IAAI;;;;;;;;;;;;oCAE7G,iCACC,0JAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,0JAAC;;kDACC,0JAAC;wCACC,WAAU;wCACV,SAAS,IAAM,sBAAsB,CAAC;;0DAEtC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,SAAS;gDAAiB,WAAU;0DAA4E;;;;;;0DAG9I,0JAAC,gNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,qBAAqB,eAAe,IAAI;;;;;;;;;;;;oCAEhH,oCACC,0JAAC;wCAAI,WAAU;kDACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,0JAAC;;kDACC,0JAAC;wCACC,WAAU;wCACV,SAAS,IAAM,gCAAgC,CAAC;;0DAEhD,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,SAAS;gDAAiB,WAAU;0DAA8E;;;;;;0DAGlJ,0JAAC,gNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,+BAA+B,eAAe,IAAI;;;;;;;;;;;;oCAE1H,8CACC,0JAAC;wCAAI,WAAU;kDACZ,4BAA4B,GAAG,CAAC,CAAC,SAAS,sBACzC,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,0JAAC;0CACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;0CAMH,0JAAC;;kDACC,0JAAC;wCACC,WAAU;wCACV,SAAS,IAAM,qBAAqB,CAAC;;0DAErC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,SAAS;gDAAiB,WAAU;0DAA4E;;;;;;0DAGpI,0JAAC,gNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,oBAAoB,eAAe,IAAI;;;;;;;;;;;;oCAE/G,mCACC,0JAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,0JAAC;0CACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;0CAMH,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAlgBM;KAAA;uCAogBS", "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Footer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { Mail, Phone, MapPin, ArrowRight, Facebook, Instagram, Linkedin, Twitter, MessageCircle, Globe } from 'lucide-react';\r\n\r\nconst Footer: React.FC = () => {\r\n  return (\r\n    <footer className=\"relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden min-h-screen flex flex-col justify-center\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0 opacity-5\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/20 to-transparent\"></div>\r\n        <div className=\"absolute top-20 right-0 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute bottom-0 left-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative max-w-7xl mx-auto px-6 py-16\">\r\n        {/* Top Section - CTA */}\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent\">\r\n            Ready to Grow Your Business?\r\n          </h2>\r\n          <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\r\n            Let&apos;s create something amazing together. Get your free consultation today.\r\n          </p>\r\n          <Link href=\"/free-estimate\">\r\n            <button className=\"group inline-flex items-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl shadow-blue-500/25\">\r\n              Get Free Proposal\r\n              <ArrowRight size={20} className=\"ml-2 transition-transform duration-300 group-hover:translate-x-1\" />\r\n            </button>\r\n          </Link>\r\n        </div>\r\n\r\n        {/* Main Content */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12\">\r\n          {/* Company Info */}\r\n          <div className=\"lg:col-span-2\">\r\n            <div className=\"flex items-center mb-6\">\r\n              <Link href=\"/\">\r\n                <Image \r\n                  src=\"/VesaLogo.svg\" \r\n                  alt=\"VESA Solutions Logo\" \r\n                  width={120} \r\n                  height={46}\r\n                  className=\"w-30 h-auto transition-transform duration-300 hover:scale-105\"\r\n                />\r\n              </Link>\r\n            </div>\r\n            <p className=\"text-gray-300 text-lg leading-relaxed mb-8\">\r\n              Full-service digital marketing agency helping businesses attract, impress, and convert more leads online since 2015.\r\n            </p>\r\n            \r\n            {/* Contact Info */}\r\n            <div className=\"space-y-4\">\r\n              <a\r\n                href=\"mailto:<EMAIL>\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Mail size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span><EMAIL></span>\r\n              </a>\r\n              <a\r\n                href=\"tel:+***********\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Phone size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span>****** 628 3793</span>\r\n              </a>\r\n              <a\r\n                href=\"tel:+355694046408\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Phone size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span>+355 69 404 6408</span>\r\n              </a>\r\n              <a \r\n                href=\"https://share.google/T9q3WjqOOmMHrBnJY\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"flex items-start text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <MapPin size={18} className=\"mr-3 text-blue-400 mt-0.5 flex-shrink-0 group-hover:scale-110 transition-transform\" />\r\n                <span>Bulevardi Dyrrah, Pallati 394, Kati 4-t<br />2001, Durrës, Albania</span>\r\n              </a>\r\n              <div className=\"flex items-center text-gray-300\">\r\n                <div className=\"w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse\"></div>\r\n                <span className=\"text-green-400 font-medium\">Open 24 Hours</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Services Grid */}\r\n          <div className=\"lg:col-span-3 grid grid-cols-1 md:grid-cols-4 gap-8\">\r\n            {/* SEO Services */}\r\n            <div>\r\n              <Link href=\"/seo-search-engine-optimization\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-green-400 mb-6 relative transition-colors duration-300\">\r\n                  SEO Services\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-green-400 to-green-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/on-page-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  On-Page SEO\r\n                </Link></li>\r\n                <li><Link href=\"/off-page-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Off-Page SEO\r\n                </Link></li>\r\n                <li><Link href=\"/technical-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Technical SEO\r\n                </Link></li>\r\n                <li><Link href=\"/local-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Local SEO\r\n                </Link></li>\r\n                <li><Link href=\"/content-writing\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Content Writing\r\n                </Link></li>\r\n                <li><Link href=\"/seo-analytics\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  SEO Analytics\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Web Development */}\r\n            <div>\r\n              <Link href=\"/web-development\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-blue-400 mb-6 relative transition-colors duration-300\">\r\n                  Web Development\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/custom-website-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Custom Websites\r\n                </Link></li>\r\n                <li><Link href=\"/ecommerce-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  E-commerce Dev\r\n                </Link></li>\r\n                <li><Link href=\"/mobile-app-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Mobile Apps\r\n                </Link></li>\r\n                <li><Link href=\"/website-speed-optimization\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 flex-shrink-0 transition-all duration-300 group-hover:w-2\"></span>\r\n                  <span className=\"whitespace-nowrap\">Speed Optimization</span>\r\n                </Link></li>\r\n                <li><Link href=\"/web-application-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Web Applications\r\n                </Link></li>\r\n                <li><Link href=\"/website-maintenance-support\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Maintenance\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Digital Marketing */}\r\n            <div>\r\n              <Link href=\"/digital-marketing\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-purple-400 mb-6 relative transition-colors duration-300\">\r\n                  Digital Marketing\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-purple-400 to-purple-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/ppc\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  PPC Advertising\r\n                </Link></li>\r\n                <li><Link href=\"/email-marketing\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Email Marketing\r\n                </Link></li>\r\n                <li><Link href=\"/social-media\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Social Media\r\n                </Link></li>\r\n                <li><Link href=\"/branding-services\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Branding Services\r\n                </Link></li>\r\n                <li><Link href=\"/conversion-optimization\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Conversion Optimization\r\n                </Link></li>\r\n                <li><Link href=\"/reputation-management\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Reputation Management\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Company */}\r\n            <div>\r\n              <h3 className=\"text-lg font-bold text-white mb-6 relative\">\r\n                Company\r\n                <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600\"></span>\r\n              </h3>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/services\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Services\r\n                </Link></li>\r\n                <li><Link href=\"/case-studies\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Case Studies\r\n                </Link></li>\r\n                <li><Link href=\"/about\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  About Us\r\n                </Link></li>\r\n                <li><Link href=\"/contact-us\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Contact Us\r\n                </Link></li>\r\n                <li><Link href=\"/blog\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Blog\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Social Media & Bottom Section */}\r\n        <div className=\"flex flex-col lg:flex-row justify-between items-center pt-8 border-t border-gray-700\">\r\n          <div className=\"mb-6 lg:mb-0\">\r\n            <h4 className=\"text-white font-semibold mb-4\">Follow Our Journey</h4>\r\n            <div className=\"flex space-x-4\">\r\n              <a\r\n                href=\"https://m.facebook.com/VesaSolutions/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Facebook\"\r\n              >\r\n                <Facebook size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://www.instagram.com/vesasolutions/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-gradient-to-r hover:from-pink-500 hover:to-purple-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Instagram\"\r\n              >\r\n                <Instagram size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://al.linkedin.com/company/vesasolutions\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-700 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on LinkedIn\"\r\n              >\r\n                <Linkedin size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"#\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-500 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Twitter\"\r\n              >\r\n                <Twitter size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://wa.me/***********\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-green-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Chat with us on WhatsApp\"\r\n              >\r\n                <MessageCircle size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://vesasolutions.com/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-indigo-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Visit our website\"\r\n              >\r\n                <Globe size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"text-center lg:text-right\">\r\n            <div className=\"text-2xl font-bold text-white mb-2\">Growing Businesses Since 2015</div>\r\n            <div className=\"text-gray-400 text-sm\">Trusted by 200+ companies worldwide</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Copyright */}\r\n        <div className=\"mt-12 pt-8 border-t border-gray-700 text-center\">\r\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\r\n            <p className=\"text-gray-400 text-sm\">\r\n              © 2025 Vesa Solutions Marketing Agency. All rights reserved.\r\n            </p>\r\n            <div className=\"flex flex-wrap justify-center md:justify-end space-x-6 text-sm\">\r\n              <Link href=\"/privacy-policy\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Privacy Policy</Link>\r\n              <Link href=\"/terms-of-service\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Terms of Service</Link>\r\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Sitemap</Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAOA,MAAM,SAAmB;IACvB,qBACE,0JAAC;QAAO,WAAU;;0BAEhB,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAG,WAAU;0CAA4G;;;;;;0CAG1H,0JAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,0JAAC;oCAAO,WAAU;;wCAA2P;sDAE3Q,0JAAC,8MAAA,CAAA,aAAU;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMtC,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,0JAAC,yHAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;kDAIhB,0JAAC;wCAAE,WAAU;kDAA6C;;;;;;kDAK1D,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,0JAAC,8LAAA,CAAA,OAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC1B,0JAAC;kEAAK;;;;;;;;;;;;0DAER,0JAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,0JAAC,gMAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC3B,0JAAC;kEAAK;;;;;;;;;;;;0DAER,0JAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,0JAAC,gMAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC3B,0JAAC;kEAAK;;;;;;;;;;;;0DAER,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,0JAAC,sMAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC5B,0JAAC;;4DAAK;0EAAuC,0JAAC;;;;;4DAAK;;;;;;;;;;;;;0DAErD,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAI,WAAU;;;;;;kEACf,0JAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;0CAMnD,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC;;0DACC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,WAAU;0DACrD,cAAA,0JAAC;oDAAG,WAAU;;wDAAiG;sEAE7G,0JAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,0JAAC;gDAAG,WAAU;;kEACZ,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAe,WAAU;;8EACtC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EACxC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAa,WAAU;;8EACpC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;;8EAC1C,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EACxC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;;;;;;;;;;;;;kDAOlH,0JAAC;;0DACC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;0DACtC,cAAA,0JAAC;oDAAG,WAAU;;wDAAgG;sEAE5G,0JAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,0JAAC;gDAAG,WAAU;;kEACZ,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA8B,WAAU;;8EACrD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAyB,WAAU;;8EAChD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA0B,WAAU;;8EACjD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA8B,WAAU;;8EACrD,0JAAC;oEAAK,WAAU;;;;;;8EAChB,0JAAC;oEAAK,WAAU;8EAAoB;;;;;;;;;;;;;;;;;kEAEtC,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA+B,WAAU;;8EACtD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA+B,WAAU;;8EACtD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;;;;;;;;;;;;;kDAOjH,0JAAC;;0DACC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DACxC,cAAA,0JAAC;oDAAG,WAAU;;wDAAkG;sEAE9G,0JAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,0JAAC;gDAAG,WAAU;;kEACZ,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAO,WAAU;;8EAC9B,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;;8EAC1C,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAqB,WAAU;;8EAC5C,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA2B,WAAU;;8EAClD,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAyB,WAAU;;8EAChD,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;;;;;;;;;;;;;kDAOnH,0JAAC;;0DACC,0JAAC;gDAAG,WAAU;;oDAA6C;kEAEzD,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAElB,0JAAC;gDAAG,WAAU;;kEACZ,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAY,WAAU;;8EACnC,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;;8EAChC,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAc,WAAU;;8EACrC,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAQ,WAAU;;8EAC/B,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrH,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,sMAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEhC,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,wMAAA,CAAA,YAAS;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEjC,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,sMAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEhC,0JAAC;gDACC,MAAK;gDACL,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,oMAAA,CAAA,UAAO;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAE/B,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,oNAAA,CAAA,gBAAa;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAErC,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,gMAAA,CAAA,QAAK;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAKjC,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,0JAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAK3C,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,0JAAC;oCAAI,WAAU;;sDACb,0JAAC,wHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAkB,WAAU;sDAAsD;;;;;;sDAC7F,0JAAC,wHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAoB,WAAU;sDAAsD;;;;;;sDAC/F,0JAAC,wHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpG;KArTM;uCAuTS", "debugId": null}}, {"offset": {"line": 2974, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/lib/sanity.ts"], "sourcesContent": ["// lib/sanity.ts\r\nimport { createClient } from '@sanity/client'\r\nimport imageUrlBuilder from '@sanity/image-url'\r\nimport { SanityImageSource } from '@sanity/image-url/lib/types/types'\r\n\r\n// Sanity client configuration\r\nexport const client = createClient({\r\n  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,\r\n  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,\r\n  apiVersion: '2023-05-03',\r\n  useCdn: false,\r\n})\r\n\r\n// Image URL builder\r\nconst builder = imageUrlBuilder(client)\r\n\r\nexport function urlForImage(source: SanityImageSource) {\r\n  return builder.image(source)\r\n}\r\n\r\n// GROQ queries - UPDATED with missing fields\r\nexport const subServiceQuery = `\r\n  *[_type == \"subService\" && slug.current == $slug][0] {\r\n    _id,\r\n    title,\r\n    slug,\r\n    parentService,\r\n    hero {\r\n      badgeText,\r\n      badgeIcon,\r\n      title,\r\n      subtitle,\r\n      stats[] {\r\n        value,\r\n        label\r\n      },\r\n      backgroundGradient\r\n    },\r\n    whyMatters {\r\n      title,\r\n      description,\r\n      features[] {\r\n        text\r\n      },\r\n      stats[] {\r\n        value,\r\n        label,\r\n        color\r\n      },\r\n      ctaButton {\r\n        text\r\n      }\r\n    },\r\n    services[] {\r\n      icon,\r\n      title,\r\n      description,\r\n      fullDescription,\r\n      features,\r\n      benefits,\r\n      result,\r\n      image\r\n    },\r\n    strategicImplementation {\r\n      title,\r\n      description,\r\n      secondaryDescription,\r\n      image,\r\n      features,\r\n      sections[] {\r\n        icon,\r\n        title,\r\n        description,\r\n        color\r\n      }\r\n    },\r\n    marketIntelligence {\r\n      title,\r\n      description,\r\n      secondaryDescription,\r\n      image,\r\n      stats[] {\r\n        value,\r\n        label\r\n      },\r\n      ctaButton {\r\n        text\r\n      },\r\n      backgroundGradient\r\n    },\r\n    process {\r\n      title,\r\n      description,\r\n      steps[] {\r\n        step,\r\n        title,\r\n        description,\r\n        icon,\r\n        details\r\n      }\r\n    },\r\n    caseStudy {\r\n      title,\r\n      description,\r\n      image,\r\n      results[] {\r\n        value,\r\n        label\r\n      },\r\n      ctaButton {\r\n        text\r\n      },\r\n      backgroundGradient\r\n    },\r\n    cta {\r\n      title,\r\n      description,\r\n      benefits,\r\n      formSettings {\r\n        ctaText,\r\n        messagePlaceholder\r\n      }\r\n    },\r\n    testimonials[] {\r\n      name,\r\n      business,\r\n      location,\r\n      image,\r\n      quote,\r\n      result,\r\n      rating\r\n    },\r\n    faqs[] {\r\n      question,\r\n      answer\r\n    },\r\n    footerCta {\r\n      title,\r\n      description,\r\n      primaryButton {\r\n        text,\r\n        icon\r\n      },\r\n      secondaryButton {\r\n        text,\r\n        icon\r\n      },\r\n      backgroundGradient\r\n    },\r\n    seo {\r\n      metaTitle,\r\n      metaDescription,\r\n      keywords,\r\n      ogImage\r\n    }\r\n  }\r\n`\r\n\r\nexport const allSubServicesQuery = `\r\n  *[_type == \"subService\"] | order(_createdAt desc) {\r\n    _id,\r\n    title,\r\n    slug,\r\n    hero {\r\n      title,\r\n      subtitle\r\n    },\r\n    seo {\r\n      metaTitle,\r\n      metaDescription\r\n    }\r\n  }\r\n`\r\n\r\n// Fetch functions\r\nexport async function getSubService(slug: string) {\r\n  try {\r\n    const result = await client.fetch(subServiceQuery, { slug })\r\n    console.log('Fetched sub-service:', result) // Debug log\r\n    console.log('Has strategicImplementation:', !!result?.strategicImplementation) // Debug\r\n    console.log('Has marketIntelligence:', !!result?.marketIntelligence) // Debug\r\n    return result\r\n  } catch (error) {\r\n    console.error('Error fetching sub-service:', error)\r\n    return null\r\n  }\r\n}\r\n\r\nexport async function getAllSubServices() {\r\n  try {\r\n    return await client.fetch(allSubServicesQuery)\r\n  } catch (error) {\r\n    console.error('Error fetching all sub-services:', error)\r\n    return []\r\n  }\r\n}\r\n\r\nexport async function getSubServiceSlugs() {\r\n  try {\r\n    const query = `*[_type == \"subService\"] { \"slug\": slug.current }`\r\n    return await client.fetch(query)\r\n  } catch (error) {\r\n    console.error('Error fetching sub-service slugs:', error)\r\n    return []\r\n  }\r\n}"], "names": [], "mappings": "AAAA,gBAAgB;;;;;;;;;;AAOH;AANb;AACA;;;AAIO,MAAM,SAAS,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE;IACjC,SAAS;IACT,OAAO;IACP,YAAY;IACZ,QAAQ;AACV;AAEA,oBAAoB;AACpB,MAAM,UAAU,CAAA,GAAA,6KAAA,CAAA,UAAe,AAAD,EAAE;AAEzB,SAAS,YAAY,MAAyB;IACnD,OAAO,QAAQ,KAAK,CAAC;AACvB;AAGO,MAAM,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuIhC,CAAC;AAEM,MAAM,sBAAsB,CAAC;;;;;;;;;;;;;;AAcpC,CAAC;AAGM,eAAe,cAAc,IAAY;IAC9C,IAAI;QACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,iBAAiB;YAAE;QAAK;QAC1D,QAAQ,GAAG,CAAC,wBAAwB,QAAQ,YAAY;;QACxD,QAAQ,GAAG,CAAC,gCAAgC,CAAC,CAAC,QAAQ,yBAAyB,QAAQ;;QACvF,QAAQ,GAAG,CAAC,2BAA2B,CAAC,CAAC,QAAQ,oBAAoB,QAAQ;;QAC7E,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAEO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,OAAO,KAAK,CAAC;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,EAAE;IACX;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,CAAC,iDAAiD,CAAC;QACjE,OAAO,MAAM,OAAO,KAAK,CAAC;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,EAAE;IACX;AACF", "debugId": null}}, {"offset": {"line": 3194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/IconRender.tsx"], "sourcesContent": ["// IconRenderer.tsx - Complete with all schema icons\r\nimport React from 'react'\r\nimport {\r\n  // All icons from the schema\r\n  MapPin, Search, Target, TrendingUp, Users, Building, Star, Phone, Mail, Globe,\r\n  Smartphone, Calendar, Award, CheckCircle, Navigation, Zap, Rocket, Lightbulb,\r\n  BarChart3, Shield, Cog, ArrowRight, User, ChevronDown,\r\n  // Missing icons that were causing build warnings\r\n  Share2, DollarSign, ShoppingCart, AlertTriangle, Database, Eye, Settings,\r\n  FileText, Layers, Monitor, Code, Palette, Megaphone, MessageSquare,\r\n  // Additional missing icons from migration files\r\n  Image, Cloud, Heart, Store, CreditCard\r\n} from 'lucide-react'\r\n\r\ninterface IconProps {\r\n  size?: number\r\n  className?: string\r\n}\r\n\r\nexport const IconRenderer = ({ iconName, size = 24, className = '' }: { \r\n  iconName?: string\r\n  size?: number\r\n  className?: string \r\n}) => {\r\n  if (!iconName) return null\r\n  \r\n  // Complete map matching schema exactly\r\n  const iconMap: { [key: string]: React.ComponentType<IconProps> } = {\r\n    // Hero/Badge icons\r\n    MapPin,\r\n    Search,\r\n    Target,\r\n    TrendingUp,\r\n    Users,\r\n    Building,\r\n    Star,\r\n    Phone,\r\n    Mail,\r\n    Globe,\r\n    Smartphone,\r\n    Calendar,\r\n    Award,\r\n    CheckCircle,\r\n    Navigation,\r\n    Zap,\r\n    Rocket,\r\n    Lightbulb,\r\n\r\n    // Service icons\r\n    BarChart3,\r\n    Shield,\r\n    Cog,\r\n\r\n    // Button/CTA icons\r\n    ArrowRight,\r\n    User,\r\n\r\n    // Missing icons that were causing warnings\r\n    Share2,\r\n    DollarSign,\r\n    ShoppingCart,\r\n    AlertTriangle,\r\n    Database,\r\n    Eye,\r\n    Settings,\r\n    FileText,\r\n    Layers,\r\n    Monitor,\r\n    Code,\r\n    Palette,\r\n    Megaphone,\r\n    MessageSquare,\r\n\r\n    // Additional missing icons from migration files\r\n    Image,\r\n    Cloud,\r\n    Heart,\r\n    Store,\r\n    CreditCard,\r\n\r\n    // Other\r\n    ChevronDown\r\n  }\r\n  \r\n  const IconComponent = iconMap[iconName]\r\n  \r\n  if (!IconComponent) {\r\n    console.warn(`Icon \"${iconName}\" not found in iconMap. Available icons:`, Object.keys(iconMap))\r\n    return null\r\n  }\r\n  \r\n  return <IconComponent size={size} className={className} />\r\n}"], "names": [], "mappings": "AAAA,oDAAoD;;;;;AAEpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAiBO,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,YAAY,EAAE,EAIjE;IACC,IAAI,CAAC,UAAU,OAAO;IAEtB,uCAAuC;IACvC,MAAM,UAA6D;QACjE,mBAAmB;QACnB,QAAA,sMAAA,CAAA,SAAM;QACN,QAAA,kMAAA,CAAA,SAAM;QACN,QAAA,kMAAA,CAAA,SAAM;QACN,YAAA,8MAAA,CAAA,aAAU;QACV,OAAA,gMAAA,CAAA,QAAK;QACL,UAAA,sMAAA,CAAA,WAAQ;QACR,MAAA,8LAAA,CAAA,OAAI;QACJ,OAAA,gMAAA,CAAA,QAAK;QACL,MAAA,8LAAA,CAAA,OAAI;QACJ,OAAA,gMAAA,CAAA,QAAK;QACL,YAAA,0MAAA,CAAA,aAAU;QACV,UAAA,sMAAA,CAAA,WAAQ;QACR,OAAA,gMAAA,CAAA,QAAK;QACL,aAAA,uNAAA,CAAA,cAAW;QACX,YAAA,0MAAA,CAAA,aAAU;QACV,KAAA,4LAAA,CAAA,MAAG;QACH,QAAA,kMAAA,CAAA,SAAM;QACN,WAAA,wMAAA,CAAA,YAAS;QAET,gBAAgB;QAChB,WAAA,8MAAA,CAAA,YAAS;QACT,QAAA,kMAAA,CAAA,SAAM;QACN,KAAA,4LAAA,CAAA,MAAG;QAEH,mBAAmB;QACnB,YAAA,8MAAA,CAAA,aAAU;QACV,MAAA,8LAAA,CAAA,OAAI;QAEJ,2CAA2C;QAC3C,QAAA,sMAAA,CAAA,SAAM;QACN,YAAA,8MAAA,CAAA,aAAU;QACV,cAAA,kNAAA,CAAA,eAAY;QACZ,eAAA,oNAAA,CAAA,gBAAa;QACb,UAAA,sMAAA,CAAA,WAAQ;QACR,KAAA,4LAAA,CAAA,MAAG;QACH,UAAA,sMAAA,CAAA,WAAQ;QACR,UAAA,0MAAA,CAAA,WAAQ;QACR,QAAA,kMAAA,CAAA,SAAM;QACN,SAAA,oMAAA,CAAA,UAAO;QACP,MAAA,8LAAA,CAAA,OAAI;QACJ,SAAA,oMAAA,CAAA,UAAO;QACP,WAAA,wMAAA,CAAA,YAAS;QACT,eAAA,oNAAA,CAAA,gBAAa;QAEb,gDAAgD;QAChD,OAAA,gMAAA,CAAA,QAAK;QACL,OAAA,gMAAA,CAAA,QAAK;QACL,OAAA,gMAAA,CAAA,QAAK;QACL,OAAA,gMAAA,CAAA,QAAK;QACL,YAAA,8MAAA,CAAA,aAAU;QAEV,QAAQ;QACR,aAAA,gNAAA,CAAA,cAAW;IACb;IAEA,MAAM,gBAAgB,OAAO,CAAC,SAAS;IAEvC,IAAI,CAAC,eAAe;QAClB,QAAQ,IAAI,CAAC,CAAC,MAAM,EAAE,SAAS,wCAAwC,CAAC,EAAE,OAAO,IAAI,CAAC;QACtF,OAAO;IACT;IAEA,qBAAO,0JAAC;QAAc,MAAM;QAAM,WAAW;;;;;;AAC/C;KAzEa", "debugId": null}}, {"offset": {"line": 3324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/sub-services/SubServiceHero.tsx"], "sourcesContent": ["// SubServiceHero.tsx - Fixed with complete icon support\r\nimport React from 'react'\r\nimport { IconRenderer } from '@/components/global/IconRender'\r\nimport { SubServiceHero as SubServiceHeroType } from '@/types/subService'\r\n\r\ninterface SubServiceHeroProps {\r\n  data: SubServiceHeroType\r\n}\r\n\r\nexport const SubServiceHero: React.FC<SubServiceHeroProps> = ({ data }) => {\r\n  return (\r\n    <section className={`bg-gradient-to-r ${data.backgroundGradient || 'from-blue-600 to-blue-800'} py-16`}>\r\n      <div className=\"max-w-7xl mx-auto px-6\">\r\n        <div className=\"text-center\">\r\n          {data.badgeText && (\r\n            <div className=\"inline-flex items-center bg-blue-500 text-white text-sm font-semibold px-4 py-2 rounded-full mb-6\">\r\n              <IconRenderer iconName={data.badgeIcon} size={16} className=\"mr-2\" />\r\n              {data.badgeText}\r\n            </div>\r\n          )}\r\n\r\n          <h1 className=\"text-4xl md:text-6xl font-bold text-white mb-6\">\r\n            {data.title}\r\n          </h1>\r\n\r\n          {data.subtitle && (\r\n            <p className=\"text-xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed\">\r\n              {data.subtitle}\r\n            </p>\r\n          )}\r\n\r\n          {data.stats && data.stats.length > 0 && (\r\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto\">\r\n              {data.stats.map((stat, index) => (\r\n                <div key={index} className=\"text-center\">\r\n                  <div className=\"text-3xl font-bold text-blue-200 mb-1\">\r\n                    {stat.value}\r\n                  </div>\r\n                  <div className=\"text-blue-100 text-sm\">\r\n                    {stat.label}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}"], "names": [], "mappings": "AAAA,wDAAwD;;;;;AAExD;;;AAOO,MAAM,iBAAgD,CAAC,EAAE,IAAI,EAAE;IACpE,qBACE,0JAAC;QAAQ,WAAW,CAAC,iBAAiB,EAAE,KAAK,kBAAkB,IAAI,4BAA4B,MAAM,CAAC;kBACpG,cAAA,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;oBACZ,KAAK,SAAS,kBACb,0JAAC;wBAAI,WAAU;;0CACb,0JAAC,+HAAA,CAAA,eAAY;gCAAC,UAAU,KAAK,SAAS;gCAAE,MAAM;gCAAI,WAAU;;;;;;4BAC3D,KAAK,SAAS;;;;;;;kCAInB,0JAAC;wBAAG,WAAU;kCACX,KAAK,KAAK;;;;;;oBAGZ,KAAK,QAAQ,kBACZ,0JAAC;wBAAE,WAAU;kCACV,KAAK,QAAQ;;;;;;oBAIjB,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,mBACjC,0JAAC;wBAAI,WAAU;kCACZ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,0JAAC;gCAAgB,WAAU;;kDACzB,0JAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;kDAEb,0JAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;;+BALL;;;;;;;;;;;;;;;;;;;;;;;;;;AAe1B;KAxCa", "debugId": null}}, {"offset": {"line": 3436, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Form.tsx"], "sourcesContent": ["// components/global/Form.tsx - Fixed with static colors (not affected by dark/light mode)\r\nimport React, { useState, useEffect } from 'react';\r\nimport PhoneInput from 'react-phone-number-input';\r\nimport 'react-phone-number-input/style.css';\r\nimport { useForm, Controller } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { z } from 'zod';\r\nimport { getCountryCallingCode, Country } from 'react-phone-number-input';\r\n\r\n// Form validation schema\r\nconst contactSchema = z.object({\r\n  businessName: z.string().min(2, 'Business name is required'),\r\n  fullName: z.string().min(2, 'Your name is required'),\r\n  email: z.string().email('Valid email is required'),\r\n  phone: z.string().min(10, 'Valid phone number is required'),\r\n  location: z.string().min(2, 'Business location is required'),\r\n  website: z.string().url().optional().or(z.literal('')),\r\n  message: z.string().min(10, 'Please describe your goals (minimum 10 characters)'),\r\n  service: z.string(),\r\n});\r\n\r\ntype ContactFormData = z.infer<typeof contactSchema>;\r\n\r\n// Extended form data type for submission\r\ninterface ExtendedFormData extends ContactFormData {\r\n  userCountry?: string;\r\n  timestamp?: string;\r\n}\r\n\r\ninterface ServiceContactFormProps {\r\n  service: {\r\n    name: string;\r\n    type: 'seo' | 'local-seo' | 'ppc' | 'web-design' | 'social-media' | 'email-marketing' | 'branding' | 'conversion-optimization' | 'reputation-management';\r\n    ctaText?: string;\r\n    messagePlaceholder?: string;\r\n    benefits?: string[];\r\n  };\r\n  className?: string;\r\n  onSubmit?: (data: ExtendedFormData) => Promise<void>;\r\n  initialWebsite?: string;\r\n}\r\n\r\n// API Response types\r\ninterface ApiResponse {\r\n  message: string;\r\n  success: boolean;\r\n  error?: string;\r\n}\r\n\r\n// Helper function to safely get error message\r\nfunction getErrorMessage(error: unknown): string {\r\n  if (error instanceof Error) {\r\n    return error.message;\r\n  }\r\n  if (typeof error === 'string') {\r\n    return error;\r\n  }\r\n  return 'An unknown error occurred';\r\n}\r\n\r\nconst ServiceContactForm: React.FC<ServiceContactFormProps> = ({\r\n  service,\r\n  className = '',\r\n  onSubmit,\r\n  initialWebsite = ''\r\n}) => {\r\n  const [userCountry, setUserCountry] = useState<string>('US');\r\n  const [defaultPhoneCountry, setDefaultPhoneCountry] = useState<Country>('US');\r\n  const [initialPhoneValue, setInitialPhoneValue] = useState<string>('');\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\r\n  const [errorMessage, setErrorMessage] = useState<string>('');\r\n\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    control,\r\n    formState: { errors },\r\n    reset,\r\n    setValue\r\n  } = useForm<ContactFormData>({\r\n    resolver: zodResolver(contactSchema),\r\n    defaultValues: {\r\n      service: service.name,\r\n      website: initialWebsite,\r\n    }\r\n  });\r\n\r\n  // Auto-detect user country on component mount\r\n  useEffect(() => {\r\n    const detectUserCountry = async () => {\r\n      try {\r\n        // Method 1: Try IP-based detection (most reliable)\r\n        const response = await fetch('https://ipapi.co/json/');\r\n        const data = await response.json();\r\n        \r\n        if (data.country_code) {\r\n          const countryCode = data.country_code;\r\n          setUserCountry(countryCode);\r\n          setDefaultPhoneCountry(countryCode as Country);\r\n          \r\n          // Get the country calling code and set initial phone value\r\n          try {\r\n            const callingCode = getCountryCallingCode(countryCode as Country);\r\n            const initialValue = `+${callingCode}`;\r\n            setInitialPhoneValue(initialValue);\r\n            setValue('phone', initialValue);\r\n          } catch {\r\n            console.log('Error getting calling code');\r\n            setInitialPhoneValue('+1');\r\n            setValue('phone', '+1');\r\n          }\r\n          return;\r\n        }\r\n      } catch {\r\n        console.log('IP detection failed, trying locale detection');\r\n      }\r\n\r\n      try {\r\n        const locale = navigator.language || (navigator.languages && navigator.languages[0]);\r\n        const countryCode = locale?.split('-')[1] || 'US';\r\n        setUserCountry(countryCode);\r\n        setDefaultPhoneCountry(countryCode as Country);\r\n        \r\n        // Get the country calling code and set initial phone value\r\n        try {\r\n          const callingCode = getCountryCallingCode(countryCode as Country);\r\n          const initialValue = `+${callingCode}`;\r\n          setInitialPhoneValue(initialValue);\r\n          setValue('phone', initialValue);\r\n        } catch {\r\n          console.log('Error getting calling code');\r\n          setInitialPhoneValue('+1');\r\n          setValue('phone', '+1');\r\n        }\r\n      } catch {\r\n        // Method 3: Default to US\r\n        setUserCountry('US');\r\n        setDefaultPhoneCountry('US');\r\n        setInitialPhoneValue('+1');\r\n        setValue('phone', '+1');\r\n      }\r\n    };\r\n\r\n    detectUserCountry();\r\n  }, [setValue]);\r\n\r\n  // Dynamic form content based on service type\r\n  const getServiceContent = () => {\r\n    // Determine if this is a quote-based service (web development, design, etc.)\r\n    const isQuoteService = service.type === 'web-design' ||\r\n                          service.name.toLowerCase().includes('website') ||\r\n                          service.name.toLowerCase().includes('web design') ||\r\n                          service.name.toLowerCase().includes('web development') ||\r\n                          service.name.toLowerCase().includes('development')\r\n\r\n    const actionWord = isQuoteService ? 'Quote' : 'Analysis'\r\n\r\n    const baseContent = {\r\n      title: `Get Your Free ${service.name} ${actionWord}`,\r\n      subtitle: isQuoteService\r\n        ? `Get a custom ${service.name.toLowerCase()} quote tailored to your business needs and goals.`\r\n        : `See exactly how your business can improve with our ${service.name.toLowerCase()} strategies.`,\r\n      ctaText: service.ctaText || `Get My Free ${service.name} ${actionWord}`,\r\n      messagePlaceholder: service.messagePlaceholder || `Tell us about your ${service.name.toLowerCase()} ${isQuoteService ? 'requirements' : 'goals'}*`,\r\n    };\r\n\r\n    switch (service.type) {\r\n      case 'local-seo':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle for local SEO\r\n          subtitle: 'See exactly how your business ranks against local competitors and discover untapped opportunities.',\r\n          benefits: service.benefits || [\r\n            'Complete local SEO audit & competitor analysis',\r\n            'Google My Business optimization recommendations',\r\n            'Local keyword opportunities & strategy roadmap',\r\n            'Citation audit & local link building plan'\r\n          ]\r\n        };\r\n\r\n      case 'seo':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle for SEO\r\n          subtitle: 'Discover exactly how to dominate Google search results in your industry.',\r\n          benefits: service.benefits || [\r\n            'Complete SEO audit & competitor analysis',\r\n            'Keyword opportunities & content strategy',\r\n            'Technical SEO recommendations',\r\n            'Link building and authority plan'\r\n          ]\r\n        };\r\n\r\n      case 'ppc':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle for PPC\r\n          subtitle: 'See how to maximize your advertising ROI and reduce cost per acquisition.',\r\n          benefits: service.benefits || [\r\n            'PPC account audit & optimization plan',\r\n            'Keyword research & bid strategy',\r\n            'Ad copy and landing page recommendations',\r\n            'Campaign structure & targeting analysis'\r\n          ]\r\n        };\r\n\r\n      case 'web-design':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title and subtitle for web services\r\n          benefits: service.benefits || [\r\n            'Complete website audit & UX analysis',\r\n            'Design recommendations & best practices',\r\n            'Mobile optimization strategy',\r\n            'Conversion optimization plan'\r\n          ]\r\n        };\r\n\r\n      case 'social-media':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle\r\n          subtitle: 'See how to build engaged communities and drive social media ROI.',\r\n          benefits: service.benefits || [\r\n            'Social media audit & competitor analysis',\r\n            'Content strategy & engagement plan',\r\n            'Platform optimization recommendations',\r\n            'Social advertising strategy'\r\n          ]\r\n        };\r\n\r\n      case 'email-marketing':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle\r\n          subtitle: 'Discover how to build relationships that drive revenue through email.',\r\n          benefits: service.benefits || [\r\n            'Email marketing audit & performance analysis',\r\n            'List building & segmentation strategy',\r\n            'Automation workflow recommendations',\r\n            'Campaign optimization plan'\r\n          ]\r\n        };\r\n\r\n      case 'branding':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle\r\n          subtitle: 'See how to build a powerful brand identity that resonates with your audience.',\r\n          benefits: service.benefits || [\r\n            'Complete brand audit & competitive analysis',\r\n            'Brand positioning & messaging strategy',\r\n            'Visual identity recommendations',\r\n            'Brand implementation roadmap'\r\n          ]\r\n        };\r\n\r\n      case 'conversion-optimization':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle\r\n          subtitle: 'Discover how to turn more visitors into customers with data-driven optimization.',\r\n          benefits: service.benefits || [\r\n            'Conversion audit & performance analysis',\r\n            'A/B testing strategy & recommendations',\r\n            'User experience optimization plan',\r\n            'Revenue growth projections'\r\n          ]\r\n        };\r\n\r\n      case 'reputation-management':\r\n        return {\r\n          ...baseContent,\r\n          // Use dynamic title but keep specific subtitle\r\n          subtitle: 'See how to protect and enhance your online reputation across all platforms.',\r\n          benefits: service.benefits || [\r\n            'Complete reputation audit & risk assessment',\r\n            'Review management strategy',\r\n            'Crisis prevention & response plan',\r\n            'Brand authority building roadmap'\r\n          ]\r\n        };\r\n\r\n      default:\r\n        return {\r\n          ...baseContent,\r\n          benefits: service.benefits || [\r\n            'Comprehensive analysis of your current situation',\r\n            'Custom strategy recommendations',\r\n            'Implementation roadmap',\r\n            'Performance tracking plan'\r\n          ]\r\n        };\r\n    }\r\n  };\r\n\r\n  const serviceContent = getServiceContent();\r\n\r\n  const onFormSubmit = async (data: ContactFormData) => {\r\n    setIsSubmitting(true);\r\n    setSubmitStatus('idle');\r\n    setErrorMessage('');\r\n    \r\n    try {\r\n      // Add user country and timestamp to form data\r\n      const formDataWithExtras: ExtendedFormData = {\r\n        ...data,\r\n        userCountry,\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n\r\n      if (onSubmit) {\r\n        // Use custom onSubmit if provided\r\n        await onSubmit(formDataWithExtras);\r\n        setSubmitStatus('success');\r\n        reset();\r\n        // Reset phone to initial value after form reset\r\n        setTimeout(() => {\r\n          setValue('phone', initialPhoneValue);\r\n        }, 100);\r\n      } else {\r\n        // Default: Send to API\r\n        const response = await fetch('/api/contact', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify(formDataWithExtras),\r\n        });\r\n\r\n        const result: ApiResponse = await response.json();\r\n\r\n        if (!response.ok) {\r\n          throw new Error(result.message || 'Failed to submit form');\r\n        }\r\n\r\n        if (result.success) {\r\n          setSubmitStatus('success');\r\n          reset();\r\n          // Reset phone to initial value after form reset\r\n          setTimeout(() => {\r\n            setValue('phone', initialPhoneValue);\r\n          }, 100);\r\n        } else {\r\n          throw new Error(result.message || 'Form submission failed');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Form submission error:', error);\r\n      const errorMsg = getErrorMessage(error);\r\n      setErrorMessage(errorMsg);\r\n      setSubmitStatus('error');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Success state\r\n  if (submitStatus === 'success') {\r\n    return (\r\n      <div \r\n        className={`rounded-2xl shadow-xl p-8 ${className}`}\r\n        style={{ backgroundColor: '#ffffff' }}\r\n      >\r\n        <div className=\"text-center\">\r\n          <div \r\n            className=\"w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\"\r\n            style={{ backgroundColor: '#dcfce7' }}\r\n          >\r\n            <svg \r\n              className=\"w-8 h-8\" \r\n              fill=\"none\" \r\n              stroke=\"currentColor\" \r\n              viewBox=\"0 0 24 24\"\r\n              style={{ color: '#16a34a' }}\r\n            >\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\"></path>\r\n            </svg>\r\n          </div>\r\n          <h3 \r\n            className=\"text-2xl font-bold mb-2\"\r\n            style={{ color: '#1f2937' }}\r\n          >\r\n            Thank You! 🎉\r\n          </h3>\r\n          <p \r\n            className=\"mb-4\"\r\n            style={{ color: '#4b5563' }}\r\n          >\r\n            Your request has been submitted successfully. We&apos;ll contact you within 24 hours with your free {service.name.toLowerCase()} analysis.\r\n          </p>\r\n          <p \r\n            className=\"text-sm mb-6\"\r\n            style={{ color: '#6b7280' }}\r\n          >\r\n            Check your email for confirmation and next steps.\r\n          </p>\r\n          <button \r\n            onClick={() => {\r\n              setSubmitStatus('idle');\r\n              setErrorMessage('');\r\n              // Reset phone to initial value when showing form again\r\n              setTimeout(() => {\r\n                setValue('phone', initialPhoneValue);\r\n              }, 100);\r\n            }}\r\n            className=\"font-semibold transition-colors\"\r\n            style={{ color: '#2563eb' }}\r\n            onMouseEnter={(e) => {\r\n              e.currentTarget.style.color = '#1d4ed8';\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              e.currentTarget.style.color = '#2563eb';\r\n            }}\r\n          >\r\n            Submit Another Request\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div \r\n      className={`rounded-2xl shadow-xl p-8 ${className}`}\r\n      style={{ backgroundColor: '#ffffff' }}\r\n    >\r\n      <h3 \r\n        className=\"text-2xl font-bold mb-2\"\r\n        style={{ color: '#1f2937' }}\r\n      >\r\n        {serviceContent.title}\r\n      </h3>\r\n      <p \r\n        className=\"mb-6\"\r\n        style={{ color: '#4b5563' }}\r\n      >\r\n        {serviceContent.subtitle}\r\n      </p>\r\n\r\n      <form onSubmit={handleSubmit(onFormSubmit)} className=\"space-y-4\">\r\n        {/* Business Name & Your Name */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n          <div>\r\n            <input \r\n              {...register('businessName')}\r\n              type=\"text\" \r\n              placeholder=\"Business Name*\" \r\n              className=\"w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors\"\r\n              style={{ \r\n                border: '1px solid #d1d5db',\r\n                color: '#374151',\r\n                backgroundColor: '#ffffff'\r\n              }}\r\n              onFocus={(e) => {\r\n                e.target.style.borderColor = 'transparent';\r\n                e.target.style.boxShadow = '0 0 0 2px #3b82f6';\r\n              }}\r\n              onBlur={(e) => {\r\n                e.target.style.borderColor = '#d1d5db';\r\n                e.target.style.boxShadow = 'none';\r\n              }}\r\n            />\r\n            {errors.businessName && (\r\n              <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n                {errors.businessName.message}\r\n              </p>\r\n            )}\r\n          </div>\r\n          <div>\r\n            <input \r\n              {...register('fullName')}\r\n              type=\"text\" \r\n              placeholder=\"Your Name*\" \r\n              className=\"w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors\"\r\n              style={{ \r\n                border: '1px solid #d1d5db',\r\n                color: '#374151',\r\n                backgroundColor: '#ffffff'\r\n              }}\r\n              onFocus={(e) => {\r\n                e.target.style.borderColor = 'transparent';\r\n                e.target.style.boxShadow = '0 0 0 2px #3b82f6';\r\n              }}\r\n              onBlur={(e) => {\r\n                e.target.style.borderColor = '#d1d5db';\r\n                e.target.style.boxShadow = 'none';\r\n              }}\r\n            />\r\n            {errors.fullName && (\r\n              <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n                {errors.fullName.message}\r\n              </p>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Email */}\r\n        <div>\r\n          <input \r\n            {...register('email')}\r\n            type=\"email\" \r\n            placeholder=\"Email Address*\" \r\n            className=\"w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors\"\r\n            style={{ \r\n              border: '1px solid #d1d5db',\r\n              color: '#374151',\r\n              backgroundColor: '#ffffff'\r\n            }}\r\n            onFocus={(e) => {\r\n              e.target.style.borderColor = 'transparent';\r\n              e.target.style.boxShadow = '0 0 0 2px #3b82f6';\r\n            }}\r\n            onBlur={(e) => {\r\n              e.target.style.borderColor = '#d1d5db';\r\n              e.target.style.boxShadow = 'none';\r\n            }}\r\n          />\r\n          {errors.email && (\r\n            <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n              {errors.email.message}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Phone Number with Country Detection and Pre-filled Prefix */}\r\n        <div>\r\n          <Controller\r\n            name=\"phone\"\r\n            control={control}\r\n            render={({ field }) => (\r\n              <div className=\"flex gap-2\">\r\n                <PhoneInput\r\n                  {...field}\r\n                  value={field.value || initialPhoneValue}\r\n                  defaultCountry={defaultPhoneCountry}\r\n                  placeholder=\"Phone Number*\"\r\n                  className=\"w-full\"\r\n                  style={{\r\n                    '--PhoneInputCountryFlag-borderColor': 'transparent',\r\n                    '--PhoneInput-color': '#374151',\r\n                  }}\r\n                  numberInputProps={{\r\n                    className: 'flex-1 px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors',\r\n                    style: { \r\n                      border: '1px solid #d1d5db',\r\n                      color: '#374151',\r\n                      backgroundColor: '#ffffff'\r\n                    }\r\n                  }}\r\n                  countrySelectProps={{\r\n                    className: 'px-3 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors',\r\n                    style: { \r\n                      border: '1px solid #d1d5db',\r\n                      backgroundColor: '#ffffff',\r\n                      minWidth: '80px',\r\n                      width: '80px'\r\n                    }\r\n                  }}\r\n                  onChange={(value) => {\r\n                    field.onChange(value);\r\n                    // If user clears the field but country is still selected, restore the prefix\r\n                    if (!value && defaultPhoneCountry) {\r\n                      try {\r\n                        const callingCode = getCountryCallingCode(defaultPhoneCountry);\r\n                        const prefixValue = `+${callingCode}`;\r\n                        setTimeout(() => {\r\n                          field.onChange(prefixValue);\r\n                        }, 10);\r\n                      } catch {\r\n                        console.log('Error restoring prefix');\r\n                      }\r\n                    }\r\n                  }}\r\n                />\r\n              </div>\r\n            )}\r\n          />\r\n          {errors.phone && (\r\n            <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n              {errors.phone.message}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Business Location */}\r\n        <div>\r\n          <input \r\n            {...register('location')}\r\n            type=\"text\" \r\n            placeholder=\"Business Location (City, State)*\" \r\n            className=\"w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors\"\r\n            style={{ \r\n              border: '1px solid #d1d5db',\r\n              color: '#374151',\r\n              backgroundColor: '#ffffff'\r\n            }}\r\n            onFocus={(e) => {\r\n              e.target.style.borderColor = 'transparent';\r\n              e.target.style.boxShadow = '0 0 0 2px #3b82f6';\r\n            }}\r\n            onBlur={(e) => {\r\n              e.target.style.borderColor = '#d1d5db';\r\n              e.target.style.boxShadow = 'none';\r\n            }}\r\n          />\r\n          {errors.location && (\r\n            <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n              {errors.location.message}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Website URL */}\r\n        <div>\r\n          <input \r\n            {...register('website')}\r\n            type=\"url\" \r\n            placeholder=\"Website URL (optional)\" \r\n            className=\"w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors\"\r\n            style={{ \r\n              border: '1px solid #d1d5db',\r\n              color: '#374151',\r\n              backgroundColor: '#ffffff'\r\n            }}\r\n            onFocus={(e) => {\r\n              e.target.style.borderColor = 'transparent';\r\n              e.target.style.boxShadow = '0 0 0 2px #3b82f6';\r\n            }}\r\n            onBlur={(e) => {\r\n              e.target.style.borderColor = '#d1d5db';\r\n              e.target.style.boxShadow = 'none';\r\n            }}\r\n          />\r\n          {errors.website && (\r\n            <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n              {errors.website.message}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Message */}\r\n        <div>\r\n          <textarea \r\n            {...register('message')}\r\n            placeholder={serviceContent.messagePlaceholder}\r\n            rows={3}\r\n            className=\"w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 resize-none transition-colors\"\r\n            style={{ \r\n              border: '1px solid #d1d5db',\r\n              color: '#374151',\r\n              backgroundColor: '#ffffff'\r\n            }}\r\n            onFocus={(e) => {\r\n              e.target.style.borderColor = 'transparent';\r\n              e.target.style.boxShadow = '0 0 0 2px #3b82f6';\r\n            }}\r\n            onBlur={(e) => {\r\n              e.target.style.borderColor = '#d1d5db';\r\n              e.target.style.boxShadow = 'none';\r\n            }}\r\n          />\r\n          {errors.message && (\r\n            <p className=\"text-xs mt-1\" style={{ color: '#ef4444' }}>\r\n              {errors.message.message}\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Hidden service field */}\r\n        <input {...register('service')} type=\"hidden\" />\r\n\r\n        {/* Error message */}\r\n        {submitStatus === 'error' && (\r\n          <div \r\n            className=\"px-4 py-3 rounded-lg\"\r\n            style={{ \r\n              backgroundColor: '#fef2f2',\r\n              border: '1px solid #fecaca',\r\n              color: '#b91c1c'\r\n            }}\r\n          >\r\n            <div className=\"flex items-center\">\r\n              <svg \r\n                className=\"w-5 h-5 mr-2 flex-shrink-0\" \r\n                fill=\"currentColor\" \r\n                viewBox=\"0 0 20 20\"\r\n                style={{ color: '#b91c1c' }}\r\n              >\r\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n              </svg>\r\n              <div>\r\n                <p className=\"text-sm font-medium\">Form submission failed</p>\r\n                <p className=\"text-sm mt-1\">{errorMessage || 'Please try again or call us directly at (555) 123-4567.'}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Submit Button */}\r\n        <button \r\n          type=\"submit\"\r\n          disabled={isSubmitting}\r\n          className=\"w-full font-bold py-4 rounded-lg transition-all duration-300 flex items-center justify-center\"\r\n          style={{\r\n            background: isSubmitting \r\n              ? 'linear-gradient(to right, #9ca3af, #6b7280)'\r\n              : 'linear-gradient(to right, #2563eb, #1d4ed8)',\r\n            color: '#ffffff',\r\n            cursor: isSubmitting ? 'not-allowed' : 'pointer'\r\n          }}\r\n          onMouseEnter={(e) => {\r\n            if (!isSubmitting) {\r\n              e.currentTarget.style.background = 'linear-gradient(to right, #1d4ed8, #1e40af)';\r\n            }\r\n          }}\r\n          onMouseLeave={(e) => {\r\n            if (!isSubmitting) {\r\n              e.currentTarget.style.background = 'linear-gradient(to right, #2563eb, #1d4ed8)';\r\n            }\r\n          }}\r\n        >\r\n          {isSubmitting ? (\r\n            <>\r\n              <svg \r\n                className=\"animate-spin -ml-1 mr-3 h-5 w-5\" \r\n                xmlns=\"http://www.w3.org/2000/svg\" \r\n                fill=\"none\" \r\n                viewBox=\"0 0 24 24\"\r\n                style={{ color: '#ffffff' }}\r\n              >\r\n                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n              </svg>\r\n              Submitting...\r\n            </>\r\n          ) : (\r\n            serviceContent.ctaText\r\n          )}\r\n        </button>\r\n      </form>\r\n\r\n      {/* Trust indicator */}\r\n      <p \r\n        className=\"text-xs text-center mt-4\"\r\n        style={{ color: '#6b7280' }}\r\n      >\r\n        🔒 Your information is secure and will never be shared. We&apos;ll contact you within 24 hours.\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ServiceContactForm;"], "names": [], "mappings": "AAAA,0FAA0F;;;;;AAC1F;AACA;AAEA;AACA;AACA;AAAA;;;;;;;;;;AAGA,yBAAyB;AACzB,MAAM,gBAAgB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAC1B,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,6KAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAClD,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAC5B,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM;AACnB;AA8BA,8CAA8C;AAC9C,SAAS,gBAAgB,KAAc;IACrC,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAEA,MAAM,qBAAwD,CAAC,EAC7D,OAAO,EACP,YAAY,EAAE,EACd,QAAQ,EACR,iBAAiB,EAAE,EACpB;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IACxE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAgC;IAC/E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACL,QAAQ,EACT,GAAG,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,SAAS,QAAQ,IAAI;YACrB,SAAS;QACX;IACF;IAEA,8CAA8C;IAC9C,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;kEAAoB;oBACxB,IAAI;wBACF,mDAAmD;wBACnD,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;wBAEhC,IAAI,KAAK,YAAY,EAAE;4BACrB,MAAM,cAAc,KAAK,YAAY;4BACrC,eAAe;4BACf,uBAAuB;4BAEvB,2DAA2D;4BAC3D,IAAI;gCACF,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,wBAAqB,AAAD,EAAE;gCAC1C,MAAM,eAAe,CAAC,CAAC,EAAE,aAAa;gCACtC,qBAAqB;gCACrB,SAAS,SAAS;4BACpB,EAAE,OAAM;gCACN,QAAQ,GAAG,CAAC;gCACZ,qBAAqB;gCACrB,SAAS,SAAS;4BACpB;4BACA;wBACF;oBACF,EAAE,OAAM;wBACN,QAAQ,GAAG,CAAC;oBACd;oBAEA,IAAI;wBACF,MAAM,SAAS,UAAU,QAAQ,IAAK,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,EAAE;wBACnF,MAAM,cAAc,QAAQ,MAAM,IAAI,CAAC,EAAE,IAAI;wBAC7C,eAAe;wBACf,uBAAuB;wBAEvB,2DAA2D;wBAC3D,IAAI;4BACF,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,wBAAqB,AAAD,EAAE;4BAC1C,MAAM,eAAe,CAAC,CAAC,EAAE,aAAa;4BACtC,qBAAqB;4BACrB,SAAS,SAAS;wBACpB,EAAE,OAAM;4BACN,QAAQ,GAAG,CAAC;4BACZ,qBAAqB;4BACrB,SAAS,SAAS;wBACpB;oBACF,EAAE,OAAM;wBACN,0BAA0B;wBAC1B,eAAe;wBACf,uBAAuB;wBACvB,qBAAqB;wBACrB,SAAS,SAAS;oBACpB;gBACF;;YAEA;QACF;uCAAG;QAAC;KAAS;IAEb,6CAA6C;IAC7C,MAAM,oBAAoB;QACxB,6EAA6E;QAC7E,MAAM,iBAAiB,QAAQ,IAAI,KAAK,gBAClB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,cACpC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,iBACpC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,sBACpC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QAE1D,MAAM,aAAa,iBAAiB,UAAU;QAE9C,MAAM,cAAc;YAClB,OAAO,CAAC,cAAc,EAAE,QAAQ,IAAI,CAAC,CAAC,EAAE,YAAY;YACpD,UAAU,iBACN,CAAC,aAAa,EAAE,QAAQ,IAAI,CAAC,WAAW,GAAG,iDAAiD,CAAC,GAC7F,CAAC,mDAAmD,EAAE,QAAQ,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC;YAClG,SAAS,QAAQ,OAAO,IAAI,CAAC,YAAY,EAAE,QAAQ,IAAI,CAAC,CAAC,EAAE,YAAY;YACvE,oBAAoB,QAAQ,kBAAkB,IAAI,CAAC,mBAAmB,EAAE,QAAQ,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,iBAAiB,iBAAiB,QAAQ,CAAC,CAAC;QACpJ;QAEA,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,6DAA6D;oBAC7D,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,uDAAuD;oBACvD,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,uDAAuD;oBACvD,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,kDAAkD;oBAClD,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,+CAA+C;oBAC/C,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,+CAA+C;oBAC/C,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,+CAA+C;oBAC/C,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,+CAA+C;oBAC/C,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,+CAA+C;oBAC/C,UAAU;oBACV,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;YAEF;gBACE,OAAO;oBACL,GAAG,WAAW;oBACd,UAAU,QAAQ,QAAQ,IAAI;wBAC5B;wBACA;wBACA;wBACA;qBACD;gBACH;QACJ;IACF;IAEA,MAAM,iBAAiB;IAEvB,MAAM,eAAe,OAAO;QAC1B,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,8CAA8C;YAC9C,MAAM,qBAAuC;gBAC3C,GAAG,IAAI;gBACP;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,IAAI,UAAU;gBACZ,kCAAkC;gBAClC,MAAM,SAAS;gBACf,gBAAgB;gBAChB;gBACA,gDAAgD;gBAChD,WAAW;oBACT,SAAS,SAAS;gBACpB,GAAG;YACL,OAAO;gBACL,uBAAuB;gBACvB,MAAM,WAAW,MAAM,MAAM,gBAAgB;oBAC3C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,MAAM,SAAsB,MAAM,SAAS,IAAI;gBAE/C,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,gBAAgB;oBAChB;oBACA,gDAAgD;oBAChD,WAAW;wBACT,SAAS,SAAS;oBACpB,GAAG;gBACL,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,WAAW,gBAAgB;YACjC,gBAAgB;YAChB,gBAAgB;QAClB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,gBAAgB;IAChB,IAAI,iBAAiB,WAAW;QAC9B,qBACE,0JAAC;YACC,WAAW,CAAC,0BAA0B,EAAE,WAAW;YACnD,OAAO;gBAAE,iBAAiB;YAAU;sBAEpC,cAAA,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAU;kCAEpC,cAAA,0JAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,OAAO;gCAAE,OAAO;4BAAU;sCAE1B,cAAA,0JAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAY;gCAAI,GAAE;;;;;;;;;;;;;;;;kCAGzE,0JAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAU;kCAC3B;;;;;;kCAGD,0JAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAU;;4BAC3B;4BACsG,QAAQ,IAAI,CAAC,WAAW;4BAAG;;;;;;;kCAElI,0JAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAU;kCAC3B;;;;;;kCAGD,0JAAC;wBACC,SAAS;4BACP,gBAAgB;4BAChB,gBAAgB;4BAChB,uDAAuD;4BACvD,WAAW;gCACT,SAAS,SAAS;4BACpB,GAAG;wBACL;wBACA,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAU;wBAC1B,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;wBAChC;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;wBAChC;kCACD;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,0JAAC;QACC,WAAW,CAAC,0BAA0B,EAAE,WAAW;QACnD,OAAO;YAAE,iBAAiB;QAAU;;0BAEpC,0JAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,OAAO;gBAAU;0BAEzB,eAAe,KAAK;;;;;;0BAEvB,0JAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,OAAO;gBAAU;0BAEzB,eAAe,QAAQ;;;;;;0BAG1B,0JAAC;gBAAK,UAAU,aAAa;gBAAe,WAAU;;kCAEpD,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;;kDACC,0JAAC;wCACE,GAAG,SAAS,eAAe;wCAC5B,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,OAAO;4CACL,QAAQ;4CACR,OAAO;4CACP,iBAAiB;wCACnB;wCACA,SAAS,CAAC;4CACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;4CAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wCAC7B;wCACA,QAAQ,CAAC;4CACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;4CAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wCAC7B;;;;;;oCAED,OAAO,YAAY,kBAClB,0JAAC;wCAAE,WAAU;wCAAe,OAAO;4CAAE,OAAO;wCAAU;kDACnD,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;0CAIlC,0JAAC;;kDACC,0JAAC;wCACE,GAAG,SAAS,WAAW;wCACxB,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,OAAO;4CACL,QAAQ;4CACR,OAAO;4CACP,iBAAiB;wCACnB;wCACA,SAAS,CAAC;4CACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;4CAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wCAC7B;wCACA,QAAQ,CAAC;4CACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;4CAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wCAC7B;;;;;;oCAED,OAAO,QAAQ,kBACd,0JAAC;wCAAE,WAAU;wCAAe,OAAO;4CAAE,OAAO;wCAAU;kDACnD,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;kCAOhC,0JAAC;;0CACC,0JAAC;gCACE,GAAG,SAAS,QAAQ;gCACrB,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,OAAO;oCACL,QAAQ;oCACR,OAAO;oCACP,iBAAiB;gCACnB;gCACA,SAAS,CAAC;oCACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;gCACA,QAAQ,CAAC;oCACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;;;;;;4BAED,OAAO,KAAK,kBACX,0JAAC;gCAAE,WAAU;gCAAe,OAAO;oCAAE,OAAO;gCAAU;0CACnD,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAM3B,0JAAC;;0CACC,0JAAC,0JAAA,CAAA,aAAU;gCACT,MAAK;gCACL,SAAS;gCACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC,6JAAA,CAAA,UAAU;4CACR,GAAG,KAAK;4CACT,OAAO,MAAM,KAAK,IAAI;4CACtB,gBAAgB;4CAChB,aAAY;4CACZ,WAAU;4CACV,OAAO;gDACL,uCAAuC;gDACvC,sBAAsB;4CACxB;4CACA,kBAAkB;gDAChB,WAAW;gDACX,OAAO;oDACL,QAAQ;oDACR,OAAO;oDACP,iBAAiB;gDACnB;4CACF;4CACA,oBAAoB;gDAClB,WAAW;gDACX,OAAO;oDACL,QAAQ;oDACR,iBAAiB;oDACjB,UAAU;oDACV,OAAO;gDACT;4CACF;4CACA,UAAU,CAAC;gDACT,MAAM,QAAQ,CAAC;gDACf,6EAA6E;gDAC7E,IAAI,CAAC,SAAS,qBAAqB;oDACjC,IAAI;wDACF,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,wBAAqB,AAAD,EAAE;wDAC1C,MAAM,cAAc,CAAC,CAAC,EAAE,aAAa;wDACrC,WAAW;4DACT,MAAM,QAAQ,CAAC;wDACjB,GAAG;oDACL,EAAE,OAAM;wDACN,QAAQ,GAAG,CAAC;oDACd;gDACF;4CACF;;;;;;;;;;;;;;;;4BAKP,OAAO,KAAK,kBACX,0JAAC;gCAAE,WAAU;gCAAe,OAAO;oCAAE,OAAO;gCAAU;0CACnD,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAM3B,0JAAC;;0CACC,0JAAC;gCACE,GAAG,SAAS,WAAW;gCACxB,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,OAAO;oCACL,QAAQ;oCACR,OAAO;oCACP,iBAAiB;gCACnB;gCACA,SAAS,CAAC;oCACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;gCACA,QAAQ,CAAC;oCACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;;;;;;4BAED,OAAO,QAAQ,kBACd,0JAAC;gCAAE,WAAU;gCAAe,OAAO;oCAAE,OAAO;gCAAU;0CACnD,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;kCAM9B,0JAAC;;0CACC,0JAAC;gCACE,GAAG,SAAS,UAAU;gCACvB,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,OAAO;oCACL,QAAQ;oCACR,OAAO;oCACP,iBAAiB;gCACnB;gCACA,SAAS,CAAC;oCACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;gCACA,QAAQ,CAAC;oCACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;;;;;;4BAED,OAAO,OAAO,kBACb,0JAAC;gCAAE,WAAU;gCAAe,OAAO;oCAAE,OAAO;gCAAU;0CACnD,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;kCAM7B,0JAAC;;0CACC,0JAAC;gCACE,GAAG,SAAS,UAAU;gCACvB,aAAa,eAAe,kBAAkB;gCAC9C,MAAM;gCACN,WAAU;gCACV,OAAO;oCACL,QAAQ;oCACR,OAAO;oCACP,iBAAiB;gCACnB;gCACA,SAAS,CAAC;oCACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;gCACA,QAAQ,CAAC;oCACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oCAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC7B;;;;;;4BAED,OAAO,OAAO,kBACb,0JAAC;gCAAE,WAAU;gCAAe,OAAO;oCAAE,OAAO;gCAAU;0CACnD,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;kCAM7B,0JAAC;wBAAO,GAAG,SAAS,UAAU;wBAAE,MAAK;;;;;;oBAGpC,iBAAiB,yBAChB,0JAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,QAAQ;4BACR,OAAO;wBACT;kCAEA,cAAA,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;oCACR,OAAO;wCAAE,OAAO;oCAAU;8CAE1B,cAAA,0JAAC;wCAAK,UAAS;wCAAU,GAAE;wCAA0N,UAAS;;;;;;;;;;;8CAEhQ,0JAAC;;sDACC,0JAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,0JAAC;4CAAE,WAAU;sDAAgB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;kCAOrD,0JAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;wBACV,OAAO;4BACL,YAAY,eACR,gDACA;4BACJ,OAAO;4BACP,QAAQ,eAAe,gBAAgB;wBACzC;wBACA,cAAc,CAAC;4BACb,IAAI,CAAC,cAAc;gCACjB,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;4BACrC;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,CAAC,cAAc;gCACjB,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;4BACrC;wBACF;kCAEC,6BACC;;8CACE,0JAAC;oCACC,WAAU;oCACV,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,OAAO;wCAAE,OAAO;oCAAU;;sDAE1B,0JAAC;4CAAO,WAAU;4CAAa,IAAG;4CAAK,IAAG;4CAAK,GAAE;4CAAK,QAAO;4CAAe,aAAY;;;;;;sDACxF,0JAAC;4CAAK,WAAU;4CAAa,MAAK;4CAAe,GAAE;;;;;;;;;;;;gCAC/C;;2CAIR,eAAe,OAAO;;;;;;;;;;;;0BAM5B,0JAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,OAAO;gBAAU;0BAC3B;;;;;;;;;;;;AAKP;GAprBM;;QAoBA,0JAAA,CAAA,UAAO;;;KApBP;uCAsrBS", "debugId": null}}, {"offset": {"line": 4408, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/sub-services/ServiceCTA.tsx"], "sourcesContent": ["// ServiceCTA.tsx - Fixed with complete icon support\r\nimport React from 'react'\r\nimport { IconRenderer } from '@/components/global/IconRender'\r\nimport { ServiceCTA as ServiceCTAType } from '@/types/subService'\r\nimport ServiceContactForm from '@/components/global/Form'\r\n\r\ninterface ServiceCTAProps {\r\n  data?: ServiceCTAType\r\n  serviceName?: string\r\n}\r\n\r\n// Helper function to safely get error message\r\nfunction getErrorMessage(error: unknown): string {\r\n  if (error instanceof Error) {\r\n    return error.message\r\n  }\r\n  if (typeof error === 'string') {\r\n    return error\r\n  }\r\n  return 'An unknown error occurred'\r\n}\r\n\r\ninterface FormData {\r\n  businessName: string\r\n  fullName: string\r\n  email: string\r\n  phone: string\r\n  location: string\r\n  website?: string\r\n  message: string\r\n  service: string\r\n  userCountry?: string\r\n  timestamp?: string\r\n}\r\n\r\nexport const ServiceCTA: React.FC<ServiceCTAProps> = ({ data, serviceName = 'Service' }) => {\r\n  if (!data) return null\r\n\r\n  const handleFormSubmit = async (formData: FormData) => {\r\n    try {\r\n      console.log('Service form submitted:', formData)\r\n      \r\n      // Send to API\r\n      const response = await fetch('/api/contact', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(formData),\r\n      })\r\n\r\n      const result = await response.json()\r\n\r\n      if (!response.ok) {\r\n        throw new Error(result.message || 'Failed to submit form')\r\n      }\r\n\r\n      if (!result.success) {\r\n        throw new Error(result.message || 'Form submission failed')\r\n      }\r\n\r\n      console.log('Form submitted successfully:', result)\r\n      \r\n    } catch (error) {\r\n      console.error('Form submission error:', error)\r\n      throw new Error(getErrorMessage(error))\r\n    }\r\n  }\r\n\r\n  // Convert serviceName to valid type - Updated to handle all services dynamically\r\n  const getServiceType = (name: string): \"seo\" | \"local-seo\" | \"ppc\" | \"web-design\" | \"social-media\" | \"email-marketing\" | \"branding\" | \"conversion-optimization\" | \"reputation-management\" => {\r\n    const normalized = name.toLowerCase().replace(/\\s+/g, '-').replace(/services?$/, '')\r\n\r\n    // Handle keyword-based detection for better matching\r\n    if (normalized.includes('local') && normalized.includes('seo')) return 'local-seo'\r\n    if (normalized.includes('ppc') || normalized.includes('advertising')) return 'ppc'\r\n    if (normalized.includes('web') && (normalized.includes('design') || normalized.includes('development'))) return 'web-design'\r\n    if (normalized.includes('social') && normalized.includes('media')) return 'social-media'\r\n    if (normalized.includes('email') && normalized.includes('marketing')) return 'email-marketing'\r\n    if (normalized.includes('branding') || normalized.includes('brand')) return 'branding'\r\n    if (normalized.includes('conversion') && normalized.includes('optimization')) return 'conversion-optimization'\r\n    if (normalized.includes('reputation') && normalized.includes('management')) return 'reputation-management'\r\n\r\n    // Handle specific service titles from Sanity (without \"services\" suffix)\r\n    switch (normalized) {\r\n      case 'local-seo':\r\n        return 'local-seo'\r\n      case 'ppc-advertising':\r\n      case 'ppc':\r\n        return 'ppc'\r\n      case 'web-design':\r\n      case 'web-development':\r\n        return 'web-design'\r\n      case 'social-media-marketing':\r\n      case 'social-media':\r\n        return 'social-media'\r\n      case 'email-marketing':\r\n        return 'email-marketing'\r\n      case 'branding':\r\n        return 'branding'\r\n      case 'conversion-optimization':\r\n        return 'conversion-optimization'\r\n      case 'reputation-management':\r\n        return 'reputation-management'\r\n      default:\r\n        // If it contains 'seo' but not 'local', default to seo\r\n        if (normalized.includes('seo')) return 'seo'\r\n        return 'seo'\r\n    }\r\n  }\r\n\r\n  return (\r\n    <section className=\"bg-blue-50 py-16\">\r\n      <div className=\"max-w-6xl mx-auto px-6\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\r\n          <div>\r\n            {data.title && (\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-800 mb-6\">\r\n                {data.title}\r\n              </h2>\r\n            )}\r\n            \r\n            {data.description && (\r\n              <p className=\"text-lg text-gray-600 mb-6 leading-relaxed\">\r\n                {data.description}\r\n              </p>\r\n            )}\r\n            \r\n            {data.benefits && data.benefits.length > 0 && (\r\n              <div className=\"space-y-4 mb-8\">\r\n                {data.benefits.map((benefit, index) => (\r\n                  <div key={index} className=\"flex items-center\">\r\n                    <IconRenderer iconName=\"CheckCircle\" className=\"text-blue-600 mr-3\" size={20} />\r\n                    <span className=\"text-gray-700\">{benefit}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            \r\n\r\n          </div>\r\n          \r\n          <ServiceContactForm \r\n            service={{\r\n              name: serviceName,\r\n              type: getServiceType(serviceName),\r\n              ctaText: data.formSettings?.ctaText || `Get My Free ${serviceName} Analysis`,\r\n              messagePlaceholder: data.formSettings?.messagePlaceholder || `Tell us about your business and ${serviceName.toLowerCase()} goals*`,\r\n              benefits: data.benefits || []\r\n            }}\r\n            onSubmit={handleFormSubmit}\r\n          />\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}"], "names": [], "mappings": "AAAA,oDAAoD;;;;;AAEpD;AAEA;;;;AAOA,8CAA8C;AAC9C,SAAS,gBAAgB,KAAc;IACrC,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAeO,MAAM,aAAwC,CAAC,EAAE,IAAI,EAAE,cAAc,SAAS,EAAE;IACrF,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,QAAQ,GAAG,CAAC,gCAAgC;QAE9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM,gBAAgB;QAClC;IACF;IAEA,iFAAiF;IACjF,MAAM,iBAAiB,CAAC;QACtB,MAAM,aAAa,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,cAAc;QAEjF,qDAAqD;QACrD,IAAI,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,QAAQ,OAAO;QACvE,IAAI,WAAW,QAAQ,CAAC,UAAU,WAAW,QAAQ,CAAC,gBAAgB,OAAO;QAC7E,IAAI,WAAW,QAAQ,CAAC,UAAU,CAAC,WAAW,QAAQ,CAAC,aAAa,WAAW,QAAQ,CAAC,cAAc,GAAG,OAAO;QAChH,IAAI,WAAW,QAAQ,CAAC,aAAa,WAAW,QAAQ,CAAC,UAAU,OAAO;QAC1E,IAAI,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,cAAc,OAAO;QAC7E,IAAI,WAAW,QAAQ,CAAC,eAAe,WAAW,QAAQ,CAAC,UAAU,OAAO;QAC5E,IAAI,WAAW,QAAQ,CAAC,iBAAiB,WAAW,QAAQ,CAAC,iBAAiB,OAAO;QACrF,IAAI,WAAW,QAAQ,CAAC,iBAAiB,WAAW,QAAQ,CAAC,eAAe,OAAO;QAEnF,yEAAyE;QACzE,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,uDAAuD;gBACvD,IAAI,WAAW,QAAQ,CAAC,QAAQ,OAAO;gBACvC,OAAO;QACX;IACF;IAEA,qBACE,0JAAC;QAAQ,WAAU;kBACjB,cAAA,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;;4BACE,KAAK,KAAK,kBACT,0JAAC;gCAAG,WAAU;0CACX,KAAK,KAAK;;;;;;4BAId,KAAK,WAAW,kBACf,0JAAC;gCAAE,WAAU;0CACV,KAAK,WAAW;;;;;;4BAIpB,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACvC,0JAAC;gCAAI,WAAU;0CACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,0JAAC;wCAAgB,WAAU;;0DACzB,0JAAC,+HAAA,CAAA,eAAY;gDAAC,UAAS;gDAAc,WAAU;gDAAqB,MAAM;;;;;;0DAC1E,0JAAC;gDAAK,WAAU;0DAAiB;;;;;;;uCAFzB;;;;;;;;;;;;;;;;kCAWlB,0JAAC,yHAAA,CAAA,UAAkB;wBACjB,SAAS;4BACP,MAAM;4BACN,MAAM,eAAe;4BACrB,SAAS,KAAK,YAAY,EAAE,WAAW,CAAC,YAAY,EAAE,YAAY,SAAS,CAAC;4BAC5E,oBAAoB,KAAK,YAAY,EAAE,sBAAsB,CAAC,gCAAgC,EAAE,YAAY,WAAW,GAAG,OAAO,CAAC;4BAClI,UAAU,KAAK,QAAQ,IAAI,EAAE;wBAC/B;wBACA,UAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB;KAzHa", "debugId": null}}, {"offset": {"line": 4600, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/sub-services/ServiceDetails.tsx"], "sourcesContent": ["// ServiceDetails.tsx - Complete implementation with all 4 schema sections\r\nimport React from 'react'\r\nimport Image from 'next/image'\r\nimport Link from 'next/link'\r\nimport { PortableText } from '@portabletext/react'\r\nimport { urlForImage } from '@/lib/sanity'\r\nimport { IconRenderer } from '@/components/global/IconRender'\r\nimport { \r\n  ServiceDetail, \r\n  WhyServiceMatters, \r\n  StrategicImplementation, \r\n  MarketIntelligence \r\n} from '@/types/subService'\r\n\r\ninterface ServiceDetailsProps {\r\n  whyMatters?: WhyServiceMatters\r\n  services?: ServiceDetail[]\r\n  strategicImplementation?: StrategicImplementation\r\n  marketIntelligence?: MarketIntelligence\r\n}\r\n\r\nexport const ServiceDetails: React.FC<ServiceDetailsProps> = ({\r\n  whyMatters,\r\n  services,\r\n  strategicImplementation,\r\n  marketIntelligence\r\n}) => {\r\n  return (\r\n    <>\r\n      {/* Section 1: Why Service Matters */}\r\n      {whyMatters && (\r\n        <section className=\"py-24 bg-white\">\r\n          <div className=\"max-w-7xl mx-auto px-6\">\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\r\n              <div>\r\n                {whyMatters.title && (\r\n                  <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-6\">\r\n                    {whyMatters.title}\r\n                  </h2>\r\n                )}\r\n                \r\n                {whyMatters.description && (\r\n                  <div className=\"text-xl text-gray-600 mb-6 leading-relaxed prose prose-lg max-w-none\">\r\n                    <PortableText value={whyMatters.description} />\r\n                  </div>\r\n                )}\r\n                \r\n                {whyMatters.features && whyMatters.features.length > 0 && (\r\n                  <div className=\"space-y-4 mb-8\">\r\n                    {whyMatters.features.map((feature, index) => (\r\n                      <div key={index} className=\"flex items-center\">\r\n                        <div className=\"bg-blue-100 p-2 rounded-full mr-4\">\r\n                          <IconRenderer iconName=\"CheckCircle\" className=\"text-blue-600\" size={20} />\r\n                        </div>\r\n                        <span className=\"text-gray-700\">{feature.text}</span>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n\r\n\r\n              </div>\r\n              \r\n              {whyMatters.stats && whyMatters.stats.length > 0 && (\r\n                <div className=\"grid grid-cols-2 gap-6\">\r\n                  {whyMatters.stats.map((stat, index) => (\r\n                    <div key={index} className={`bg-gradient-to-br ${stat.color || 'from-blue-50 to-blue-100'} p-6 rounded-2xl text-center`}>\r\n                      <div className=\"text-4xl font-bold text-blue-600 mb-2\">{stat.value}</div>\r\n                      <div className=\"text-gray-700 text-sm\">{stat.label}</div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </section>\r\n      )}\r\n\r\n      {/* Section 2: Complete Service Details */}\r\n      {services && services.length > 0 && (\r\n        <section className=\"py-24 bg-gray-50\">\r\n          <div className=\"max-w-7xl mx-auto px-6\">\r\n            <div className=\"text-center mb-20\">\r\n              <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-6\">\r\n                Complete Service Solutions\r\n              </h2>\r\n              <p className=\"text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed\">\r\n                Our comprehensive approach combines strategic optimization, technical expertise, and ongoing management to establish your business as the dominant authority in your market.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"space-y-20\">\r\n              {services.map((service, index) => {\r\n                const isEven = index % 2 === 0\r\n                \r\n                return (\r\n                  <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-16 items-center`}>\r\n                    <div className={`${isEven ? 'lg:order-1' : 'lg:order-2'}`}>\r\n                      <div className=\"flex items-center mb-6\">\r\n                        <div className=\"bg-blue-600 p-4 rounded-2xl mr-4\">\r\n                          <IconRenderer iconName={service.icon} size={32} className=\"text-white\" />\r\n                        </div>\r\n                        <div>\r\n                          <h3 className=\"text-3xl font-bold text-gray-800\">{service.title}</h3>\r\n                          {service.result && (\r\n                            <div className=\"bg-blue-50 px-4 py-2 rounded-full inline-block mt-2\">\r\n                              <span className=\"text-blue-600 font-semibold\">{service.result}</span>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                      \r\n                      <div className=\"mb-8\">\r\n                        {service.description && (\r\n                          <p className=\"text-lg text-gray-600 mb-6 leading-relaxed\">\r\n                            {service.description}\r\n                          </p>\r\n                        )}\r\n                        {service.fullDescription && (\r\n                          <p className=\"text-gray-600 mb-6 leading-relaxed\">\r\n                            {service.fullDescription}\r\n                          </p>\r\n                        )}\r\n                        {service.benefits && (\r\n                          <p className=\"text-gray-600 leading-relaxed\">\r\n                            {service.benefits}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-2xl\">\r\n                        <h4 className=\"text-lg font-semibold text-blue-800 mb-3\">Service Impact</h4>\r\n                        <p className=\"text-blue-700 leading-relaxed\">\r\n                          This specialized approach creates measurable improvements in search visibility, customer engagement, and business growth through systematic optimization and ongoing management.\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <div className={`${isEven ? 'lg:order-2' : 'lg:order-1'}`}>\r\n                      <div className=\"bg-white rounded-3xl p-8 shadow-lg\">\r\n                        {service.image && (\r\n                          <Image\r\n                            src={urlForImage(service.image).width(600).height(400).url()}\r\n                            alt={`${service.title} optimization`}\r\n                            width={600}\r\n                            height={192}\r\n                            className=\"w-full h-48 object-cover rounded-2xl mb-6\"\r\n                          />\r\n                        )}\r\n                        \r\n                        {service.features && service.features.length > 0 && (\r\n                          <>\r\n                            <h4 className=\"text-xl font-bold text-gray-800 mb-6\">\r\n                              Implementation Elements\r\n                            </h4>\r\n                            <div className=\"space-y-4\">\r\n                              {service.features.map((feature, featureIndex) => (\r\n                                <div key={featureIndex} className=\"flex items-start\">\r\n                                  <div className=\"bg-blue-100 p-1 rounded-full mr-3 mt-1 flex-shrink-0\">\r\n                                    <IconRenderer iconName=\"CheckCircle\" size={14} className=\"text-blue-600\" />\r\n                                  </div>\r\n                                  <span className=\"text-gray-700\">{feature}</span>\r\n                                </div>\r\n                              ))}\r\n                            </div>\r\n                          </>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )\r\n              })}\r\n            </div>\r\n          </div>\r\n        </section>\r\n      )}\r\n\r\n      {/* Section 3: Strategic Implementation */}\r\n      {strategicImplementation && (\r\n        <section className=\"py-24 bg-white\">\r\n          <div className=\"max-w-7xl mx-auto px-6\">\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20\">\r\n              <div>\r\n                {strategicImplementation.title && (\r\n                  <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-6\">\r\n                    {strategicImplementation.title}\r\n                  </h2>\r\n                )}\r\n                {strategicImplementation.description && (\r\n                  <p className=\"text-xl text-gray-600 mb-6 leading-relaxed\">\r\n                    {strategicImplementation.description}\r\n                  </p>\r\n                )}\r\n                {strategicImplementation.secondaryDescription && (\r\n                  <p className=\"text-lg text-gray-600 mb-8 leading-relaxed\">\r\n                    {strategicImplementation.secondaryDescription}\r\n                  </p>\r\n                )}\r\n                {strategicImplementation.features && strategicImplementation.features.length > 0 && (\r\n                  <div className=\"space-y-4\">\r\n                    {strategicImplementation.features.map((feature, index) => (\r\n                      <div key={index} className=\"flex items-center\">\r\n                        <div className=\"bg-blue-100 p-2 rounded-full mr-4\">\r\n                          <IconRenderer iconName=\"CheckCircle\" className=\"text-blue-600\" size={20} />\r\n                        </div>\r\n                        <span className=\"text-gray-700\">{feature}</span>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n              </div>\r\n              <div>\r\n                {strategicImplementation.image && (\r\n                  <Image\r\n                    src={urlForImage(strategicImplementation.image).width(600).height(400).url()}\r\n                    alt=\"Strategic implementation planning\"\r\n                    width={600}\r\n                    height={400}\r\n                    className=\"w-full rounded-3xl shadow-2xl\"\r\n                  />\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {strategicImplementation.sections && strategicImplementation.sections.length > 0 && (\r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n                {strategicImplementation.sections.map((section, index) => (\r\n                  <div key={index} className={`bg-gradient-to-br ${section.color || 'from-blue-50 to-blue-100'} rounded-2xl p-8 text-center`}>\r\n                    <div className=\"bg-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                      <IconRenderer iconName={section.icon} size={32} className=\"text-white\" />\r\n                    </div>\r\n                    <h3 className=\"text-xl font-bold text-gray-800 mb-3\">{section.title}</h3>\r\n                    <p className=\"text-gray-600 leading-relaxed\">{section.description}</p>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </section>\r\n      )}\r\n\r\n      {/* Section 4: Market Intelligence */}\r\n      {marketIntelligence && (\r\n        <section className={`py-24 bg-gradient-to-r ${marketIntelligence.backgroundGradient || 'from-blue-50 to-blue-100'}`}>\r\n          <div className=\"max-w-7xl mx-auto px-6\">\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\r\n              <div>\r\n                {marketIntelligence.image && (\r\n                  <Image\r\n                    src={urlForImage(marketIntelligence.image).width(600).height(400).url()}\r\n                    alt=\"Market intelligence and insights\"\r\n                    width={600}\r\n                    height={400}\r\n                    className=\"w-full rounded-3xl shadow-xl\"\r\n                  />\r\n                )}\r\n              </div>\r\n              <div>\r\n                {marketIntelligence.title && (\r\n                  <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-6\">\r\n                    {marketIntelligence.title}\r\n                  </h2>\r\n                )}\r\n                {marketIntelligence.description && (\r\n                  <p className=\"text-xl text-gray-600 mb-6 leading-relaxed\">\r\n                    {marketIntelligence.description}\r\n                  </p>\r\n                )}\r\n                {marketIntelligence.secondaryDescription && (\r\n                  <p className=\"text-lg text-gray-600 mb-8 leading-relaxed\">\r\n                    {marketIntelligence.secondaryDescription}\r\n                  </p>\r\n                )}\r\n                \r\n                {marketIntelligence.stats && marketIntelligence.stats.length > 0 && (\r\n                  <div className=\"grid grid-cols-2 gap-6 mb-8\">\r\n                    {marketIntelligence.stats.map((stat, index) => (\r\n                      <div key={index} className=\"bg-white p-6 rounded-xl text-center shadow-md\">\r\n                        <div className=\"text-3xl font-bold text-blue-600 mb-2\">{stat.value}</div>\r\n                        <div className=\"text-gray-700 text-sm\">{stat.label}</div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n\r\n                {marketIntelligence.ctaButton?.text && (\r\n                  <Link\r\n                    href=\"/contact-us\"\r\n                    className=\"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg inline-block\"\r\n                  >\r\n                    {marketIntelligence.ctaButton.text}\r\n                  </Link>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </section>\r\n      )}\r\n    </>\r\n  )\r\n}"], "names": [], "mappings": "AAAA,0EAA0E;;;;;AAE1E;AACA;AACA;AACA;AACA;;;;;;;AAeO,MAAM,iBAAgD,CAAC,EAC5D,UAAU,EACV,QAAQ,EACR,uBAAuB,EACvB,kBAAkB,EACnB;IACC,qBACE;;YAEG,4BACC,0JAAC;gBAAQ,WAAU;0BACjB,cAAA,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;;oCACE,WAAW,KAAK,kBACf,0JAAC;wCAAG,WAAU;kDACX,WAAW,KAAK;;;;;;oCAIpB,WAAW,WAAW,kBACrB,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC,oKAAA,CAAA,eAAY;4CAAC,OAAO,WAAW,WAAW;;;;;;;;;;;oCAI9C,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG,mBACnD,0JAAC;wCAAI,WAAU;kDACZ,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACjC,0JAAC;gDAAgB,WAAU;;kEACzB,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC,+HAAA,CAAA,eAAY;4DAAC,UAAS;4DAAc,WAAU;4DAAgB,MAAM;;;;;;;;;;;kEAEvE,0JAAC;wDAAK,WAAU;kEAAiB,QAAQ,IAAI;;;;;;;+CAJrC;;;;;;;;;;;;;;;;4BAajB,WAAW,KAAK,IAAI,WAAW,KAAK,CAAC,MAAM,GAAG,mBAC7C,0JAAC;gCAAI,WAAU;0CACZ,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC3B,0JAAC;wCAAgB,WAAW,CAAC,kBAAkB,EAAE,KAAK,KAAK,IAAI,2BAA2B,4BAA4B,CAAC;;0DACrH,0JAAC;gDAAI,WAAU;0DAAyC,KAAK,KAAK;;;;;;0DAClE,0JAAC;gDAAI,WAAU;0DAAyB,KAAK,KAAK;;;;;;;uCAF1C;;;;;;;;;;;;;;;;;;;;;;;;;;YAavB,YAAY,SAAS,MAAM,GAAG,mBAC7B,0JAAC;gBAAQ,WAAU;0BACjB,cAAA,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,0JAAC;oCAAE,WAAU;8CAA0D;;;;;;;;;;;;sCAKzE,0JAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS;gCACtB,MAAM,SAAS,QAAQ,MAAM;gCAE7B,qBACE,0JAAC;oCAAgB,WAAW,CAAC,mDAAmD,CAAC;;sDAC/E,0JAAC;4CAAI,WAAW,GAAG,SAAS,eAAe,cAAc;;8DACvD,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAI,WAAU;sEACb,cAAA,0JAAC,+HAAA,CAAA,eAAY;gEAAC,UAAU,QAAQ,IAAI;gEAAE,MAAM;gEAAI,WAAU;;;;;;;;;;;sEAE5D,0JAAC;;8EACC,0JAAC;oEAAG,WAAU;8EAAoC,QAAQ,KAAK;;;;;;gEAC9D,QAAQ,MAAM,kBACb,0JAAC;oEAAI,WAAU;8EACb,cAAA,0JAAC;wEAAK,WAAU;kFAA+B,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;8DAMrE,0JAAC;oDAAI,WAAU;;wDACZ,QAAQ,WAAW,kBAClB,0JAAC;4DAAE,WAAU;sEACV,QAAQ,WAAW;;;;;;wDAGvB,QAAQ,eAAe,kBACtB,0JAAC;4DAAE,WAAU;sEACV,QAAQ,eAAe;;;;;;wDAG3B,QAAQ,QAAQ,kBACf,0JAAC;4DAAE,WAAU;sEACV,QAAQ,QAAQ;;;;;;;;;;;;8DAKvB,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,0JAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAMjD,0JAAC;4CAAI,WAAW,GAAG,SAAS,eAAe,cAAc;sDACvD,cAAA,0JAAC;gDAAI,WAAU;;oDACZ,QAAQ,KAAK,kBACZ,0JAAC,yHAAA,CAAA,UAAK;wDACJ,KAAK,CAAA,GAAA,yGAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG;wDAC1D,KAAK,GAAG,QAAQ,KAAK,CAAC,aAAa,CAAC;wDACpC,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;oDAIb,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBAC7C;;0EACE,0JAAC;gEAAG,WAAU;0EAAuC;;;;;;0EAGrD,0JAAC;gEAAI,WAAU;0EACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,0JAAC;wEAAuB,WAAU;;0FAChC,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC,+HAAA,CAAA,eAAY;oFAAC,UAAS;oFAAc,MAAM;oFAAI,WAAU;;;;;;;;;;;0FAE3D,0JAAC;gFAAK,WAAU;0FAAiB;;;;;;;uEAJzB;;;;;;;;;;;;;;;;;;;;;;;;mCA7Dd;;;;;4BA2Ed;;;;;;;;;;;;;;;;;YAOP,yCACC,0JAAC;gBAAQ,WAAU;0BACjB,cAAA,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;;wCACE,wBAAwB,KAAK,kBAC5B,0JAAC;4CAAG,WAAU;sDACX,wBAAwB,KAAK;;;;;;wCAGjC,wBAAwB,WAAW,kBAClC,0JAAC;4CAAE,WAAU;sDACV,wBAAwB,WAAW;;;;;;wCAGvC,wBAAwB,oBAAoB,kBAC3C,0JAAC;4CAAE,WAAU;sDACV,wBAAwB,oBAAoB;;;;;;wCAGhD,wBAAwB,QAAQ,IAAI,wBAAwB,QAAQ,CAAC,MAAM,GAAG,mBAC7E,0JAAC;4CAAI,WAAU;sDACZ,wBAAwB,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9C,0JAAC;oDAAgB,WAAU;;sEACzB,0JAAC;4DAAI,WAAU;sEACb,cAAA,0JAAC,+HAAA,CAAA,eAAY;gEAAC,UAAS;gEAAc,WAAU;gEAAgB,MAAM;;;;;;;;;;;sEAEvE,0JAAC;4DAAK,WAAU;sEAAiB;;;;;;;mDAJzB;;;;;;;;;;;;;;;;8CAUlB,0JAAC;8CACE,wBAAwB,KAAK,kBAC5B,0JAAC,yHAAA,CAAA,UAAK;wCACJ,KAAK,CAAA,GAAA,yGAAA,CAAA,cAAW,AAAD,EAAE,wBAAwB,KAAK,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG;wCAC1E,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;;wBAMjB,wBAAwB,QAAQ,IAAI,wBAAwB,QAAQ,CAAC,MAAM,GAAG,mBAC7E,0JAAC;4BAAI,WAAU;sCACZ,wBAAwB,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9C,0JAAC;oCAAgB,WAAW,CAAC,kBAAkB,EAAE,QAAQ,KAAK,IAAI,2BAA2B,4BAA4B,CAAC;;sDACxH,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC,+HAAA,CAAA,eAAY;gDAAC,UAAU,QAAQ,IAAI;gDAAE,MAAM;gDAAI,WAAU;;;;;;;;;;;sDAE5D,0JAAC;4CAAG,WAAU;sDAAwC,QAAQ,KAAK;;;;;;sDACnE,0JAAC;4CAAE,WAAU;sDAAiC,QAAQ,WAAW;;;;;;;mCALzD;;;;;;;;;;;;;;;;;;;;;YAerB,oCACC,0JAAC;gBAAQ,WAAW,CAAC,uBAAuB,EAAE,mBAAmB,kBAAkB,IAAI,4BAA4B;0BACjH,cAAA,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;0CACE,mBAAmB,KAAK,kBACvB,0JAAC,yHAAA,CAAA,UAAK;oCACJ,KAAK,CAAA,GAAA,yGAAA,CAAA,cAAW,AAAD,EAAE,mBAAmB,KAAK,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG;oCACrE,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAIhB,0JAAC;;oCACE,mBAAmB,KAAK,kBACvB,0JAAC;wCAAG,WAAU;kDACX,mBAAmB,KAAK;;;;;;oCAG5B,mBAAmB,WAAW,kBAC7B,0JAAC;wCAAE,WAAU;kDACV,mBAAmB,WAAW;;;;;;oCAGlC,mBAAmB,oBAAoB,kBACtC,0JAAC;wCAAE,WAAU;kDACV,mBAAmB,oBAAoB;;;;;;oCAI3C,mBAAmB,KAAK,IAAI,mBAAmB,KAAK,CAAC,MAAM,GAAG,mBAC7D,0JAAC;wCAAI,WAAU;kDACZ,mBAAmB,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACnC,0JAAC;gDAAgB,WAAU;;kEACzB,0JAAC;wDAAI,WAAU;kEAAyC,KAAK,KAAK;;;;;;kEAClE,0JAAC;wDAAI,WAAU;kEAAyB,KAAK,KAAK;;;;;;;+CAF1C;;;;;;;;;;oCAQf,mBAAmB,SAAS,EAAE,sBAC7B,0JAAC,wHAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,mBAAmB,SAAS,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtD;KAvRa", "debugId": null}}, {"offset": {"line": 5296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/sub-services/CaseStudy.tsx"], "sourcesContent": ["// CaseStudy.tsx - Fixed version\r\nimport React from 'react'\r\nimport Image from 'next/image'\r\nimport { urlForImage } from '@/lib/sanity'\r\nimport { CaseStudy as CaseStudyType } from '@/types/subService'\r\n\r\ninterface CaseStudyProps {\r\n  data?: CaseStudyType\r\n}\r\n\r\nexport const CaseStudy: React.FC<CaseStudyProps> = ({ data }) => {\r\n  if (!data) return null\r\n\r\n  return (\r\n    <section className={`py-24 bg-gradient-to-r ${data.backgroundGradient || 'from-blue-600 to-blue-800'}`}>\r\n      <div className=\"max-w-7xl mx-auto px-6\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\r\n          <div>\r\n            {data.title && (\r\n              <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-8\">\r\n                {data.title}\r\n              </h2>\r\n            )}\r\n            \r\n            {data.description && (\r\n              <p className=\"text-xl text-blue-100 mb-8 leading-relaxed\">\r\n                {data.description}\r\n              </p>\r\n            )}\r\n            \r\n            {data.results && data.results.length > 0 && (\r\n              <div className=\"grid grid-cols-2 gap-6 mb-8\">\r\n                {data.results.map((result, index) => (\r\n                  <div key={index} className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-center\">\r\n                    <div className=\"text-3xl font-bold text-blue-200 mb-2\">{result.value}</div>\r\n                    <div className=\"text-blue-100 text-sm\">{result.label}</div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            \r\n            {data.ctaButton?.text && (\r\n              <button className=\"bg-white text-blue-600 font-semibold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300\">\r\n                {data.ctaButton.text}\r\n              </button>\r\n            )}\r\n          </div>\r\n          \r\n          <div>\r\n            {data.image && (\r\n              <Image\r\n                src={urlForImage(data.image).width(600).height(400).url()}\r\n                alt=\"Case study success story\"\r\n                width={600}\r\n                height={400}\r\n                className=\"w-full rounded-3xl shadow-2xl\"\r\n              />\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}"], "names": [], "mappings": "AAAA,gCAAgC;;;;;AAEhC;AACA;;;;AAOO,MAAM,YAAsC,CAAC,EAAE,IAAI,EAAE;IAC1D,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,0JAAC;QAAQ,WAAW,CAAC,uBAAuB,EAAE,KAAK,kBAAkB,IAAI,6BAA6B;kBACpG,cAAA,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;;4BACE,KAAK,KAAK,kBACT,0JAAC;gCAAG,WAAU;0CACX,KAAK,KAAK;;;;;;4BAId,KAAK,WAAW,kBACf,0JAAC;gCAAE,WAAU;0CACV,KAAK,WAAW;;;;;;4BAIpB,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,mBACrC,0JAAC;gCAAI,WAAU;0CACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACzB,0JAAC;wCAAgB,WAAU;;0DACzB,0JAAC;gDAAI,WAAU;0DAAyC,OAAO,KAAK;;;;;;0DACpE,0JAAC;gDAAI,WAAU;0DAAyB,OAAO,KAAK;;;;;;;uCAF5C;;;;;;;;;;4BAQf,KAAK,SAAS,EAAE,sBACf,0JAAC;gCAAO,WAAU;0CACf,KAAK,SAAS,CAAC,IAAI;;;;;;;;;;;;kCAK1B,0JAAC;kCACE,KAAK,KAAK,kBACT,0JAAC,yHAAA,CAAA,UAAK;4BACJ,KAAK,CAAA,GAAA,yGAAA,CAAA,cAAW,AAAD,EAAE,KAAK,KAAK,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG;4BACvD,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1B;KArDa", "debugId": null}}, {"offset": {"line": 5425, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/sub-services/ServiceProcess.tsx"], "sourcesContent": ["// ServiceProcess.tsx - Fixed with IconRenderer and CaseStudy integration\r\nimport React from 'react'\r\nimport { Icon<PERSON><PERSON><PERSON> } from '@/components/global/IconRender'\r\nimport { CaseStudy } from './CaseStudy'\r\nimport { ServiceProcess as ServiceProcessType, CaseStudy as CaseStudyType } from '@/types/subService'\r\n\r\ninterface ServiceProcessProps {\r\n  data?: ServiceProcessType\r\n  caseStudy?: CaseStudyType\r\n}\r\n\r\nexport const ServiceProcess: React.FC<ServiceProcessProps> = ({ data, caseStudy }) => {\r\n  return (\r\n    <>\r\n      {/* Process Section */}\r\n      {data && data.steps && data.steps.length > 0 && (\r\n        <section className=\"py-24 bg-white\">\r\n          <div className=\"max-w-7xl mx-auto px-6\">\r\n            <div className=\"text-center mb-20\">\r\n              <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-6\">\r\n                {data.title || 'Our Proven Process'}\r\n              </h2>\r\n              {data.description && (\r\n                <p className=\"text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed\">\r\n                  {data.description}\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"space-y-16\">\r\n              {data.steps.map((step, index) => (\r\n                <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${index % 2 === 0 ? '' : 'lg:grid-cols-2'}`}>\r\n                  <div className={`${index % 2 === 0 ? 'lg:order-1' : 'lg:order-2'}`}>\r\n                    <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mb-6\">\r\n                      {step.step}\r\n                    </div>\r\n                    <h3 className=\"text-3xl font-bold text-gray-800 mb-4\">\r\n                      {step.title}\r\n                    </h3>\r\n                    <p className=\"text-lg text-gray-600 mb-6 leading-relaxed\">\r\n                      {step.description}\r\n                    </p>\r\n                    {step.details && step.details.length > 0 && (\r\n                      <ul className=\"space-y-3\">\r\n                        {step.details.map((detail, detailIndex) => (\r\n                          <li key={detailIndex} className=\"flex items-center text-gray-700\">\r\n                            <IconRenderer \r\n                              iconName={step.icon} \r\n                              size={20} \r\n                              className=\"text-blue-500 mr-3 flex-shrink-0\" \r\n                            />\r\n                            {detail}\r\n                          </li>\r\n                        ))}\r\n                      </ul>\r\n                    )}\r\n                  </div>\r\n                  <div className={`${index % 2 === 0 ? 'lg:order-2' : 'lg:order-1'}`}>\r\n                    <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-3xl\">\r\n                      <div className=\"flex justify-center mb-4\">\r\n                        <IconRenderer \r\n                          iconName={step.icon} \r\n                          size={80} \r\n                          className=\"text-blue-500\" \r\n                        />\r\n                      </div>\r\n                      <div className=\"text-center\">\r\n                        <div className=\"text-2xl font-bold text-gray-800 mb-2\">\r\n                          Step {step.step}\r\n                        </div>\r\n                        <div className=\"text-blue-600 font-semibold\">\r\n                          {step.title}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </section>\r\n      )}\r\n\r\n      {/* Case Study Section */}\r\n      <CaseStudy data={caseStudy} />\r\n    </>\r\n  )\r\n}"], "names": [], "mappings": "AAAA,yEAAyE;;;;;AAEzE;AACA;;;;AAQO,MAAM,iBAAgD,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE;IAC/E,qBACE;;YAEG,QAAQ,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,mBACzC,0JAAC;gBAAQ,WAAU;0BACjB,cAAA,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAG,WAAU;8CACX,KAAK,KAAK,IAAI;;;;;;gCAEhB,KAAK,WAAW,kBACf,0JAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;;;;;;sCAKvB,0JAAC;4BAAI,WAAU;sCACZ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,0JAAC;oCAAgB,WAAW,CAAC,oDAAoD,EAAE,QAAQ,MAAM,IAAI,KAAK,kBAAkB;;sDAC1H,0JAAC;4CAAI,WAAW,GAAG,QAAQ,MAAM,IAAI,eAAe,cAAc;;8DAChE,0JAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI;;;;;;8DAEZ,0JAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,0JAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;gDAElB,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,mBACrC,0JAAC;oDAAG,WAAU;8DACX,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,0JAAC;4DAAqB,WAAU;;8EAC9B,0JAAC,+HAAA,CAAA,eAAY;oEACX,UAAU,KAAK,IAAI;oEACnB,MAAM;oEACN,WAAU;;;;;;gEAEX;;2DANM;;;;;;;;;;;;;;;;sDAYjB,0JAAC;4CAAI,WAAW,GAAG,QAAQ,MAAM,IAAI,eAAe,cAAc;sDAChE,cAAA,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC,+HAAA,CAAA,eAAY;4DACX,UAAU,KAAK,IAAI;4DACnB,MAAM;4DACN,WAAU;;;;;;;;;;;kEAGd,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;;oEAAwC;oEAC/C,KAAK,IAAI;;;;;;;0EAEjB,0JAAC;gEAAI,WAAU;0EACZ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;mCAxCX;;;;;;;;;;;;;;;;;;;;;0BAqDpB,0JAAC,mJAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;;;AAGvB;KA5Ea", "debugId": null}}, {"offset": {"line": 5636, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/sub-services/FAQ.tsx"], "sourcesContent": ["// FAQ.tsx - Fixed with IconRenderer\r\nimport React, { useState } from 'react'\r\nimport { IconRenderer } from '@/components/global/IconRender'\r\nimport { FAQ as FAQType } from '@/types/subService'\r\n\r\ninterface FAQProps {\r\n  data?: FAQType[]\r\n}\r\n\r\nexport const FAQ: React.FC<FAQProps> = ({ data }) => {\r\n  const [openFaq, setOpenFaq] = useState<number | null>(null)\r\n\r\n  if (!data || data.length === 0) return null\r\n\r\n  return (\r\n    <section className=\"py-24 bg-white\">\r\n      <div className=\"max-w-4xl mx-auto px-6\">\r\n        <div className=\"text-center mb-20\">\r\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-6\">\r\n            Frequently Asked Questions\r\n          </h2>\r\n          <p className=\"text-xl text-gray-600 leading-relaxed\">\r\n            Get answers to common questions about our services and how they can help your business grow.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"space-y-6\">\r\n          {data.map((faq, index) => (\r\n            <div key={index} className=\"bg-gray-50 rounded-2xl overflow-hidden shadow-lg border border-gray-200 transition-all duration-300 hover:shadow-xl\">\r\n              <button \r\n                onClick={() => setOpenFaq(openFaq === index ? null : index)}\r\n                className=\"w-full p-8 text-left flex justify-between items-center hover:bg-gray-100 transition-colors duration-200\"\r\n              >\r\n                <h3 className=\"text-xl font-semibold text-gray-800 pr-8\">\r\n                  {faq.question}\r\n                </h3>\r\n                <IconRenderer \r\n                  iconName=\"ChevronDown\"\r\n                  size={24} \r\n                  className={`text-blue-600 transform transition-all duration-300 ease-in-out flex-shrink-0 ${\r\n                    openFaq === index ? 'rotate-180 scale-110' : 'rotate-0 scale-100'\r\n                  }`}\r\n                />\r\n              </button>\r\n              <div \r\n                className={`bg-white transition-all duration-500 ease-in-out overflow-hidden ${\r\n                  openFaq === index \r\n                    ? 'max-h-96 opacity-100' \r\n                    : 'max-h-0 opacity-0'\r\n                }`}\r\n              >\r\n                <div \r\n                  className={`px-8 pb-8 transform transition-all duration-500 ease-out ${\r\n                    openFaq === index \r\n                      ? 'translate-y-0 opacity-100' \r\n                      : '-translate-y-4 opacity-0'\r\n                  }`}\r\n                >\r\n                  <div className=\"h-px bg-gradient-to-r from-transparent via-blue-200 to-transparent mb-6\"></div>\r\n                  <p className=\"text-gray-600 leading-relaxed\">\r\n                    {faq.answer}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}"], "names": [], "mappings": "AAAA,oCAAoC;;;;;AACpC;AACA;;;;;AAOO,MAAM,MAA0B,CAAC,EAAE,IAAI,EAAE;;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG,OAAO;IAEvC,qBACE,0JAAC;QAAQ,WAAU;kBACjB,cAAA,0JAAC;YAAI,WAAU;;8BACb,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,0JAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAKvD,0JAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,0JAAC;4BAAgB,WAAU;;8CACzB,0JAAC;oCACC,SAAS,IAAM,WAAW,YAAY,QAAQ,OAAO;oCACrD,WAAU;;sDAEV,0JAAC;4CAAG,WAAU;sDACX,IAAI,QAAQ;;;;;;sDAEf,0JAAC,+HAAA,CAAA,eAAY;4CACX,UAAS;4CACT,MAAM;4CACN,WAAW,CAAC,8EAA8E,EACxF,YAAY,QAAQ,yBAAyB,sBAC7C;;;;;;;;;;;;8CAGN,0JAAC;oCACC,WAAW,CAAC,iEAAiE,EAC3E,YAAY,QACR,yBACA,qBACJ;8CAEF,cAAA,0JAAC;wCACC,WAAW,CAAC,yDAAyD,EACnE,YAAY,QACR,8BACA,4BACJ;;0DAEF,0JAAC;gDAAI,WAAU;;;;;;0DACf,0JAAC;gDAAE,WAAU;0DACV,IAAI,MAAM;;;;;;;;;;;;;;;;;;2BAhCT;;;;;;;;;;;;;;;;;;;;;AA0CtB;GA7Da;KAAA", "debugId": null}}, {"offset": {"line": 5780, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/sub-services/Testimonials.tsx"], "sourcesContent": ["// Testimonials.tsx - Fixed with FAQ integration\r\nimport React from 'react'\r\nimport Image from 'next/image'\r\nimport { Icon<PERSON><PERSON><PERSON> } from '@/components/global/IconRender'\r\nimport { FAQ } from './FAQ'\r\nimport { urlForImage } from '@/lib/sanity'\r\nimport { SubServiceTestimonial, FAQ as FAQType } from '@/types/subService'\r\n\r\ninterface TestimonialsProps {\r\n  data?: SubServiceTestimonial[]\r\n  faqs?: FAQType[]\r\n}\r\n\r\nexport const Testimonials: React.FC<TestimonialsProps> = ({ data, faqs }) => {\r\n  return (\r\n    <>\r\n      {/* Testimonials Section */}\r\n      {data && data.length > 0 && (\r\n        <section className=\"py-24 bg-gray-50\">\r\n          <div className=\"max-w-7xl mx-auto px-6\">\r\n            <div className=\"text-center mb-20\">\r\n              <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-6\">\r\n                Business Success Stories\r\n              </h2>\r\n              <p className=\"text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed\">\r\n                Real results from real businesses that trusted us with their success.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n              {data.map((testimonial, index) => (\r\n                <div key={index} className=\"bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100\">\r\n                  <div className=\"flex items-center mb-6\">\r\n                    {testimonial.image && (\r\n                      <Image\r\n                        src={urlForImage(testimonial.image).width(64).height(64).url()}\r\n                        alt={testimonial.name || 'Customer'}\r\n                        width={64}\r\n                        height={64}\r\n                        className=\"w-16 h-16 rounded-full mr-4 object-cover\"\r\n                      />\r\n                    )}\r\n                    <div>\r\n                      <div className=\"font-bold text-gray-800\">{testimonial.name}</div>\r\n                      <div className=\"text-blue-600 font-semibold\">{testimonial.business}</div>\r\n                      <div className=\"text-gray-500 text-sm\">{testimonial.location}</div>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {testimonial.result && (\r\n                    <div className=\"bg-blue-50 px-4 py-2 rounded-full inline-block mb-4\">\r\n                      <span className=\"text-blue-600 font-bold text-sm\">{testimonial.result}</span>\r\n                    </div>\r\n                  )}\r\n                  \r\n                  {testimonial.quote && (\r\n                    <p className=\"text-gray-700 leading-relaxed mb-4\">\r\n                      &ldquo;{testimonial.quote}&rdquo;\r\n                    </p>\r\n                  )}\r\n                  \r\n                  <div className=\"flex text-yellow-400\">\r\n                    {[...Array(testimonial.rating || 5)].map((_, i) => (\r\n                      <IconRenderer key={i} iconName=\"Star\" size={20} className=\"fill-current\" />\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </section>\r\n      )}\r\n\r\n      {/* FAQ Section */}\r\n      <FAQ data={faqs} />\r\n    </>\r\n  )\r\n}"], "names": [], "mappings": "AAAA,gDAAgD;;;;;AAEhD;AACA;AACA;AACA;;;;;;AAQO,MAAM,eAA4C,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE;IACtE,qBACE;;YAEG,QAAQ,KAAK,MAAM,GAAG,mBACrB,0JAAC;gBAAQ,WAAU;0BACjB,cAAA,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,0JAAC;oCAAE,WAAU;8CAA0D;;;;;;;;;;;;sCAKzE,0JAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC,aAAa,sBACtB,0JAAC;oCAAgB,WAAU;;sDACzB,0JAAC;4CAAI,WAAU;;gDACZ,YAAY,KAAK,kBAChB,0JAAC,yHAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,yGAAA,CAAA,cAAW,AAAD,EAAE,YAAY,KAAK,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG;oDAC5D,KAAK,YAAY,IAAI,IAAI;oDACzB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAGd,0JAAC;;sEACC,0JAAC;4DAAI,WAAU;sEAA2B,YAAY,IAAI;;;;;;sEAC1D,0JAAC;4DAAI,WAAU;sEAA+B,YAAY,QAAQ;;;;;;sEAClE,0JAAC;4DAAI,WAAU;sEAAyB,YAAY,QAAQ;;;;;;;;;;;;;;;;;;wCAI/D,YAAY,MAAM,kBACjB,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAK,WAAU;0DAAmC,YAAY,MAAM;;;;;;;;;;;wCAIxE,YAAY,KAAK,kBAChB,0JAAC;4CAAE,WAAU;;gDAAqC;gDACxC,YAAY,KAAK;gDAAC;;;;;;;sDAI9B,0JAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM,YAAY,MAAM,IAAI;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC3C,0JAAC,+HAAA,CAAA,eAAY;oDAAS,UAAS;oDAAO,MAAM;oDAAI,WAAU;mDAAvC;;;;;;;;;;;mCAhCf;;;;;;;;;;;;;;;;;;;;;0BA2CpB,0JAAC,6IAAA,CAAA,MAAG;gBAAC,MAAM;;;;;;;;AAGjB;KAhEa", "debugId": null}}, {"offset": {"line": 5973, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/sub-services/FooterCTA.tsx"], "sourcesContent": ["// FooterCTA.tsx - Fixed with IconRenderer\r\nimport React from 'react'\r\nimport Link from 'next/link'\r\nimport { IconRenderer } from '@/components/global/IconRender'\r\nimport { FooterCTA as FooterCTAType } from '@/types/subService'\r\n\r\ninterface FooterCTAProps {\r\n  data?: FooterCTAType\r\n}\r\n\r\nexport const FooterCTA: React.FC<FooterCTAProps> = ({ data }) => {\r\n  if (!data) return null\r\n\r\n  return (\r\n    <>\r\n      {/* Final CTA Section */}\r\n      <section className={`py-24 bg-gradient-to-r ${data.backgroundGradient || 'from-blue-700 to-blue-900'}`}>\r\n        <div className=\"max-w-4xl mx-auto px-6 text-center\">\r\n          {data.title && (\r\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\r\n              {data.title}\r\n            </h2>\r\n          )}\r\n          \r\n          {data.description && (\r\n            <p className=\"text-xl text-blue-100 mb-8 leading-relaxed\">\r\n              {data.description}\r\n            </p>\r\n          )}\r\n          \r\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n            {data.primaryButton && (\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg inline-flex items-center justify-center\"\r\n              >\r\n                <IconRenderer iconName={data.primaryButton.icon} size={20} className=\"mr-2\" />\r\n                {data.primaryButton.text}\r\n              </Link>\r\n            )}\r\n            \r\n            {data.secondaryButton && (\r\n              <a\r\n                href=\"tel:+***********\"\r\n                className=\"bg-white text-blue-900 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 inline-flex items-center justify-center\"\r\n              >\r\n                <IconRenderer iconName={data.secondaryButton.icon} size={20} className=\"mr-2\" />\r\n                {data.secondaryButton.text} ****** 628 3793\r\n              </a>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n\r\n    </>\r\n  )\r\n}"], "names": [], "mappings": "AAAA,0CAA0C;;;;;AAE1C;AACA;;;;AAOO,MAAM,YAAsC,CAAC,EAAE,IAAI,EAAE;IAC1D,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;kBAEE,cAAA,0JAAC;YAAQ,WAAW,CAAC,uBAAuB,EAAE,KAAK,kBAAkB,IAAI,6BAA6B;sBACpG,cAAA,0JAAC;gBAAI,WAAU;;oBACZ,KAAK,KAAK,kBACT,0JAAC;wBAAG,WAAU;kCACX,KAAK,KAAK;;;;;;oBAId,KAAK,WAAW,kBACf,0JAAC;wBAAE,WAAU;kCACV,KAAK,WAAW;;;;;;kCAIrB,0JAAC;wBAAI,WAAU;;4BACZ,KAAK,aAAa,kBACjB,0JAAC,wHAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,0JAAC,+HAAA,CAAA,eAAY;wCAAC,UAAU,KAAK,aAAa,CAAC,IAAI;wCAAE,MAAM;wCAAI,WAAU;;;;;;oCACpE,KAAK,aAAa,CAAC,IAAI;;;;;;;4BAI3B,KAAK,eAAe,kBACnB,0JAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,0JAAC,+HAAA,CAAA,eAAY;wCAAC,UAAU,KAAK,eAAe,CAAC,IAAI;wCAAE,MAAM;wCAAI,WAAU;;;;;;oCACtE,KAAK,eAAe,CAAC,IAAI;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;AAU3C;KA/Ca", "debugId": null}}, {"offset": {"line": 6082, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/sub-services/WhyServiceMatters.tsx"], "sourcesContent": ["import React from 'react'\r\nimport Link from 'next/link'\r\nimport { CheckCircle } from 'lucide-react'\r\nimport { PortableText } from '@portabletext/react'\r\nimport { WhyServiceMatters as WhyServiceMattersType } from '@/types/subService'\r\n\r\ninterface WhyServiceMattersProps {\r\n  data?: WhyServiceMattersType\r\n}\r\n\r\nexport const WhyServiceMatters: React.FC<WhyServiceMattersProps> = ({ data }) => {\r\n  if (!data) return null\r\n\r\n  return (\r\n    <section className=\"py-24 bg-white\">\r\n      <div className=\"max-w-7xl mx-auto px-6\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\r\n          <div>\r\n            {data.title && (\r\n              <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-6\">\r\n                {data.title}\r\n              </h2>\r\n            )}\r\n            \r\n            {data.description && (\r\n              <div className=\"text-xl text-gray-600 mb-6 leading-relaxed prose prose-lg max-w-none\">\r\n                <PortableText value={data.description} />\r\n              </div>\r\n            )}\r\n            \r\n            {data.features && data.features.length > 0 && (\r\n              <div className=\"space-y-4 mb-8\">\r\n                {data.features.map((feature, index) => (\r\n                  <div key={index} className=\"flex items-center\">\r\n                    <div className=\"bg-blue-100 p-2 rounded-full mr-4\">\r\n                      <CheckCircle className=\"text-blue-600\" size={20} />\r\n                    </div>\r\n                    <span className=\"text-gray-700\">{feature.text}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n\r\n            {data.ctaButton?.text && (\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg shadow-blue-500/25 inline-block text-center\"\r\n              >\r\n                {data.ctaButton.text}\r\n              </Link>\r\n            )}\r\n          </div>\r\n          \r\n          {data.stats && data.stats.length > 0 && (\r\n            <div className=\"grid grid-cols-2 gap-6\">\r\n              {data.stats.map((stat, index) => (\r\n                <div key={index} className={`bg-gradient-to-br ${stat.color || 'from-blue-50 to-blue-100'} p-6 rounded-2xl text-center`}>\r\n                  <div className=\"text-4xl font-bold text-blue-600 mb-2\">{stat.value}</div>\r\n                  <div className=\"text-gray-700 text-sm\">{stat.label}</div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAOO,MAAM,oBAAsD,CAAC,EAAE,IAAI,EAAE;IAC1E,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,0JAAC;QAAQ,WAAU;kBACjB,cAAA,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;;4BACE,KAAK,KAAK,kBACT,0JAAC;gCAAG,WAAU;0CACX,KAAK,KAAK;;;;;;4BAId,KAAK,WAAW,kBACf,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC,oKAAA,CAAA,eAAY;oCAAC,OAAO,KAAK,WAAW;;;;;;;;;;;4BAIxC,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACvC,0JAAC;gCAAI,WAAU;0CACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,0JAAC;wCAAgB,WAAU;;0DACzB,0JAAC;gDAAI,WAAU;0DACb,cAAA,0JAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;oDAAgB,MAAM;;;;;;;;;;;0DAE/C,0JAAC;gDAAK,WAAU;0DAAiB,QAAQ,IAAI;;;;;;;uCAJrC;;;;;;;;;;4BAUf,KAAK,SAAS,EAAE,sBACf,0JAAC,wHAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAET,KAAK,SAAS,CAAC,IAAI;;;;;;;;;;;;oBAKzB,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,mBACjC,0JAAC;wBAAI,WAAU;kCACZ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,0JAAC;gCAAgB,WAAW,CAAC,kBAAkB,EAAE,KAAK,KAAK,IAAI,2BAA2B,4BAA4B,CAAC;;kDACrH,0JAAC;wCAAI,WAAU;kDAAyC,KAAK,KAAK;;;;;;kDAClE,0JAAC;wCAAI,WAAU;kDAAyB,KAAK,KAAK;;;;;;;+BAF1C;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1B;KAzDa", "debugId": null}}, {"offset": {"line": 6241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/services/sub-services/index.ts"], "sourcesContent": ["// components/services/sub-services/index.ts - Complete exports\r\nexport { Icon<PERSON>enderer } from '@/components/global/IconRender'\r\n\r\n// Core 6-section layout (matches LocalSEO exactly):\r\nexport { SubServiceHero } from './SubServiceHero'           // 1. Hero\r\nexport { ServiceCTA } from './ServiceCTA'                   // 2. CTA\r\nexport { ServiceDetails } from './ServiceDetails'           // 3. Combined 4 sections\r\nexport { ServiceProcess } from './ServiceProcess'           // 4. Process (+ CaseStudy) \r\nexport { Testimonials } from './Testimonials'               // 5. Testimonials (+ FAQ)\r\nexport { FooterCTA } from './FooterCTA'                     // 6. FooterCTA\r\n\r\n// Individual components (still available for flexibility):\r\nexport { WhyServiceMatters } from './WhyServiceMatters'     // Part of ServiceDetails\r\nexport { CaseStudy } from './CaseStudy'                     // Part of ServiceProcess\r\nexport { FAQ } from './FAQ'                                 // Part of Testimonials"], "names": [], "mappings": "AAAA,+DAA+D;;AAC/D;AAEA,oDAAoD;AACpD,+QAA4D,UAAU;AACtE,uQAA4D,SAAS;AACrE,+QAA4D,yBAAyB;AACrF,+QAA4D,4BAA4B;AACxF,2QAA4D,0BAA0B;AACtF,qQAA4D,eAAe;AAE3E,2DAA2D;AAC3D,qRAA4D,yBAAyB;AACrF,qQAA4D,yBAAyB;AACrF,yPAA4D,uBAAuB", "debugId": null}}, {"offset": {"line": 6292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/pages/%5Bslug%5D.tsx"], "sourcesContent": ["// pages/[slug].tsx - Updated to match LocalSEO structure\r\nimport React from 'react'\r\nimport { GetStaticProps, GetStaticPaths } from 'next'\r\nimport Head from 'next/head'\r\nimport Link from 'next/link'\r\nimport Header from '@/components/global/Header'\r\nimport Footer from '@/components/global/Footer'\r\nimport { getSubService, getSubServiceSlugs, urlForImage } from '@/lib/sanity'\r\nimport { SubServiceData } from '@/types/subService'\r\nimport {\r\n  SubServiceHero,\r\n  ServiceDetails,\r\n  ServiceProcess,\r\n  //CaseStudy,\r\n  ServiceCTA,\r\n  //Testimonials,\r\n  FAQ,\r\n  FooterCTA\r\n} from '@/components/services/sub-services'\r\n\r\ninterface SubServicePageProps {\r\n  subService: SubServiceData\r\n}\r\n\r\nconst SubServicePage: React.FC<SubServicePageProps> = ({ subService }) => {\r\n  const seo = subService.seo || {}\r\n  const pageTitle = seo.metaTitle || `${subService.title} | Vesa Solutions`\r\n  const pageDescription = seo.metaDescription || `Professional ${subService.title} services to grow your business.`\r\n  const canonicalUrl = `https://vesasolutions.com/${subService.slug?.current || ''}`\r\n  const ogImageUrl = seo.ogImage ? urlForImage(seo.ogImage).width(1200).height(630).url() : null\r\n\r\n  return (\r\n    <>\r\n      <Head>\r\n        <title>{pageTitle}</title>\r\n        <meta name=\"description\" content={pageDescription} />\r\n        {seo.keywords && seo.keywords.length > 0 && (\r\n          <meta name=\"keywords\" content={seo.keywords.join(', ')} />\r\n        )}\r\n        \r\n        {/* Open Graph */}\r\n        <meta property=\"og:title\" content={pageTitle} />\r\n        <meta property=\"og:description\" content={pageDescription} />\r\n        <meta property=\"og:type\" content=\"website\" />\r\n        <meta property=\"og:url\" content={canonicalUrl} />\r\n        {ogImageUrl && <meta property=\"og:image\" content={ogImageUrl} />}\r\n        \r\n        {/* Twitter */}\r\n        <meta name=\"twitter:card\" content=\"summary_large_image\" />\r\n        <meta name=\"twitter:title\" content={pageTitle} />\r\n        <meta name=\"twitter:description\" content={pageDescription} />\r\n        {ogImageUrl && <meta name=\"twitter:image\" content={ogImageUrl} />}\r\n        \r\n        {/* Canonical URL */}\r\n        <link rel=\"canonical\" href={canonicalUrl} />\r\n        \r\n        {/* Additional SEO */}\r\n        <meta name=\"robots\" content=\"index, follow\" />\r\n        <meta name=\"author\" content=\"Vesa Solutions\" />\r\n      </Head>\r\n\r\n      <Header isVisible={true} />\r\n\r\n      {/* Breadcrumbs */}\r\n      <div className=\"bg-gray-50 py-4\">\r\n        <div className=\"max-w-7xl mx-auto px-6\">\r\n          <nav className=\"text-sm\" aria-label=\"Breadcrumb\">\r\n            <ol className=\"flex items-center space-x-2\">\r\n              <li>\r\n                <Link href=\"/\" className=\"text-gray-500 hover:text-blue-600 transition-colors\">\r\n                  Home\r\n                </Link>\r\n              </li>\r\n              <li className=\"text-gray-400\">/</li>\r\n              <li>\r\n                <Link href=\"/services\" className=\"text-gray-500 hover:text-blue-600 transition-colors\">\r\n                  Services\r\n                </Link>\r\n              </li>\r\n              <li className=\"text-gray-400\">/</li>\r\n              {subService.parentService && (\r\n                <li>\r\n                  <Link\r\n                    href={`/${subService.parentService === 'seo' ? 'seo-search-engine-optimization' :\r\n                             subService.parentService === 'web-development' ? 'web-development' :\r\n                             subService.parentService === 'digital-marketing' ? 'digital-marketing' :\r\n                             subService.parentService}`}\r\n                    className=\"text-gray-500 hover:text-blue-600 transition-colors\"\r\n                  >\r\n                    {subService.parentService === 'seo' ? 'SEO Search Engine Optimization' :\r\n                     subService.parentService === 'web-development' ? 'Website Development' :\r\n                     subService.parentService === 'digital-marketing' ? 'Digital Marketing' :\r\n                     subService.parentService}\r\n                  </Link>\r\n                </li>\r\n              )}\r\n              {subService.parentService && <li className=\"text-gray-400\">/</li>}\r\n              <li className=\"text-gray-400\">/</li>\r\n              <li className=\"text-gray-900 font-medium\" aria-current=\"page\">\r\n                {subService.title}\r\n              </li>\r\n            </ol>\r\n          </nav>\r\n        </div>\r\n      </div>\r\n\r\n      <main className=\"min-h-screen bg-white\">\r\n        {/* 1. Hero Section */}\r\n        {subService.hero ? (\r\n          <SubServiceHero data={subService.hero} />\r\n        ) : (\r\n          <DefaultHero title={subService.title} />\r\n        )}\r\n\r\n        {/* 2. CTA Section */}\r\n        {subService.cta && (\r\n          <ServiceCTA\r\n            data={subService.cta}\r\n            serviceName={subService.title}\r\n          />\r\n        )}\r\n\r\n        {/* 3. ServiceDetails - Combined 4 Sections */}\r\n        <ServiceDetails\r\n          whyMatters={subService.whyMatters}\r\n          services={subService.services}\r\n          strategicImplementation={subService.strategicImplementation}\r\n          marketIntelligence={subService.marketIntelligence}\r\n        />\r\n\r\n        {/* 4. FooterCTA - Moved above ServiceProcess */}\r\n        {subService.footerCta && (\r\n          <FooterCTA data={subService.footerCta} />\r\n        )}\r\n\r\n        {/* 5. ServiceProcess - Combined 2 Sections */}\r\n        <>\r\n          {subService.process && (\r\n            <ServiceProcess data={subService.process} />\r\n          )}\r\n          {/* {subService.caseStudy && (\r\n            <CaseStudy data={subService.caseStudy} />\r\n          )} */}\r\n        </>\r\n\r\n        {/* 6. Testimonials - Combined 2 Sections */}\r\n        <>\r\n          {/* {subService.testimonials && subService.testimonials.length > 0 && (\r\n            <Testimonials data={subService.testimonials} />\r\n          )} */}\r\n          {subService.faqs && subService.faqs.length > 0 && (\r\n            <FAQ data={subService.faqs} />\r\n          )}\r\n        </>\r\n      </main>\r\n\r\n      <Footer />\r\n    </>\r\n  )\r\n}\r\n\r\n// Default hero component for fallback\r\nconst DefaultHero: React.FC<{ title: string }> = ({ title }) => (\r\n  <section className=\"bg-gradient-to-r from-blue-600 to-blue-800 py-20\">\r\n    <div className=\"container mx-auto px-4 text-center text-white\">\r\n      <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">{title}</h1>\r\n      <p className=\"text-xl opacity-90 max-w-2xl mx-auto\">\r\n        Professional {title.toLowerCase()} services to help grow your business.\r\n      </p>\r\n    </div>\r\n  </section>\r\n)\r\n\r\nexport const getStaticPaths: GetStaticPaths = async () => {\r\n  try {\r\n    const slugs = await getSubServiceSlugs()\r\n    \r\n    if (!slugs || slugs.length === 0) {\r\n      console.warn('⚠️ No service slugs found in getStaticPaths')\r\n      return { \r\n        paths: [], \r\n        fallback: 'blocking' \r\n      }\r\n    }\r\n    \r\n    // Filter out any invalid slugs\r\n    const validSlugs = slugs.filter((item: { slug: string }) => \r\n      item.slug && typeof item.slug === 'string' && item.slug.length > 0\r\n    )\r\n    \r\n    const paths = validSlugs.map((item: { slug: string }) => ({\r\n      params: { slug: item.slug }\r\n    }))\r\n\r\n    console.log(`✅ Generated ${paths.length} service paths`)\r\n    \r\n    return {\r\n      paths,\r\n      fallback: 'blocking'\r\n    }\r\n  } catch (error) {\r\n    console.error('❌ Error in getStaticPaths:', error)\r\n    return {\r\n      paths: [],\r\n      fallback: 'blocking'\r\n    }\r\n  }\r\n}\r\n\r\nexport const getStaticProps: GetStaticProps = async (context) => {\r\n  const { params, preview = false } = context\r\n  const slug = params?.slug as string\r\n  \r\n  console.log(`🔍 getStaticProps called for slug: ${slug}`)\r\n  \r\n  if (!slug || typeof slug !== 'string') {\r\n    console.log('❌ Invalid or missing slug')\r\n    return { notFound: true }\r\n  }\r\n\r\n  try {\r\n    const subService = await getSubService(slug)\r\n    \r\n    if (!subService) {\r\n      console.log(`❌ SubService not found for slug: ${slug}`)\r\n      return { notFound: true }\r\n    }\r\n\r\n    // Validate essential data structure\r\n    if (!subService.title || !subService.slug?.current) {\r\n      console.error(`❌ Invalid subService data structure for slug: ${slug}`)\r\n      return { notFound: true }\r\n    }\r\n\r\n    console.log(`✅ Successfully loaded service: ${subService.title}`)\r\n\r\n    return {\r\n      props: { \r\n        subService \r\n      },\r\n      // Only use revalidate in production to prevent ISR manifest HMR errors\r\n      ...(process.env.NODE_ENV === 'production' && { \r\n        revalidate: preview ? 1 : 60 \r\n      }),\r\n    }\r\n  } catch (error) {\r\n    console.error(`❌ Error fetching subService for slug ${slug}:`, error)\r\n    return { notFound: true }\r\n  }\r\n}\r\n\r\nexport default SubServicePage"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;AAGzD;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AAeA,MAAM,iBAAgD,CAAC,EAAE,UAAU,EAAE;IACnE,MAAM,MAAM,WAAW,GAAG,IAAI,CAAC;IAC/B,MAAM,YAAY,IAAI,SAAS,IAAI,GAAG,WAAW,KAAK,CAAC,iBAAiB,CAAC;IACzE,MAAM,kBAAkB,IAAI,eAAe,IAAI,CAAC,aAAa,EAAE,WAAW,KAAK,CAAC,gCAAgC,CAAC;IACjH,MAAM,eAAe,CAAC,0BAA0B,EAAE,WAAW,IAAI,EAAE,WAAW,IAAI;IAClF,MAAM,aAAa,IAAI,OAAO,GAAG,CAAA,GAAA,yGAAA,CAAA,cAAW,AAAD,EAAE,IAAI,OAAO,EAAE,KAAK,CAAC,MAAM,MAAM,CAAC,KAAK,GAAG,KAAK;IAE1F,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;kCAAO;;;;;;kCACR,0JAAC;wBAAK,MAAK;wBAAc,SAAS;;;;;;oBACjC,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,MAAM,GAAG,mBACrC,0JAAC;wBAAK,MAAK;wBAAW,SAAS,IAAI,QAAQ,CAAC,IAAI,CAAC;;;;;;kCAInD,0JAAC;wBAAK,UAAS;wBAAW,SAAS;;;;;;kCACnC,0JAAC;wBAAK,UAAS;wBAAiB,SAAS;;;;;;kCACzC,0JAAC;wBAAK,UAAS;wBAAU,SAAQ;;;;;;kCACjC,0JAAC;wBAAK,UAAS;wBAAS,SAAS;;;;;;oBAChC,4BAAc,0JAAC;wBAAK,UAAS;wBAAW,SAAS;;;;;;kCAGlD,0JAAC;wBAAK,MAAK;wBAAe,SAAQ;;;;;;kCAClC,0JAAC;wBAAK,MAAK;wBAAgB,SAAS;;;;;;kCACpC,0JAAC;wBAAK,MAAK;wBAAsB,SAAS;;;;;;oBACzC,4BAAc,0JAAC;wBAAK,MAAK;wBAAgB,SAAS;;;;;;kCAGnD,0JAAC;wBAAK,KAAI;wBAAY,MAAM;;;;;;kCAG5B,0JAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;kCAC5B,0JAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;;;;;;;0BAG9B,0JAAC,2HAAA,CAAA,UAAM;gBAAC,WAAW;;;;;;0BAGnB,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;wBAAU,cAAW;kCAClC,cAAA,0JAAC;4BAAG,WAAU;;8CACZ,0JAAC;8CACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAsD;;;;;;;;;;;8CAIjF,0JAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,0JAAC;8CACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAsD;;;;;;;;;;;8CAIzF,0JAAC;oCAAG,WAAU;8CAAgB;;;;;;gCAC7B,WAAW,aAAa,kBACvB,0JAAC;8CACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;wCACH,MAAM,CAAC,CAAC,EAAE,WAAW,aAAa,KAAK,QAAQ,mCACtC,WAAW,aAAa,KAAK,oBAAoB,oBACjD,WAAW,aAAa,KAAK,sBAAsB,sBACnD,WAAW,aAAa,EAAE;wCACnC,WAAU;kDAET,WAAW,aAAa,KAAK,QAAQ,mCACrC,WAAW,aAAa,KAAK,oBAAoB,wBACjD,WAAW,aAAa,KAAK,sBAAsB,sBACnD,WAAW,aAAa;;;;;;;;;;;gCAI9B,WAAW,aAAa,kBAAI,0JAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC3D,0JAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,0JAAC;oCAAG,WAAU;oCAA4B,gBAAa;8CACpD,WAAW,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3B,0JAAC;gBAAK,WAAU;;oBAEb,WAAW,IAAI,iBACd,0JAAC,wJAAA,CAAA,iBAAc;wBAAC,MAAM,WAAW,IAAI;;;;;6CAErC,0JAAC;wBAAY,OAAO,WAAW,KAAK;;;;;;oBAIrC,WAAW,GAAG,kBACb,0JAAC,oJAAA,CAAA,aAAU;wBACT,MAAM,WAAW,GAAG;wBACpB,aAAa,WAAW,KAAK;;;;;;kCAKjC,0JAAC,wJAAA,CAAA,iBAAc;wBACb,YAAY,WAAW,UAAU;wBACjC,UAAU,WAAW,QAAQ;wBAC7B,yBAAyB,WAAW,uBAAuB;wBAC3D,oBAAoB,WAAW,kBAAkB;;;;;;oBAIlD,WAAW,SAAS,kBACnB,0JAAC,mJAAA,CAAA,YAAS;wBAAC,MAAM,WAAW,SAAS;;;;;;kCAIvC;kCACG,WAAW,OAAO,kBACjB,0JAAC,wJAAA,CAAA,iBAAc;4BAAC,MAAM,WAAW,OAAO;;;;;;;kCAQ5C;kCAIG,WAAW,IAAI,IAAI,WAAW,IAAI,CAAC,MAAM,GAAG,mBAC3C,0JAAC,6IAAA,CAAA,MAAG;4BAAC,MAAM,WAAW,IAAI;;;;;;;;;;;;;0BAKhC,0JAAC,2HAAA,CAAA,UAAM;;;;;;;AAGb;KAvIM;AAyIN,sCAAsC;AACtC,MAAM,cAA2C,CAAC,EAAE,KAAK,EAAE,iBACzD,0JAAC;QAAQ,WAAU;kBACjB,cAAA,0JAAC;YAAI,WAAU;;8BACb,0JAAC;oBAAG,WAAU;8BAAuC;;;;;;8BACrD,0JAAC;oBAAE,WAAU;;wBAAuC;wBACpC,MAAM,WAAW;wBAAG;;;;;;;;;;;;;;;;;;MALpC;;uCAyFS", "debugId": null}}, {"offset": {"line": 6701, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/[slug]\";\n\n/// <reference types=\"next/client\" />\r\n\r\n// inserted by rust code\r\ndeclare const PAGE_PATH: string\r\n\r\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\r\n;(window.__NEXT_P = window.__NEXT_P || []).push([\r\n  PAGE_PATH,\r\n  () => {\r\n    return require('PAGE')\r\n  },\r\n])\r\n// @ts-expect-error module.hot exists\r\nif (module.hot) {\r\n  // @ts-expect-error module.hot exists\r\n  module.hot.dispose(function () {\r\n    window.__NEXT_P.push([PAGE_PATH])\r\n  })\r\n}\r\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}