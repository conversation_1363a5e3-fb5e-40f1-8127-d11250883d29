import React from 'react';
import { GetStaticProps } from 'next';
import Head from 'next/head';
import Link from 'next/link';
import Header from '@/components/global/Header';
import Footer from '@/components/global/Footer';
import { getAllCaseStudies, CaseStudy } from '@/lib/sanity-case-studies';

// Import case studies section components
import CaseStudiesArchiveSection from '@/components/case-studies/CaseStudiesArchiveSection';
import CaseStudiesCTASection from '@/components/case-studies/CaseStudiesCTASection';

interface CaseStudiesPageProps {
  caseStudies: CaseStudy[];
}

const CaseStudiesPage: React.FC<CaseStudiesPageProps> = ({ caseStudies }) => {
  return (
    <>
      <Head>
        <title>Client Success Stories & Case Studies | VESA Solutions</title>
        <meta name="description" content="Discover how VESA Solutions has helped businesses achieve remarkable growth through our digital marketing strategies. Real results, real clients, real success stories." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="keywords" content="case studies, client success stories, digital marketing results, SEO case studies, web development portfolio, VESA Solutions clients" />
        
        {/* Open Graph */}
        <meta property="og:title" content="Client Success Stories & Case Studies | VESA Solutions" />
        <meta property="og:description" content="Discover how VESA Solutions has helped businesses achieve remarkable growth through our digital marketing strategies." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://vesasolutions.com/case-studies" />
        
        {/* Twitter Card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Client Success Stories & Case Studies | VESA Solutions" />
        <meta name="twitter:description" content="Discover how VESA Solutions has helped businesses achieve remarkable growth through our digital marketing strategies." />
        
        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "CollectionPage",
              "name": "Case Studies",
              "description": "Client success stories and case studies from VESA Solutions",
              "url": "https://vesasolutions.com/case-studies",
              "publisher": {
                "@type": "Organization",
                "name": "VESA Solutions",
                "url": "https://vesasolutions.com"
              }
            })
          }}
        />
      </Head>
      
      <Header />
      
      {/* Breadcrumbs */}
      <div className="bg-gray-50 py-4">
        <div className="max-w-7xl mx-auto px-6">
          <nav className="text-sm" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              <li>
                <Link href="/" className="text-gray-500 hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-gray-900 font-medium" aria-current="page">
                Case Studies
              </li>
            </ol>
          </nav>
        </div>
      </div>
      
      <main className="min-h-screen bg-white">
        {/* Case Studies Archive */}
        <CaseStudiesArchiveSection caseStudies={caseStudies} />
        <CaseStudiesCTASection />
      </main>
      
      <Footer />
    </>
  );
};

export const getStaticProps: GetStaticProps = async () => {
  const caseStudies = await getAllCaseStudies();

  return {
    props: {
      caseStudies,
    },
    revalidate: 60, // Revalidate every minute
  };
};

export default CaseStudiesPage;
