// components/home/<USER>
import React, { useState } from 'react';
import Image from 'next/image';
import { ChevronLeft, ChevronRight, User, Mail, Clock } from 'lucide-react';
import { CalendarDay } from '@/types';
import { format } from 'date-fns';


interface SchedulingSectionProps {
  className?: string;
}

const SchedulingSection: React.FC<SchedulingSectionProps> = ({ className = '' }) => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showForm, setShowForm] = useState<boolean>(false);
  const [formData, setFormData] = useState({
    name: '',
    email: ''
  });
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState<string>('');
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());





  // Handle form submission
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedDate || !formData.name || !formData.email) {
      setSubmitStatus('error');
      setStatusMessage('Please fill in all fields and select a date.');
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      const formattedDate = format(selectedDate, 'MMMM d, yyyy');

      // Prepare data for API
      const scheduleData = {
        name: formData.name,
        email: formData.email,
        date: formattedDate
      };

      // Send to API
      const response = await fetch('/api/schedule-consultation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(scheduleData),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setSubmitStatus('success');
        setStatusMessage(result.message || 'Consultation scheduled successfully! You will receive a confirmation email shortly.');

        // Reset form
        setFormData({ name: '', email: '' });
        setSelectedDate(null);
        setShowForm(false);
      } else {
        setSubmitStatus('error');
        setStatusMessage(result.message || 'Failed to schedule consultation. Please try again.');
      }

    } catch {
      setSubmitStatus('error');
      setStatusMessage('Failed to schedule consultation. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);

      // Clear status message after 8 seconds
      setTimeout(() => {
        setSubmitStatus('idle');
        setStatusMessage('');
      }, 8000);
    }
  };





  // Generate calendar data for the current month view
  const generateCalendarDays = (): CalendarDay[] => {
    const today = new Date();
    const days: CalendarDay[] = [];

    // Get first day of the current month
    const firstDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
    const lastDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);

    // Get the day of week for the first day (0 = Sunday, 1 = Monday, etc.)
    // Convert to Monday = 0, Sunday = 6
    const firstDayOfWeek = (firstDayOfMonth.getDay() + 6) % 7;

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfWeek; i++) {
      const prevDate = new Date(firstDayOfMonth);
      prevDate.setDate(prevDate.getDate() - (firstDayOfWeek - i));

      days.push({
        date: prevDate.getDate(),
        available: false,
        fullDate: prevDate,
        isToday: false,
        isNextMonth: true // These are from previous month
      });
    }

    // Add all days of the current month
    for (let day = 1; day <= lastDayOfMonth.getDate(); day++) {
      const dayDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
      const isToday = dayDate.toDateString() === today.toDateString();
      const isPastDate = dayDate < today && !isToday;

      days.push({
        date: day,
        available: !isPastDate, // Available if not in the past
        fullDate: dayDate,
        isToday: isToday,
        isNextMonth: false
      });
    }

    // Fill remaining cells to complete the 6-week grid (42 cells)
    const remainingCells = 42 - days.length;
    for (let i = 1; i <= remainingCells; i++) {
      const nextDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, i);

      days.push({
        date: i,
        available: true, // Future month dates are always available
        fullDate: nextDate,
        isToday: false,
        isNextMonth: true
      });
    }

    return days;
  };

  // Navigation functions
  const goToPreviousMonth = () => {
    const today = new Date();
    const prevMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1);

    // Don't allow going to months before the current month
    if (prevMonth.getFullYear() > today.getFullYear() ||
        (prevMonth.getFullYear() === today.getFullYear() && prevMonth.getMonth() >= today.getMonth())) {
      setCurrentMonth(prevMonth);
    }
  };

  const goToNextMonth = () => {
    const nextMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1);
    const today = new Date();
    const maxMonth = new Date(today.getFullYear() + 1, today.getMonth(), 1); // Allow up to 1 year ahead

    // Don't allow going more than 1 year into the future
    if (nextMonth <= maxMonth) {
      setCurrentMonth(nextMonth);
    }
  };

  const calendarDays = generateCalendarDays();

  const weekDays: string[] = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];

  return (
    <section className={`w-full bg-blue-50 py-24 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <p className="text-blue-700 font-medium text-lg mb-3">BOOK A CONSULTATION</p>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 max-w-4xl mx-auto">
            SCHEDULE YOUR FREE ONLINE REPUTATION CONSULTATION
          </h2>
          <p className="text-gray-700 text-lg mt-4 max-w-3xl mx-auto">
            Take the first step towards a better online presence. Our experts will analyze your current reputation and suggest strategies to improve it.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="flex flex-col lg:flex-row">
              {/* Left side - Professional Image */}
              <div className="lg:w-1/3 relative">
                <Image
                  src="https://img.freepik.com/free-photo/man-signs-documents-manager-working-office_1157-42026.jpg"
                  alt="Professional consultant"
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>

              {/* Center - Calendar */}
              <div className="lg:w-1/3 p-6 bg-gray-50">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 text-center">
                  Select a Date
                </h3>

                {/* Month Navigation */}
                <div className="flex items-center justify-between mb-6">
                  <button
                    onClick={goToPreviousMonth}
                    className="p-1 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={currentMonth.getFullYear() === new Date().getFullYear() && currentMonth.getMonth() === new Date().getMonth()}
                    aria-label="Go to previous month"
                  >
                    <ChevronLeft className="w-5 h-5 text-blue-600" />
                  </button>
                  <span className="text-base font-medium text-gray-700">
                    {format(currentMonth, 'MMMM yyyy')}
                  </span>
                  <button
                    onClick={goToNextMonth}
                    className="p-1 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={(() => {
                      const today = new Date();
                      const maxMonth = new Date(today.getFullYear() + 1, today.getMonth(), 1);
                      const nextMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1);
                      return nextMonth > maxMonth;
                    })()}
                    aria-label="Go to next month"
                  >
                    <ChevronRight className="w-5 h-5 text-blue-600" />
                  </button>
                </div>

                {/* Week Days */}
                <div className="grid grid-cols-7 gap-1 mb-3">
                  {weekDays.map((day) => (
                    <div
                      key={day}
                      className="text-xs font-medium text-gray-500 text-center py-2"
                    >
                      {day}
                    </div>
                  ))}
                </div>

                {/* Calendar Grid */}
                <div className="grid grid-cols-7 gap-1">
                  {calendarDays.map((day, index) => {
                    const dayKey = day.fullDate ? day.fullDate.toISOString() : `${day.date}-${index}`;
                    const isSelected = selectedDate && day.fullDate && selectedDate.toDateString() === day.fullDate.toDateString();

                    return (
                      <button
                        key={dayKey}
                        onClick={() => {
                          if (day.available && day.fullDate) {
                            setSelectedDate(day.fullDate);
                            setShowForm(true);
                          }
                        }}
                        disabled={!day.available}
                        className={`
                          aspect-square flex items-center justify-center text-sm font-medium rounded-lg
                          transition-all duration-200 relative
                          ${
                            day.available
                              ? day.isNextMonth
                                ? 'text-gray-500 hover:bg-gray-100 cursor-pointer border border-gray-100'
                                : 'text-blue-600 hover:bg-blue-100 cursor-pointer border border-gray-200'
                              : 'text-gray-300 cursor-not-allowed bg-gray-50'
                          }
                          ${
                            isSelected
                              ? 'bg-blue-600 text-white hover:bg-blue-700 border-blue-600'
                              : ''
                          }
                          ${day.isToday ? 'ring-2 ring-blue-300 ring-offset-1' : ''}
                          ${day.isNextMonth ? 'opacity-70' : ''}
                        `}
                        title={day.fullDate ? format(day.fullDate, 'EEEE, MMMM d, yyyy') : ''}
                        aria-label={day.fullDate ? `Select ${format(day.fullDate, 'EEEE, MMMM d, yyyy')}` : `Day ${day.date}`}
                      >
                        {day.date}
                        {day.isToday && (
                          <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"></div>
                        )}
                      </button>
                    );
                  })}
                </div>




              </div>

              {/* Right side - Consultation Details & Form */}
              <div className="lg:w-1/3 p-8 flex flex-col justify-center">
                {!showForm ? (
                  <>
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">
                      Online Reputation Management Consultation
                    </h2>

                    <div className="flex items-center gap-2 text-gray-600 mb-6">
                      <Clock className="w-5 h-5" />
                      <span className="text-base">30 min</span>
                    </div>

                    <p className="text-gray-700 mb-6 leading-relaxed">
                      Schedule a free online reputation management consultation with Vesa Solutions.
                    </p>

                    <p className="text-gray-600 text-sm mb-8 leading-relaxed">
                      Your custom online reputation management strategy will be outlined for you step-by-step during our call. If we believe we can help you, we will outline what it will look like if we work together.
                    </p>

                    <div className="text-center">
                      <p className="text-gray-600 text-lg">
                        Select a date from the calendar to get started
                      </p>
                    </div>
                  </>
                ) : (
                  <>
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">
                      Complete Your Booking
                    </h2>

                    <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <h4 className="font-medium text-blue-800 mb-2">Appointment Details</h4>
                      <div className="text-blue-700 text-sm space-y-1">
                        <div>📅 {selectedDate ? format(selectedDate, 'EEEE, MMMM d, yyyy') : 'Invalid date'}</div>
                        <div>⏱️ 30 minutes consultation</div>
                      </div>
                    </div>

                    <form onSubmit={handleFormSubmit} className="space-y-4">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                          <User className="w-4 h-4 inline mr-1" />
                          Full Name *
                        </label>
                        <input
                          type="text"
                          id="name"
                          value={formData.name}
                          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                          required
                          disabled={isSubmitting}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
                          placeholder="Enter your full name"
                        />
                      </div>

                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                          <Mail className="w-4 h-4 inline mr-1" />
                          Email Address *
                        </label>
                        <input
                          type="email"
                          id="email"
                          value={formData.email}
                          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                          required
                          disabled={isSubmitting}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
                          placeholder="Enter your email address"
                        />
                      </div>

                      <div className="flex gap-3 pt-4">
                        <button
                          type="button"
                          onClick={() => setShowForm(false)}
                          disabled={isSubmitting}
                          className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50"
                        >
                          Back
                        </button>
                        <button
                          type="submit"
                          disabled={isSubmitting || !formData.name || !formData.email}
                          className="flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 disabled:cursor-not-allowed"
                        >
                          {isSubmitting ? 'Scheduling...' : 'Schedule Consultation'}
                        </button>
                      </div>
                    </form>
                  </>
                )}

                {/* Status Message */}
                {statusMessage && (
                  <div className={`mt-4 p-3 rounded-lg text-sm ${
                    submitStatus === 'success'
                      ? 'bg-green-50 text-green-700 border border-green-200'
                      : submitStatus === 'error'
                      ? 'bg-red-50 text-red-700 border border-red-200'
                      : ''
                  }`}>
                    {statusMessage}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SchedulingSection;