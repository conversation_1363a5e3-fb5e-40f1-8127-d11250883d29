// Testimonials.tsx - Fixed with FAQ integration
import React from 'react'
import Image from 'next/image'
import { Icon<PERSON><PERSON><PERSON> } from '@/components/global/IconRender'
import { FAQ } from './FAQ'
import { urlForImage } from '@/lib/sanity'
import { SubServiceTestimonial, FAQ as FAQType } from '@/types/subService'

interface TestimonialsProps {
  data?: SubServiceTestimonial[]
  faqs?: FAQType[]
}

export const Testimonials: React.FC<TestimonialsProps> = ({ data, faqs }) => {
  return (
    <>
      {/* Testimonials Section */}
      {data && data.length > 0 && (
        <section className="py-24 bg-gray-50">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
                Business Success Stories
              </h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Real results from real businesses that trusted us with their success.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {data.map((testimonial, index) => (
                <div key={index} className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                  <div className="flex items-center mb-6">
                    {testimonial.image && (
                      <Image
                        src={urlForImage(testimonial.image).width(64).height(64).url()}
                        alt={testimonial.name || 'Customer'}
                        width={64}
                        height={64}
                        className="w-16 h-16 rounded-full mr-4 object-cover"
                      />
                    )}
                    <div>
                      <div className="font-bold text-gray-800">{testimonial.name}</div>
                      <div className="text-blue-600 font-semibold">{testimonial.business}</div>
                      <div className="text-gray-500 text-sm">{testimonial.location}</div>
                    </div>
                  </div>
                  
                  {testimonial.result && (
                    <div className="bg-blue-50 px-4 py-2 rounded-full inline-block mb-4">
                      <span className="text-blue-600 font-bold text-sm">{testimonial.result}</span>
                    </div>
                  )}
                  
                  {testimonial.quote && (
                    <p className="text-gray-700 leading-relaxed mb-4">
                      &ldquo;{testimonial.quote}&rdquo;
                    </p>
                  )}
                  
                  <div className="flex text-yellow-400">
                    {[...Array(testimonial.rating || 5)].map((_, i) => (
                      <IconRenderer key={i} iconName="Star" size={20} className="fill-current" />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* FAQ Section */}
      <FAQ data={faqs} />
    </>
  )
}