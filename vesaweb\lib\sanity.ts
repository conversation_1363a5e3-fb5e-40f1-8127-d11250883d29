// lib/sanity.ts
import { createClient } from '@sanity/client'
import imageUrlBuilder from '@sanity/image-url'
import { SanityImageSource } from '@sanity/image-url/lib/types/types'

// Sanity client configuration
export const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: '2023-05-03',
  useCdn: false,
})

// Image URL builder
const builder = imageUrlBuilder(client)

export function urlForImage(source: SanityImageSource) {
  return builder.image(source)
}

// GROQ queries - UPDATED with missing fields
export const subServiceQuery = `
  *[_type == "subService" && slug.current == $slug][0] {
    _id,
    title,
    slug,
    parentService,
    hero {
      badgeText,
      badgeIcon,
      title,
      subtitle,
      stats[] {
        value,
        label
      },
      backgroundGradient
    },
    whyMatters {
      title,
      description,
      features[] {
        text
      },
      stats[] {
        value,
        label,
        color
      },
      ctaButton {
        text
      }
    },
    services[] {
      icon,
      title,
      description,
      fullDescription,
      features,
      benefits,
      result,
      image
    },
    strategicImplementation {
      title,
      description,
      secondaryDescription,
      image,
      features,
      sections[] {
        icon,
        title,
        description,
        color
      }
    },
    marketIntelligence {
      title,
      description,
      secondaryDescription,
      image,
      stats[] {
        value,
        label
      },
      ctaButton {
        text
      },
      backgroundGradient
    },
    process {
      title,
      description,
      steps[] {
        step,
        title,
        description,
        icon,
        details
      }
    },
    caseStudy {
      title,
      description,
      image,
      results[] {
        value,
        label
      },
      ctaButton {
        text
      },
      backgroundGradient
    },
    cta {
      title,
      description,
      benefits,
      formSettings {
        ctaText,
        messagePlaceholder
      }
    },
    testimonials[] {
      name,
      business,
      location,
      image,
      quote,
      result,
      rating
    },
    faqs[] {
      question,
      answer
    },
    footerCta {
      title,
      description,
      primaryButton {
        text,
        icon
      },
      secondaryButton {
        text,
        icon
      },
      backgroundGradient
    },
    seo {
      metaTitle,
      metaDescription,
      keywords,
      ogImage
    }
  }
`

export const allSubServicesQuery = `
  *[_type == "subService"] | order(_createdAt desc) {
    _id,
    title,
    slug,
    hero {
      title,
      subtitle
    },
    seo {
      metaTitle,
      metaDescription
    }
  }
`

// Fetch functions
export async function getSubService(slug: string) {
  try {
    const result = await client.fetch(subServiceQuery, { slug })
    console.log('Fetched sub-service:', result) // Debug log
    console.log('Has strategicImplementation:', !!result?.strategicImplementation) // Debug
    console.log('Has marketIntelligence:', !!result?.marketIntelligence) // Debug
    return result
  } catch (error) {
    console.error('Error fetching sub-service:', error)
    return null
  }
}

export async function getAllSubServices() {
  try {
    return await client.fetch(allSubServicesQuery)
  } catch (error) {
    console.error('Error fetching all sub-services:', error)
    return []
  }
}

export async function getSubServiceSlugs() {
  try {
    const query = `*[_type == "subService"] { "slug": slug.current }`
    return await client.fetch(query)
  } catch (error) {
    console.error('Error fetching sub-service slugs:', error)
    return []
  }
}