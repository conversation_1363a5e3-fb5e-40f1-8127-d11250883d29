// components/global/Form.tsx - Fixed with static colors (not affected by dark/light mode)
import React, { useState, useEffect } from 'react';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { getCountryCallingCode, Country } from 'react-phone-number-input';

// Form validation schema
const contactSchema = z.object({
  businessName: z.string().min(2, 'Business name is required'),
  fullName: z.string().min(2, 'Your name is required'),
  email: z.string().email('Valid email is required'),
  phone: z.string().min(10, 'Valid phone number is required'),
  location: z.string().min(2, 'Business location is required'),
  website: z.string().url().optional().or(z.literal('')),
  message: z.string().min(10, 'Please describe your goals (minimum 10 characters)'),
  service: z.string(),
});

type ContactFormData = z.infer<typeof contactSchema>;

// Extended form data type for submission
interface ExtendedFormData extends ContactFormData {
  userCountry?: string;
  timestamp?: string;
}

interface ServiceContactFormProps {
  service: {
    name: string;
    type: 'seo' | 'local-seo' | 'ppc' | 'web-design' | 'social-media' | 'email-marketing' | 'branding' | 'conversion-optimization' | 'reputation-management';
    ctaText?: string;
    messagePlaceholder?: string;
    benefits?: string[];
  };
  className?: string;
  onSubmit?: (data: ExtendedFormData) => Promise<void>;
  initialWebsite?: string;
}

// API Response types
interface ApiResponse {
  message: string;
  success: boolean;
  error?: string;
}

// Helper function to safely get error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
}

const ServiceContactForm: React.FC<ServiceContactFormProps> = ({
  service,
  className = '',
  onSubmit,
  initialWebsite = ''
}) => {
  const [userCountry, setUserCountry] = useState<string>('US');
  const [defaultPhoneCountry, setDefaultPhoneCountry] = useState<Country>('US');
  const [initialPhoneValue, setInitialPhoneValue] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    setValue
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      service: service.name,
      website: initialWebsite,
    }
  });

  // Auto-detect user country on component mount
  useEffect(() => {
    const detectUserCountry = async () => {
      try {
        // Method 1: Try IP-based detection (most reliable)
        const response = await fetch('https://ipapi.co/json/');
        const data = await response.json();
        
        if (data.country_code) {
          const countryCode = data.country_code;
          setUserCountry(countryCode);
          setDefaultPhoneCountry(countryCode as Country);
          
          // Get the country calling code and set initial phone value
          try {
            const callingCode = getCountryCallingCode(countryCode as Country);
            const initialValue = `+${callingCode}`;
            setInitialPhoneValue(initialValue);
            setValue('phone', initialValue);
          } catch {
            console.log('Error getting calling code');
            setInitialPhoneValue('+1');
            setValue('phone', '+1');
          }
          return;
        }
      } catch {
        console.log('IP detection failed, trying locale detection');
      }

      try {
        const locale = navigator.language || (navigator.languages && navigator.languages[0]);
        const countryCode = locale?.split('-')[1] || 'US';
        setUserCountry(countryCode);
        setDefaultPhoneCountry(countryCode as Country);
        
        // Get the country calling code and set initial phone value
        try {
          const callingCode = getCountryCallingCode(countryCode as Country);
          const initialValue = `+${callingCode}`;
          setInitialPhoneValue(initialValue);
          setValue('phone', initialValue);
        } catch {
          console.log('Error getting calling code');
          setInitialPhoneValue('+1');
          setValue('phone', '+1');
        }
      } catch {
        // Method 3: Default to US
        setUserCountry('US');
        setDefaultPhoneCountry('US');
        setInitialPhoneValue('+1');
        setValue('phone', '+1');
      }
    };

    detectUserCountry();
  }, [setValue]);

  // Dynamic form content based on service type
  const getServiceContent = () => {
    // Determine if this is a quote-based service (web development, design, etc.)
    const isQuoteService = service.type === 'web-design' ||
                          service.name.toLowerCase().includes('website') ||
                          service.name.toLowerCase().includes('web design') ||
                          service.name.toLowerCase().includes('web development') ||
                          service.name.toLowerCase().includes('development')

    const actionWord = isQuoteService ? 'Quote' : 'Analysis'

    const baseContent = {
      title: `Get Your Free ${service.name} ${actionWord}`,
      subtitle: isQuoteService
        ? `Get a custom ${service.name.toLowerCase()} quote tailored to your business needs and goals.`
        : `See exactly how your business can improve with our ${service.name.toLowerCase()} strategies.`,
      ctaText: service.ctaText || `Get My Free ${service.name} ${actionWord}`,
      messagePlaceholder: service.messagePlaceholder || `Tell us about your ${service.name.toLowerCase()} ${isQuoteService ? 'requirements' : 'goals'}*`,
    };

    switch (service.type) {
      case 'local-seo':
        return {
          ...baseContent,
          // Use dynamic title but keep specific subtitle for local SEO
          subtitle: 'See exactly how your business ranks against local competitors and discover untapped opportunities.',
          benefits: service.benefits || [
            'Complete local SEO audit & competitor analysis',
            'Google My Business optimization recommendations',
            'Local keyword opportunities & strategy roadmap',
            'Citation audit & local link building plan'
          ]
        };

      case 'seo':
        return {
          ...baseContent,
          // Use dynamic title but keep specific subtitle for SEO
          subtitle: 'Discover exactly how to dominate Google search results in your industry.',
          benefits: service.benefits || [
            'Complete SEO audit & competitor analysis',
            'Keyword opportunities & content strategy',
            'Technical SEO recommendations',
            'Link building and authority plan'
          ]
        };

      case 'ppc':
        return {
          ...baseContent,
          // Use dynamic title but keep specific subtitle for PPC
          subtitle: 'See how to maximize your advertising ROI and reduce cost per acquisition.',
          benefits: service.benefits || [
            'PPC account audit & optimization plan',
            'Keyword research & bid strategy',
            'Ad copy and landing page recommendations',
            'Campaign structure & targeting analysis'
          ]
        };

      case 'web-design':
        return {
          ...baseContent,
          // Use dynamic title and subtitle for web services
          benefits: service.benefits || [
            'Complete website audit & UX analysis',
            'Design recommendations & best practices',
            'Mobile optimization strategy',
            'Conversion optimization plan'
          ]
        };

      case 'social-media':
        return {
          ...baseContent,
          // Use dynamic title but keep specific subtitle
          subtitle: 'See how to build engaged communities and drive social media ROI.',
          benefits: service.benefits || [
            'Social media audit & competitor analysis',
            'Content strategy & engagement plan',
            'Platform optimization recommendations',
            'Social advertising strategy'
          ]
        };

      case 'email-marketing':
        return {
          ...baseContent,
          // Use dynamic title but keep specific subtitle
          subtitle: 'Discover how to build relationships that drive revenue through email.',
          benefits: service.benefits || [
            'Email marketing audit & performance analysis',
            'List building & segmentation strategy',
            'Automation workflow recommendations',
            'Campaign optimization plan'
          ]
        };

      case 'branding':
        return {
          ...baseContent,
          // Use dynamic title but keep specific subtitle
          subtitle: 'See how to build a powerful brand identity that resonates with your audience.',
          benefits: service.benefits || [
            'Complete brand audit & competitive analysis',
            'Brand positioning & messaging strategy',
            'Visual identity recommendations',
            'Brand implementation roadmap'
          ]
        };

      case 'conversion-optimization':
        return {
          ...baseContent,
          // Use dynamic title but keep specific subtitle
          subtitle: 'Discover how to turn more visitors into customers with data-driven optimization.',
          benefits: service.benefits || [
            'Conversion audit & performance analysis',
            'A/B testing strategy & recommendations',
            'User experience optimization plan',
            'Revenue growth projections'
          ]
        };

      case 'reputation-management':
        return {
          ...baseContent,
          // Use dynamic title but keep specific subtitle
          subtitle: 'See how to protect and enhance your online reputation across all platforms.',
          benefits: service.benefits || [
            'Complete reputation audit & risk assessment',
            'Review management strategy',
            'Crisis prevention & response plan',
            'Brand authority building roadmap'
          ]
        };

      default:
        return {
          ...baseContent,
          benefits: service.benefits || [
            'Comprehensive analysis of your current situation',
            'Custom strategy recommendations',
            'Implementation roadmap',
            'Performance tracking plan'
          ]
        };
    }
  };

  const serviceContent = getServiceContent();

  const onFormSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');
    
    try {
      // Add user country and timestamp to form data
      const formDataWithExtras: ExtendedFormData = {
        ...data,
        userCountry,
        timestamp: new Date().toISOString(),
      };

      if (onSubmit) {
        // Use custom onSubmit if provided
        await onSubmit(formDataWithExtras);
        setSubmitStatus('success');
        reset();
        // Reset phone to initial value after form reset
        setTimeout(() => {
          setValue('phone', initialPhoneValue);
        }, 100);
      } else {
        // Default: Send to API
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formDataWithExtras),
        });

        const result: ApiResponse = await response.json();

        if (!response.ok) {
          throw new Error(result.message || 'Failed to submit form');
        }

        if (result.success) {
          setSubmitStatus('success');
          reset();
          // Reset phone to initial value after form reset
          setTimeout(() => {
            setValue('phone', initialPhoneValue);
          }, 100);
        } else {
          throw new Error(result.message || 'Form submission failed');
        }
      }
    } catch (error) {
      console.error('Form submission error:', error);
      const errorMsg = getErrorMessage(error);
      setErrorMessage(errorMsg);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Success state
  if (submitStatus === 'success') {
    return (
      <div 
        className={`rounded-2xl shadow-xl p-8 ${className}`}
        style={{ backgroundColor: '#ffffff' }}
      >
        <div className="text-center">
          <div 
            className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
            style={{ backgroundColor: '#dcfce7' }}
          >
            <svg 
              className="w-8 h-8" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
              style={{ color: '#16a34a' }}
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h3 
            className="text-2xl font-bold mb-2"
            style={{ color: '#1f2937' }}
          >
            Thank You! 🎉
          </h3>
          <p 
            className="mb-4"
            style={{ color: '#4b5563' }}
          >
            Your request has been submitted successfully. We&apos;ll contact you within 24 hours with your free {service.name.toLowerCase()} analysis.
          </p>
          <p 
            className="text-sm mb-6"
            style={{ color: '#6b7280' }}
          >
            Check your email for confirmation and next steps.
          </p>
          <button 
            onClick={() => {
              setSubmitStatus('idle');
              setErrorMessage('');
              // Reset phone to initial value when showing form again
              setTimeout(() => {
                setValue('phone', initialPhoneValue);
              }, 100);
            }}
            className="font-semibold transition-colors"
            style={{ color: '#2563eb' }}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = '#1d4ed8';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = '#2563eb';
            }}
          >
            Submit Another Request
          </button>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`rounded-2xl shadow-xl p-8 ${className}`}
      style={{ backgroundColor: '#ffffff' }}
    >
      <h3 
        className="text-2xl font-bold mb-2"
        style={{ color: '#1f2937' }}
      >
        {serviceContent.title}
      </h3>
      <p 
        className="mb-6"
        style={{ color: '#4b5563' }}
      >
        {serviceContent.subtitle}
      </p>

      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
        {/* Business Name & Your Name */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <input 
              {...register('businessName')}
              type="text" 
              placeholder="Business Name*" 
              className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors"
              style={{ 
                border: '1px solid #d1d5db',
                color: '#374151',
                backgroundColor: '#ffffff'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = 'transparent';
                e.target.style.boxShadow = '0 0 0 2px #3b82f6';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db';
                e.target.style.boxShadow = 'none';
              }}
            />
            {errors.businessName && (
              <p className="text-xs mt-1" style={{ color: '#ef4444' }}>
                {errors.businessName.message}
              </p>
            )}
          </div>
          <div>
            <input 
              {...register('fullName')}
              type="text" 
              placeholder="Your Name*" 
              className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors"
              style={{ 
                border: '1px solid #d1d5db',
                color: '#374151',
                backgroundColor: '#ffffff'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = 'transparent';
                e.target.style.boxShadow = '0 0 0 2px #3b82f6';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db';
                e.target.style.boxShadow = 'none';
              }}
            />
            {errors.fullName && (
              <p className="text-xs mt-1" style={{ color: '#ef4444' }}>
                {errors.fullName.message}
              </p>
            )}
          </div>
        </div>

        {/* Email */}
        <div>
          <input 
            {...register('email')}
            type="email" 
            placeholder="Email Address*" 
            className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors"
            style={{ 
              border: '1px solid #d1d5db',
              color: '#374151',
              backgroundColor: '#ffffff'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = 'transparent';
              e.target.style.boxShadow = '0 0 0 2px #3b82f6';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = '#d1d5db';
              e.target.style.boxShadow = 'none';
            }}
          />
          {errors.email && (
            <p className="text-xs mt-1" style={{ color: '#ef4444' }}>
              {errors.email.message}
            </p>
          )}
        </div>

        {/* Phone Number with Country Detection and Pre-filled Prefix */}
        <div>
          <Controller
            name="phone"
            control={control}
            render={({ field }) => (
              <div className="flex gap-2">
                <PhoneInput
                  {...field}
                  value={field.value || initialPhoneValue}
                  defaultCountry={defaultPhoneCountry}
                  placeholder="Phone Number*"
                  className="w-full"
                  style={{
                    '--PhoneInputCountryFlag-borderColor': 'transparent',
                    '--PhoneInput-color': '#374151',
                  }}
                  numberInputProps={{
                    className: 'flex-1 px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors',
                    style: { 
                      border: '1px solid #d1d5db',
                      color: '#374151',
                      backgroundColor: '#ffffff'
                    }
                  }}
                  countrySelectProps={{
                    className: 'px-3 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors',
                    style: { 
                      border: '1px solid #d1d5db',
                      backgroundColor: '#ffffff',
                      minWidth: '80px',
                      width: '80px'
                    }
                  }}
                  onChange={(value) => {
                    field.onChange(value);
                    // If user clears the field but country is still selected, restore the prefix
                    if (!value && defaultPhoneCountry) {
                      try {
                        const callingCode = getCountryCallingCode(defaultPhoneCountry);
                        const prefixValue = `+${callingCode}`;
                        setTimeout(() => {
                          field.onChange(prefixValue);
                        }, 10);
                      } catch {
                        console.log('Error restoring prefix');
                      }
                    }
                  }}
                />
              </div>
            )}
          />
          {errors.phone && (
            <p className="text-xs mt-1" style={{ color: '#ef4444' }}>
              {errors.phone.message}
            </p>
          )}
        </div>

        {/* Business Location */}
        <div>
          <input 
            {...register('location')}
            type="text" 
            placeholder="Business Location (City, State)*" 
            className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors"
            style={{ 
              border: '1px solid #d1d5db',
              color: '#374151',
              backgroundColor: '#ffffff'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = 'transparent';
              e.target.style.boxShadow = '0 0 0 2px #3b82f6';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = '#d1d5db';
              e.target.style.boxShadow = 'none';
            }}
          />
          {errors.location && (
            <p className="text-xs mt-1" style={{ color: '#ef4444' }}>
              {errors.location.message}
            </p>
          )}
        </div>

        {/* Website URL */}
        <div>
          <input 
            {...register('website')}
            type="url" 
            placeholder="Website URL (optional)" 
            className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 transition-colors"
            style={{ 
              border: '1px solid #d1d5db',
              color: '#374151',
              backgroundColor: '#ffffff'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = 'transparent';
              e.target.style.boxShadow = '0 0 0 2px #3b82f6';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = '#d1d5db';
              e.target.style.boxShadow = 'none';
            }}
          />
          {errors.website && (
            <p className="text-xs mt-1" style={{ color: '#ef4444' }}>
              {errors.website.message}
            </p>
          )}
        </div>

        {/* Message */}
        <div>
          <textarea 
            {...register('message')}
            placeholder={serviceContent.messagePlaceholder}
            rows={3}
            className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 resize-none transition-colors"
            style={{ 
              border: '1px solid #d1d5db',
              color: '#374151',
              backgroundColor: '#ffffff'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = 'transparent';
              e.target.style.boxShadow = '0 0 0 2px #3b82f6';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = '#d1d5db';
              e.target.style.boxShadow = 'none';
            }}
          />
          {errors.message && (
            <p className="text-xs mt-1" style={{ color: '#ef4444' }}>
              {errors.message.message}
            </p>
          )}
        </div>

        {/* Hidden service field */}
        <input {...register('service')} type="hidden" />

        {/* Error message */}
        {submitStatus === 'error' && (
          <div 
            className="px-4 py-3 rounded-lg"
            style={{ 
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              color: '#b91c1c'
            }}
          >
            <div className="flex items-center">
              <svg 
                className="w-5 h-5 mr-2 flex-shrink-0" 
                fill="currentColor" 
                viewBox="0 0 20 20"
                style={{ color: '#b91c1c' }}
              >
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <div>
                <p className="text-sm font-medium">Form submission failed</p>
                <p className="text-sm mt-1">{errorMessage || 'Please try again or call us directly at (555) 123-4567.'}</p>
              </div>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <button 
          type="submit"
          disabled={isSubmitting}
          className="w-full font-bold py-4 rounded-lg transition-all duration-300 flex items-center justify-center"
          style={{
            background: isSubmitting 
              ? 'linear-gradient(to right, #9ca3af, #6b7280)'
              : 'linear-gradient(to right, #2563eb, #1d4ed8)',
            color: '#ffffff',
            cursor: isSubmitting ? 'not-allowed' : 'pointer'
          }}
          onMouseEnter={(e) => {
            if (!isSubmitting) {
              e.currentTarget.style.background = 'linear-gradient(to right, #1d4ed8, #1e40af)';
            }
          }}
          onMouseLeave={(e) => {
            if (!isSubmitting) {
              e.currentTarget.style.background = 'linear-gradient(to right, #2563eb, #1d4ed8)';
            }
          }}
        >
          {isSubmitting ? (
            <>
              <svg 
                className="animate-spin -ml-1 mr-3 h-5 w-5" 
                xmlns="http://www.w3.org/2000/svg" 
                fill="none" 
                viewBox="0 0 24 24"
                style={{ color: '#ffffff' }}
              >
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Submitting...
            </>
          ) : (
            serviceContent.ctaText
          )}
        </button>
      </form>

      {/* Trust indicator */}
      <p 
        className="text-xs text-center mt-4"
        style={{ color: '#6b7280' }}
      >
        🔒 Your information is secure and will never be shared. We&apos;ll contact you within 24 hours.
      </p>
    </div>
  );
};

export default ServiceContactForm;