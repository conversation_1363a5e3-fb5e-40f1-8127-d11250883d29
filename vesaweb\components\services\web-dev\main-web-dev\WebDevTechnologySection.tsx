import React from 'react';
import { Code, Database, Cloud, Shield, Smartphone, Monitor } from 'lucide-react';

const WebDevTechnologySection: React.FC = () => {
  const technologies = [
    {
      name: 'React & Next.js',
      description: 'Modern JavaScript frameworks for fast, interactive user interfaces',
      purpose: 'Frontend development with server-side rendering and optimal performance'
    },
    {
      name: 'Node.js & Express',
      description: 'Scalable backend development with JavaScript runtime environment',
      purpose: 'API development, server-side logic, and database integration'
    },
    {
      name: 'WordPress & Headless CMS',
      description: 'Content management systems for easy content updates and management',
      purpose: 'User-friendly content management with flexible frontend options'
    },
    {
      name: 'MongoDB & PostgreSQL',
      description: 'Modern database solutions for reliable data storage and retrieval',
      purpose: 'Scalable data management and complex query optimization'
    },
    {
      name: 'AWS & Google Cloud',
      description: 'Enterprise-grade cloud hosting and infrastructure services',
      purpose: 'Reliable hosting, automatic scaling, and global content delivery'
    },
    {
      name: 'Shopify & WooCommerce',
      description: 'E-commerce platforms for online stores and payment processing',
      purpose: 'Complete e-commerce solutions with inventory and order management'
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
            Cutting-Edge Technologies We Use
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            We leverage the latest technologies and frameworks to build websites that are fast, secure, scalable, and future-proof.
          </p>
        </div>

        {/* Technology Stack */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {technologies.map((tech, index) => (
            <div key={index} className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <h4 className="text-xl font-bold text-gray-800 mb-4">{tech.name}</h4>
              <p className="text-gray-600 mb-4">{tech.description}</p>
              <p className="text-sm text-blue-600 font-medium">{tech.purpose}</p>
            </div>
          ))}
        </div>

        {/* Modern Web Standards */}
        <div className="bg-white rounded-3xl p-12 shadow-xl mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Built for the Modern Web
            </h3>
            <p className="text-xl text-gray-600">
              Every website we develop follows modern web standards and best practices for optimal performance and user experience.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl">
                <Smartphone className="text-blue-600 mb-4 mx-auto" size={48} />
                <h4 className="text-xl font-bold text-gray-800 mb-4">Mobile-First Design</h4>
                <p className="text-gray-600">Responsive design that works perfectly on all devices, from smartphones to desktops.</p>
              </div>
            </div>
            <div className="text-center">
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl">
                <Monitor className="text-green-600 mb-4 mx-auto" size={48} />
                <h4 className="text-xl font-bold text-gray-800 mb-4">Progressive Web Apps</h4>
                <p className="text-gray-600">App-like experiences that work offline and can be installed on any device.</p>
              </div>
            </div>
            <div className="text-center">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl">
                <Shield className="text-blue-600 mb-4 mx-auto" size={48} />
                <h4 className="text-xl font-bold text-gray-800 mb-4">Security First</h4>
                <p className="text-gray-600">SSL certificates, secure coding practices, and regular security updates included.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Performance Features */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div>
            <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-8">
              Performance That Drives Results
            </h3>
            <p className="text-xl text-gray-600 mb-8">
              We optimize every aspect of your website for maximum speed, search engine visibility, and user engagement.
            </p>
            
            <div className="space-y-6">
              <div className="flex items-start">
                <div className="bg-blue-100 p-3 rounded-lg mr-4 flex-shrink-0">
                  <Code className="text-blue-600" size={24} />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-800 mb-2">Clean, Optimized Code</h4>
                  <p className="text-gray-600">Hand-written, semantic code that loads fast and ranks well in search engines.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="bg-blue-100 p-3 rounded-lg mr-4 flex-shrink-0">
                  <Database className="text-blue-600" size={24} />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-800 mb-2">Optimized Database Queries</h4>
                  <p className="text-gray-600">Efficient data retrieval and caching strategies for lightning-fast page loads.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="bg-green-100 p-3 rounded-lg mr-4 flex-shrink-0">
                  <Cloud className="text-green-600" size={24} />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-800 mb-2">CDN & Cloud Optimization</h4>
                  <p className="text-gray-600">Global content delivery networks ensure fast loading times worldwide.</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-3xl p-8 text-white">
            <h4 className="text-2xl font-bold mb-6">Performance Guarantees</h4>
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <span>Page Load Time</span>
                <span className="font-bold text-blue-200">&lt; 3 seconds</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Uptime Guarantee</span>
                <span className="font-bold text-blue-200">99.9%</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Mobile Performance Score</span>
                <span className="font-bold text-blue-200">90+</span>
              </div>
              <div className="flex justify-between items-center">
                <span>SEO Score</span>
                <span className="font-bold text-blue-200">95+</span>
              </div>
            </div>
            <div className="mt-8 p-4 bg-blue-700 rounded-2xl">
              <p className="text-sm text-blue-100">
                <strong>Money-Back Guarantee:</strong> If we don&apos;t meet these performance standards, we&apos;ll refund your investment.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WebDevTechnologySection;
