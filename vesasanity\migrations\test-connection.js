// migrations/test-connection.js - Test Sanity connection
const { createClient } = require('@sanity/client')

const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

async function testConnection() {
  console.log('Testing Sanity connection...');
  
  try {
    // Test basic connection by fetching project info
    const result = await client.fetch('*[_type == "caseStudy"][0..2]');
    console.log('✅ Connection successful!');
    console.log(`Found ${result.length} existing case studies`);
    
    if (result.length > 0) {
      console.log('Existing case studies:');
      result.forEach((cs, index) => {
        console.log(`  ${index + 1}. ${cs.title || 'Untitled'}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    
    if (error.message.includes('Unauthorized')) {
      console.log('💡 Check your API token permissions');
    } else if (error.message.includes('not found')) {
      console.log('💡 Check your project ID and dataset name');
    }
  }
}

// Run the test
testConnection();
