import React from 'react';
import Image from 'next/image';
import { CheckCircle } from 'lucide-react';

const DigitalMarketingProcessSection: React.FC = () => {
  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
            Our Proven Digital Marketing Process
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            VESA Solutions follows a systematic, data-driven approach that has delivered consistent results for hundreds of clients across diverse industries.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-24">
          <div>
            <Image
              src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop"
              alt="Digital marketing analysis and strategy planning"
              width={600}
              height={400}
              className="w-full rounded-3xl shadow-xl"
            />
          </div>
          <div>
            <div className="bg-purple-500 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mb-6">
              1
            </div>
            <h3 className="text-3xl font-bold text-gray-800 mb-6">Comprehensive Marketing Audit & Analysis</h3>
            <p className="text-lg text-gray-600 leading-relaxed mb-6">
              We begin with a thorough analysis of your business, marketing goals, target audience, and competitive landscape. This includes digital presence audits, market research, customer analysis, and competitor benchmarking.
            </p>
            <ul className="space-y-3">
              <li className="flex items-center text-gray-700">
                <CheckCircle size={16} className="text-green-500 mr-3" />
                Digital presence audit and recommendations
              </li>
              <li className="flex items-center text-gray-700">
                <CheckCircle size={16} className="text-green-500 mr-3" />
                Target audience research and persona development
              </li>
              <li className="flex items-center text-gray-700">
                <CheckCircle size={16} className="text-green-500 mr-3" />
                Competitor analysis and market positioning
              </li>
              <li className="flex items-center text-gray-700">
                <CheckCircle size={16} className="text-green-500 mr-3" />
                Current campaign performance evaluation
              </li>
            </ul>
          </div>
        </div>

        {/* Process Steps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-3xl p-8">
            <div className="bg-purple-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6">
              2
            </div>
            <h4 className="text-xl font-bold text-gray-800 mb-4">Strategy Development</h4>
            <p className="text-gray-600 mb-4">Custom digital marketing strategy creation based on audit findings, business goals, and competitive analysis.</p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Multi-channel campaign strategy</li>
              <li>• Content marketing plan</li>
              <li>• PPC and social media roadmap</li>
              <li>• Email marketing automation</li>
            </ul>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-8">
            <div className="bg-green-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6">
              3
            </div>
            <h4 className="text-xl font-bold text-gray-800 mb-4">Implementation & Launch</h4>
            <p className="text-gray-600 mb-4">Execute marketing campaigns including PPC setup, social media management, and content creation.</p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Campaign setup and launch</li>
              <li>• Content creation and publishing</li>
              <li>• Social media management</li>
              <li>• Email automation setup</li>
            </ul>
          </div>

          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-3xl p-8">
            <div className="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6">
              4
            </div>
            <h4 className="text-xl font-bold text-gray-800 mb-4">Monitoring & Optimization</h4>
            <p className="text-gray-600 mb-4">Continuous monitoring, analysis, and optimization based on performance data and market trends.</p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Monthly performance reports</li>
              <li>• Campaign optimization</li>
              <li>• ROI analysis and insights</li>
              <li>• Strategy refinement</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DigitalMarketingProcessSection;
