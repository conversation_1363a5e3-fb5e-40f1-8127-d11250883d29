import React from 'react';

const WebDevProcessSection: React.FC = () => {
  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
            Our Proven Web Development Process
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            VESA Solutions follows a systematic, client-focused approach that ensures your website is delivered on time, on budget, and exceeds your expectations.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16">
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-3xl p-8">
            <div className="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6">
              1
            </div>
            <h4 className="text-xl font-bold text-gray-800 mb-4">Discovery & Planning</h4>
            <p className="text-gray-600 mb-4">Comprehensive analysis of your business goals, target audience, and technical requirements to create a detailed project roadmap.</p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Business goals assessment</li>
              <li>• Target audience research</li>
              <li>• Technical requirements analysis</li>
              <li>• Project timeline & milestones</li>
            </ul>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-8">
            <div className="bg-green-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6">
              2
            </div>
            <h4 className="text-xl font-bold text-gray-800 mb-4">Design & Prototyping</h4>
            <p className="text-gray-600 mb-4">Creating wireframes, mockups, and interactive prototypes to visualize the user experience before development begins.</p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Wireframe creation</li>
              <li>• Visual design mockups</li>
              <li>• Interactive prototypes</li>
              <li>• Client feedback & revisions</li>
            </ul>
          </div>

          <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-3xl p-8">
            <div className="bg-orange-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6">
              3
            </div>
            <h4 className="text-xl font-bold text-gray-800 mb-4">Development & Testing</h4>
            <p className="text-gray-600 mb-4">Building your website using modern technologies with rigorous testing to ensure optimal performance and functionality.</p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Frontend & backend development</li>
              <li>• Responsive design implementation</li>
              <li>• Performance optimization</li>
              <li>• Cross-browser testing</li>
            </ul>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-3xl p-8">
            <div className="bg-purple-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6">
              4
            </div>
            <h4 className="text-xl font-bold text-gray-800 mb-4">Launch & Optimization</h4>
            <p className="text-gray-600 mb-4">Deploying your website with ongoing monitoring, optimization, and support to ensure continued success.</p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Website deployment & launch</li>
              <li>• Performance monitoring</li>
              <li>• SEO optimization</li>
              <li>• Ongoing support & maintenance</li>
            </ul>
          </div>
        </div>

        {/* Timeline Section */}
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-3xl p-12">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Typical Project Timeline
            </h3>
            <p className="text-xl text-gray-600">
              Most web development projects follow this timeline, though complex projects may require additional time.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <div className="text-3xl font-bold text-blue-600 mb-2">1-2</div>
              <div className="text-gray-800 font-semibold mb-2">Weeks</div>
              <div className="text-sm text-gray-600">Discovery & Planning Phase</div>
            </div>
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <div className="text-3xl font-bold text-green-600 mb-2">2-3</div>
              <div className="text-gray-800 font-semibold mb-2">Weeks</div>
              <div className="text-sm text-gray-600">Design & Prototyping Phase</div>
            </div>
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <div className="text-3xl font-bold text-orange-600 mb-2">4-8</div>
              <div className="text-gray-800 font-semibold mb-2">Weeks</div>
              <div className="text-sm text-gray-600">Development & Testing Phase</div>
            </div>
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <div className="text-3xl font-bold text-purple-600 mb-2">1</div>
              <div className="text-gray-800 font-semibold mb-2">Week</div>
              <div className="text-sm text-gray-600">Launch & Optimization Phase</div>
            </div>
          </div>
          
          <div className="text-center mt-8">
            <p className="text-gray-600 mb-4">
              <strong>Total Timeline:</strong> 8-14 weeks for most projects
            </p>
            <p className="text-sm text-gray-500">
              Complex e-commerce or web applications may require 12-20 weeks depending on features and integrations.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WebDevProcessSection;
