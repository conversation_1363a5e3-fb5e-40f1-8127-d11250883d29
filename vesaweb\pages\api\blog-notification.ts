// pages/api/blog-notification.ts - API endpoint for sending blog notifications
import { NextApiRequest, NextApiResponse } from 'next';
import { sendBlogNotificationToSubscribers, BlogNotificationData } from '@/lib/newsletter';
import { getBlogPost } from '@/lib/sanity-blog';
import { urlForImage } from '@/lib/sanity-blog';

interface BlogNotificationRequest {
  blogSlug: string;
  // Optional: allow manual override of blog data
  blogData?: BlogNotificationData;
}

// Helper function to get error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) return error.message;
  return String(error);
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { blogSlug, blogData }: BlogNotificationRequest = req.body;

    if (!blogSlug && !blogData) {
      return res.status(400).json({ 
        message: 'Blog slug or blog data is required',
        success: false 
      });
    }

    let notificationData: BlogNotificationData;

    if (blogData) {
      // Use provided blog data
      notificationData = blogData;
    } else {
      // Fetch blog data from Sanity
      const blogPost = await getBlogPost(blogSlug);
      
      if (!blogPost) {
        return res.status(404).json({ 
          message: 'Blog post not found',
          success: false 
        });
      }

      // Convert blog post to notification data
      notificationData = {
        title: blogPost.title,
        excerpt: blogPost.excerpt,
        slug: blogPost.slug.current,
        featuredImage: blogPost.featuredImage ? urlForImage(blogPost.featuredImage).width(600).height(300).url() : undefined,
        publishedAt: blogPost.publishedAt
      };
    }

    // Send notifications to all subscribers
    const result = await sendBlogNotificationToSubscribers(notificationData);

    if (result.success) {
      console.log(`Blog notification sent successfully: ${result.sentCount} emails sent, ${result.failedCount} failed`);
      
      res.status(200).json({
        message: `Blog notification sent to ${result.sentCount} subscribers`,
        success: true,
        details: {
          sentCount: result.sentCount,
          failedCount: result.failedCount,
          totalSubscribers: result.sentCount + result.failedCount
        }
      });
    } else {
      console.error('Blog notification failed:', result.errors);
      
      res.status(500).json({
        message: 'Failed to send blog notifications',
        success: false,
        details: {
          sentCount: result.sentCount,
          failedCount: result.failedCount,
          errors: result.errors
        }
      });
    }

  } catch (error) {
    console.error('Blog notification API error:', getErrorMessage(error));
    res.status(500).json({ 
      message: 'Internal server error while sending blog notifications',
      success: false,
      error: process.env.NODE_ENV === 'development' ? getErrorMessage(error) : 'Internal server error'
    });
  }
}
