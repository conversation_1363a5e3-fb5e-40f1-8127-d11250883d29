// CaseStudy.tsx - Fixed version
import React from 'react'
import Image from 'next/image'
import { urlForImage } from '@/lib/sanity'
import { CaseStudy as CaseStudyType } from '@/types/subService'

interface CaseStudyProps {
  data?: CaseStudyType
}

export const CaseStudy: React.FC<CaseStudyProps> = ({ data }) => {
  if (!data) return null

  return (
    <section className={`py-24 bg-gradient-to-r ${data.backgroundGradient || 'from-blue-600 to-blue-800'}`}>
      <div className="max-w-7xl mx-auto px-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div>
            {data.title && (
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-8">
                {data.title}
              </h2>
            )}
            
            {data.description && (
              <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                {data.description}
              </p>
            )}
            
            {data.results && data.results.length > 0 && (
              <div className="grid grid-cols-2 gap-6 mb-8">
                {data.results.map((result, index) => (
                  <div key={index} className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-center">
                    <div className="text-3xl font-bold text-blue-200 mb-2">{result.value}</div>
                    <div className="text-blue-100 text-sm">{result.label}</div>
                  </div>
                ))}
              </div>
            )}
            
            {data.ctaButton?.text && (
              <button className="bg-white text-blue-600 font-semibold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300">
                {data.ctaButton.text}
              </button>
            )}
          </div>
          
          <div>
            {data.image && (
              <Image
                src={urlForImage(data.image).width(600).height(400).url()}
                alt="Case study success story"
                width={600}
                height={400}
                className="w-full rounded-3xl shadow-2xl"
              />
            )}
          </div>
        </div>
      </div>
    </section>
  )
}