// Comprehensive migration to update all sub-service colors based on parent service
const { createClient } = require('@sanity/client')

const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

// Color schemes based on parent service
const COLOR_SCHEMES = {
  'seo': {
    // Hero Section - darker gradients
    heroGradient: 'from-green-600 to-green-800',
    // Footer CTA - darker gradients  
    footerCtaGradient: 'from-green-700 to-green-900',
    // Market Intelligence - light gradients
    marketIntelligenceGradient: 'from-green-50 to-green-100',
    // Stats and sections - light gradients
    statsColor: 'from-green-50 to-green-100'
  },
  'web-development': {
    // Keep existing blue colors for web development
    heroGradient: 'from-blue-600 to-blue-800',
    footerCtaGradient: 'from-blue-700 to-blue-900', 
    marketIntelligenceGradient: 'from-blue-50 to-blue-100',
    statsColor: 'from-blue-50 to-blue-100'
  },
  'digital-marketing': {
    heroGradient: 'from-purple-600 to-purple-800',
    footerCtaGradient: 'from-purple-700 to-purple-900',
    marketIntelligenceGradient: 'from-purple-50 to-purple-100', 
    statsColor: 'from-purple-50 to-purple-100'
  }
}

async function updateSubServiceColors() {
  try {
    console.log('🎨 Starting comprehensive sub-service color update...')
    
    // Get all sub-services with their current data
    const subServices = await client.fetch(`
      *[_type == "subService"] {
        _id,
        title,
        parentService,
        hero,
        whyMatters,
        strategicImplementation,
        marketIntelligence,
        footerCta
      }
    `)
    
    console.log(`Found ${subServices.length} sub-services to update`)
    
    for (const subService of subServices) {
      const parentService = subService.parentService
      const colorScheme = COLOR_SCHEMES[parentService]
      
      if (!colorScheme) {
        console.log(`⚠️  No color scheme found for parent service: ${parentService} (${subService.title})`)
        continue
      }
      
      console.log(`\n🎨 Updating colors for: ${subService.title} (${parentService})`)
      
      const updates = {}
      
      // 1. Update Hero Section background gradient
      if (subService.hero) {
        updates['hero.backgroundGradient'] = colorScheme.heroGradient
        console.log(`  ✅ Hero gradient: ${colorScheme.heroGradient}`)
      }
      
      // 2. Update WhyMatters stats colors (if they exist and are using default blue)
      if (subService.whyMatters && subService.whyMatters.stats) {
        subService.whyMatters.stats.forEach((stat, index) => {
          if (!stat.color || stat.color.includes('blue')) {
            updates[`whyMatters.stats[${index}].color`] = colorScheme.statsColor
          }
        })
        console.log(`  ✅ WhyMatters stats colors updated`)
      }
      
      // 3. Update Strategic Implementation section colors
      if (subService.strategicImplementation && subService.strategicImplementation.sections) {
        subService.strategicImplementation.sections.forEach((section, index) => {
          if (!section.color || section.color.includes('blue')) {
            updates[`strategicImplementation.sections[${index}].color`] = colorScheme.statsColor
          }
        })
        console.log(`  ✅ Strategic Implementation section colors updated`)
      }
      
      // 4. Update Market Intelligence background gradient
      if (subService.marketIntelligence) {
        updates['marketIntelligence.backgroundGradient'] = colorScheme.marketIntelligenceGradient
        console.log(`  ✅ Market Intelligence gradient: ${colorScheme.marketIntelligenceGradient}`)
      }
      
      // 5. Update Footer CTA background gradient
      if (subService.footerCta) {
        updates['footerCta.backgroundGradient'] = colorScheme.footerCtaGradient
        console.log(`  ✅ Footer CTA gradient: ${colorScheme.footerCtaGradient}`)
      }
      
      // Apply all updates
      if (Object.keys(updates).length > 0) {
        await client.patch(subService._id).set(updates).commit()
        console.log(`  🎉 Successfully updated ${Object.keys(updates).length} color fields`)
      } else {
        console.log(`  ⏭️  No color updates needed`)
      }
    }
    
    console.log('\n✅ Comprehensive sub-service color update completed successfully!')
    console.log('\n📊 Summary:')
    console.log('- SEO services: Green color scheme')
    console.log('- Web Development services: Blue color scheme (unchanged)')
    console.log('- Digital Marketing services: Purple color scheme')
    
  } catch (error) {
    console.error('❌ Error during color update:', error)
    throw error
  }
}

// Run the update
if (require.main === module) {
  updateSubServiceColors()
    .then(() => {
      console.log('🎉 Migration completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error)
      process.exit(1)
    })
}

module.exports = { updateSubServiceColors }
