@keyframes scrollY {
  0% { transform: translateY(0); }
  100% { transform: translateY(-50%); }
}

.infiniteScrollY {
  animation-name: scrollY;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-fill-mode: forwards;
  animation-play-state: running;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

.scrollWrapper {
  overflow: hidden;
  width: 100%;
  position: relative;
}

.smoothScroll {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: max-content;
}

.heroMockup {
  width: 380px;
  height: 220px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
  border: 4px solid rgba(255, 255, 255, 0.15);
  position: relative;
}

/* Keep same size on tablet */
@media (min-width: 768px) {
  .heroMockup {
    width: 380px;
    height: 220px;
  }
}

/* Larger mockups on desktop only */
@media (min-width: 1024px) {
  .heroMockup {
    width: 580px;
    height: 340px;
  }
}

/* Even larger mockups on large desktop */
@media (min-width: 1440px) {
  .heroMockup {
    width: 620px;
    height: 360px;
  }
}

.heroMockup img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top center;
}

.heroMockup::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 5%;
  right: 5%;
  height: 6px;
  background: rgba(0, 0, 0, 0.3);
  filter: blur(4px);
  border-radius: 50%;
}

/* Base styles for mobile (2 columns) */
.columnsContainer {
  transform: rotate(30deg) scale(1.2);
  transform-origin: right center;
  width: 140%;
  height: 400%;
  position: absolute;
  right: 5%;
  top: -40%;
  gap: 0.5rem;
}

.column3, .column4, .column5 {
  display: none;
}

/* Tablet styles (3 columns) */
@media (min-width: 768px) {
  .columnsContainer {
    transform: rotate(30deg) scale(1.0);
    transform-origin: right center;
    width: 125%;
    height: 400%;
    position: absolute;
    right: 6%;
    top: -40%;
    gap: 0.4rem;
  }
  
  .column3 {
    display: block;
  }
  .column4, .column5 {
    display: none;
  }
}

/* Desktop styles (4 columns with massive images) */
@media (min-width: 1024px) {
  .columnsContainer {
    transform: rotate(30deg) scale(0.75);
    transform-origin: right center;
    width: 140%;
    height: 400%;
    position: absolute;
    right: -1%;
    top: -40%;
    gap: 0.5rem;
  }
  
  .column3, .column4 {
    display: block;
  }
  .column5 {
    display: none;
  }
}

/* Large desktop styles (4 columns with even more massive images) */
@media (min-width: 1440px) {
  .columnsContainer {
    transform: rotate(30deg) scale(0.7);
    transform-origin: right center;
    width: 135%;
    height: 400%;
    position: absolute;
    right: 2%;
    top: -40%;
    gap: 0.6rem;
  }
  
  .column3, .column4 {
    display: block;
  }
  .column5 {
    display: none;
  }
}