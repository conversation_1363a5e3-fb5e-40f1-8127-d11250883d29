// schemas/subService.ts - Complete schema with Strategic Implementation and Market Intelligence
import { defineType, defineField } from 'sanity'

export const subService = defineType({
 name: 'subService',
 title: 'Sub Service',
 type: 'document',
 groups: [
   {
     name: 'content',
     title: 'Content',
     default: true
   },
   {
     name: 'hero',
     title: 'Hero Section'
   },
   {
     name: 'services',
     title: 'Services & Features'
   },
   {
     name: 'strategic',
     title: 'Strategic Implementation'
   },
   {
     name: 'market',
     title: 'Market Intelligence'
   },
   {
     name: 'process',
     title: 'Process & Case Study'
   },
   {
     name: 'social',
     title: 'Social Proof'
   },
   {
     name: 'cta',
     title: 'Call to Actions'
   },
   {
     name: 'seo',
     title: 'SEO & Settings'
   }
 ],
 fields: [
   // Service Classification
   defineField({
     name: 'parentService',
     title: 'Parent Service Category',
     type: 'string',
     group: 'content',
     options: {
       list: [
         { title: '🔍 SEO (Search Engine Optimization)', value: 'seo' },
         { title: '💻 Web Development', value: 'web-development' },
         { title: '📈 Digital Marketing', value: 'digital-marketing' }
       ]
     },
     validation: Rule => Rule.required(),
     description: 'Select the main service category this sub-service belongs to'
   }),
   defineField({
     name: 'title',
     title: 'Service Title',
     type: 'string',
     group: 'content',
     validation: Rule => Rule.required()
   }),
   defineField({
     name: 'slug',
     title: 'Slug',
     type: 'slug',
     group: 'content',
     options: {
       source: 'title',
       maxLength: 96,
     },
     validation: Rule => Rule.required()
   }),
   // Hero Section
   defineField({
     name: 'hero',
     title: 'Hero Section',
     type: 'object',
     group: 'hero',
     options: {
       collapsible: true,
       collapsed: false
     },
     fields: [
       defineField({
         name: 'badgeText',
         title: 'Badge Text',
         type: 'string'
       }),
       defineField({
         name: 'badgeIcon',
         title: 'Badge Icon',
         type: 'string',
         options: {
           list: [
             { title: 'Map Pin', value: 'MapPin' },
             { title: 'Search', value: 'Search' },
             { title: 'Target', value: 'Target' },
             { title: 'Trending Up', value: 'TrendingUp' },
             { title: 'Users', value: 'Users' },
             { title: 'Building', value: 'Building' },
             { title: 'Star', value: 'Star' },
             { title: 'Phone', value: 'Phone' },
             { title: 'Mail', value: 'Mail' },
             { title: 'Globe', value: 'Globe' },
             { title: 'Smartphone', value: 'Smartphone' },
             { title: 'Calendar', value: 'Calendar' },
             { title: 'Award', value: 'Award' },
             { title: 'Check Circle', value: 'CheckCircle' },
             { title: 'Navigation', value: 'Navigation' },
             { title: 'Zap', value: 'Zap' },
             { title: 'Rocket', value: 'Rocket' },
             { title: 'Lightbulb', value: 'Lightbulb' }
           ]
         }
       }),
       defineField({
         name: 'title',
         title: 'Main Title',
         type: 'string',
         validation: Rule => Rule.required()
       }),
       defineField({
         name: 'subtitle',
         title: 'Subtitle',
         type: 'text',
         rows: 3
       }),
       defineField({
         name: 'stats',
         title: 'Statistics',
         type: 'array',
         of: [
           {
             type: 'object',
             name: 'heroStat',
             title: 'Hero Statistic',
             fields: [
               defineField({
                 name: 'value',
                 title: 'Statistic Value',
                 type: 'string'
               }),
               defineField({
                 name: 'label',
                 title: 'Statistic Label',
                 type: 'string'
               })
             ],
             preview: {
               select: {
                 value: 'value',
                 label: 'label'
               },
               prepare(selection) {
                 const {value, label} = selection
                 return {
                   title: `${value || 'No value'}: ${label || 'No label'}`,
                   subtitle: 'Hero statistic'
                 }
               }
             }
           }
         ],
         validation: Rule => Rule.max(4)
       }),
       defineField({
         name: 'backgroundGradient',
         title: 'Background Gradient',
         type: 'string',
         options: {
           list: [
             { title: 'Blue', value: 'from-blue-600 to-blue-800' },
             { title: 'Green', value: 'from-green-600 to-green-800' },
             { title: 'Purple', value: 'from-purple-600 to-purple-800' },
             { title: 'Red', value: 'from-red-600 to-red-800' },
             { title: 'Indigo', value: 'from-indigo-600 to-indigo-800' }
           ]
         },
         initialValue: 'from-blue-600 to-blue-800'
       })
     ]
   }),
   // Why Service Matters
   defineField({
     name: 'whyMatters',
     title: 'Why This Service Matters',
     type: 'object',
     group: 'services',
     options: {
       collapsible: true,
       collapsed: true
     },
     fields: [
       defineField({
         name: 'title',
         title: 'Section Title',
         type: 'string'
       }),
       defineField({
         name: 'description',
         title: 'Description',
         type: 'array',
         of: [{ type: 'block' }]
       }),
       defineField({
         name: 'features',
         title: 'Key Features',
         type: 'array',
         of: [
           {
             type: 'object',
             name: 'feature',
             title: 'Feature',
             fields: [
               defineField({
                 name: 'text',
                 title: 'Feature Text',
                 type: 'string'
               })
             ],
             preview: {
               select: {
                 text: 'text'
               },
               prepare(selection) {
                 const {text} = selection
                 return {
                   title: text || 'No text entered',
                   subtitle: 'Feature'
                 }
               }
             }
           }
         ]
       }),
       defineField({
         name: 'stats',
         title: 'Supporting Statistics',
         type: 'array',
         of: [
           {
             type: 'object',
             name: 'whyMattersStat',
             title: 'Supporting Statistic',
             fields: [
               defineField({
                 name: 'value',
                 title: 'Statistic Value',
                 type: 'string'
               }),
               defineField({
                 name: 'label',
                 title: 'Statistic Label',
                 type: 'string'
               }),
               defineField({
                 name: 'color',
                 title: 'Background Color',
                 type: 'string',
                 options: {
                   list: [
                     { title: 'Blue 50-100', value: 'from-blue-50 to-blue-100' },
                     { title: 'Blue 100-200', value: 'from-blue-100 to-blue-200' },
                     { title: 'Blue 200-300', value: 'from-blue-200 to-blue-300' },
                     { title: 'Blue 300-400', value: 'from-blue-300 to-blue-400' }
                   ]
                 }
               })
             ],
             preview: {
               select: {
                 value: 'value',
                 label: 'label'
               },
               prepare(selection) {
                 const {value, label} = selection
                 return {
                   title: `${value || 'No value'}: ${label || 'No label'}`,
                   subtitle: 'Supporting statistic'
                 }
               }
             }
           }
         ],
         validation: Rule => Rule.max(4)
       }),
       defineField({
         name: 'ctaButton',
         title: 'CTA Button',
         type: 'object',
         fields: [
           defineField({
             name: 'text',
             title: 'Button Text',
             type: 'string'
           })
         ]
       })
     ]
   }),
   // Services
   defineField({
     name: 'services',
     title: 'Service Details',
     type: 'array',
     group: 'services',
     options: {
       modal: { type: 'dialog' }
     },
     of: [
       {
         type: 'object',
         name: 'service',
         title: 'Service',
         fields: [
           defineField({
             name: 'icon',
             title: 'Service Icon',
             type: 'string',
             options: {
               list: [
                 { title: 'Map Pin', value: 'MapPin' },
                 { title: 'Search', value: 'Search' },
                 { title: 'Star', value: 'Star' },
                 { title: 'Building', value: 'Building' },
                 { title: 'Navigation', value: 'Navigation' },
                 { title: 'Smartphone', value: 'Smartphone' },
                 { title: 'Target', value: 'Target' },
                 { title: 'Trending Up', value: 'TrendingUp' },
                 { title: 'Users', value: 'Users' },
                 { title: 'Award', value: 'Award' },
                 { title: 'Check Circle', value: 'CheckCircle' },
                 { title: 'Globe', value: 'Globe' },
                 { title: 'Zap', value: 'Zap' },
                 { title: 'Rocket', value: 'Rocket' }
               ]
             }
           }),
           defineField({
             name: 'title',
             title: 'Service Title',
             type: 'string'
           }),
           defineField({
             name: 'description',
             title: 'Short Description',
             type: 'text'
           }),
           defineField({
             name: 'fullDescription',
             title: 'Full Description',
             type: 'text'
           }),
           defineField({
             name: 'features',
             title: 'Service Features',
             type: 'array',
             of: [{ type: 'string' }]
           }),
           defineField({
             name: 'benefits',
             title: 'Benefits Description',
             type: 'text'
           }),
           defineField({
             name: 'result',
             title: 'Key Result Metric',
             type: 'string'
           }),
           defineField({
             name: 'image',
             title: 'Service Image',
             type: 'image',
             options: {
               hotspot: true
             }
           })
         ],
         preview: {
           select: {
             title: 'title',
             subtitle: 'result',
             icon: 'icon'
           },
           prepare(selection) {
             const {title, subtitle} = selection
             return {
               title: title || 'Untitled Service',
               subtitle: subtitle || 'No result specified',
             }
           }
         }
       }
     ]
   }),

   // NEW: Strategic Implementation Section
   defineField({
     name: 'strategicImplementation',
     title: 'Strategic Implementation',
     type: 'object',
     group: 'strategic',
     options: {
       collapsible: true,
       collapsed: true
     },
     fields: [
       defineField({
         name: 'title',
         title: 'Section Title',
         type: 'string'
       }),
       defineField({
         name: 'description',
         title: 'Main Description',
         type: 'text'
       }),
       defineField({
         name: 'secondaryDescription',
         title: 'Secondary Description',
         type: 'text'
       }),
       defineField({
         name: 'image',
         title: 'Strategic Implementation Image',
         type: 'image',
         options: {
           hotspot: true
         }
       }),
       defineField({
         name: 'features',
         title: 'Implementation Features',
         type: 'array',
         of: [{ type: 'string' }],
         validation: Rule => Rule.max(3)
       }),
       defineField({
         name: 'sections',
         title: 'Implementation Sections',
         type: 'array',
         of: [
           {
             type: 'object',
             name: 'implementationSection',
             title: 'Implementation Section',
             fields: [
               defineField({
                 name: 'icon',
                 title: 'Section Icon',
                 type: 'string',
                 options: {
                   list: [
                     { title: 'Search', value: 'Search' },
                     { title: 'Users', value: 'Users' },
                     { title: 'Trending Up', value: 'TrendingUp' },
                     { title: 'Target', value: 'Target' },
                     { title: 'Award', value: 'Award' },
                     { title: 'Building', value: 'Building' }
                   ]
                 }
               }),
               defineField({
                 name: 'title',
                 title: 'Section Title',
                 type: 'string'
               }),
               defineField({
                 name: 'description',
                 title: 'Section Description',
                 type: 'text'
               }),
               defineField({
                 name: 'color',
                 title: 'Background Color',
                 type: 'string',
                 options: {
                   list: [
                     { title: 'Blue 50-100', value: 'from-blue-50 to-blue-100' },
                     { title: 'Blue 100-200', value: 'from-blue-100 to-blue-200' },
                     { title: 'Blue 200-300', value: 'from-blue-200 to-blue-300' }
                   ]
                 }
               })
             ]
           }
         ],
         validation: Rule => Rule.max(3)
       })
     ]
   }),

   // NEW: Market Intelligence Section
   defineField({
     name: 'marketIntelligence',
     title: 'Market Intelligence',
     type: 'object',
     group: 'market',
     options: {
       collapsible: true,
       collapsed: true
     },
     fields: [
       defineField({
         name: 'title',
         title: 'Section Title',
         type: 'string'
       }),
       defineField({
         name: 'description',
         title: 'Main Description',
         type: 'text'
       }),
       defineField({
         name: 'secondaryDescription',
         title: 'Secondary Description',
         type: 'text'
       }),
       defineField({
         name: 'image',
         title: 'Market Intelligence Image',
         type: 'image',
         options: {
           hotspot: true
         }
       }),
       defineField({
         name: 'stats',
         title: 'Intelligence Statistics',
         type: 'array',
         of: [
           {
             type: 'object',
             name: 'intelligenceStat',
             title: 'Intelligence Statistic',
             fields: [
               defineField({
                 name: 'value',
                 title: 'Statistic Value',
                 type: 'string'
               }),
               defineField({
                 name: 'label',
                 title: 'Statistic Label',
                 type: 'string'
               })
             ]
           }
         ],
         validation: Rule => Rule.max(4)
       }),
       defineField({
         name: 'ctaButton',
         title: 'CTA Button',
         type: 'object',
         fields: [
           defineField({
             name: 'text',
             title: 'Button Text',
             type: 'string'
           })
         ]
       }),
       defineField({
         name: 'backgroundGradient',
         title: 'Background Gradient',
         type: 'string',
         options: {
           list: [
             { title: 'Blue 50-100', value: 'from-blue-50 to-blue-100' },
             { title: 'Blue 100-200', value: 'from-blue-100 to-blue-200' },
             { title: 'Gray 50-100', value: 'from-gray-50 to-gray-100' }
           ]
         }
       })
     ]
   }),

   // Process
   defineField({
     name: 'process',
     title: 'Service Process',
     type: 'object',
     group: 'process',
     options: {
       collapsible: true,
       collapsed: true
     },
     fields: [
       defineField({
         name: 'title',
         title: 'Section Title',
         type: 'string'
       }),
       defineField({
         name: 'description',
         title: 'Section Description',
         type: 'text'
       }),
       defineField({
         name: 'steps',
         title: 'Process Steps',
         type: 'array',
         of: [
           {
             type: 'object',
             name: 'processStep',
             title: 'Process Step',
             fields: [
               defineField({
                 name: 'step',
                 title: 'Step Number',
                 type: 'number'
               }),
               defineField({
                 name: 'title',
                 title: 'Step Title',
                 type: 'string'
               }),
               defineField({
                 name: 'description',
                 title: 'Step Description',
                 type: 'text'
               }),
               defineField({
                 name: 'icon',
                 title: 'Step Icon',
                 type: 'string',
                 options: {
                   list: [
                     { title: 'Target', value: 'Target' },
                     { title: 'Search', value: 'Search' },
                     { title: 'Award', value: 'Award' },
                     { title: 'Trending Up', value: 'TrendingUp' },
                     { title: 'Users', value: 'Users' },
                     { title: 'Building', value: 'Building' },
                     { title: 'Star', value: 'Star' },
                     { title: 'Check Circle', value: 'CheckCircle' },
                     { title: 'Zap', value: 'Zap' },
                     { title: 'Rocket', value: 'Rocket' }
                   ]
                 }
               }),
               defineField({
                 name: 'details',
                 title: 'Step Details',
                 type: 'array',
                 of: [{ type: 'string' }]
               })
             ],
             preview: {
               select: {
                 title: 'title',
                 step: 'step'
               },
               prepare(selection) {
                 const {title, step} = selection
                 return {
                   title: `Step ${step}: ${title || 'Untitled'}`,
                   subtitle: `Process step ${step}`
                 }
               }
             }
           }
         ]
       })
     ]
   }),
   // Case Study
   defineField({
     name: 'caseStudy',
     title: 'Case Study',
     type: 'object',
     group: 'process',
     options: {
       collapsible: true,
       collapsed: true
     },
     fields: [
       defineField({
         name: 'title',
         title: 'Case Study Title',
         type: 'string'
       }),
       defineField({
         name: 'description',
         title: 'Case Study Description',
         type: 'text'
       }),
       defineField({
         name: 'image',
         title: 'Case Study Image',
         type: 'image',
         options: {
           hotspot: true
         }
       }),
       defineField({
         name: 'results',
         title: 'Case Study Results',
         type: 'array',
         of: [
           {
             type: 'object',
             name: 'caseStudyResult',
             title: 'Result',
             fields: [
               defineField({
                 name: 'value',
                 title: 'Result Value',
                 type: 'string'
               }),
               defineField({
                 name: 'label',
                 title: 'Result Label',
                 type: 'string'
               })
             ],
             preview: {
               select: {
                 value: 'value',
                 label: 'label'
               },
               prepare(selection) {
                 const {value, label} = selection
                 return {
                   title: `${value} - ${label || 'No label'}`,
                   subtitle: 'Case study result'
                 }
               }
             }
           }
         ],
         validation: Rule => Rule.max(4)
       }),
       defineField({
         name: 'ctaButton',
         title: 'CTA Button',
         type: 'object',
         fields: [
           defineField({
             name: 'text',
             title: 'Button Text',
             type: 'string'
           })
         ]
       }),
       defineField({
         name: 'backgroundGradient',
         title: 'Background Gradient',
         type: 'string',
         options: {
           list: [
             { title: 'Blue', value: 'from-blue-600 to-blue-800' },
             { title: 'Green', value: 'from-green-600 to-green-800' },
             { title: 'Purple', value: 'from-purple-600 to-purple-800' }
           ]
         }
       })
     ]
   }),
   // CTA
   defineField({
     name: 'cta',
     title: 'Call to Action',
     type: 'object',
     group: 'cta',
     options: {
       collapsible: true,
       collapsed: true
     },
     fields: [
       defineField({
         name: 'title',
         title: 'CTA Title',
         type: 'string'
       }),
       defineField({
         name: 'description',
         title: 'CTA Description',
         type: 'text'
       }),
       defineField({
         name: 'benefits',
         title: 'CTA Benefits',
         type: 'array',
         of: [{ type: 'string' }]
       }),

       defineField({
         name: 'formSettings',
         title: 'Form Settings',
         type: 'object',
         fields: [
           defineField({
             name: 'ctaText',
             title: 'Form CTA Text',
             type: 'string'
           }),
           defineField({
             name: 'messagePlaceholder',
             title: 'Message Placeholder',
             type: 'string'
           })
         ]
       })
     ]
   }),
   // Testimonials
   defineField({
     name: 'testimonials',
     title: 'Testimonials',
     type: 'array',
     group: 'social',
     options: {
       modal: { type: 'dialog' }
     },
     of: [
       {
         type: 'object',
         name: 'testimonial',
         title: 'Testimonial',
         fields: [
           defineField({
             name: 'name',
             title: 'Customer Name',
             type: 'string'
           }),
           defineField({
             name: 'business',
             title: 'Business Name',
             type: 'string'
           }),
           defineField({
             name: 'location',
             title: 'Location',
             type: 'string'
           }),
           defineField({
             name: 'image',
             title: 'Customer Photo',
             type: 'image',
             options: {
               hotspot: true
             }
           }),
           defineField({
             name: 'quote',
             title: 'Testimonial Quote',
             type: 'text'
           }),
           defineField({
             name: 'result',
             title: 'Key Result',
             type: 'string'
           }),
           defineField({
             name: 'rating',
             title: 'Star Rating',
             type: 'number',
             validation: Rule => Rule.min(1).max(5),
             initialValue: 5
           })
         ],
         preview: {
           select: {
             title: 'name',
             subtitle: 'business',
             media: 'image'
           },
           prepare(selection) {
             const {title, subtitle, media} = selection
             return {
               title: title || 'Anonymous',
               subtitle: subtitle || 'No business specified',
               media: media
             }
           }
         }
       }
     ]
   }),
   // FAQs
   defineField({
     name: 'faqs',
     title: 'Frequently Asked Questions',
     type: 'array',
     group: 'social',
     options: {
       modal: { type: 'dialog' }
     },
     of: [
       {
         type: 'object',
         name: 'faq',
         title: 'FAQ',
         fields: [
           defineField({
             name: 'question',
             title: 'Question',
             type: 'string'
           }),
           defineField({
             name: 'answer',
             title: 'Answer',
             type: 'text'
           })
         ],
         preview: {
           select: {
             title: 'question'
           },
           prepare(selection) {
             const {title} = selection
             return {
               title: title || 'No question entered',
               subtitle: 'FAQ'
             }
           }
         }
       }
     ]
   }),
   // Footer CTA
   defineField({
     name: 'footerCta',
     title: 'Footer CTA',
     type: 'object',
     group: 'cta',
     options: {
       collapsible: true,
       collapsed: true
     },
     fields: [
       defineField({
         name: 'title',
         title: 'CTA Title',
         type: 'string'
       }),
       defineField({
         name: 'description',
         title: 'CTA Description',
         type: 'text'
       }),
       defineField({
         name: 'primaryButton',
         title: 'Primary Button',
         type: 'object',
         fields: [
           defineField({
             name: 'text',
             title: 'Button Text',
             type: 'string'
           }),
           defineField({
             name: 'icon',
             title: 'Button Icon',
             type: 'string',
             options: {
               list: [
                 { title: 'Calendar', value: 'Calendar' },
                 { title: 'Phone', value: 'Phone' },
                 { title: 'Mail', value: 'Mail' },
                 { title: 'User', value: 'User' },
                 { title: 'Check Circle', value: 'CheckCircle' }
               ]
             }
           })
         ]
       }),
       defineField({
         name: 'secondaryButton',
         title: 'Secondary Button',
         type: 'object',
         fields: [
           defineField({
             name: 'text',
             title: 'Button Text',
             type: 'string'
           }),
           defineField({
             name: 'icon',
             title: 'Button Icon',
             type: 'string',
             options: {
               list: [
                 { title: 'Phone', value: 'Phone' },
                 { title: 'Mail', value: 'Mail' },
                 { title: 'Calendar', value: 'Calendar' },
                 { title: 'User', value: 'User' }
               ]
             }
           })
         ]
       }),

       defineField({
         name: 'backgroundGradient',
         title: 'Background Gradient',
         type: 'string',
         options: {
           list: [
             { title: 'Blue', value: 'from-blue-700 to-blue-900' },
             { title: 'Green', value: 'from-green-700 to-green-900' },
             { title: 'Purple', value: 'from-purple-700 to-purple-900' }
           ]
         }
       })
     ]
   }),
   // SEO
   defineField({
     name: 'seo',
     title: 'SEO Settings',
     type: 'object',
     group: 'seo',
     options: {
       collapsible: true,
       collapsed: true
     },
     fields: [
       defineField({
         name: 'metaTitle',
         title: 'Meta Title',
         type: 'string',
         validation: Rule => Rule.max(60)
       }),
       defineField({
         name: 'metaDescription',
         title: 'Meta Description',
         type: 'text',
         validation: Rule => Rule.max(160)
       }),
       defineField({
         name: 'keywords',
         title: 'Focus Keywords',
         type: 'array',
         of: [{ type: 'string' }]
       }),
       defineField({
         name: 'ogImage',
         title: 'Social Share Image',
         type: 'image',
         options: {
           hotspot: true
         }
       })
     ]
   })
 ],
 preview: {
   select: {
     title: 'title',
     parentService: 'parentService',
     media: 'seo.ogImage'
   },
   prepare(selection) {
     const {title, parentService, media} = selection

     // Get parent service display name with emoji
     const parentServiceNames: { [key: string]: string } = {
       'seo': '🔍 SEO',
       'web-development': '💻 Web Development',
       'digital-marketing': '📈 Digital Marketing'
     }

     const parentName = parentServiceNames[parentService] || '❓ Unknown Category'

     return {
       title: title || 'Untitled Sub Service',
       subtitle: parentName,
       media: media
     }
   }
 }
})