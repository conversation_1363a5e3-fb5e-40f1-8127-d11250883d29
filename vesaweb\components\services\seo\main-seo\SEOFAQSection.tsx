import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';

const SEOFAQSection: React.FC = () => {
  const [openFaq, setOpenFaq] = useState<number | null>(null);

  const faqs = [
    {
      question: 'What exactly does an SEO company do for my business?',
      answer: 'An SEO company like Vesa Solutions optimizes your website to improve search engine rankings through comprehensive keyword research, content optimization, technical improvements, and link building strategies. We analyze your business, competitors, and target audience to create a custom SEO strategy that drives qualified traffic and increases conversions.'
    },
    {
      question: 'How long does it take to see results from SEO services?',
      answer: 'SEO results typically begin showing within 3-6 months, with significant improvements often seen within 6-12 months. SEO is a long-term investment that builds momentum over time. Some technical improvements can show faster results, while competitive keyword rankings may take longer to achieve.'
    },
    {
      question: 'How do I know if SEO is working for my business?',
      answer: 'Key indicators include increased organic traffic, improved keyword rankings, higher conversion rates, more qualified leads, and ultimately increased revenue from organic search. We provide detailed monthly reports with comprehensive analytics showing traffic growth, ranking improvements, and ROI metrics.'
    },
    {
      question: 'Can I do SEO myself, or should I hire an agency like VESA Solutions?',
      answer: 'While basic SEO can be learned, professional SEO requires extensive expertise, specialized tools, and significant time investment. An experienced agency like VESA Solutions brings proven strategies, dedicated resources, industry knowledge, and the ability to stay current with constantly changing search algorithms.'
    },
    {
      question: 'What makes VESA Solutions different from other SEO agencies?',
      answer: 'VESA Solutions stands out through our data-driven approach, transparent reporting, custom strategies for each client, and proven track record of delivering measurable results. We focus on long-term sustainable growth rather than quick fixes, and we provide comprehensive digital marketing solutions beyond just SEO.'
    },
    {
      question: 'Do I need ongoing SEO, or can I stop after achieving initial results?',
      answer: 'SEO requires ongoing maintenance and optimization. Search algorithms constantly evolve, competitors continuously improve their strategies, and new content needs to be optimized. Ongoing SEO ensures you maintain and improve your rankings while adapting to industry changes and expanding into new keyword opportunities.'
    },
    {
      question: 'How much does professional SEO cost?',
      answer: 'SEO costs vary based on your industry competitiveness, target keywords, website size, and business goals. VESA Solutions offers flexible pricing options including monthly retainers and project-based pricing. We provide custom quotes after analyzing your specific needs and competitive landscape.'
    },
    {
      question: 'Will SEO work for my specific industry?',
      answer: 'Yes! VESA Solutions has extensive experience across numerous industries including e-commerce, healthcare, legal services, real estate, professional services, and technology. We adapt our proven SEO strategies to your specific industry regulations, target audience, and competitive landscape.'
    },
    {
      question: 'What is the difference between SEO and paid advertising?',
      answer: 'SEO focuses on organic search results and provides long-term, sustainable traffic growth without ongoing ad spend. Paid advertising provides immediate visibility but requires continuous investment. SEO typically offers better ROI over time and builds lasting brand authority and trust.'
    },
    {
      question: 'How do you measure SEO success and ROI?',
      answer: 'We track comprehensive metrics including organic traffic growth, keyword rankings, conversion rates, lead quality, revenue attribution, and overall ROI. Our detailed reporting shows how SEO efforts directly impact your business goals and bottom line, with clear before-and-after comparisons.'
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-4xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
            Frequently Asked Questions About SEO
          </h2>
          <p className="text-xl text-gray-600">
            Get answers to common questions about SEO services, strategies, and what to expect when working with VESA Solutions.
          </p>
        </div>

        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <div key={index} className="bg-white rounded-2xl overflow-hidden shadow-lg">
              <button 
                onClick={() => setOpenFaq(openFaq === index ? null : index)}
                className="w-full p-8 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
              >
                <h3 className="text-xl font-semibold text-gray-800 pr-8">
                  {faq.question}
                </h3>
                <ChevronDown 
                  size={24} 
                  className={`text-blue-600 transform transition-transform ${openFaq === index ? 'rotate-180' : ''}`}
                />
              </button>
              {openFaq === index && (
                <div className="px-8 pb-8">
                  <p className="text-gray-600 leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SEOFAQSection;