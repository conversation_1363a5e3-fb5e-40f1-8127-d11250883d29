'use client';

import React, { useState, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

interface HeaderProps {
  isVisible?: boolean;
}

const Header: React.FC<HeaderProps> = ({ isVisible = true }) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);
  const [seoDropdownOpen, setSeoDropdownOpen] = useState<boolean>(false);
  const [webDevDropdownOpen, setWebDevDropdownOpen] = useState<boolean>(false);
  const [digitalMarketingDropdownOpen, setDigitalMarketingDropdownOpen] = useState<boolean>(false);
  const [aboutDropdownOpen, setAboutDropdownOpen] = useState<boolean>(false);

  const [mounted, setMounted] = useState<boolean>(false);

  // Ensure component is hydrated before rendering state-dependent UI
  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle body scroll when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [mobileMenuOpen]);

  // Close mobile menu on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [mobileMenuOpen]);

  // SEO Sub-services
  const seoSubServices = [
    { name: 'On-Page SEO Optimization', href: '/on-page-seo', description: 'Optimize content and structure' },
    { name: 'Off-Page SEO & Link Building', href: '/off-page-seo', description: 'Build authority and backlinks' },
    { name: 'Technical SEO Services', href: '/technical-seo', description: 'Optimize technical performance' },
    { name: 'Local SEO Marketing', href: '/local-seo', description: 'Dominate local search results' },
    { name: 'Content Writing', href: '/content-writing', description: 'SEO-optimized content creation' },
    { name: 'SEO Analytics', href: '/seo-analytics', description: 'Comprehensive SEO analysis' }
  ];

  // Web Development Sub-services
  const webDevSubServices = [
    { name: 'Custom Website Development', href: '/custom-website-development', description: 'Unique, tailored websites' },
    { name: 'E-commerce Development', href: '/ecommerce-development', description: 'Online store solutions' },
    { name: 'Mobile App Development', href: '/mobile-app-development', description: 'iOS & Android apps' },
    { name: 'Website Speed Optimization', href: '/website-speed-optimization', description: 'Lightning-fast websites' },
    { name: 'Web Application Development', href: '/web-application-development', description: 'Custom web applications' },
    { name: 'Website Maintenance & Support', href: '/website-maintenance-support', description: '24/7 website care' }
  ];

  // Digital Marketing Sub-services
  const digitalMarketingSubServices = [
    { name: 'PPC Advertising', href: '/ppc', description: 'Targeted pay-per-click campaigns' },
    { name: 'Email Marketing', href: '/email-marketing', description: 'Automated campaigns that convert' },
    { name: 'Social Media Marketing', href: '/social-media', description: 'Engage your audience effectively' },
    { name: 'Branding Services', href: '/branding-services', description: 'Build powerful brand identity' },
    { name: 'Conversion Optimization', href: '/conversion-optimization', description: 'Turn visitors into customers' },
    { name: 'Reputation Management', href: '/reputation-management', description: 'Protect and enhance your brand' }
  ];

  // About Sub-services
  const aboutSubServices = [
    { name: 'Case Studies', href: '/case-studies', description: 'View our successful projects' },
    { name: 'Blog', href: '/blog', description: 'Latest insights and industry news' }
  ];

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
    // Reset all dropdown states when closing mobile menu
    setSeoDropdownOpen(false);
    setWebDevDropdownOpen(false);
    setDigitalMarketingDropdownOpen(false);
    setAboutDropdownOpen(false);
  };

  return (
    <>
      <header className={`w-full bg-white/95 backdrop-blur-md border-b border-gray-100 z-50 transition-all duration-500 ease-in-out ${
        isVisible 
          ? 'sticky top-0 translate-y-0 opacity-100' 
          : 'fixed top-0 -translate-y-full opacity-0 pointer-events-none'
      }`}>
        <div className="max-w-[1440px] mx-auto px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex justify-between items-center">
            {/* Logo */}
            <div className="flex items-center group cursor-pointer">
              <div className="relative">
                <Link href="/">
                  <Image 
                    src="/VesaLogo.svg" 
                    alt="VESA Solutions Logo" 
                    width={94}
                    height={40}
                    priority
                    className="transition-transform duration-300 group-hover:scale-105 w-20 h-auto sm:w-[94px]"
                  />
                </Link>
              </div>
            </div>
            
            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-12">
              {/* SEO Dropdown */}
              <div
                className="relative group"
                onMouseEnter={() => setSeoDropdownOpen(true)}
                onMouseLeave={() => setSeoDropdownOpen(false)}
              >
                <Link href="/seo-search-engine-optimization" className="flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-green-600 transition-all duration-300 group">
                  SEO
                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-green-500 to-green-600 transition-all duration-300 group-hover:w-full"></span>
                </Link>

                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${
                  seoDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'
                }`}>
                  <div className="w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden">
                    <div className="p-3">
                      <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3">SEO Services</div>
                      {seoSubServices.map((service, index) => (
                        <Link
                          key={index}
                          href={service.href}
                          className="group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100/50 transition-all duration-200 ease-out border border-transparent hover:border-green-200/50 hover:shadow-sm"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-200 text-sm">
                                {service.name}
                              </div>
                              <div className="text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200">
                                {service.description}
                              </div>
                            </div>
                            <div className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                              <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Web Development Dropdown */}
              <div
                className="relative group"
                onMouseEnter={() => setWebDevDropdownOpen(true)}
                onMouseLeave={() => setWebDevDropdownOpen(false)}
              >
                <Link href="/web-development" className="flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group">
                  WEB DEVELOPMENT
                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full"></span>
                </Link>

                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${
                  webDevDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'
                }`}>
                  <div className="w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden">
                    <div className="p-3">
                      <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3">Web Development</div>
                      {webDevSubServices.map((service, index) => (
                        <Link
                          key={index}
                          href={service.href}
                          className="group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm">
                                {service.name}
                              </div>
                              <div className="text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200">
                                {service.description}
                              </div>
                            </div>
                            <div className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Digital Marketing Dropdown */}
              <div
                className="relative group"
                onMouseEnter={() => setDigitalMarketingDropdownOpen(true)}
                onMouseLeave={() => setDigitalMarketingDropdownOpen(false)}
              >
                <Link href="/digital-marketing" className="flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-purple-600 transition-all duration-300 group">
                  DIGITAL MARKETING
                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-purple-600 transition-all duration-300 group-hover:w-full"></span>
                </Link>

                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${
                  digitalMarketingDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'
                }`}>
                  <div className="w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden">
                    <div className="p-3">
                      <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3">Digital Marketing</div>
                      {digitalMarketingSubServices.map((service, index) => (
                        <Link
                          key={index}
                          href={service.href}
                          className="group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50/50 transition-all duration-200 ease-out border border-transparent hover:border-indigo-200/50 hover:shadow-sm"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="font-semibold text-gray-900 group-hover:text-purple-600 transition-colors duration-200 text-sm">
                                {service.name}
                              </div>
                              <div className="text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200">
                                {service.description}
                              </div>
                            </div>
                            <div className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Services */}
              <Link href="/services" className="relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300">
                SERVICES
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full"></span>
              </Link>

              {/* About Dropdown */}
              <div
                className="relative group"
                onMouseEnter={() => setAboutDropdownOpen(true)}
                onMouseLeave={() => setAboutDropdownOpen(false)}
              >
                <Link href="/about" className="flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group">
                  ABOUT
                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full"></span>
                </Link>

                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${
                  aboutDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'
                }`}>
                  <div className="w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden">
                    <div className="p-3">
                      <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3">About</div>
                      {aboutSubServices.map((service, index) => (
                        <Link
                          key={index}
                          href={service.href}
                          className="group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm">
                                {service.name}
                              </div>
                              <div className="text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200">
                                {service.description}
                              </div>
                            </div>
                            <div className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact */}
              <Link href="/contact-us" className="relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300">
                CONTACT
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full"></span>
              </Link>
            </nav>


            {/* CTA Button and Mobile Menu */}
            <div className="flex items-center space-x-3 sm:space-x-4">
              {/* Desktop CTA */}
              <Link
                href="/free-estimate"
                className="hidden sm:block group relative bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-sm font-semibold px-6 lg:px-8 py-2.5 lg:py-3 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-blue-500/25"
              >
                <span className="relative z-10">GET FREE PROPOSAL</span>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </Link>

              {/* Mobile Menu Button */}
              <button
                className="lg:hidden relative p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-all duration-300 group"
                onClick={toggleMobileMenu}
                aria-label="Toggle mobile menu"
              >
                <div className="w-6 h-6 relative">
                  <span className={`absolute top-1 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${
                    mobileMenuOpen ? 'rotate-45 translate-y-2' : ''
                  }`}></span>
                  <span className={`absolute top-2.5 left-0 w-6 h-0.5 bg-current transition-all duration-300 ${
                    mobileMenuOpen ? 'opacity-0' : ''
                  }`}></span>
                  <span className={`absolute top-4 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${
                    mobileMenuOpen ? '-rotate-45 -translate-y-2' : ''
                  }`}></span>
                </div>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Menu */}
      {mounted && mobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-40 bg-white overflow-y-auto">
          {/* Mobile Header */}
          <div className="flex justify-between items-center p-4 border-b border-gray-200">
            <Link href="/" onClick={closeMobileMenu}>
              <Image
                src="/VesaLogo.svg"
                alt="VESA Solutions Logo"
                width={80}
                height={34}
                priority
                className="w-20 h-auto"
              />
            </Link>
            <button
              onClick={closeMobileMenu}
              className="p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-colors"
              aria-label="Close mobile menu"
            >
              <div className="w-6 h-6 relative">
                <span className="absolute top-2.5 left-0 w-6 h-0.5 bg-current transform rotate-45"></span>
                <span className="absolute top-2.5 left-0 w-6 h-0.5 bg-current transform -rotate-45"></span>
              </div>
            </button>
          </div>

          {/* Mobile Navigation */}
          <div className="p-4 space-y-6">
            {/* SEO Section */}
            <div>
              <div
                className="flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer"
                onClick={() => setSeoDropdownOpen(!seoDropdownOpen)}
              >
                <Link href="/seo-search-engine-optimization" onClick={closeMobileMenu} className="text-lg font-semibold text-gray-900 hover:text-green-600 transition-colors">
                  SEO
                </Link>
                <ChevronDown size={20} className={`transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />
              </div>
              {seoDropdownOpen && (
                <div className="pl-4 mt-2 space-y-2">
                  {seoSubServices.map((service, index) => (
                    <Link
                      key={index}
                      href={service.href}
                      onClick={closeMobileMenu}
                      className="block py-2 text-gray-600 hover:text-green-600 transition-colors"
                    >
                      {service.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Web Development Section */}
            <div>
              <div
                className="flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer"
                onClick={() => setWebDevDropdownOpen(!webDevDropdownOpen)}
              >
                <Link href="/web-development" onClick={closeMobileMenu} className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                  WEB DEVELOPMENT
                </Link>
                <ChevronDown size={20} className={`transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />
              </div>
              {webDevDropdownOpen && (
                <div className="pl-4 mt-2 space-y-2">
                  {webDevSubServices.map((service, index) => (
                    <Link
                      key={index}
                      href={service.href}
                      onClick={closeMobileMenu}
                      className="block py-2 text-gray-600 hover:text-blue-600 transition-colors"
                    >
                      {service.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Digital Marketing Section */}
            <div>
              <div
                className="flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer"
                onClick={() => setDigitalMarketingDropdownOpen(!digitalMarketingDropdownOpen)}
              >
                <Link href="/digital-marketing" onClick={closeMobileMenu} className="text-lg font-semibold text-gray-900 hover:text-purple-600 transition-colors">
                  DIGITAL MARKETING
                </Link>
                <ChevronDown size={20} className={`transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />
              </div>
              {digitalMarketingDropdownOpen && (
                <div className="pl-4 mt-2 space-y-2">
                  {digitalMarketingSubServices.map((service, index) => (
                    <Link
                      key={index}
                      href={service.href}
                      onClick={closeMobileMenu}
                      className="block py-2 text-gray-600 hover:text-purple-600 transition-colors"
                    >
                      {service.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Services */}
            <div>
              <Link
                href="/services"
                onClick={closeMobileMenu}
                className="block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors"
              >
                SERVICES
              </Link>
            </div>

            {/* About Section */}
            <div>
              <div
                className="flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer"
                onClick={() => setAboutDropdownOpen(!aboutDropdownOpen)}
              >
                <Link href="/about" onClick={closeMobileMenu} className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                  ABOUT
                </Link>
                <ChevronDown size={20} className={`transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />
              </div>
              {aboutDropdownOpen && (
                <div className="pl-4 mt-2 space-y-2">
                  {aboutSubServices.map((service, index) => (
                    <Link
                      key={index}
                      href={service.href}
                      onClick={closeMobileMenu}
                      className="block py-2 text-gray-600 hover:text-blue-600 transition-colors"
                    >
                      {service.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Contact */}
            <div>
              <Link
                href="/contact-us"
                onClick={closeMobileMenu}
                className="block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors"
              >
                CONTACT
              </Link>
            </div>

            {/* Mobile CTA Button */}
            <div className="pt-4">
              <Link
                href="/free-estimate"
                onClick={closeMobileMenu}
                className="block w-full text-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-6 py-4 rounded-full transition-all duration-300 transform hover:scale-105"
              >
                GET FREE PROPOSAL
              </Link>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Header;
