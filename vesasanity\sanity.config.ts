import {defineConfig} from 'sanity'
import {structureTool} from 'sanity/structure'
import {visionTool} from '@sanity/vision'
import {schemaTypes} from './schemaTypes'
import VesaIcon from './components/VesaIcon'

// Custom structure for organizing subservices by category
const structure = (S: any) =>
  S.list()
    .title('Content')
    .items([
      // Subservices organized by category
      S.listItem()
        .title('Subservices')
        .child(
          S.list()
            .title('Subservices by Category')
            .items([
              // SEO Services
              S.listItem()
                .title('🔍 SEO Services')
                .child(
                  S.documentList()
                    .title('SEO Services')
                    .filter('_type == "subService" && parentService == "seo"')
                    .defaultOrdering([{field: 'title', direction: 'asc'}])
                ),

              // Web Development Services
              S.listItem()
                .title('💻 Web Development Services')
                .child(
                  S.documentList()
                    .title('Web Development Services')
                    .filter('_type == "subService" && parentService == "web-development"')
                    .defaultOrdering([{field: 'title', direction: 'asc'}])
                ),

              // Digital Marketing Services
              S.listItem()
                .title('📈 Digital Marketing Services')
                .child(
                  S.documentList()
                    .title('Digital Marketing Services')
                    .filter('_type == "subService" && parentService == "digital-marketing"')
                    .defaultOrdering([{field: 'title', direction: 'asc'}])
                ),

              // All Subservices (fallback)
              S.divider(),
              S.listItem()
                .title('All Subservices')
                .child(
                  S.documentList()
                    .title('All Subservices')
                    .filter('_type == "subService"')
                    .defaultOrdering([{field: 'parentService', direction: 'asc'}, {field: 'title', direction: 'asc'}])
                ),
            ])
        ),

      // Case Studies
      S.listItem()
        .title('Case Studies')
        .child(
          S.documentList()
            .title('Case Studies')
            .filter('_type == "caseStudy"')
            .defaultOrdering([{field: 'publishedAt', direction: 'desc'}])
        ),

      // Divider
      S.divider(),

      // Blog System
      S.listItem()
        .title('📝 Blog')
        .child(
          S.list()
            .title('Blog Management')
            .items([
              // Blog Posts
              S.listItem()
                .title('📄 Blog Posts')
                .child(
                  S.documentList()
                    .title('Blog Posts')
                    .filter('_type == "blogPost"')
                    .defaultOrdering([{field: 'publishedAt', direction: 'desc'}])
                ),

              // Blog Categories
              S.listItem()
                .title('📁 Categories')
                .child(
                  S.documentList()
                    .title('Blog Categories')
                    .filter('_type == "blogCategory"')
                    .defaultOrdering([{field: 'title', direction: 'asc'}])
                ),

              // Blog Tags
              S.listItem()
                .title('🏷️ Tags')
                .child(
                  S.documentList()
                    .title('Blog Tags')
                    .filter('_type == "blogTag"')
                    .defaultOrdering([{field: 'title', direction: 'asc'}])
                ),


            ])
        ),

      // Divider
      S.divider(),

      // All other document types (automatically generated)
      ...S.documentTypeListItems().filter(
        (listItem: any) => !['subService', 'caseStudy', 'blogPost', 'blogCategory', 'blogTag'].includes(listItem.getId())
      ),
    ])

export default defineConfig({
  name: 'default',
  title: 'VesaWeb',
  icon: VesaIcon,

  projectId: 'zleti5e4',
  dataset: 'production',

  plugins: [
    structureTool({
      structure
    }),
    visionTool()
  ],

  studio: {
    components: {
      logo: VesaIcon
    }
  },

  schema: {
    types: schemaTypes,
  },
})
