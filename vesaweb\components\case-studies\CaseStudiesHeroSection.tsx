import React from 'react';
import Image from 'next/image';
import { ArrowR<PERSON>, TrendingUp, Users, Award } from 'lucide-react';

const CaseStudiesHeroSection: React.FC = () => {
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image
          src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1920&h=1080&fit=crop"
          alt="Success Analytics Dashboard"
          fill
          className="object-cover opacity-20"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-blue-800/80"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 py-20 flex items-center min-h-screen">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center w-full">
          <div>
            <div className="bg-green-600 text-white text-sm font-bold px-4 py-2 rounded-full inline-block mb-6">
              Real Results, Real Success Stories
            </div>
            
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight mb-8">
              Client Success Stories That
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-blue-400"> Speak Volumes</span>
            </h1>
            
            <p className="text-xl text-blue-100 mb-12 leading-relaxed">
              Discover how VESA Solutions has helped businesses across industries achieve remarkable growth through our proven digital marketing strategies. These aren&apos;t just numbers—they&apos;re real transformations.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-300 mb-2">500+</div>
                <div className="text-blue-100">Successful Projects</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-300 mb-2">300%</div>
                <div className="text-blue-100">Average ROI</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-300 mb-2">98%</div>
                <div className="text-blue-100">Client Satisfaction</div>
              </div>
            </div>

            <button className="group bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold px-10 py-5 rounded-full transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-blue-500/25">
              <span className="flex items-center justify-center text-lg">
                <Award className="mr-3" size={24} />
                Explore Success Stories
                <ArrowRight size={24} className="ml-3 transition-transform duration-300 group-hover:translate-x-1" />
              </span>
            </button>
          </div>

          {/* Success Metrics Cards */}
          <div className="space-y-6">
            <div className="bg-white/95 backdrop-blur-sm rounded-3xl p-8 shadow-2xl">
              <div className="flex items-center mb-4">
                <div className="bg-green-100 p-3 rounded-full mr-4">
                  <TrendingUp className="text-green-600" size={32} />
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-800">E-commerce Client</div>
                  <div className="text-gray-600">Fashion Retailer</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-green-50 rounded-xl">
                  <div className="text-2xl font-bold text-green-600">450%</div>
                  <div className="text-sm text-gray-600">Revenue Growth</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-xl">
                  <div className="text-2xl font-bold text-blue-600">280%</div>
                  <div className="text-sm text-gray-600">Traffic Increase</div>
                </div>
              </div>
            </div>

            <div className="bg-white/95 backdrop-blur-sm rounded-3xl p-8 shadow-2xl">
              <div className="flex items-center mb-4">
                <div className="bg-blue-100 p-3 rounded-full mr-4">
                  <Users className="text-blue-600" size={32} />
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-800">SaaS Startup</div>
                  <div className="text-gray-600">Technology Company</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-purple-50 rounded-xl">
                  <div className="text-2xl font-bold text-purple-600">340%</div>
                  <div className="text-sm text-gray-600">Lead Generation</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-xl">
                  <div className="text-2xl font-bold text-orange-600">85%</div>
                  <div className="text-sm text-gray-600">Cost Reduction</div>
                </div>
              </div>
            </div>

            <div className="bg-white/95 backdrop-blur-sm rounded-3xl p-8 shadow-2xl">
              <div className="flex items-center mb-4">
                <div className="bg-purple-100 p-3 rounded-full mr-4">
                  <Award className="text-purple-600" size={32} />
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-800">Local Business</div>
                  <div className="text-gray-600">Restaurant Chain</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-green-50 rounded-xl">
                  <div className="text-2xl font-bold text-green-600">250%</div>
                  <div className="text-sm text-gray-600">Online Orders</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-xl">
                  <div className="text-2xl font-bold text-blue-600">180%</div>
                  <div className="text-sm text-gray-600">Local Visibility</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CaseStudiesHeroSection;
