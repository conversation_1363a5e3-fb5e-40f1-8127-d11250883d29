import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Header from '@/components/global/Header';
import Footer from '@/components/global/Footer';
import { ArrowRight, Search, Code, TrendingUp, Target, Zap, Shield, Users, BarChart3 } from 'lucide-react';

const ServicesPage: React.FC = () => {
  const mainServices = [
    {
      title: 'SEO Search Engine Optimization',
      description: 'Dominate Google with our data-driven SEO strategies. Increase organic traffic, improve rankings, and drive more qualified leads to your business.',
      href: '/seo-search-engine-optimization',
      icon: Search,
      color: 'from-green-500 to-green-600',
      subServices: [
        { name: 'On-Page SEO Optimization', href: '/on-page-seo' },
        { name: 'Off-Page SEO & Link Building', href: '/off-page-seo' },
        { name: 'Technical SEO Services', href: '/technical-seo' },
        { name: 'Local SEO Marketing', href: '/local-seo' },
        { name: 'Content Writing', href: '/content-writing' },
        { name: 'SEO Analytics', href: '/seo-analytics' }
      ]
    },
    {
      title: 'Web Development',
      description: 'Custom web development solutions that convert visitors into customers. Modern, fast, and mobile-responsive websites that drive business growth.',
      href: '/web-development',
      icon: Code,
      color: 'from-blue-500 to-blue-600',
      subServices: [
        { name: 'Custom Website Development', href: '/custom-website-development' },
        { name: 'E-commerce Development', href: '/ecommerce-development' },
        { name: 'Mobile App Development', href: '/mobile-app-development' },
        { name: 'Website Speed Optimization', href: '/website-speed-optimization' },
        { name: 'Web Application Development', href: '/web-application-development' },
        { name: 'Website Maintenance & Support', href: '/website-maintenance-support' }
      ]
    },
    {
      title: 'Digital Marketing',
      description: 'Comprehensive digital marketing strategies that increase leads, boost sales, and grow your business across all digital channels.',
      href: '/digital-marketing',
      icon: TrendingUp,
      color: 'from-purple-500 to-purple-600',
      subServices: [
        { name: 'PPC Advertising', href: '/ppc' },
        { name: 'Email Marketing', href: '/email-marketing' },
        { name: 'Social Media Marketing', href: '/social-media' },
        { name: 'Branding Services', href: '/branding-services' },
        { name: 'Conversion Optimization', href: '/conversion-optimization' },
        { name: 'Reputation Management', href: '/reputation-management' }
      ]
    }
  ];

  return (
    <>
      <Head>
        <title>Digital Marketing & Web Development Services | VESA Solutions</title>
        <meta name="description" content="Comprehensive digital marketing and web development services. SEO, web development, PPC, social media marketing, and more. Drive growth with proven strategies." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      
      <Header />
      
      {/* Breadcrumbs */}
      <div className="bg-gray-50 py-4">
        <div className="max-w-7xl mx-auto px-6">
          <nav className="text-sm" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              <li>
                <Link href="/" className="text-gray-500 hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-gray-900 font-medium" aria-current="page">
                Services
              </li>
            </ol>
          </nav>
        </div>
      </div>
      
      <main className="min-h-screen bg-white">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-16">
          <div className="max-w-7xl mx-auto px-6 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Our Services
            </h1>
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Comprehensive digital marketing and web development solutions to grow your business online.
            </p>
          </div>
        </section>

        {/* Main Services Section */}
        <section className="py-20 lg:py-32 bg-gray-50">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-4 py-2 bg-blue-100 rounded-full text-blue-700 text-sm font-medium mb-6">
                <Target className="h-4 w-4 mr-2" />
                Our Core Services
              </div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Comprehensive Digital Solutions
              </h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                From strategy to execution, we provide end-to-end digital solutions designed to drive growth,
                increase visibility, and maximize your online success across all channels.
              </p>
            </div>

            {/* Improved Horizontal Stacked Cards */}
            <div className="max-w-5xl mx-auto space-y-10">
              {mainServices.map((service, index) => {
                const IconComponent = service.icon;
                return (
                  <div key={index} className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300">
                    <div className="flex flex-col md:flex-row">
                      {/* Left Side - Service Overview */}
                      <div className="md:w-2/5 p-8 flex flex-col justify-center min-w-0">
                        <div className="flex items-center mb-6">
                          <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${service.color} flex items-center justify-center mr-4 flex-shrink-0`}>
                            <IconComponent className="h-8 w-8 text-white" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <h3 className="text-xl font-bold text-gray-900 leading-tight">
                              {service.title}
                            </h3>
                            <p className="text-sm text-gray-500 mt-1">
                              {service.subServices.length} Services Available
                            </p>
                          </div>
                        </div>

                        <p className="text-gray-600 leading-relaxed mb-6">
                          {service.description}
                        </p>

                        <Link
                          href={service.href}
                          className={`inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r ${service.color} text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300 w-fit`}
                        >
                          Learn More
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </div>

                      {/* Right Side - Services Grid */}
                      <div className="md:w-3/5 bg-gray-50 p-8 min-w-0">
                        <h4 className="text-lg font-semibold text-gray-900 mb-6">Our Services Include:</h4>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          {service.subServices.map((subService, subIndex) => (
                            <Link
                              key={subIndex}
                              href={subService.href}
                              className="group bg-white p-4 rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-sm transition-all duration-200 min-w-0"
                            >
                              <div className="flex items-center min-w-0">
                                <div className="w-3 h-3 bg-blue-500 rounded-full mr-3 group-hover:scale-125 transition-transform duration-200 flex-shrink-0"></div>
                                <span className="text-gray-700 font-medium group-hover:text-blue-600 transition-colors duration-200 text-sm truncate">
                                  {subService.name}
                                </span>
                              </div>
                            </Link>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 lg:py-32 bg-white">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Why Choose VESA Solutions?
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                15+ years of proven expertise delivering exceptional results for businesses of all sizes.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center group">
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 w-16 h-16 rounded-2xl mx-auto mb-6 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Expert Team</h3>
                <p className="text-gray-600">Certified professionals with 15+ years of industry experience</p>
              </div>

              <div className="text-center group">
                <div className="bg-gradient-to-r from-green-500 to-blue-600 w-16 h-16 rounded-2xl mx-auto mb-6 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <BarChart3 className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Proven Results</h3>
                <p className="text-gray-600">Measurable outcomes and successful projects</p>
              </div>

              <div className="text-center group">
                <div className="bg-gradient-to-r from-purple-500 to-pink-600 w-16 h-16 rounded-2xl mx-auto mb-6 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Full Service</h3>
                <p className="text-gray-600">Complete digital solutions under one roof</p>
              </div>

              <div className="text-center group">
                <div className="bg-gradient-to-r from-orange-500 to-red-600 w-16 h-16 rounded-2xl mx-auto mb-6 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Zap className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">24/7 Support</h3>
                <p className="text-gray-600">Dedicated support and account management</p>
              </div>
            </div>
          </div>
        </section>


      </main>
      
      <Footer />
    </>
  );
};

export default ServicesPage;
