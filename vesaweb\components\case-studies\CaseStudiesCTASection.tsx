import React from 'react';
import Link from 'next/link';
import { Phone, Calendar, Award, TrendingUp } from 'lucide-react';

const CaseStudiesCTASection: React.FC = () => {

  const successStats = [
    {
      icon: TrendingUp,
      value: '500+',
      label: 'Successful Projects',
      description: 'Completed across all industries'
    },
    {
      icon: Award,
      value: '300%',
      label: 'Average ROI',
      description: 'Return on investment for clients'
    },
    {
      icon: Phone,
      value: '98%',
      label: 'Client Satisfaction',
      description: 'Happy clients who recommend us'
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-blue-50 to-blue-100">
      <div className="max-w-7xl mx-auto px-6">
        {/* Main CTA Header */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Ready to Write Your Own Success Story?
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            These case studies represent real businesses that took action and achieved extraordinary results. Your success story could be next. Let&apos;s discuss how we can help you achieve similar growth.
          </p>
        </div>

        {/* Success Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {successStats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <div key={index} className="bg-white rounded-2xl p-8 text-center shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="bg-blue-100 p-4 rounded-2xl inline-block mb-6">
                  <IconComponent className="text-blue-600" size={32} />
                </div>
                <div className="text-4xl font-bold text-gray-800 mb-2">{stat.value}</div>
                <div className="text-xl font-semibold text-gray-800 mb-2">{stat.label}</div>
                <div className="text-gray-600">{stat.description}</div>
              </div>
            );
          })}
        </div>

        {/* CTA Buttons */}
        <div className="text-center mb-16">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact-us"
              className="bg-blue-600 text-white font-bold px-10 py-4 rounded-full hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
            >
              <Phone className="mr-2" size={20} />
              Get Your Free Consultation
            </Link>
            <Link
              href="/free-estimate"
              className="border-2 border-blue-600 text-blue-600 font-bold px-10 py-4 rounded-full hover:bg-blue-600 hover:text-white transition-all duration-300 flex items-center justify-center"
            >
              <Calendar className="mr-2" size={20} />
              Request Free Estimate
            </Link>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-12 text-white text-center">
          <h3 className="text-3xl md:text-4xl font-bold mb-6">
            Join Our Growing List of Success Stories
          </h3>
          <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
            Every success story started with a single conversation. Let&apos;s discuss how we can help you achieve the growth and results you&apos;re looking for.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="tel:+14166283793"
              className="bg-white text-blue-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
            >
              <Phone className="mr-2" size={20} />
              Call ****** 628 3793
            </a>
            <Link
              href="/free-estimate"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-all duration-300 flex items-center justify-center"
            >
              <Calendar className="mr-2" size={20} />
              Schedule Free Consultation
            </Link>
          </div>

          <div className="mt-8 text-blue-100 text-sm">
            <p>No long-term contracts • No setup fees • Results-driven approach</p>
          </div>
        </div>
      </div>
    </section>

  );
};

export default CaseStudiesCTASection;
