import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Header from '@/components/global/Header';
import Footer from '@/components/global/Footer';

// Import web development section components
import WebDevHeroSection from '@/components/services/web-dev/main-web-dev/WebDevHeroSection';
import WebDevAboutSection from '@/components/services/web-dev/main-web-dev/WebDevAboutSection';
import WebDevServicesSection from '@/components/services/web-dev/main-web-dev/WebDevServicesSection';
import WebDevIndustrySection from '@/components/services/web-dev/main-web-dev/WebDevIndustrySection';
import WebDevProcessSection from '@/components/services/web-dev/main-web-dev/WebDevProcessSection';
import WebDevCTASection from '@/components/services/web-dev/main-web-dev/WebDevCTASection';
import WebDevTechnologySection from '@/components/services/web-dev/main-web-dev/WebDevTechnologySection';
// import WebDevResultsSection from '@/components/services/web-dev/main-web-dev/WebDevResultsSection';
import WebDevFAQSection from '@/components/services/web-dev/main-web-dev/WebDevFAQSection';

const WebDevelopmentPage: React.FC = () => {
  return (
    <>
      <Head>
        <title>Professional Web Development Services That Drive Results | Vesa Solutions</title>
        <meta name="description" content="Custom web development solutions that convert visitors into customers. Modern, fast, and mobile-responsive websites. Free consultation included." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      
      <Header />
      
      {/* Breadcrumbs */}
      <div className="bg-gray-50 py-4">
        <div className="max-w-7xl mx-auto px-6">
          <nav className="text-sm" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              <li>
                <Link href="/" className="text-gray-500 hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li>
                <Link href="/services" className="text-gray-500 hover:text-blue-600 transition-colors">
                  Services
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-gray-900 font-medium" aria-current="page">
                Website Development
              </li>
            </ol>
          </nav>
        </div>
      </div>
      
      <main className="min-h-screen bg-white">
        {/* All Web Development Page Sections */}
        <WebDevHeroSection />
        <WebDevAboutSection />
        <WebDevServicesSection />
        <WebDevIndustrySection />
        <WebDevProcessSection />
        <WebDevCTASection />
        <WebDevTechnologySection />
        {/* <WebDevResultsSection /> */}
        <WebDevFAQSection />
      </main>
      
      <Footer />
    </>
  );
};

export default WebDevelopmentPage;
