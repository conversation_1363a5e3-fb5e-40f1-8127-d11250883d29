// components/blog/BlogFilters.tsx - Blog filtering component
import React, { useState } from 'react'
import { Search, X, Filter, ChevronDown, ChevronUp } from 'lucide-react'
import { BlogCategory, BlogTag, BlogFilters as BlogFiltersType } from '@/types/blog'

interface BlogFiltersProps {
  categories: BlogCategory[]
  tags: BlogTag[]
  onFilterChange: (filters: BlogFiltersType) => void
  loading: boolean
}

const BlogFilters: React.FC<BlogFiltersProps> = ({
  categories,
  tags,
  onFilterChange,
  loading
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedTag, setSelectedTag] = useState('')
  const [showFilters, setShowFilters] = useState(false)

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    applyFilters()
  }

  const applyFilters = () => {
    const filters: BlogFiltersType = {}

    if (searchTerm.trim()) filters.search = searchTerm.trim()
    if (selectedCategory) filters.category = selectedCategory
    if (selectedTag) filters.tag = selectedTag

    onFilterChange(filters)
  }

  const clearFilters = () => {
    setSearchTerm('')
    setSelectedCategory('')
    setSelectedTag('')
    onFilterChange({})
  }

  const hasActiveFilters = searchTerm || selectedCategory || selectedTag

  const getCategoryColor = (color: string) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-800 border-blue-200',
      green: 'bg-green-100 text-green-800 border-green-200',
      purple: 'bg-purple-100 text-purple-800 border-purple-200',
      orange: 'bg-orange-100 text-orange-800 border-orange-200',
      red: 'bg-red-100 text-red-800 border-red-200',
      teal: 'bg-teal-100 text-teal-800 border-teal-200'
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  // Deduplicate categories and tags to prevent duplicates
  const uniqueCategories = categories.filter((category, index, self) =>
    index === self.findIndex(c => c._id === category._id)
  )

  const uniqueTags = tags.filter((tag, index, self) =>
    index === self.findIndex(t => t._id === tag._id)
  )

  return (
    <div className="mb-12">
      {/* Search Bar */}
      <div className="max-w-3xl mx-auto mb-8">
        <form onSubmit={handleSearch} className="relative">
          <div className="relative">
            <Search size={20} className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search articles..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value)
                if (e.target.value === '') {
                  applyFilters()
                }
              }}
              className="w-full pl-12 pr-24 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-lg shadow-sm"
              disabled={loading}
            />
            <button
              type="submit"
              disabled={loading}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50"
            >
              Search
            </button>
          </div>
        </form>
      </div>

      {/* Filter Toggle Button */}
      <div className="text-center mb-6">
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="inline-flex items-center bg-white border border-gray-200 rounded-xl px-6 py-3 hover:bg-gray-50 transition-all duration-200 shadow-sm"
        >
          <Filter size={18} className="mr-2 text-gray-600" />
          <span className="font-medium text-gray-700">
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </span>
          {hasActiveFilters && (
            <span className="ml-2 bg-blue-600 text-white text-xs rounded-full px-2 py-1 font-medium">
              {[searchTerm, selectedCategory, selectedTag].filter(Boolean).length}
            </span>
          )}
          {showFilters ? (
            <ChevronUp size={18} className="ml-2 text-gray-400" />
          ) : (
            <ChevronDown size={18} className="ml-2 text-gray-400" />
          )}
        </button>
      </div>

      {/* Quick Filters - Categories and Tags in horizontal layout */}
      {showFilters && (
        <div className="max-w-5xl mx-auto mb-8">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            {/* Categories */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-4 flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                Categories
              </h3>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => {
                    setSelectedCategory('')
                    applyFilters()
                  }}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                    !selectedCategory
                      ? 'bg-blue-600 text-white shadow-md'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  All
                </button>
                {uniqueCategories.map((category) => (
                  <button
                    key={category._id}
                    onClick={() => {
                      setSelectedCategory(category.slug.current)
                      applyFilters()
                    }}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                      selectedCategory === category.slug.current
                        ? `${getCategoryColor(category.color)} shadow-md border`
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {category.title}
                  </button>
                ))}
              </div>
            </div>

            {/* Tags */}
            <div>
              <h3 className="text-sm font-semibold text-gray-700 mb-4 flex items-center">
                <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => {
                    setSelectedTag('')
                    applyFilters()
                  }}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                    !selectedTag
                      ? 'bg-purple-600 text-white shadow-md'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  All
                </button>
                {uniqueTags.slice(0, 8).map((tag) => (
                  <button
                    key={tag._id}
                    onClick={() => {
                      setSelectedTag(tag.slug.current)
                      applyFilters()
                    }}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                      selectedTag === tag.slug.current
                        ? 'bg-purple-100 text-purple-800 border border-purple-200 shadow-md'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {tag.title}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="max-w-5xl mx-auto mb-6">
          <div className="flex flex-wrap items-center gap-3">
            <span className="text-sm text-gray-600 font-medium">Active filters:</span>
            {searchTerm && (
              <span className="inline-flex items-center bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm border border-blue-200">
                &quot;{searchTerm}&quot;
                <button
                  onClick={() => {
                    setSearchTerm('')
                    applyFilters()
                  }}
                  className="ml-2 hover:text-blue-900 transition-colors"
                >
                  <X size={14} />
                </button>
              </span>
            )}
            {selectedCategory && (
              <span className="inline-flex items-center bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm border border-green-200">
                {uniqueCategories.find(c => c.slug.current === selectedCategory)?.title}
                <button
                  onClick={() => {
                    setSelectedCategory('')
                    applyFilters()
                  }}
                  className="ml-2 hover:text-green-900 transition-colors"
                >
                  <X size={14} />
                </button>
              </span>
            )}
            {selectedTag && (
              <span className="inline-flex items-center bg-purple-50 text-purple-700 px-3 py-1 rounded-full text-sm border border-purple-200">
                {uniqueTags.find(t => t.slug.current === selectedTag)?.title}
                <button
                  onClick={() => {
                    setSelectedTag('')
                    applyFilters()
                  }}
                  className="ml-2 hover:text-purple-900 transition-colors"
                >
                  <X size={14} />
                </button>
              </span>
            )}
            <button
              onClick={clearFilters}
              className="text-sm text-gray-500 hover:text-red-600 transition-colors duration-200 ml-2"
            >
              Clear all
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default BlogFilters
