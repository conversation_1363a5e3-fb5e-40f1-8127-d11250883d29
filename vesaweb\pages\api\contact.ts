// pages/api/contact.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import nodemailer from 'nodemailer';

interface ContactFormData {
  businessName: string;
  fullName: string;
  email: string;
  phone: string;
  location: string;
  website?: string;
  message: string;
  service: string;
  userCountry?: string;
  timestamp?: string;
}

// Helper function to safely get error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
}

// Helper function to get business notification recipients from environment variables
function getBusinessNotificationEmails(): string[] {
  const emailsString = process.env.BUSINESS_NOTIFICATION_EMAILS;
  if (!emailsString) {
    console.warn('BUSINESS_NOTIFICATION_EMAILS not found, falling back to BUSINESS_EMAIL');
    return process.env.BUSINESS_EMAIL ? [process.env.BUSINESS_EMAIL] : [];
  }
  
  // Split by comma and trim whitespace
  return emailsString.split(',').map(email => email.trim()).filter(email => email.length > 0);
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const formData: ContactFormData = req.body;

    // Validate required fields
    const { businessName, fullName, email, phone, location, message, service } = formData;
    
    if (!businessName || !fullName || !email || !phone || !location || !message || !service) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Validate environment variables
    if (!process.env.BUSINESS_EMAIL || !process.env.APP_PASS) {
      console.error('Missing environment variables: BUSINESS_EMAIL or APP_PASS');
      return res.status(500).json({ 
        message: 'Server configuration error',
        success: false 
      });
    }

    // Get business notification emails from environment variables
    const businessNotificationEmails = getBusinessNotificationEmails();
    if (businessNotificationEmails.length === 0) {
      console.error('No business notification emails configured');
      return res.status(500).json({ 
        message: 'Email configuration error',
        success: false 
      });
    }

    // Create transporter for Google Workspace/Business Gmail
    const transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: process.env.BUSINESS_EMAIL,
        pass: process.env.APP_PASS,
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Verify the transporter
    try {
      await transporter.verify();
      console.log('SMTP connection verified successfully');
    } catch (verifyError) {
      console.error('SMTP verification failed:', getErrorMessage(verifyError));
      return res.status(500).json({ 
        message: 'Email service configuration error',
        success: false,
        error: process.env.NODE_ENV === 'development' ? getErrorMessage(verifyError) : 'Email service unavailable'
      });
    }

    // Email to your business
    const businessEmailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb; border-bottom: 2px solid #2563eb; padding-bottom: 10px;">
          New ${service} Lead 🎯
        </h2>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1e40af; margin-top: 0;">Contact Information</h3>
          <p><strong>Business Name:</strong> ${businessName}</p>
          <p><strong>Contact Person:</strong> ${fullName}</p>
          <p><strong>Email:</strong> <a href="mailto:${email}">${email}</a></p>
          <p><strong>Phone:</strong> <a href="tel:${phone}">${phone}</a></p>
          <p><strong>Location:</strong> ${location}</p>
          ${formData.website ? `<p><strong>Website:</strong> <a href="${formData.website}" target="_blank">${formData.website}</a></p>` : ''}
          ${formData.userCountry ? `<p><strong>Country:</strong> ${formData.userCountry}</p>` : ''}
        </div>

        <div style="background: #eff6ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1e40af; margin-top: 0;">Service Interest</h3>
          <p><strong>Service:</strong> ${service}</p>
          <p><strong>Message:</strong></p>
          <div style="background: white; padding: 15px; border-radius: 4px; border-left: 4px solid #2563eb;">
            ${message.replace(/\n/g, '<br>')}
          </div>
        </div>

        <div style="background: #f1f5f9; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <p style="margin: 0; color: #64748b; font-size: 14px;">
            <strong>Submitted:</strong> ${new Date().toLocaleString()}<br>
            <strong>Source:</strong> ${service} Landing Page
          </p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="mailto:${email}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Reply to Lead
          </a>
        </div>
      </div>
    `;

    // Thank you email to the customer
    const customerEmailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="text-align: center; padding: 20px 0;">
          <h1 style="color: #2563eb;">VESA Solutions</h1>
        </div>
        
        <h2 style="color: #1e40af;">Thank you for your interest in our ${service} services! 🚀</h2>
        
        <p>Hi ${fullName},</p>
        
        <p>Thank you for requesting a free ${service} analysis for <strong>${businessName}</strong>. We're excited to help you grow your business!</p>
        
        <div style="background: #eff6ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1e40af; margin-top: 0;">What happens next?</h3>
          <ul style="color: #374151;">
            <li>📋 Our expert team will analyze your current ${service.toLowerCase()} performance</li>
            <li>🔍 We'll research your competition and identify opportunities</li>
            <li>📞 A senior strategist will call you within 24 hours</li>
            <li>📊 You'll receive a detailed analysis and custom strategy</li>
          </ul>
        </div>

        <div style="background: #f0fdf4; border: 1px solid #22c55e; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #16a34a; margin-top: 0;">⚡ Quick Response Guarantee</h3>
          <p style="margin: 0; color: #15803d;">We'll contact you within <strong>24 hours</strong> (usually much sooner!) to discuss your ${service.toLowerCase()} strategy.</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <p style="color: #64748b;">Have questions? Call us directly:</p>
          <a href="tel:+15551234567" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
            📞 (*************
          </a>
        </div>

        <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 30px; color: #64748b; font-size: 14px;">
          <p>Best regards,<br>
          <strong>The VESA Solutions Team</strong><br>
          Digital Marketing Experts</p>
        </div>
      </div>
    `;

    // Send email to business (internal notification) - MULTIPLE RECIPIENTS
    try {
      await transporter.sendMail({
        from: `"VESA Solutions" <${process.env.BUSINESS_EMAIL}>`,
        to: businessNotificationEmails, // SEND TO MULTIPLE EMAILS
        subject: `🎯 New ${service} Lead: ${businessName} - ${fullName}`,
        html: businessEmailHtml,
        replyTo: email,
      });
      console.log(`Business notification email sent to: ${businessNotificationEmails.join(', ')}`);
    } catch (emailError) {
      console.error('Failed to send business notification:', getErrorMessage(emailError));
      // Continue to try sending customer email even if business email fails
    }

    // Send thank you email to customer
    try {
      await transporter.sendMail({
        from: `"VESA Solutions" <${process.env.BUSINESS_EMAIL}>`,
        to: email,
        subject: `Thank you for your ${service} inquiry - VESA Solutions`,
        html: customerEmailHtml,
        replyTo: process.env.BUSINESS_EMAIL,
      });
      console.log('Customer thank you email sent');
    } catch (emailError) {
      console.error('Failed to send customer email:', getErrorMessage(emailError));
      // We'll still return success since the form submission was received
    }

    console.log('Form submission processed successfully');
    res.status(200).json({ 
      message: 'Form submitted successfully',
      success: true 
    });

  } catch (error) {
    console.error('Email sending error:', getErrorMessage(error));
    res.status(500).json({ 
      message: 'Failed to process form submission',
      success: false,
      error: process.env.NODE_ENV === 'development' ? getErrorMessage(error) : 'Internal server error'
    });
  }
}