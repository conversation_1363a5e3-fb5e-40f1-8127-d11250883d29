// components/services/sub-services/index.ts - Complete exports
export { Icon<PERSON>enderer } from '@/components/global/IconRender'

// Core 6-section layout (matches LocalSEO exactly):
export { SubServiceHero } from './SubServiceHero'           // 1. Hero
export { ServiceCTA } from './ServiceCTA'                   // 2. CTA
export { ServiceDetails } from './ServiceDetails'           // 3. Combined 4 sections
export { ServiceProcess } from './ServiceProcess'           // 4. Process (+ CaseStudy) 
export { Testimonials } from './Testimonials'               // 5. Testimonials (+ FAQ)
export { FooterCTA } from './FooterCTA'                     // 6. FooterCTA

// Individual components (still available for flexibility):
export { WhyServiceMatters } from './WhyServiceMatters'     // Part of ServiceDetails
export { CaseStudy } from './CaseStudy'                     // Part of ServiceProcess
export { FAQ } from './FAQ'                                 // Part of Testimonials