import React from 'react';
import Image from 'next/image';

interface Platform {
  name: string;
  logo: string;
}

interface PlatformLogosSectionProps {
  className?: string;
}

const PlatformLogosSection: React.FC<PlatformLogosSectionProps> = ({ className = '' }) => {
  const platforms: Platform[] = [
    {
      name: 'SEMrush',
      logo: '/homepage/semrush.png'
    },
    {
      name: 'WordPress',
      logo: '/homepage/wordpress logo.png'
    },
    {
      name: 'Shopify',
      logo: '/homepage/shopify.png'
    },
    {
      name: 'WooCommerce',
      logo: '/homepage/WooCommerce-Logo.jpg'
    },
    {
      name: 'Google Ads',
      logo: '/homepage/google ads.png'
    }
  ];

  return (
    <section className={`w-full relative overflow-hidden ${className}`} style={{
      background: 'linear-gradient(to bottom, #ffffff, rgba(239, 246, 255, 0.3))'
    }}>
      {/* Background decorative elements */}
      <div className="absolute inset-0" style={{ opacity: 0.05 }}>
        <div className="absolute w-32 h-32 rounded-full" style={{
          top: '80px',
          left: '40px',
          border: '1px solid #DBEAFE'
        }}></div>
        <div className="absolute w-24 h-24 rounded-lg transform rotate-45" style={{
          bottom: '80px',
          right: '40px',
          border: '1px solid #DBEAFE'
        }}></div>
        <div className="absolute w-16 h-16 rounded-lg transform -rotate-12" style={{
          top: '50%',
          left: '25%',
          border: '1px solid #DBEAFE'
        }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
        {/* Enhanced Header */}
        <div className="text-center mb-12 sm:mb-16 lg:mb-20">
          <p className="font-medium text-base sm:text-lg mb-3 tracking-wide" style={{
            color: '#3B82F6'
          }}>
            OUR TECHNOLOGY PARTNERS
          </p>
          <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight mb-4 sm:mb-6" style={{
            color: '#1F2937'
          }}>
            WE PROVIDE EXCEPTIONAL EXPERTISE IN<br className="hidden sm:block" />
            <span className="sm:hidden"> </span>SUPPORTING DIVERSE PLATFORMS
          </h2>
          <p className="text-base sm:text-lg lg:text-xl max-w-4xl mx-auto leading-relaxed" style={{
            color: '#4B5563'
          }}>
            From SEO optimization to e-commerce solutions, we leverage industry-leading tools and platforms to deliver exceptional results for your digital presence.
          </p>
        </div>
        
        {/* Platform Logos Container */}
        <div className="relative">
          {/* Background card for logos */}
          <div className="rounded-2xl lg:rounded-3xl p-6 sm:p-8 lg:p-12" style={{
            backgroundColor: 'rgba(255, 255, 255, 0.6)',
            backdropFilter: 'blur(8px)',
            boxShadow: '0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
            border: '1px solid #F3F4F6'
          }}>
            {/* Platform Logos Grid */}
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-6 sm:gap-8 lg:gap-12 items-center justify-items-center">
              {platforms.map((platform, index) => (
                <div
                  key={platform.name}
                  className="group flex items-center justify-center w-full h-16 sm:h-20 lg:h-24"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div 
                    className="relative w-full h-full flex items-center justify-center p-2 rounded-xl"
                    style={{
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'rgba(239, 246, 255, 0.5)';
                      e.currentTarget.style.transform = 'scale(1.05)';
                      e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                      e.currentTarget.style.transform = 'scale(1)';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    <Image
                      src={platform.logo}
                      alt={platform.name}
                      width={200}
                      height={64}
                      className="h-10 sm:h-12 lg:h-16 w-auto max-w-[140px] sm:max-w-[160px] lg:max-w-[200px] object-contain transition-all duration-300 hover:scale-110"
                    />
                    
                    {/* Hover effect overlay */}
                    <div 
                      className="absolute inset-0 rounded-xl border-2"
                      style={{
                        borderColor: 'transparent',
                        transition: 'all 0.3s ease',
                        opacity: 0
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.borderColor = '#DBEAFE';
                        e.currentTarget.style.opacity = '1';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.borderColor = 'transparent';
                        e.currentTarget.style.opacity = '0';
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Subtle bottom accent */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-32 sm:w-48 lg:w-64 h-1 rounded-full" style={{
            bottom: '-4px',
            background: 'linear-gradient(to right, transparent, #DBEAFE, transparent)'
          }}></div>
        </div>

        {/* Call to action text */}
        <div className="text-center mt-12 sm:mt-16 lg:mt-20">
          <p className="text-sm sm:text-base lg:text-lg italic" style={{
            color: '#4B5563'
          }}>
            Ready to leverage these powerful platforms for your business?
          </p>
          <button
            className="mt-4 sm:mt-6 font-medium rounded-full px-6 sm:px-8 py-3 sm:py-4 text-sm sm:text-base lg:text-lg"
            style={{
              backgroundColor: '#2563EB',
              color: '#FFFFFF',
              transition: 'all 0.3s ease',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
              transform: 'translateY(0)'
            }}
            onClick={() => window.location.href = '/free-estimate'}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#1d4ed8';
              e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
              e.currentTarget.style.transform = 'translateY(-4px)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#2563EB';
              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
              e.currentTarget.style.transform = 'translateY(0)';
            }}
          >
            Get Started Today
          </button>
        </div>
      </div>
    </section>
  );
};

export default PlatformLogosSection;