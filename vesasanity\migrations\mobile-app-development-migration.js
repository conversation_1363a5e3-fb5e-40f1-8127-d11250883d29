// scripts/migrate-mobile-app-development.js - Working migration with proper schema-compatible icons and BLUE colors
const { createClient } = require('@sanity/client')
const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

const mobileAppData = {
  _type: 'subService',
  parentService: 'web-development',
  title: 'Mobile App Development Services',
  slug: {
    _type: 'slug',
    current: 'mobile-app-development'
  },
  
  // Hero Section
  hero: {
    badgeText: 'Mobile App Development',
    badgeIcon: 'Smartphone',
    title: 'Build Powerful Mobile Applications',
    subtitle: 'Create native and cross-platform mobile apps that engage users, drive business growth, and deliver exceptional mobile experiences. Our mobile app development services bring your ideas to life on iOS and Android platforms.',
    stats: [
      { _key: 'stat1', value: '6.8B', label: 'Smartphone Users Worldwide' },
      { _key: 'stat2', value: '90%', label: 'Mobile Time Spent in Apps' },
      { _key: 'stat3', value: '4.8★', label: 'Average App Store Rating' },
      { _key: 'stat4', value: '24/7', label: 'App Support & Monitoring' }
    ],
    backgroundGradient: 'from-blue-600 to-blue-800'
  },

  // Why Service Matters
  whyMatters: {
    title: 'Why Professional Mobile App Development is Essential',
    description: [
      {
        _type: 'block',
        _key: 'desc1',
        children: [
          {
            _type: 'span',
            _key: 'span1',
            text: "Mobile apps have become the primary way people interact with businesses, with users spending over 90% of their mobile time in apps rather than mobile websites. A professionally developed mobile app provides direct access to your customers, enables push notifications, and creates engaging experiences that drive loyalty and revenue."
          }
        ],
        markDefs: [],
        style: 'normal'
      },
      {
        _type: 'block',
        _key: 'desc2',
        children: [
          {
            _type: 'span',
            _key: 'span2',
            text: "VESA Solutions specializes in creating high-quality mobile applications that not only look stunning but also provide seamless user experiences, robust functionality, and the performance needed to succeed in competitive app stores and drive business growth."
          }
        ],
        markDefs: [],
        style: 'normal'
      }
    ],
    features: [
      { _key: 'feature1', text: 'Native iOS and Android app development' },
      { _key: 'feature2', text: 'Cross-platform solutions for maximum reach' },
      { _key: 'feature3', text: 'User-centric design and intuitive interfaces' },
      { _key: 'feature4', text: 'App store optimization and deployment' }
    ],
    stats: [
      {
        _key: 'stat1',
        value: '90%',
        label: 'of mobile time is spent in apps',
        color: 'from-blue-50 to-blue-100'
      },
      {
        _key: 'stat2',
        value: '5x',
        label: 'higher engagement than mobile web',
        color: 'from-blue-100 to-blue-200'
      },
      {
        _key: 'stat3',
        value: '3x',
        label: 'better conversion rates in apps',
        color: 'from-blue-200 to-blue-300'
      },
      {
        _key: 'stat4',
        value: '88%',
        label: 'of consumers use mobile apps daily',
        color: 'from-blue-300 to-blue-400'
      }
    ],
    ctaButton: {
      text: 'Explore Our Mobile App Solutions'
    }
  },

  // All Services (with schema-compatible icons)
  services: [
    {
      _key: 'service1',
      icon: 'Smartphone',
      title: 'Native iOS & Android Development',
      description: 'Build high-performance native mobile apps for iOS and Android platforms with platform-specific features and optimal user experiences.',
      fullDescription: 'Native app development provides the best performance, user experience, and access to platform-specific features. We create native iOS and Android apps that take full advantage of each platform\'s capabilities and design guidelines.',
      features: [
        'Native iOS development with Swift and Objective-C',
        'Native Android development with Kotlin and Java',
        'Platform-specific UI/UX design and interactions',
        'Access to device hardware and native features',
        'Optimal performance and battery efficiency',
        'App Store and Google Play Store optimization',
        'Push notifications and background processing',
        'Integration with platform-specific services'
      ],
      benefits: 'Native development provides 40% better performance, superior user experience, and full access to platform features, resulting in higher user satisfaction and app store ratings.',
      result: '+40% Better Performance'
    },
    {
      _key: 'service2',
      icon: 'Layers',
      title: 'Cross-Platform App Development',
      description: 'Develop apps that work seamlessly across multiple platforms using modern cross-platform frameworks for cost-effective mobile solutions.',
      fullDescription: 'Cross-platform development allows you to reach both iOS and Android users with a single codebase, reducing development time and costs while maintaining high quality and performance across platforms.',
      features: [
        'React Native and Flutter development',
        'Single codebase for multiple platforms',
        'Native-like performance and user experience',
        'Shared business logic and faster development',
        'Cost-effective solution for multi-platform reach',
        'Easy maintenance and updates across platforms',
        'Access to native device features and APIs',
        'Consistent branding and functionality'
      ],
      benefits: 'Cross-platform development reduces development costs by 60%, accelerates time-to-market by 50%, and ensures consistent user experience across all platforms.',
      result: '+60% Cost Reduction'
    },
    {
      _key: 'service3',
      icon: 'Palette',
      title: 'Mobile App UI/UX Design',
      description: 'Create intuitive, engaging mobile app interfaces that provide exceptional user experiences and drive user engagement and retention.',
      fullDescription: 'Mobile app success depends heavily on user experience design. We create intuitive, visually appealing interfaces that follow platform design guidelines while reflecting your brand and optimizing for user engagement.',
      features: [
        'User experience (UX) research and design',
        'Platform-specific design guidelines compliance',
        'Interactive prototyping and user testing',
        'Custom iconography and visual elements',
        'Accessibility design and compliance',
        'Animation and micro-interaction design',
        'Responsive design for different screen sizes',
        'Brand integration and visual consistency'
      ],
      benefits: 'Professional UI/UX design increases user engagement by 200%, improves app store ratings by 85%, and reduces user churn by 45% through intuitive, enjoyable experiences.',
      result: '+200% User Engagement'
    },
    {
      _key: 'service4',
      icon: 'Database',
      title: 'Backend Development & APIs',
      description: 'Build robust backend systems and APIs that power your mobile app with secure data management, user authentication, and scalable architecture.',
      fullDescription: 'Mobile apps require powerful backend systems to handle data, user management, and business logic. We develop secure, scalable backend solutions that support your app\'s functionality and growth.',
      features: [
        'RESTful API development and integration',
        'Database design and management systems',
        'User authentication and authorization',
        'Cloud infrastructure and hosting setup',
        'Real-time data synchronization',
        'Push notification systems',
        'Payment processing integration',
        'Analytics and reporting systems'
      ],
      benefits: 'Robust backend development ensures 99.9% uptime, supports unlimited scalability, and provides secure data management that builds user trust and enables business growth.',
      result: '+99.9% Uptime Reliability'
    },
    {
      _key: 'service5',
      icon: 'Store',
      title: 'App Store Optimization & Deployment',
      description: 'Optimize your app for app store success with professional deployment, ASO strategies, and ongoing performance monitoring.',
      fullDescription: 'Getting your app discovered in crowded app stores requires strategic optimization and professional deployment. We handle the entire app store process and optimize for maximum visibility and downloads.',
      features: [
        'App Store and Google Play Store submission',
        'App store optimization (ASO) strategies',
        'App metadata and keyword optimization',
        'Screenshot and video preview creation',
        'App store compliance and guidelines adherence',
        'Beta testing and TestFlight deployment',
        'App performance monitoring and analytics',
        'Update management and version control'
      ],
      benefits: 'Professional ASO increases app visibility by 150%, improves download rates by 80%, and ensures successful app store approval on first submission.',
      result: '+150% App Visibility'
    },
    {
      _key: 'service6',
      icon: 'Shield',
      title: 'App Security & Maintenance',
      description: 'Implement comprehensive security measures and ongoing maintenance to keep your mobile app secure, updated, and performing optimally.',
      fullDescription: 'Mobile app security is critical for protecting user data and maintaining trust. We implement comprehensive security measures and provide ongoing maintenance to ensure your app remains secure and up-to-date.',
      features: [
        'Data encryption and secure storage',
        'User authentication and access controls',
        'API security and secure communications',
        'Regular security audits and updates',
        'Compliance with privacy regulations',
        'Performance monitoring and optimization',
        'Bug fixes and feature updates',
        'Crash reporting and error tracking'
      ],
      benefits: 'Comprehensive security and maintenance reduces security incidents by 95%, ensures regulatory compliance, and maintains high app performance and user satisfaction.',
      result: '+95% Security Protection'
    }
  ],

  // Strategic Implementation Section (with schema-compatible icons and colors)
  strategicImplementation: {
    title: 'Comprehensive Mobile App Development Process',
    description: 'Successful mobile app development requires strategic planning, expert design, and meticulous development. Our integrated approach ensures your app not only functions perfectly but also engages users and drives business results.',
    secondaryDescription: 'From initial concept to app store success, we guide you through every step of the mobile app development process with expertise, transparency, and dedication to your success.',
    features: [
      'Strategic planning and user research',
      'Expert design and development implementation',
      'App store optimization and ongoing support'
    ],
    sections: [
      {
        _key: 'section1',
        icon: 'Target',
        title: 'Strategy & Research',
        description: 'Comprehensive market research, user analysis, and strategic planning to create the perfect mobile app development roadmap.',
        color: 'from-blue-50 to-blue-100'
      },
      {
        _key: 'section2',
        icon: 'Code',
        title: 'Design & Development',
        description: 'Expert mobile app design and development using cutting-edge technologies and best practices for optimal performance and user experience.',
        color: 'from-blue-100 to-blue-200'
      },
      {
        _key: 'section3',
        icon: 'Rocket',
        title: 'Launch & Growth',
        description: 'Successful app store launch with optimization strategies and ongoing support to ensure your app achieves maximum success and growth.',
        color: 'from-blue-200 to-blue-300'
      }
    ]
  },

  // Market Intelligence Section (with schema-compatible background)
  marketIntelligence: {
    title: 'Advanced Mobile App Development Intelligence',
    description: 'Our mobile app development strategies are powered by industry insights, proven methodologies, and cutting-edge technologies that deliver exceptional results for businesses across all industries.',
    secondaryDescription: 'Through years of experience and dozens of successful app launches, we\'ve developed the expertise and processes needed to create mobile apps that not only function perfectly but also achieve app store success and drive business growth.',
    stats: [
      {
        _key: 'stat1',
        value: '50+',
        label: 'Mobile Apps Launched'
      },
      {
        _key: 'stat2',
        value: '4.8★',
        label: 'Average App Store Rating'
      },
      {
        _key: 'stat3',
        value: '99.9%',
        label: 'App Uptime Guarantee'
      },
      {
        _key: 'stat4',
        value: '24/7',
        label: 'App Support & Monitoring'
      }
    ],
    ctaButton: {
      text: 'Start Your Mobile App Project'
    },
    backgroundGradient: 'from-blue-50 to-blue-100'
  },

  // Process (with schema-compatible icons)
  process: {
    title: 'Our Proven Mobile App Development Process',
    description: 'VESA Solutions follows a systematic approach that has delivered successful mobile apps for dozens of businesses, ensuring quality, performance, and app store success at every step.',
    steps: [
      {
        _key: 'step1',
        step: 1,
        title: 'Discovery & Strategy',
        description: 'Comprehensive market research, user analysis, and strategic planning to define your app\'s success roadmap.',
        icon: 'Search',
        details: [
          'Market research and competitive analysis',
          'Target user research and persona development',
          'Feature planning and app architecture design',
          'Platform selection and technology planning',
          'Monetization strategy and business model planning'
        ]
      },
      {
        _key: 'step2',
        step: 2,
        title: 'Design & Prototyping',
        description: 'Create intuitive user experiences and stunning visual designs with interactive prototypes for validation.',
        icon: 'Palette',
        details: [
          'User experience (UX) design and wireframing',
          'Visual design and brand integration',
          'Interactive prototype development',
          'User testing and design validation',
          'Platform-specific design optimization'
        ]
      },
      {
        _key: 'step3',
        step: 3,
        title: 'Development & Testing',
        description: 'Expert app development with comprehensive testing across devices, platforms, and use cases.',
        icon: 'Code',
        details: [
          'Native or cross-platform app development',
          'Backend API development and integration',
          'Comprehensive testing across devices',
          'Performance optimization and debugging',
          'Security implementation and testing'
        ]
      },
      {
        _key: 'step4',
        step: 4,
        title: 'Launch & Optimization',
        description: 'Successful app store launch with optimization strategies and ongoing support for continued success.',
        icon: 'Rocket',
        details: [
          'App store submission and approval process',
          'App store optimization (ASO) implementation',
          'Launch marketing and user acquisition',
          'Performance monitoring and analytics setup',
          'Ongoing updates and feature development'
        ]
      }
    ]
  },

  // Case Study (with schema-compatible background)
  caseStudy: {
    title: 'Fitness App Achieves 100K+ Downloads in 6 Months',
    description: 'Discover how our mobile app development helped FitTracker Pro create a successful fitness app that achieved over 100,000 downloads and 4.8-star rating within six months of launch.',
    results: [
      {
        _key: 'result1',
        value: '100K+',
        label: 'App Downloads'
      },
      {
        _key: 'result2',
        value: '4.8★',
        label: 'App Store Rating'
      },
      {
        _key: 'result3',
        value: '85%',
        label: 'User Retention Rate'
      },
      {
        _key: 'result4',
        value: '300%',
        label: 'Revenue Growth'
      }
    ],
    ctaButton: {
      text: 'View Complete Case Study'
    },
    backgroundGradient: 'from-blue-600 to-blue-800'
  },

  // CTA
  cta: {
    title: 'Get Your Free Mobile App Development Consultation',
    description: 'Ready to bring your mobile app idea to life? Let\'s discuss your vision and create a development strategy that turns your concept into a successful mobile application.',
    benefits: [
      'Complete app strategy & market analysis consultation',
      'Platform recommendations & technology planning',
      'UI/UX design concepts & development timeline',
      'App store optimization & launch strategy guidance'
    ],
    phoneNumber: '(*************',
    formSettings: {
      ctaText: 'Get My Free Mobile App Consultation',
      messagePlaceholder: 'Tell us about your mobile app idea and goals*'
    }
  },

  // All Testimonials (without image references)
  testimonials: [
    {
      _key: 'testimonial1',
      name: 'Alex Johnson',
      business: 'FitTracker Pro',
      location: 'Austin, TX',
      quote: 'The mobile app VESA developed for us exceeded all expectations. We achieved over 100,000 downloads in just 6 months and maintain a 4.8-star rating. The user experience is incredible and our revenue has grown 300%.',
      result: '+100K Downloads',
      rating: 5
    },
    {
      _key: 'testimonial2',
      name: 'Rachel Martinez',
      business: 'LocalEats Delivery',
      location: 'San Diego, CA',
      quote: 'Working with VESA on our food delivery app was fantastic. The cross-platform solution they built works flawlessly on both iOS and Android, and our customer engagement has increased dramatically since launch.',
      result: '+250% Customer Engagement',
      rating: 5
    },
    {
      _key: 'testimonial3',
      name: 'Kevin Chen',
      business: 'TaskMaster Productivity',
      location: 'Seattle, WA',
      quote: 'The productivity app they created has transformed our business. The intuitive design and powerful features have attracted thousands of users, and the backend system handles everything perfectly. Highly recommended!',
      result: '+4.9★ App Store Rating',
      rating: 5
    }
  ],

  // All FAQs
  faqs: [
    {
      _key: 'faq1',
      question: 'Should I build a native app or cross-platform app?',
      answer: 'The choice depends on your specific needs, budget, and timeline. Native apps offer the best performance and platform-specific features, while cross-platform apps are more cost-effective and faster to develop. We\'ll recommend the best approach based on your requirements.'
    },
    {
      _key: 'faq2',
      question: 'How long does mobile app development take?',
      answer: 'Mobile app development typically takes 3-6 months depending on complexity and features. This includes planning, design, development, testing, and app store submission. We provide detailed timelines during the planning phase and keep you updated throughout the process.'
    },
    {
      _key: 'faq3',
      question: 'How much does mobile app development cost?',
      answer: 'App development costs vary based on complexity, features, and platform requirements. Simple apps start around $15,000, while complex apps can range from $50,000-$150,000+. We provide detailed cost estimates after understanding your specific requirements.'
    },
    {
      _key: 'faq4',
      question: 'Will you help with app store submission?',
      answer: 'Yes! We handle the entire app store submission process for both Apple App Store and Google Play Store, including app store optimization (ASO), compliance with guidelines, and managing the approval process to ensure successful launch.'
    },
    {
      _key: 'faq5',
      question: 'Do you provide ongoing app maintenance and updates?',
      answer: 'Yes, we offer comprehensive app maintenance services including bug fixes, security updates, performance optimization, new feature development, and ongoing support to keep your app running smoothly and up-to-date.'
    },
    {
      _key: 'faq6',
      question: 'Can you integrate my app with existing business systems?',
      answer: 'Absolutely! We specialize in integrating mobile apps with existing business systems including CRMs, databases, payment processors, analytics platforms, and other third-party services through APIs and custom development.'
    },
    {
      _key: 'faq7',
      question: 'How do you ensure app security and data protection?',
      answer: 'We implement comprehensive security measures including data encryption, secure authentication, API security, secure data storage, and compliance with privacy regulations like GDPR and CCPA to protect user data and maintain trust.'
    },
    {
      _key: 'faq8',
      question: 'What happens after my app is launched?',
      answer: 'After launch, we provide ongoing support including performance monitoring, user feedback analysis, bug fixes, feature updates, app store optimization, and strategic guidance to help your app grow and succeed in the market.'
    }
  ],

  // Footer CTA (with schema-compatible background)
  footerCta: {
    title: 'Ready to Build Your Successful Mobile App?',
    description: 'Join dozens of businesses that trust VESA Solutions to create mobile apps that engage users, drive growth, and achieve app store success.',
    primaryButton: {
      text: 'Schedule Free Consultation',
      icon: 'Calendar'
    },
    secondaryButton: {
      text: 'Call (*************',
      icon: 'Phone',
      phoneNumber: '(*************'
    },
    trustSignals: {
      title: 'Trusted by Businesses & Certified Partners',
      ratings: [
        {
          _key: 'rating1',
          rating: '4.9/5 Rating',
          source: 'Google Reviews',
          description: 'Google Reviews'
        },
        {
          _key: 'rating2',
          rating: '4.8/5 Rating',
          source: 'Clutch Reviews',
          description: 'Clutch Reviews'
        },
        {
          _key: 'rating3',
          rating: '50+ Apps',
          source: 'Successfully Launched',
          description: 'Successfully Launched'
        }
      ]
    },
    backgroundGradient: 'from-blue-700 to-blue-900'
  },

  // SEO (without image reference)
  seo: {
    metaTitle: 'Mobile App Development Services | iOS & Android Apps | VESA Solutions',
    metaDescription: 'Professional mobile app development for iOS and Android. Native and cross-platform solutions with expert design, development, and app store optimization. Free consultation included.',
    keywords: [
      'mobile app development',
      'iOS app development',
      'android app development',
      'cross-platform app development',
      'mobile application development'
    ]
  }
}

async function migrateMobileAppData() {
  try {
    console.log('🚀 Starting Mobile App Development migration...')
    console.log('📊 Project ID: zleti5e4')
    console.log('🗃️  Dataset: production')
    console.log('')
    
    // Check if document already exists
    const existing = await client.fetch(`*[_type == "subService" && slug.current == "mobile-app-development"][0]`)
    
    if (existing) {
      console.log('📄 Mobile App Development document already exists:', existing._id)
      console.log('🔄 Updating existing document...')
      
      const result = await client
        .patch(existing._id)
        .set(mobileAppData)
        .commit()
      
      console.log('✅ Mobile App Development page updated successfully!')
      console.log(`📄 Document ID: ${result._id}`)
      console.log(`🔗 Edit in Studio: https://vesasanity.sanity.studio/structure/subService;${result._id}`)
      return result
    } else {
      const result = await client.create(mobileAppData)
      console.log('✅ Mobile App Development page created successfully!')
      console.log(`📄 Document ID: ${result._id}`)
      console.log(`🔗 Edit in Studio: https://vesasanity.sanity.studio/structure/subService;${result._id}`)
      return result
    }
    
  } catch (error) {
    console.error('')
    console.error('❌ Migration failed:')
    console.error(error)
    
    if (error.message && error.message.includes('token')) {
      console.log('')
      console.log('💡 Make sure your token is correctly set in this file')
      console.log('   Get your token from: https://sanity.io/manage/personal/tokens')
    }
    
    if (error.message && error.message.includes('Insufficient permissions')) {
      console.log('')
      console.log('💡 Make sure your token has "Editor" or "Administrator" permissions')
    }
    
    process.exit(1)
  }
}

// Export the function instead of auto-running
module.exports = { migrateMobileAppData }

// Run the migration
migrateMobileAppData()
  .then(() => {
    console.log('')
    console.log('🎉 Your Mobile App Development page is now managed by Sanity!')
    console.log('📝 You can now edit all content in Sanity Studio')
    console.log('🔄 Remember to update your Next.js page to fetch from Sanity')
    console.log('')
    console.log('🎯 The page structure is complete and functional!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Migration failed:', error)
    process.exit(1)
  })
