# SEO Schema Markup Implementation Guide

## Overview
The SchemaMarkup component provides comprehensive structured data for Vesa Solutions website to improve SEO and search engine understanding.

## Usage Examples

### 1. Homepage Implementation
```tsx
import SchemaMarkup from '@/components/seo/SchemaMarkup';

export default function HomePage() {
  return (
    <>
      <SchemaMarkup pageType="homepage" />
      {/* Your page content */}
    </>
  );
}
```

### 2. Service Page Implementation
```tsx
import SchemaMarkup from '@/components/seo/SchemaMarkup';

export default function ServicePage() {
  const serviceData = {
    name: "SEO Search Engine Optimization",
    description: "Comprehensive SEO services to improve your website's search engine rankings and organic traffic.",
    category: "SEO Services",
    price: "Starting at $1,500/month"
  };

  return (
    <>
      <SchemaMarkup 
        pageType="service" 
        serviceData={serviceData}
      />
      {/* Your page content */}
    </>
  );
}
```

### 3. Blog Post Implementation
```tsx
import SchemaMarkup from '@/components/seo/SchemaMarkup';

export default function BlogPost() {
  const articleData = {
    title: "10 SEO Tips for Better Rankings",
    description: "Learn the top SEO strategies that will help improve your website's search engine rankings.",
    publishedDate: "2024-01-15T10:00:00Z",
    modifiedDate: "2024-01-20T15:30:00Z",
    author: "Vesa Solutions Team",
    image: "https://vesasolutions.com/blog/seo-tips-image.jpg"
  };

  return (
    <>
      <SchemaMarkup 
        pageType="blog" 
        articleData={articleData}
      />
      {/* Your page content */}
    </>
  );
}
```

## Schema Types Included

### 1. Organization Schema
- Company information and contact details
- Services offered
- Location and opening hours
- Social media profiles

### 2. Local Business Schema
- Local business information
- Address and geo-coordinates
- Opening hours
- Aggregate ratings

### 3. Website Schema
- Website information
- Search functionality
- Publisher details

### 4. Professional Service Schema
- Service categories
- Service offerings
- Coverage area

### 5. Service Schema (for service pages)
- Individual service details
- Pricing information
- Provider information

### 6. Article Schema (for blog posts)
- Article metadata
- Author information
- Publication dates
- Publisher details

## Implementation in Different Page Types

### Main Service Pages
```tsx
// SEO Main Page
const seoServiceData = {
  name: "Search Engine Optimization Services",
  description: "Comprehensive SEO services including on-page optimization, technical SEO, local SEO, and content strategy.",
  category: "SEO Services"
};

// Web Development Main Page
const webDevServiceData = {
  name: "Web Development Services",
  description: "Custom website development, e-commerce solutions, and web application development.",
  category: "Web Development Services"
};

// Digital Marketing Main Page
const digitalMarketingServiceData = {
  name: "Digital Marketing Services",
  description: "Complete digital marketing solutions including PPC, social media marketing, and email marketing.",
  category: "Digital Marketing Services"
};
```

### Sub-Service Pages
```tsx
// On-Page SEO Sub-Service
const onPageSEOData = {
  name: "On-Page SEO Optimization",
  description: "Comprehensive optimization of your website content, meta tags, headers, and internal structure for maximum search engine visibility.",
  category: "SEO Services",
  price: "Starting at $800/month"
};

// Local SEO Sub-Service
const localSEOData = {
  name: "Local SEO Services",
  description: "Optimize your business for local search results and Google My Business to attract nearby customers.",
  category: "Local SEO Services",
  price: "Starting at $600/month"
};
```

## Benefits

### SEO Benefits
- Improved search engine understanding
- Enhanced rich snippets in search results
- Better local search visibility
- Increased click-through rates

### Technical Benefits
- Structured data validation
- Google Search Console insights
- Voice search optimization
- Featured snippet opportunities

## Testing and Validation

### Google Tools
1. **Rich Results Test**: https://search.google.com/test/rich-results
2. **Structured Data Testing Tool**: https://validator.schema.org/
3. **Google Search Console**: Monitor structured data performance

### Validation Steps
1. Test each page type with Google's Rich Results Test
2. Validate schema markup with Schema.org validator
3. Monitor Google Search Console for structured data errors
4. Check for rich snippet appearances in search results

## Best Practices

### Content Accuracy
- Ensure all schema data matches actual page content
- Keep contact information up-to-date
- Maintain consistent business information across all schemas

### Performance
- Minimize schema markup size where possible
- Use conditional rendering for page-specific schemas
- Avoid duplicate schema markup

### Maintenance
- Regular validation of schema markup
- Update schemas when business information changes
- Monitor Google Search Console for structured data issues

## Common Schema Patterns for Vesa Solutions

### Service Categories
```json
{
  "SEO Services": [
    "On-Page SEO Optimization",
    "Local SEO Services", 
    "Technical SEO Services",
    "Content Writing",
    "SEO Analytics"
  ],
  "Web Development Services": [
    "Custom Website Development",
    "E-commerce Development", 
    "Website Speed Optimization",
    "Website Maintenance & Support"
  ],
  "Digital Marketing Services": [
    "PPC Advertising",
    "Social Media Marketing",
    "Email Marketing", 
    "Branding Services"
  ]
}
```

### Contact Information Schema
```json
{
  "telephone": ["+***********", "+355694046408"],
  "email": "<EMAIL>",
  "address": {
    "streetAddress": "Bulevardi Dyrrah, Pallati 394, Kati 4-t",
    "addressLocality": "Durrës",
    "postalCode": "2001",
    "addressCountry": "AL"
  }
}
```

This comprehensive schema markup implementation will significantly improve your website's SEO performance and search engine visibility.
