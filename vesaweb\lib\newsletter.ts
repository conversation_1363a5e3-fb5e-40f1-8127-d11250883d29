// lib/newsletter.ts - Newsletter utility functions
import nodemailer from 'nodemailer';
import { Redis } from '@upstash/redis';

export interface NewsletterSubscription {
  email: string;
  subscribedAt: string;
  isActive: boolean;
}

export interface BlogNotificationData {
  title: string;
  excerpt: string;
  slug: string;
  featuredImage?: string;
  publishedAt: string;
}

// Helper function to get error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) return error.message;
  return String(error);
}

// Initialize Redis client
const redis = new Redis({
  url: process.env.KV_REST_API_URL!,
  token: process.env.KV_REST_API_TOKEN!,
});

// KV storage key for newsletter subscribers
const NEWSLETTER_SUBSCRIBERS_KEY = 'newsletter:subscribers';

// Helper function to read subscribers from KV storage
export async function getNewsletterSubscribers(): Promise<NewsletterSubscription[]> {
  try {
    const subscribers = await redis.get<NewsletterSubscription[]>(NEWSLETTER_SUBSCRIBERS_KEY);
    const allSubscribers = subscribers || [];
    // Return only active subscribers
    return allSubscribers.filter((sub: NewsletterSubscription) => sub.isActive);
  } catch (error) {
    console.error('Error reading newsletter subscribers from KV:', error);
    return [];
  }
}

// Helper function to get active subscriber emails
export async function getActiveSubscriberEmails(): Promise<string[]> {
  const subscribers = await getNewsletterSubscribers();
  return subscribers.map(sub => sub.email);
}

// Helper function to create email transporter
function createEmailTransporter() {
  const fromEmail = process.env.NEWSLETTER_FROM_EMAIL || process.env.BUSINESS_EMAIL;

  if (!fromEmail || !process.env.APP_PASS) {
    throw new Error('Email configuration missing');
  }

  return nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: fromEmail,
      pass: process.env.APP_PASS,
    },
    tls: {
      rejectUnauthorized: false
    }
  });
}

// Function to send blog notification to all subscribers
export async function sendBlogNotificationToSubscribers(blogData: BlogNotificationData): Promise<{
  success: boolean;
  sentCount: number;
  failedCount: number;
  errors: string[];
}> {
  const subscribers = await getActiveSubscriberEmails();
  const fromEmail = process.env.NEWSLETTER_FROM_EMAIL || process.env.BUSINESS_EMAIL;
  const fromName = process.env.NEWSLETTER_FROM_NAME || 'VESA Solutions';

  if (subscribers.length === 0) {
    console.log('No active newsletter subscribers found');
    return {
      success: true,
      sentCount: 0,
      failedCount: 0,
      errors: []
    };
  }

  const transporter = createEmailTransporter();
  
  // Verify transporter
  try {
    await transporter.verify();
  } catch (error) {
    throw new Error(`Email service configuration error: ${getErrorMessage(error)}`);
  }

  const blogNotificationHtml = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin: 0;">VESA Solutions</h1>
        <p style="color: #6b7280; margin: 5px 0;">Digital Marketing Excellence</p>
      </div>
      
      <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; padding: 30px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
        <h2 style="margin: 0 0 15px 0; font-size: 24px;">New Article Published! 📚</h2>
        <p style="margin: 0; font-size: 16px; opacity: 0.9;">Fresh insights from our digital marketing experts</p>
      </div>
      
      <div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; margin-bottom: 30px;">
        ${blogData.featuredImage ? `
          <img src="${blogData.featuredImage}" alt="${blogData.title}" style="width: 100%; height: 200px; object-fit: cover;">
        ` : ''}
        
        <div style="padding: 25px;">
          <h3 style="color: #374151; margin: 0 0 15px 0; font-size: 20px; line-height: 1.3;">
            ${blogData.title}
          </h3>
          
          <p style="color: #6b7280; margin: 0 0 20px 0; line-height: 1.6;">
            ${blogData.excerpt}
          </p>
          
          <div style="margin-bottom: 20px;">
            <span style="color: #9ca3af; font-size: 14px;">
              Published on ${new Date(blogData.publishedAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </span>
          </div>
          
          <div style="text-align: center;">
            <a href="https://vesasolutions.com/blog/${blogData.slug}" 
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
              Read Full Article
            </a>
          </div>
        </div>
      </div>
      
      <div style="background: #f9fafb; padding: 20px; border-radius: 8px; margin-bottom: 25px; text-align: center;">
        <p style="color: #6b7280; margin: 0 0 15px 0;">
          Don't miss out on our latest insights!
        </p>
        <a href="https://vesasolutions.com/blog" 
           style="color: #2563eb; text-decoration: none; font-weight: bold;">
          Browse All Articles →
        </a>
      </div>
      
      <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center;">
        <p style="color: #9ca3af; font-size: 14px; margin: 0;">
          You're receiving this because you subscribed to our newsletter at vesasolutions.com
        </p>
        <p style="color: #9ca3af; font-size: 14px; margin: 5px 0 0 0;">
          <a href="mailto:${fromEmail}?subject=Unsubscribe%20Newsletter" style="color: #9ca3af;">Unsubscribe</a> |
          © 2024 VESA Solutions. All rights reserved.
        </p>
      </div>
    </div>
  `;

  let sentCount = 0;
  let failedCount = 0;
  const errors: string[] = [];

  // Send emails in batches to avoid overwhelming the SMTP server
  const batchSize = 10;
  for (let i = 0; i < subscribers.length; i += batchSize) {
    const batch = subscribers.slice(i, i + batchSize);
    
    const emailPromises = batch.map(async (email) => {
      try {
        await transporter.sendMail({
          from: `"${fromName}" <${fromEmail}>`,
          to: email,
          subject: `📚 New Article: ${blogData.title}`,
          html: blogNotificationHtml,
        });
        sentCount++;
        console.log(`Blog notification sent to: ${email}`);
      } catch (error) {
        failedCount++;
        const errorMsg = `Failed to send to ${email}: ${getErrorMessage(error)}`;
        errors.push(errorMsg);
        console.error(errorMsg);
      }
    });

    await Promise.all(emailPromises);
    
    // Add a small delay between batches
    if (i + batchSize < subscribers.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  console.log(`Blog notification summary: ${sentCount} sent, ${failedCount} failed`);
  
  return {
    success: sentCount > 0,
    sentCount,
    failedCount,
    errors
  };
}

// Function to manually trigger blog notifications (for testing or manual sends)
export async function triggerBlogNotification(blogSlug: string): Promise<{
  success: boolean;
  message: string;
  sentCount?: number;
  failedCount?: number;
}> {
  try {
    // This would typically fetch blog data from Sanity
    // For now, we'll return a placeholder response
    console.log(`Manual blog notification triggered for: ${blogSlug}`);
    
    return {
      success: true,
      message: 'Blog notification system is ready. Integrate with Sanity webhooks for automatic notifications.',
      sentCount: 0,
      failedCount: 0
    };
  } catch (error) {
    return {
      success: false,
      message: `Failed to trigger notification: ${getErrorMessage(error)}`
    };
  }
}
