// scripts/migrate-web-application-development.js - Working migration with proper schema-compatible icons and BLUE colors
const { createClient } = require('@sanity/client')
const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

const webApplicationData = {
  _type: 'subService',
  parentService: 'web-development',
  title: 'Web Application Development Services',
  slug: {
    _type: 'slug',
    current: 'web-application-development'
  },
  
  // Hero Section
  hero: {
    badgeText: 'Web Application Development',
    badgeIcon: 'Code',
    title: 'Build Powerful Web Applications',
    subtitle: 'Create sophisticated web applications that streamline business processes, improve operational efficiency, and provide competitive advantages. Our web app development services deliver custom solutions that scale with your business.',
    stats: [
      { _key: 'stat1', value: '200%', label: 'Average Efficiency Improvement' },
      { _key: 'stat2', value: '99.9%', label: 'Application Uptime' },
      { _key: 'stat3', value: '50%', label: 'Faster Development Time' },
      { _key: 'stat4', value: '24/7', label: 'Application Support' }
    ],
    backgroundGradient: 'from-blue-600 to-blue-800'
  },

  // Why Service Matters
  whyMatters: {
    title: 'Why Custom Web Application Development is Essential',
    description: [
      {
        _type: 'block',
        _key: 'desc1',
        children: [
          {
            _type: 'span',
            _key: 'span1',
            text: "Modern businesses require sophisticated software solutions that go beyond basic websites. Web applications provide powerful functionality, automate complex processes, and create competitive advantages through custom features that perfectly match your business requirements and workflows."
          }
        ],
        markDefs: [],
        style: 'normal'
      },
      {
        _type: 'block',
        _key: 'desc2',
        children: [
          {
            _type: 'span',
            _key: 'span2',
            text: "VESA Solutions specializes in creating robust web applications that streamline operations, improve efficiency, and provide the exact functionality your business needs. Our applications are built for scalability, security, and performance to support your long-term growth."
          }
        ],
        markDefs: [],
        style: 'normal'
      }
    ],
    features: [
      { _key: 'feature1', text: 'Custom functionality tailored to your business' },
      { _key: 'feature2', text: 'Scalable architecture for business growth' },
      { _key: 'feature3', text: 'Advanced security and data protection' },
      { _key: 'feature4', text: 'Integration with existing business systems' }
    ],
    stats: [
      {
        _key: 'stat1',
        value: '73%',
        label: 'of businesses need custom software solutions',
        color: 'from-blue-50 to-blue-100'
      },
      {
        _key: 'stat2',
        value: '5x',
        label: 'better ROI with custom applications',
        color: 'from-blue-100 to-blue-200'
      },
      {
        _key: 'stat3',
        value: '60%',
        label: 'reduction in manual processes',
        color: 'from-blue-200 to-blue-300'
      },
      {
        _key: 'stat4',
        value: '85%',
        label: 'improvement in operational efficiency',
        color: 'from-blue-300 to-blue-400'
      }
    ],
    ctaButton: {
      text: 'Explore Our Web App Solutions'
    }
  },

  // All Services (with schema-compatible icons)
  services: [
    {
      _key: 'service1',
      icon: 'Code',
      title: 'Custom Web Application Development',
      description: 'Build sophisticated web applications with custom functionality, user interfaces, and business logic tailored to your specific requirements.',
      fullDescription: 'Custom web application development provides unlimited possibilities for creating software solutions that perfectly match your business needs. We build applications from the ground up with the exact features and functionality you require.',
      features: [
        'Custom user interface and experience design',
        'Complex business logic implementation',
        'Database design and management systems',
        'User authentication and authorization',
        'Real-time data processing and updates',
        'Advanced reporting and analytics',
        'Workflow automation and process management',
        'Scalable architecture and performance optimization'
      ],
      benefits: 'Custom web applications improve operational efficiency by 200%, reduce manual work by 70%, and provide competitive advantages through unique functionality that competitors cannot easily replicate.',
      result: '+200% Operational Efficiency'
    },
    {
      _key: 'service2',
      icon: 'Database',
      title: 'Enterprise Web Application Solutions',
      description: 'Develop enterprise-grade web applications that handle complex business processes, large datasets, and multiple user roles with advanced security.',
      fullDescription: 'Enterprise web applications require robust architecture, advanced security, and the ability to handle complex business processes. We create enterprise-grade solutions that scale with your organization.',
      features: [
        'Enterprise-grade security and compliance',
        'Multi-tenant architecture and user management',
        'Advanced role-based access controls',
        'Integration with enterprise systems (ERP, CRM)',
        'High-performance database optimization',
        'Scalable cloud infrastructure deployment',
        'Advanced analytics and business intelligence',
        'Comprehensive audit trails and logging'
      ],
      benefits: 'Enterprise applications improve business process efficiency by 150%, enhance data security by 95%, and provide scalable solutions that grow with your organization.',
      result: '+150% Process Efficiency'
    },
    {
      _key: 'service3',
      icon: 'Cloud',
      title: 'Cloud-Based Web Applications',
      description: 'Develop cloud-native web applications that leverage modern cloud infrastructure for scalability, reliability, and global accessibility.',
      fullDescription: 'Cloud-based web applications provide unlimited scalability, global accessibility, and reduced infrastructure costs. We build cloud-native applications that take full advantage of modern cloud platforms.',
      features: [
        'Cloud-native architecture and deployment',
        'Auto-scaling and load balancing',
        'Global content delivery and performance',
        'Serverless computing and microservices',
        'Cloud database and storage solutions',
        'DevOps and continuous deployment',
        'Disaster recovery and backup systems',
        'Cost optimization and resource management'
      ],
      benefits: 'Cloud applications reduce infrastructure costs by 40%, improve scalability by 300%, and provide 99.9% uptime with global accessibility and automatic scaling.',
      result: '+300% Scalability'
    },
    {
      _key: 'service4',
      icon: 'Puzzle',
      title: 'API Development & Integration',
      description: 'Create robust APIs and integrate web applications with existing systems, third-party services, and business tools.',
      fullDescription: 'API development and integration enable web applications to connect with existing business systems and third-party services, creating seamless workflows and data synchronization.',
      features: [
        'RESTful API design and development',
        'GraphQL API implementation',
        'Third-party service integrations',
        'Legacy system integration and modernization',
        'Real-time data synchronization',
        'API security and authentication',
        'Documentation and testing frameworks',
        'Microservices architecture implementation'
      ],
      benefits: 'API integration improves data flow efficiency by 180%, reduces manual data entry by 85%, and enables seamless connectivity between all business systems.',
      result: '+180% Data Flow Efficiency'
    },
    {
      _key: 'service5',
      icon: 'Users',
      title: 'Progressive Web Applications (PWA)',
      description: 'Build progressive web applications that provide native app-like experiences while maintaining web accessibility and performance.',
      fullDescription: 'Progressive Web Applications combine the best of web and mobile apps, providing app-like experiences that work across all devices while maintaining the accessibility and reach of web applications.',
      features: [
        'App-like user interface and interactions',
        'Offline functionality and data caching',
        'Push notifications and background sync',
        'Responsive design for all devices',
        'Fast loading and smooth performance',
        'App store distribution capabilities',
        'Cross-platform compatibility',
        'Progressive enhancement features'
      ],
      benefits: 'PWAs increase user engagement by 250%, improve mobile conversions by 140%, and provide app-like experiences without app store dependencies.',
      result: '+250% User Engagement'
    },
    {
      _key: 'service6',
      icon: 'Shield',
      title: 'Web Application Security & Maintenance',
      description: 'Implement comprehensive security measures and ongoing maintenance to keep web applications secure, updated, and performing optimally.',
      fullDescription: 'Web application security is critical for protecting sensitive data and maintaining user trust. We implement comprehensive security measures and provide ongoing maintenance to ensure applications remain secure and up-to-date.',
      features: [
        'Advanced security auditing and testing',
        'Data encryption and secure storage',
        'User authentication and access controls',
        'Regular security updates and patches',
        'Compliance with security standards',
        'Performance monitoring and optimization',
        'Backup and disaster recovery systems',
        'Ongoing maintenance and support'
      ],
      benefits: 'Comprehensive security reduces security incidents by 95%, ensures regulatory compliance, and maintains high application performance and user trust.',
      result: '+95% Security Protection'
    }
  ],

  // Strategic Implementation Section (with schema-compatible icons and colors)
  strategicImplementation: {
    title: 'Comprehensive Web Application Development Process',
    description: 'Successful web application development requires strategic planning, expert architecture, and meticulous implementation. Our integrated approach ensures applications that not only function perfectly but also drive business results.',
    secondaryDescription: 'From initial requirements analysis to post-launch optimization, we guide you through every step of the web application development process with expertise, transparency, and dedication to your success.',
    features: [
      'Strategic planning and architecture design',
      'Expert development and quality assurance',
      'Deployment and ongoing optimization'
    ],
    sections: [
      {
        _key: 'section1',
        icon: 'Target',
        title: 'Strategy & Architecture',
        description: 'Comprehensive requirements analysis and system architecture design to create the perfect foundation for your web application.',
        color: 'from-blue-50 to-blue-100'
      },
      {
        _key: 'section2',
        icon: 'Code',
        title: 'Development & Testing',
        description: 'Expert development using modern technologies and frameworks with comprehensive testing and quality assurance processes.',
        color: 'from-blue-100 to-blue-200'
      },
      {
        _key: 'section3',
        icon: 'Rocket',
        title: 'Deployment & Support',
        description: 'Successful deployment with ongoing monitoring, maintenance, and optimization to ensure continued success and growth.',
        color: 'from-blue-200 to-blue-300'
      }
    ]
  },

  // Market Intelligence Section (with schema-compatible background)
  marketIntelligence: {
    title: 'Advanced Web Application Development Intelligence',
    description: 'Our web application development strategies are powered by industry expertise, proven methodologies, and cutting-edge technologies that deliver exceptional results for businesses across all industries.',
    secondaryDescription: 'Through years of experience and dozens of successful web application projects, we\'ve developed the expertise and processes needed to create applications that not only function perfectly but also drive significant business growth.',
    stats: [
      {
        _key: 'stat1',
        value: '100+',
        label: 'Web Applications Built'
      },
      {
        _key: 'stat2',
        value: '200%',
        label: 'Average Efficiency Improvement'
      },
      {
        _key: 'stat3',
        value: '99.9%',
        label: 'Application Uptime'
      },
      {
        _key: 'stat4',
        value: '24/7',
        label: 'Application Support'
      }
    ],
    ctaButton: {
      text: 'Start Your Web App Project'
    },
    backgroundGradient: 'from-blue-50 to-blue-100'
  },

  // Process (with schema-compatible icons)
  process: {
    title: 'Our Proven Web Application Development Process',
    description: 'VESA Solutions follows a systematic approach that has delivered successful web applications for dozens of businesses, ensuring quality, performance, and business value at every step.',
    steps: [
      {
        _key: 'step1',
        step: 1,
        title: 'Requirements & Planning',
        description: 'Comprehensive analysis of business requirements, user needs, and technical specifications for web application development.',
        icon: 'Search',
        details: [
          'Business requirements analysis and documentation',
          'User research and workflow mapping',
          'Technical architecture and system design',
          'Technology stack selection and planning',
          'Project timeline and milestone planning'
        ]
      },
      {
        _key: 'step2',
        step: 2,
        title: 'Design & Prototyping',
        description: 'Create user-centered designs and interactive prototypes that optimize workflows and user experience.',
        icon: 'Palette',
        details: [
          'User experience (UX) design and wireframing',
          'User interface (UI) design and branding',
          'Interactive prototype development',
          'User testing and design validation',
          'Database design and architecture planning'
        ]
      },
      {
        _key: 'step3',
        step: 3,
        title: 'Development & Integration',
        description: 'Expert development with modern technologies, comprehensive testing, and system integrations.',
        icon: 'Code',
        details: [
          'Frontend and backend development',
          'Database implementation and optimization',
          'API development and third-party integrations',
          'Security implementation and testing',
          'Performance optimization and quality assurance'
        ]
      },
      {
        _key: 'step4',
        step: 4,
        title: 'Deployment & Support',
        description: 'Successful deployment with comprehensive training, documentation, and ongoing support services.',
        icon: 'Rocket',
        details: [
          'Production deployment and configuration',
          'User training and documentation delivery',
          'Performance monitoring and analytics setup',
          'Ongoing maintenance and support',
          'Feature updates and enhancements'
        ]
      }
    ]
  },

  // Case Study (with schema-compatible background)
  caseStudy: {
    title: 'Manufacturing Company Achieves 300% Efficiency Gain',
    description: 'Discover how our custom web application helped TechManufacturing streamline operations and achieve a 300% improvement in operational efficiency through automated workflows and real-time data management.',
    results: [
      {
        _key: 'result1',
        value: '300%',
        label: 'Efficiency Improvement'
      },
      {
        _key: 'result2',
        value: '85%',
        label: 'Manual Work Reduction'
      },
      {
        _key: 'result3',
        value: '99.9%',
        label: 'System Uptime'
      },
      {
        _key: 'result4',
        value: '60%',
        label: 'Cost Savings'
      }
    ],
    ctaButton: {
      text: 'View Complete Case Study'
    },
    backgroundGradient: 'from-blue-600 to-blue-800'
  },

  // CTA
  cta: {
    title: 'Get Your Free Web Application Development Consultation',
    description: 'Ready to streamline your business processes with a custom web application? Let\'s discuss your requirements and create a development strategy that delivers results.',
    benefits: [
      'Complete business requirements analysis & consultation',
      'Custom application architecture & technology planning',
      'User experience design concepts & development timeline',
      'Integration strategy & scalability planning'
    ],
    phoneNumber: '(*************',
    formSettings: {
      ctaText: 'Get My Free Web App Consultation',
      messagePlaceholder: 'Tell us about your web application needs and business goals*'
    }
  },

  // All Testimonials (without image references)
  testimonials: [
    {
      _key: 'testimonial1',
      name: 'Robert Johnson',
      business: 'TechManufacturing Solutions',
      location: 'Detroit, MI',
      quote: 'The web application VESA built for us has revolutionized our operations. We\'ve achieved a 300% improvement in efficiency and reduced manual work by 85%. The system handles everything perfectly and has transformed our business.',
      result: '+300% Efficiency Improvement',
      rating: 5
    },
    {
      _key: 'testimonial2',
      name: 'Lisa Chen',
      business: 'Healthcare Management Group',
      location: 'Boston, MA',
      quote: 'Working with VESA on our patient management application was exceptional. The custom features they developed have streamlined our workflows and improved patient care significantly. The security and compliance features are outstanding.',
      result: '+250% Workflow Efficiency',
      rating: 5
    },
    {
      _key: 'testimonial3',
      name: 'Mark Rodriguez',
      business: 'Financial Services Company',
      location: 'Charlotte, NC',
      quote: 'The enterprise web application they created has given us a huge competitive advantage. The real-time reporting and automated processes have improved our decision-making speed and accuracy dramatically.',
      result: '+180% Decision Speed',
      rating: 5
    }
  ],

  // All FAQs
  faqs: [
    {
      _key: 'faq1',
      question: 'What is the difference between a website and a web application?',
      answer: 'Websites primarily provide information and content, while web applications offer interactive functionality, data processing, and complex business logic. Web apps are software solutions that run in browsers and provide specific functionality like CRM systems, project management tools, or custom business solutions.'
    },
    {
      _key: 'faq2',
      question: 'How long does web application development take?',
      answer: 'Web application development typically takes 3-8 months depending on complexity and features. Simple applications may take 3-4 months, while complex enterprise applications can take 6-8 months or more. We provide detailed timelines during the planning phase.'
    },
    {
      _key: 'faq3',
      question: 'What technologies do you use for web application development?',
      answer: 'We use modern technologies including React, Node.js, Python, .NET, cloud platforms (AWS, Azure), and databases like PostgreSQL and MongoDB. Technology selection depends on your specific requirements, and we choose the best tools for your project\'s success.'
    },
    {
      _key: 'faq4',
      question: 'Can you integrate web applications with our existing systems?',
      answer: 'Yes, we specialize in integrating web applications with existing business systems including ERP, CRM, databases, and third-party services. We ensure seamless data flow and functionality between all your business systems.'
    },
    {
      _key: 'faq5',
      question: 'How do you ensure web application security?',
      answer: 'We implement comprehensive security measures including data encryption, secure authentication, access controls, regular security audits, and compliance with industry standards. Security is built into every aspect of our development process.'
    },
    {
      _key: 'faq6',
      question: 'Will the web application work on mobile devices?',
      answer: 'Yes, all our web applications are built with responsive design principles and work perfectly on all devices including smartphones, tablets, and desktops. We can also develop Progressive Web Apps (PWAs) for app-like mobile experiences.'
    },
    {
      _key: 'faq7',
      question: 'Do you provide ongoing support and maintenance?',
      answer: 'Yes, we offer comprehensive support and maintenance services including bug fixes, security updates, performance optimization, feature enhancements, and technical support to keep your web application running smoothly.'
    },
    {
      _key: 'faq8',
      question: 'How much does web application development cost?',
      answer: 'Web application costs vary based on complexity, features, and requirements. Simple applications start around $25,000, while complex enterprise applications can range from $75,000-$300,000+. We provide detailed cost estimates after understanding your specific needs.'
    }
  ],

  // Footer CTA (with schema-compatible background)
  footerCta: {
    title: 'Ready to Build Your Custom Web Application?',
    description: 'Join dozens of businesses that trust VESA Solutions to create powerful web applications that streamline operations, improve efficiency, and drive business growth.',
    primaryButton: {
      text: 'Schedule Free Consultation',
      icon: 'Calendar'
    },
    secondaryButton: {
      text: 'Call (*************',
      icon: 'Phone',
      phoneNumber: '(*************'
    },
    trustSignals: {
      title: 'Trusted by Businesses & Certified Partners',
      ratings: [
        {
          _key: 'rating1',
          rating: '4.9/5 Rating',
          source: 'Google Reviews',
          description: 'Google Reviews'
        },
        {
          _key: 'rating2',
          rating: '4.8/5 Rating',
          source: 'Clutch Reviews',
          description: 'Clutch Reviews'
        },
        {
          _key: 'rating3',
          rating: '100+ Apps',
          source: 'Successfully Built',
          description: 'Successfully Built'
        }
      ]
    },
    backgroundGradient: 'from-blue-700 to-blue-900'
  },

  // SEO (without image reference)
  seo: {
    metaTitle: 'Web Application Development Services | Custom Web Apps | VESA Solutions',
    metaDescription: 'Professional web application development services for custom business solutions. Scalable, secure, and efficient web apps that streamline operations. Free consultation included.',
    keywords: [
      'web application development',
      'custom web applications',
      'web app development services',
      'enterprise web applications',
      'business web applications'
    ]
  }
}

async function migrateWebApplicationData() {
  try {
    console.log('🚀 Starting Web Application Development migration...')
    console.log('📊 Project ID: zleti5e4')
    console.log('🗃️  Dataset: production')
    console.log('')
    
    // Check if document already exists
    const existing = await client.fetch(`*[_type == "subService" && slug.current == "web-application-development"][0]`)
    
    if (existing) {
      console.log('📄 Web Application Development document already exists:', existing._id)
      console.log('🔄 Updating existing document...')
      
      const result = await client
        .patch(existing._id)
        .set(webApplicationData)
        .commit()
      
      console.log('✅ Web Application Development page updated successfully!')
      console.log(`📄 Document ID: ${result._id}`)
      console.log(`🔗 Edit in Studio: https://vesasanity.sanity.studio/structure/subService;${result._id}`)
      return result
    } else {
      const result = await client.create(webApplicationData)
      console.log('✅ Web Application Development page created successfully!')
      console.log(`📄 Document ID: ${result._id}`)
      console.log(`🔗 Edit in Studio: https://vesasanity.sanity.studio/structure/subService;${result._id}`)
      return result
    }
    
  } catch (error) {
    console.error('')
    console.error('❌ Migration failed:')
    console.error(error)
    
    if (error.message && error.message.includes('token')) {
      console.log('')
      console.log('💡 Make sure your token is correctly set in this file')
      console.log('   Get your token from: https://sanity.io/manage/personal/tokens')
    }
    
    if (error.message && error.message.includes('Insufficient permissions')) {
      console.log('')
      console.log('💡 Make sure your token has "Editor" or "Administrator" permissions')
    }
    
    process.exit(1)
  }
}

// Export the function instead of auto-running
module.exports = { migrateWebApplicationData }

// Run the migration
migrateWebApplicationData()
  .then(() => {
    console.log('')
    console.log('🎉 Your Web Application Development page is now managed by Sanity!')
    console.log('📝 You can now edit all content in Sanity Studio')
    console.log('🔄 Remember to update your Next.js page to fetch from Sanity')
    console.log('')
    console.log('🎯 The page structure is complete and functional!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Migration failed:', error)
    process.exit(1)
  })
