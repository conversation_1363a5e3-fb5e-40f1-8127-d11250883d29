import React from 'react';
import Image from 'next/image';
import { ArrowRight, Phone } from 'lucide-react';
import ServiceContactForm from '@/components/global/Form';

// Helper function to safely get error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
}

interface FormData {
  businessName: string;
  fullName: string;
  email: string;
  phone: string;
  location: string;
  website?: string;
  message: string;
  service: string;
  userCountry?: string;
  timestamp?: string;
}

const SEOHeroSection: React.FC = () => {
  const handleFormSubmit = async (data: FormData) => {
    try {
      console.log('SEO form submitted:', data);
      
      // Send to API
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to submit form');
      }

      if (!result.success) {
        throw new Error(result.message || 'Form submission failed');
      }

      // Success is handled by the form component
      console.log('Form submitted successfully:', result);
      
    } catch (error) {
      console.error('Form submission error:', error);
      // Re-throw the error so the form component can handle it
      throw new Error(getErrorMessage(error));
    }
  };

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-green-900 via-green-800 to-green-900 overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image
          src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=1920&h=1080&fit=crop"
          alt="SEO Analytics Dashboard"
          fill
          className="object-cover opacity-20"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-green-900/80 to-green-800/80"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 py-20 flex items-center min-h-screen">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center w-full">
          <div>
            <div className="bg-red-600 text-white text-sm font-bold px-4 py-2 rounded-full inline-block mb-6">
              Don&apos;t Waste Money On Amateur SEO
            </div>
            
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight mb-8">
              Professional SEO Services That
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-green-400"> Drive Results</span>
            </h1>

            <p className="text-xl md:text-2xl text-green-100 mb-12 leading-relaxed">
              Transform your online presence with Vesa Solutions&apos; proven SEO strategies. We help businesses increase organic traffic, improve search rankings, and grow revenue.
            </p>
            
            {/* Stats */}
            <div className="grid grid-cols-2 gap-8 mb-12">
              <div className="text-center lg:text-left">
                <div className="text-4xl md:text-5xl font-bold text-green-300 mb-2">200+</div>
                <div className="text-green-100">Happy Clients</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-4xl md:text-5xl font-bold text-green-300 mb-2">300%</div>
                <div className="text-green-100">Average ROI</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-4xl md:text-5xl font-bold text-green-300 mb-2">5+</div>
                <div className="text-green-100">Years Experience</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-4xl md:text-5xl font-bold text-green-300 mb-2">99%</div>
                <div className="text-green-100">Google Searches</div>
              </div>
            </div>

            <a
              href="tel:+14166283793"
              className="group bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold px-10 py-5 rounded-full transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-green-500/25 inline-flex items-center justify-center text-lg"
            >
              <Phone className="mr-3" size={24} />
              Get A Free SEO Audit
              <ArrowRight size={24} className="ml-3 transition-transform duration-300 group-hover:translate-x-1" />
            </a>
          </div>

          {/* Updated Contact Form */}
          <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl">
            <ServiceContactForm 
              service={{
                name: 'SEO',
                type: 'seo',
                ctaText: 'Get My Free SEO Analysis',
                messagePlaceholder: 'Tell us about your business and SEO goals*',
                benefits: [
                  'Complete SEO audit & competitor analysis',
                  'Keyword opportunities & content strategy',
                  'Technical SEO recommendations',
                  'Link building and authority plan'
                ]
              }}
              onSubmit={handleFormSubmit}
              className="shadow-none"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default SEOHeroSection;