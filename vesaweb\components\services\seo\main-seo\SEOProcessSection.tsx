import React from 'react';
import Image from 'next/image';
import { CheckCircle } from 'lucide-react';

const SEOProcessSection: React.FC = () => {
  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
            Our Proven SEO Process
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            Vesa Solutions follows a systematic, data-driven approach that has delivered consistent results for hundreds of clients across diverse industries.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-24">
          <div>
            <Image
              src="https://images.unsplash.com/photo-1553877522-43269d4ea984?w=600&h=400&fit=crop"
              alt="SEO analysis and strategy planning"
              width={600}
              height={400}
              className="w-full rounded-3xl shadow-xl"
            />
          </div>
          <div>
            <div className="bg-green-500 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mb-6">
              1
            </div>
            <h3 className="text-3xl font-bold text-gray-800 mb-6">Comprehensive SEO Audit & Analysis</h3>
            <p className="text-lg text-gray-600 leading-relaxed mb-6">
              We begin with a thorough analysis of your website, business goals, target audience, and competitive landscape. This includes technical SEO audits, content analysis, keyword research, and competitor benchmarking.
            </p>
            <ul className="space-y-3">
              <li className="flex items-center text-gray-700">
                <CheckCircle size={16} className="text-green-500 mr-3" />
                Technical SEO audit and recommendations
              </li>
              <li className="flex items-center text-gray-700">
                <CheckCircle size={16} className="text-green-500 mr-3" />
                Comprehensive keyword research and analysis
              </li>
              <li className="flex items-center text-gray-700">
                <CheckCircle size={16} className="text-green-500 mr-3" />
                Competitor analysis and gap identification
              </li>
              <li className="flex items-center text-gray-700">
                <CheckCircle size={16} className="text-green-500 mr-3" />
                Content audit and optimization opportunities
              </li>
            </ul>
          </div>
        </div>

        {/* Process Steps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-8">
            <div className="bg-green-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6">
              2
            </div>
            <h4 className="text-xl font-bold text-gray-800 mb-4">Strategy Development</h4>
            <p className="text-gray-600 mb-4">Custom SEO strategy creation based on audit findings, business goals, and competitive analysis.</p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Keyword targeting strategy</li>
              <li>• Content marketing plan</li>
              <li>• Technical optimization roadmap</li>
              <li>• Link building strategy</li>
            </ul>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-8">
            <div className="bg-green-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6">
              3
            </div>
            <h4 className="text-xl font-bold text-gray-800 mb-4">Implementation & Optimization</h4>
            <p className="text-gray-600 mb-4">Execute SEO improvements including on-page optimization, technical fixes, and content creation.</p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• On-page SEO optimization</li>
              <li>• Technical SEO improvements</li>
              <li>• Content creation and optimization</li>
              <li>• Link building campaigns</li>
            </ul>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-3xl p-8">
            <div className="bg-purple-500 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mb-6">
              4
            </div>
            <h4 className="text-xl font-bold text-gray-800 mb-4">Monitoring & Reporting</h4>
            <p className="text-gray-600 mb-4">Continuous monitoring, analysis, and optimization based on performance data and algorithm updates.</p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Monthly performance reports</li>
              <li>• Ranking and traffic monitoring</li>
              <li>• ROI analysis and insights</li>
              <li>• Strategy refinement</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SEOProcessSection;