import React from 'react';
import Image from 'next/image';
import { ArrowRight, Phone } from 'lucide-react';
import ServiceContactForm from '@/components/global/Form';

// Helper function to safely get error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
}

interface FormData {
  businessName: string;
  fullName: string;
  email: string;
  phone: string;
  location: string;
  website?: string;
  message: string;
  service: string;
  userCountry?: string;
  timestamp?: string;
}

const WebDevHeroSection: React.FC = () => {
  const handleFormSubmit = async (data: FormData) => {
    try {
      console.log('Web Development form submitted:', data);
      
      // Send to API
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to submit form');
      }

      if (!result.success) {
        throw new Error(result.message || 'Form submission failed');
      }

      // Success is handled by the form component
      console.log('Form submitted successfully:', result);
      
    } catch (error) {
      console.error('Form submission error:', error);
      // Re-throw the error so the form component can handle it
      throw new Error(getErrorMessage(error));
    }
  };

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image
          src="https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=1920&h=1080&fit=crop"
          alt="Web Development Code"
          fill
          className="object-cover opacity-20"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-blue-800/80"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 py-20 flex items-center min-h-screen">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center w-full">
          <div>
            <div className="bg-green-600 text-white text-sm font-bold px-4 py-2 rounded-full inline-block mb-6">
              Don&apos;t Settle For Template Websites
            </div>
            
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight mb-8">
              Custom Web Development That
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-blue-400"> Converts Visitors</span>
            </h1>
            
            <p className="text-xl text-blue-100 mb-12 leading-relaxed">
              Get a professionally designed, lightning-fast website that turns visitors into customers. Our custom web development solutions are built for performance, conversions, and growth.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-300 mb-2">99.9%</div>
                <div className="text-blue-100">Uptime Guarantee</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-300 mb-2">&lt;3s</div>
                <div className="text-blue-100">Load Time</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-300 mb-2">100%</div>
                <div className="text-blue-100">Mobile Responsive</div>
              </div>
            </div>

            <a
              href="tel:+14166283793"
              className="group bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold px-10 py-5 rounded-full transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-blue-500/25 inline-flex items-center justify-center text-lg"
            >
              <Phone className="mr-3" size={24} />
              Get A Free Web Development Quote
              <ArrowRight size={24} className="ml-3 transition-transform duration-300 group-hover:translate-x-1" />
            </a>
          </div>

          {/* Contact Form */}
          <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl">
            <ServiceContactForm 
              service={{
                name: 'Web Development',
                type: 'web-design',
                ctaText: 'Get My Free Web Development Consultation',
                messagePlaceholder: 'Tell us about your website goals and requirements*',
                benefits: [
                  'Custom website design & development',
                  'Mobile-responsive & fast-loading',
                  'SEO-optimized structure',
                  'Ongoing support & maintenance'
                ]
              }}
              onSubmit={handleFormSubmit}
              className="shadow-none"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default WebDevHeroSection;
