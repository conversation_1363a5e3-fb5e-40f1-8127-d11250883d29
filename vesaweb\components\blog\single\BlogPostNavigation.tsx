// components/blog/single/BlogPostNavigation.tsx - Previous/Next post navigation
import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { ChevronLeft, ChevronRight } from 'lucide-react'

interface BlogPostNavigationProps {
  currentSlug: string
}

interface NavPost {
  title: string
  slug: string
}

const BlogPostNavigation: React.FC<BlogPostNavigationProps> = ({ currentSlug }) => {
  const [prevPost, setPrevPost] = useState<NavPost | null>(null)
  const [nextPost, setNextPost] = useState<NavPost | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchNavigation = async () => {
      try {
        const response = await fetch(`/api/blog/navigation?slug=${currentSlug}`)
        if (response.ok) {
          const data = await response.json()
          setPrevPost(data.prevPost)
          setNextPost(data.nextPost)
        }
      } catch (error) {
        console.error('Error fetching navigation:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchNavigation()
  }, [currentSlug])

  if (loading || (!prevPost && !nextPost)) {
    return null
  }

  return (
    <section className="bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Previous Post */}
          <div className="flex justify-start">
            {prevPost ? (
              <Link href={`/blog/${prevPost.slug}`}>
                <div className="group bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 w-full">
                  <div className="flex items-center text-blue-600 mb-2">
                    <ChevronLeft size={16} className="mr-1" />
                    <span className="text-sm font-medium">Previous Article</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2">
                    {prevPost.title}
                  </h3>
                </div>
              </Link>
            ) : (
              <div className="w-full"></div>
            )}
          </div>

          {/* Next Post */}
          <div className="flex justify-end">
            {nextPost ? (
              <Link href={`/blog/${nextPost.slug}`}>
                <div className="group bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 w-full text-right">
                  <div className="flex items-center justify-end text-blue-600 mb-2">
                    <span className="text-sm font-medium">Next Article</span>
                    <ChevronRight size={16} className="ml-1" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2">
                    {nextPost.title}
                  </h3>
                </div>
              </Link>
            ) : (
              <div className="w-full"></div>
            )}
          </div>
        </div>
      </div>
    </section>
  )
}

export default BlogPostNavigation
