import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.sanity.io',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'img.freepik.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  async redirects() {
    return [
      // Old page redirects
      {
        source: '/who-we-are/:path*',
        destination: '/about',
        permanent: true,
      },
      {
        source: '/web-development-3/:path*',
        destination: '/web-development',
        permanent: true,
      },
      {
        source: '/web-app-design/:path*',
        destination: '/web-development',
        permanent: true,
      },
      {
        source: '/hosting-support/:path*',
        destination: '/web-development',
        permanent: true,
      },
      {
        source: '/seo-smm/:path*',
        destination: '/seo-search-engine-optimization',
        permanent: true,
      },
      {
        source: '/branding-2/:path*',
        destination: '/digital-marketing',
        permanent: true,
      },
      {
        source: '/porfolio/:path*',
        destination: '/case-studies',
        permanent: true,
      },
      {
        source: '/internship/:path*',
        destination: '/contact-us',
        permanent: true,
      },
    ];
  },
};

export default nextConfig;