// pages/api/newsletter.ts - Newsletter subscription API endpoint
import { NextApiRequest, NextApiResponse } from 'next';
import nodemailer from 'nodemailer';
import { Redis } from '@upstash/redis';

interface NewsletterSubscription {
  email: string;
  subscribedAt: string;
  isActive: boolean;
}

interface NewsletterData {
  email: string;
}

// Helper function to get error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) return error.message;
  return String(error);
}

// Helper function to validate email
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Initialize Redis client
const redis = new Redis({
  url: process.env.KV_REST_API_URL!,
  token: process.env.KV_REST_API_TOKEN!,
});

// KV storage key for newsletter subscribers
const NEWSLETTER_SUBSCRIBERS_KEY = 'newsletter:subscribers';

// Helper function to read subscribers from KV storage
async function readSubscribers(): Promise<NewsletterSubscription[]> {
  try {
    // Check if KV is available
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      console.warn('KV storage not configured, using empty subscriber list');
      return [];
    }

    const subscribers = await redis.get<NewsletterSubscription[]>(NEWSLETTER_SUBSCRIBERS_KEY);
    return subscribers || [];
  } catch (error) {
    console.error('Error reading subscribers from KV:', error);
    return [];
  }
}

// Helper function to write subscribers to KV storage
async function writeSubscribers(subscribers: NewsletterSubscription[]): Promise<void> {
  try {
    // Check if KV is available
    if (!process.env.KV_REST_API_URL || !process.env.KV_REST_API_TOKEN) {
      console.warn('KV storage not configured, subscription saved temporarily');
      // In production, this would fail gracefully but still send confirmation email
      return;
    }

    await redis.set(NEWSLETTER_SUBSCRIBERS_KEY, subscribers);
  } catch (error) {
    console.error('Error writing subscribers to KV:', error);
    // Don't throw error - still allow email to be sent
    console.warn('Subscription not saved to storage, but confirmation email will be sent');
  }
}

// Helper function to send confirmation email
async function sendConfirmationEmail(email: string): Promise<void> {
  const fromEmail = process.env.NEWSLETTER_FROM_EMAIL || process.env.BUSINESS_EMAIL;
  const fromName = process.env.NEWSLETTER_FROM_NAME || 'VESA Solutions';

  if (!fromEmail || !process.env.APP_PASS) {
    throw new Error('Email configuration missing');
  }

  const transporter = nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: fromEmail,
      pass: process.env.APP_PASS,
    },
    tls: {
      rejectUnauthorized: false
    }
  });

  const confirmationEmailHtml = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin: 0;">VESA Solutions</h1>
        <p style="color: #6b7280; margin: 5px 0;">Digital Marketing Excellence</p>
      </div>
      
      <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; padding: 30px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
        <h2 style="margin: 0 0 15px 0; font-size: 24px;">Welcome to Our Newsletter! 🎉</h2>
        <p style="margin: 0; font-size: 16px; opacity: 0.9;">Thank you for subscribing to VESA Solutions newsletter</p>
      </div>
      
      <div style="background: #f9fafb; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
        <h3 style="color: #374151; margin: 0 0 15px 0;">What to Expect:</h3>
        <ul style="color: #6b7280; margin: 0; padding-left: 20px;">
          <li style="margin-bottom: 8px;">Latest digital marketing strategies and trends</li>
          <li style="margin-bottom: 8px;">Expert insights on SEO, web development, and digital marketing</li>
          <li style="margin-bottom: 8px;">Case studies and success stories</li>
          <li style="margin-bottom: 8px;">Exclusive tips and best practices</li>
        </ul>
      </div>
      
      <div style="text-align: center; margin-bottom: 25px;">
        <a href="https://vesasolutions.com/blog" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
          Read Our Latest Articles
        </a>
      </div>
      
      <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center;">
        <p style="color: #9ca3af; font-size: 14px; margin: 0;">
          You're receiving this because you subscribed to our newsletter at vesasolutions.com
        </p>
        <p style="color: #9ca3af; font-size: 14px; margin: 5px 0 0 0;">
          © 2024 VESA Solutions. All rights reserved.
        </p>
      </div>
    </div>
  `;

  await transporter.sendMail({
    from: `"${fromName}" <${fromEmail}>`,
    to: email,
    subject: 'Welcome to VESA Solutions Newsletter! 🎉',
    html: confirmationEmailHtml,
  });
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Add CORS headers for better compatibility
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  console.log('Newsletter subscription request received');
  console.log('Environment check:', {
    hasBusinessEmail: !!process.env.BUSINESS_EMAIL,
    hasAppPass: !!process.env.APP_PASS,
    hasKvUrl: !!process.env.KV_REST_API_URL,
    hasKvToken: !!process.env.KV_REST_API_TOKEN
  });

  try {
    const { email }: NewsletterData = req.body;
    console.log('Processing subscription for email:', email);

    // Validate email
    if (!email || !isValidEmail(email)) {
      console.log('Invalid email provided:', email);
      return res.status(400).json({
        message: 'Valid email address is required',
        success: false
      });
    }

    // Read existing subscribers
    const subscribers = await readSubscribers();

    // Check if email already exists
    const existingSubscriber = subscribers.find(sub => sub.email.toLowerCase() === email.toLowerCase());

    if (existingSubscriber) {
      if (existingSubscriber.isActive) {
        return res.status(400).json({
          message: 'Email is already subscribed to our newsletter',
          success: false
        });
      } else {
        // Reactivate subscription
        existingSubscriber.isActive = true;
        existingSubscriber.subscribedAt = new Date().toISOString();
      }
    } else {
      // Add new subscriber
      const newSubscriber: NewsletterSubscription = {
        email: email.toLowerCase(),
        subscribedAt: new Date().toISOString(),
        isActive: true
      };
      subscribers.push(newSubscriber);
    }

    // Save subscribers
    await writeSubscribers(subscribers);

    // Send confirmation email
    try {
      await sendConfirmationEmail(email);
      console.log('Newsletter confirmation email sent to:', email);
    } catch (emailError) {
      console.error('Failed to send confirmation email:', getErrorMessage(emailError));
      // Continue even if email fails - subscription is still saved
    }

    console.log('Newsletter subscription successful for:', email);
    res.status(200).json({ 
      message: 'Successfully subscribed to newsletter! Check your email for confirmation.',
      success: true 
    });

  } catch (error) {
    const errorMessage = getErrorMessage(error);
    console.error('Newsletter subscription error:', errorMessage);
    console.error('Error stack:', error);

    // Provide more specific error messages based on the error type
    let userMessage = 'Failed to subscribe to newsletter. Please try again.';

    if (errorMessage.includes('Email configuration missing')) {
      userMessage = 'Email service is temporarily unavailable. Please try again later.';
    } else if (errorMessage.includes('KV')) {
      userMessage = 'Database service is temporarily unavailable. Please try again later.';
    }

    res.status(500).json({
      message: userMessage,
      success: false,
      debug: process.env.NODE_ENV === 'development' ? errorMessage : undefined
    });
  }
}
