import React from 'react';

interface ReputationManagementSectionProps {
  className?: string;
}

const ReputationManagementSection: React.FC<ReputationManagementSectionProps> = ({ className = '' }) => {
  const services: string[] = [
    "Monitor and Respond to Feedback",
    "Enhance Positive Content Visibility", 
    "Leverage Social Proof",
    "Engage in Reputation Management Campaigns"
  ];

  const handleBookCall = (): void => {
    window.location.href = '/contact-us';
  };

  const handleViewWork = (): void => {
    window.location.href = '/case-studies';
  };

  return (
    <section className={`w-full bg-blue-50 min-h-screen flex items-center ${className}`}>
      <div className="container mx-auto px-4 md:px-0 py-8 sm:py-16">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-12 items-center">
          {/* Left Column - Text Content */}
          <div className="lg:col-span-4 order-1">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-4 sm:mb-6">
              Expert Online Reputation Management Services to Boost Your Digital Presence
            </h2>
            
            <p className="text-base sm:text-lg text-gray-700 mb-4 sm:mb-6">
              At Vesa Solutions, we specialize in online reputation management services designed to enhance your digital presence and boost keyword rankings in Canada and Albania. Our tailored strategies include monitoring and responding to online reviews, creating high-quality, SEO-optimized content, and managing social media engagement to build trust and credibility.
            </p>
            
            <p className="text-base sm:text-lg text-gray-700 mb-4 sm:mb-6">
              By leveraging advanced SEO techniques and data-driven insights, we ensure your business stands out in search results, helping you attract more customers and maintain a positive online image.
            </p>
            
            <p className="text-base sm:text-lg text-gray-700">
              Whether you&apos;re looking to recover from negative feedback or improve your brand visibility, our expert team is here to elevate your digital reputation.
            </p>
          </div>
          
          {/* Middle Column - Capsule Image Display */}
          <div className="lg:col-span-4 flex justify-center order-3 lg:order-2">
            <div className="relative h-[400px] sm:h-[500px] lg:h-[650px] w-full max-w-sm sm:max-w-md lg:max-w-md">

              {/* Capsule 1 */}
              <div className="absolute left-[30px] sm:left-[45px] lg:left-[20px] top-[120px] sm:top-[150px] lg:top-44 h-[200px] sm:h-[280px] lg:h-[380px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10"
                   style={{
                     backgroundImage: "url('/homepage/colleagues-working-desk.jpg')",
                     backgroundSize: "500px 650px",
                     backgroundPosition: "calc(-30px + 0px) calc(-120px + 0px)",
                     backgroundRepeat: "no-repeat"
                   }}>
              </div>

              {/* Capsule 2 */}
              <div className="absolute left-[90px] sm:left-[125px] lg:left-[120px] top-[20px] sm:top-[30px] lg:top-9 h-[320px] sm:h-[420px] lg:h-[580px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10"
                   style={{
                     backgroundImage: "url('/homepage/colleagues-working-desk.jpg')",
                     backgroundSize: "500px 650px",
                     backgroundPosition: "calc(-90px + 0px) calc(-20px + 0px)",
                     backgroundRepeat: "no-repeat"
                   }}>
              </div>

              {/* Capsule 3 */}
              <div className="absolute left-[150px] sm:left-[205px] lg:left-[220px] top-[60px] sm:top-[80px] lg:top-28 h-[300px] sm:h-[400px] lg:h-[560px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10"
                   style={{
                     backgroundImage: "url('/homepage/colleagues-working-desk.jpg')",
                     backgroundSize: "500px 650px",
                     backgroundPosition: "calc(-150px + 0px) calc(-60px + 0px)",
                     backgroundRepeat: "no-repeat"
                   }}>
              </div>

              {/* Capsule 4 */}
              <div className="absolute left-[210px] sm:left-[285px] lg:left-[320px] top-[40px] sm:top-[50px] lg:top-20 h-[280px] sm:h-[350px] lg:h-[480px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10"
                   style={{
                     backgroundImage: "url('/homepage/colleagues-working-desk.jpg')",
                     backgroundSize: "500px 650px",
                     backgroundPosition: "calc(-210px + 0px) calc(-40px + 0px)",
                     backgroundRepeat: "no-repeat"
                   }}>
              </div>

              {/* Capsule 5 */}
              <div className="absolute left-[270px] sm:left-[365px] lg:left-[420px] top-[100px] sm:top-[120px] lg:top-40 h-[280px] sm:h-[350px] lg:h-[480px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10"
                   style={{
                     backgroundImage: "url('/homepage/colleagues-working-desk.jpg')",
                     backgroundSize: "500px 650px",
                     backgroundPosition: "calc(-270px + 0px) calc(-100px + 0px)",
                     backgroundRepeat: "no-repeat"
                   }}>
              </div>

              {/* Circular reputation management badge */}
              <div className="absolute top-[40px] sm:top-[60px] lg:top-24 left-2 sm:left-3 lg:left-4 bg-white rounded-full h-20 w-20 sm:h-24 sm:w-24 lg:h-32 lg:w-32 flex items-center justify-center z-30 shadow-md">
                <div className="relative h-full w-full">
                  <svg viewBox="0 0 100 100" width="100%" height="100%">
                    <defs>
                      <path id="circle-path" d="M 50,50 m -37,0 a 37,37 0 1,1 74,0 a 37,37 0 1,1 -74,0" />
                    </defs>
                    <text fontSize="11" fill="#0084FF" fontWeight="600" letterSpacing="1" className="hidden lg:block">
                      <textPath xlinkHref="#circle-path" startOffset="0%">
                        REPUTATION • MANAGEMENT • SERVICES •
                      </textPath>
                    </text>
                    <text fontSize="9" fill="#0084FF" fontWeight="600" letterSpacing="1" className="block sm:hidden">
                      <textPath xlinkHref="#circle-path" startOffset="0%">
                        REPUTATION • MANAGEMENT •
                      </textPath>
                    </text>
                    <text fontSize="10" fill="#0084FF" fontWeight="600" letterSpacing="1" className="hidden sm:block lg:hidden">
                      <textPath xlinkHref="#circle-path" startOffset="0%">
                        REPUTATION • MANAGEMENT • SERVICES •
                      </textPath>
                    </text>
                    <circle cx="50" cy="50" r="18" fill="white" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
          
          {/* Right Column - Services & CTA */}
          <div className="lg:col-span-4 lg:pl-10 order-2 lg:order-3">
            <p className="text-blue-700 font-medium text-base sm:text-lg mb-3">WE&apos;RE HERE TO HELP</p>
            
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-6 sm:mb-8">
              REPAIR YOUR ONLINE REPUTATION
            </h2>
            
            <ul className="mb-8 sm:mb-10 space-y-4 sm:space-y-5">
              {services.map((service, index) => (
                <li key={index} className="flex items-center">
                  <svg className="h-5 w-5 sm:h-6 sm:w-6 text-blue-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                  </svg>
                  <span className="text-gray-700 text-base sm:text-lg">{service}</span>
                </li>
              ))}
            </ul>
            
            <div className="flex flex-col sm:flex-row lg:flex-col xl:flex-row gap-4 sm:gap-5">
              <button
                onClick={handleBookCall}
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-full px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg transition duration-300 w-full sm:w-auto"
              >
                BOOK YOUR CALL
              </button>
              
              <button 
                onClick={handleViewWork}
                className="bg-blue-100 hover:bg-blue-200 text-blue-800 font-medium rounded-full px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg transition duration-300 w-full sm:w-auto"
              >
                VIEW OUR WORK
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ReputationManagementSection;