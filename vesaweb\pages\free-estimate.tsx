// pages/free-estimate.tsx
import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { 
  CheckCircle, 
  Clock, 
  Shield,
  Phone,
  Mail,
  MapPin,
  ChevronDown
} from 'lucide-react';
import Header from '../components/global/Header';
import Footer from '../components/global/Footer';
import ServiceContactForm from '@/components/global/Form';

interface FormData {
  businessName: string;
  fullName: string;
  email: string;
  phone: string;
  location: string;
  website?: string;
  message: string;
  service: string;
  userCountry?: string;
  timestamp?: string;
  selectedService?: string;
  isCustomService?: boolean;
  customServiceDescription?: string;
  type?: string;
  subject?: string;
}

const FreeEstimatePage: React.FC = () => {
  const [selectedService, setSelectedService] = useState<string>('');
  const [customService, setCustomService] = useState<string>('');
  const [initialWebsiteUrl, setInitialWebsiteUrl] = useState<string>('');

  // Check for website URL from home page hero section
  useEffect(() => {
    const storedUrl = sessionStorage.getItem('websiteUrl');
    if (storedUrl) {
      setInitialWebsiteUrl(storedUrl);
      // Clear it from sessionStorage after using it
      sessionStorage.removeItem('websiteUrl');
    }
  }, []);

  const services = [
    { name: 'SEO (Search Engine Optimization)', type: 'seo' as const },
    { name: 'Local SEO', type: 'local-seo' as const },
    { name: 'Web Design & Development', type: 'web-design' as const },
    { name: 'PPC Advertising (Google Ads)', type: 'ppc' as const },
    { name: 'Social Media Marketing', type: 'social-media' as const },
    { name: 'Content Marketing & Writing', type: 'seo' as const },
    { name: 'Email Marketing', type: 'seo' as const },
    { name: 'Conversion Rate Optimization', type: 'seo' as const },
    { name: 'Online Reputation Management', type: 'seo' as const },
    { name: 'Google My Business Optimization', type: 'local-seo' as const },
    { name: 'E-commerce Development', type: 'web-design' as const },
    { name: 'WordPress Development', type: 'web-design' as const },
    { name: 'Graphic Design & Branding', type: 'web-design' as const },
    { name: 'Video Marketing', type: 'social-media' as const },
    { name: 'Influencer Marketing', type: 'social-media' as const },
    { name: 'Marketing Automation', type: 'seo' as const },
    { name: 'Analytics & Reporting', type: 'seo' as const },
    { name: 'Other (please specify)', type: 'seo' as const }
  ];

  const handleServiceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedService(e.target.value);
    if (e.target.value !== 'Other (please specify)') {
      setCustomService('');
    }
  };

  const getServiceConfig = () => {
    if (selectedService === 'Other (please specify)') {
      return {
        name: customService || 'Custom Service',
        type: 'seo' as const,
        ctaText: 'Request Custom Estimate',
        messagePlaceholder: `Tell us more about your ${customService.toLowerCase()} project and specific requirements*`,
        benefits: [
          'Custom analysis of your specific needs',
          'Honest assessment of project feasibility',
          'Recommendation of trusted partners if needed',
          'No-obligation consultation and advice'
        ]
      };
    }

    const service = services.find(s => s.name === selectedService);
    if (!service) {
      return {
        name: 'Free Estimate',
        type: 'seo' as const,
        ctaText: 'Get My Free Estimate',
        messagePlaceholder: 'Tell us about your project and goals*',
        benefits: [
          'Custom pricing tailored to your business',
          '24-hour response with detailed estimate',
          'Free consultation and strategy discussion',
          'No obligations or pressure sales tactics'
        ]
      };
    }

    // Custom configurations for different service types
    const configs = {
      'seo': {
        ctaText: 'Get My Free SEO Analysis',
        messagePlaceholder: 'Tell us about your website and SEO goals*',
        benefits: [
          'Complete SEO audit & competitor analysis',
          'Keyword opportunities & content strategy',
          'Technical SEO recommendations',
          'Custom pricing for your specific needs'
        ]
      },
      'local-seo': {
        ctaText: 'Get My Free Local SEO Audit',
        messagePlaceholder: 'Tell us about your local business and target areas*',
        benefits: [
          'Local SEO audit & competitor analysis',
          'Google My Business optimization plan',
          'Local keyword opportunities',
          'Citation audit & local link building strategy'
        ]
      },
      'ppc': {
        ctaText: 'Get My Free PPC Analysis',
        messagePlaceholder: 'Tell us about your advertising goals and current campaigns*',
        benefits: [
          'PPC account audit & optimization plan',
          'Keyword research & bid strategy',
          'Ad copy and landing page recommendations',
          'Custom budget and ROI projections'
        ]
      },
      'web-design': {
        ctaText: 'Get My Free Web Design Quote',
        messagePlaceholder: 'Tell us about your website project and design preferences*',
        benefits: [
          'Custom design & development estimate',
          'User experience recommendations',
          'Technology stack suggestions',
          'Timeline and milestone planning'
        ]
      },
      'social-media': {
        ctaText: 'Get My Free Social Media Strategy',
        messagePlaceholder: 'Tell us about your brand and social media goals*',
        benefits: [
          'Social media audit & strategy plan',
          'Content recommendations & calendar',
          'Platform-specific optimization',
          'Engagement and growth strategies'
        ]
      }
    };

    const config = configs[service.type] || configs.seo;

    return {
      name: service.name,
      type: service.type,
      ...config
    };
  };

const handleFormSubmit = async (data: FormData) => {
    try {
      console.log('Estimate form submitted:', data);
      
      const submissionData = {
        ...data,
        selectedService: selectedService,
        isCustomService: selectedService === 'Other (please specify)',
        customServiceDescription: customService,
        type: 'free-estimate',
        subject: `Free Estimate Request - ${selectedService === 'Other (please specify)' ? customService : selectedService}`
      };

      // Send to API
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to submit form');
      }

      if (!result.success) {
        throw new Error(result.message || 'Form submission failed');
      }

      console.log('Form submitted successfully:', result);
      
    } catch (error) {
      console.error('Form submission error:', error);
      throw error; // Let the form component handle the error display
    }
  };

  return (
    <>
      <Head>
        <title>Get Your Free Digital Marketing Estimate | VESA Solutions</title>
        <meta name="description" content="Get a free, no-obligation estimate for your digital marketing project. Custom pricing for SEO, web design, PPC, and social media marketing services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://vesasolutions.com/free-estimate" />
      </Head>
      
      <Header />
      
      {/* Breadcrumbs */}
      <div className="bg-gray-50 py-4">
        <div className="max-w-7xl mx-auto px-6">
          <nav className="text-sm" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              <li>
                <Link href="/" className="text-gray-500 hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-gray-900 font-medium" aria-current="page">
                Free Estimate
              </li>
            </ol>
          </nav>
        </div>
      </div>
      
      <main className="min-h-screen bg-white">
        {/* Simple Header */}
        <section className="py-12 bg-white">
          <div className="max-w-4xl mx-auto px-6 text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Request Your Free Estimate
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              Get custom pricing for your digital marketing needs. No obligations, fast response.
            </p>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-8">
          <div className="max-w-6xl mx-auto px-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
              
              {/* Left Column - Info */}
              <div className="flex flex-col items-center lg:items-start">
                <div className="w-full max-w-lg lg:max-w-none bg-blue-50 rounded-lg p-6 mb-8">
                  <h2 className="text-xl font-bold text-gray-900 mb-4 text-center lg:text-left">What You Get:</h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-center lg:justify-start">
                      <CheckCircle className="text-green-600 mr-3 flex-shrink-0" size={20} />
                      <span className="text-gray-700 text-center lg:text-left">Custom pricing tailored to your project</span>
                    </div>
                    <div className="flex items-center justify-center lg:justify-start">
                      <Clock className="text-blue-600 mr-3 flex-shrink-0" size={20} />
                      <span className="text-gray-700 text-center lg:text-left">24-hour response with detailed estimate</span>
                    </div>
                    <div className="flex items-center justify-center lg:justify-start">
                      <Shield className="text-purple-600 mr-3 flex-shrink-0" size={20} />
                      <span className="text-gray-700 text-center lg:text-left">Honest assessment of project feasibility</span>
                    </div>
                  </div>
                </div>

                {/* Services */}
                <div className="w-full max-w-lg lg:max-w-none mb-8">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center lg:text-left">Our Core Services:</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
                    <div className="flex items-center justify-center lg:justify-start">
                      <CheckCircle className="text-green-600 mr-2 flex-shrink-0" size={16} />
                      <span className="text-gray-700 text-sm">SEO & Local SEO</span>
                    </div>
                    <div className="flex items-center justify-center lg:justify-start">
                      <CheckCircle className="text-green-600 mr-2 flex-shrink-0" size={16} />
                      <span className="text-gray-700 text-sm">PPC & Google Ads</span>
                    </div>
                    <div className="flex items-center justify-center lg:justify-start">
                      <CheckCircle className="text-green-600 mr-2 flex-shrink-0" size={16} />
                      <span className="text-gray-700 text-sm">Web Design & Development</span>
                    </div>
                    <div className="flex items-center justify-center lg:justify-start">
                      <CheckCircle className="text-green-600 mr-2 flex-shrink-0" size={16} />
                      <span className="text-gray-700 text-sm">Social Media Marketing</span>
                    </div>
                    <div className="flex items-center justify-center lg:justify-start">
                      <CheckCircle className="text-green-600 mr-2 flex-shrink-0" size={16} />
                      <span className="text-gray-700 text-sm">Content Marketing</span>
                    </div>
                    <div className="flex items-center justify-center lg:justify-start">
                      <CheckCircle className="text-green-600 mr-2 flex-shrink-0" size={16} />
                      <span className="text-gray-700 text-sm">Email Marketing</span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 text-center lg:text-left px-4 lg:px-0">
                    Don&apos;t see what you need? Select &quot;Other&quot; and tell us about your project. 
                    We&apos;ll let you know if it&apos;s something we can help with or recommend trusted partners.
                  </p>
                </div>

                {/* Contact Info */}
                <div className="w-full max-w-lg lg:max-w-none bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center lg:text-left">Prefer to Call?</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-center lg:justify-start">
                      <Phone className="text-blue-600 mr-3 flex-shrink-0" size={18} />
                      <span className="text-gray-700">+355 69 404 6408</span>
                    </div>
                    <div className="flex items-center justify-center lg:justify-start">
                      <Mail className="text-blue-600 mr-3 flex-shrink-0" size={18} />
                      <span className="text-gray-700"><EMAIL></span>
                    </div>
                    <div className="flex items-center justify-center lg:justify-start">
                      <MapPin className="text-blue-600 mr-3 flex-shrink-0" size={18} />
                      <span className="text-gray-700 text-center lg:text-left">Durrës, Albania</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column - Service Selection and Form */}
              <div className="space-y-6">
                
                {/* Service Selection */}
                <div className="bg-white border border-gray-200 rounded-2xl p-6 shadow-lg">
                  <h2 className="text-xl font-bold text-gray-900 mb-4">Select Your Service</h2>
                  
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
                        What service do you need? *
                      </label>
                      <div className="relative">
                        <select
                          id="service"
                          value={selectedService}
                          onChange={handleServiceChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white"
                        >
                          <option value="">Choose a service...</option>
                          {services.map((service) => (
                            <option key={service.name} value={service.name}>
                              {service.name}
                            </option>
                          ))}
                        </select>
                        <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                      </div>
                    </div>

                    {/* Custom Service Input */}
                    {selectedService === 'Other (please specify)' && (
                      <div>
                        <label htmlFor="customService" className="block text-sm font-medium text-gray-700 mb-2">
                          Please specify your service needs *
                        </label>
                        <input
                          type="text"
                          id="customService"
                          value={customService}
                          onChange={(e) => setCustomService(e.target.value)}
                          placeholder="Describe the service you need..."
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <p className="text-sm text-gray-500 mt-2">
                          We&apos;ll let you know if this is something we can help with or recommend trusted partners.
                        </p>
                      </div>
                    )}

                    {/* Selected Service Confirmation */}
                    {selectedService && (
                      <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-1">
                          Selected: {selectedService === 'Other (please specify)' ? (customService || 'Custom Service') : selectedService}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {selectedService === 'Other (please specify)' 
                            ? 'We\'ll assess your custom requirements and provide honest feedback on feasibility.'
                            : 'Fill out the form below to get your custom estimate for this service.'
                          }
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Contact Form */}
                <div>
                  <ServiceContactForm
                    service={getServiceConfig()}
                    onSubmit={handleFormSubmit}
                    initialWebsite={initialWebsiteUrl}
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Simple Process Section */}
        <section className="bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-6">
            <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">
              How It Works
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="font-bold">1</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Select Service</h3>
                <p className="text-gray-600 text-sm">Choose your service and fill out our detailed estimate form.</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="font-bold">2</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">We Analyze & Estimate</h3>
                <p className="text-gray-600 text-sm">Our team reviews your needs and creates a custom estimate within 24 hours.</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="font-bold">3</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Strategy Discussion</h3>
                <p className="text-gray-600 text-sm">We present your estimate and discuss the best approach for your goals.</p>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </>
  );
};

export default FreeEstimatePage;