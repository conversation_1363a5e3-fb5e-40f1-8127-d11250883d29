// components/home/<USER>
import React from 'react';

interface WhyChooseVesaSectionProps {
  className?: string;
}

const WhyChooseVesaSection: React.FC<WhyChooseVesaSectionProps> = ({ className = '' }) => {
  const handleAboutUs = (): void => {
    window.location.href = '/about';
  };

  return (
    <section className={`w-full bg-blue-50 min-h-screen flex items-center ${className}`}>
      <div className="container mx-auto px-4 md:px-0 py-8 sm:py-16">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-12 items-center">
          {/* Left Column - Capsule Image Display */}
          <div className="lg:col-span-6 flex justify-center order-2 lg:order-1">
            <div className="relative h-[400px] sm:h-[500px] lg:h-[650px] w-full max-w-sm sm:max-w-md lg:max-w-lg">

              
              {/* Capsule 1 */}
              <div className="absolute left-[30px] sm:left-[45px] lg:left-[20px] top-[120px] sm:top-[150px] lg:top-44 h-[200px] sm:h-[280px] lg:h-[380px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10"
                   style={{
                     backgroundImage: "url('/homepage/map-lying-wooden-table.jpg')",
                     backgroundSize: "500px 650px",
                     backgroundPosition: "calc(-30px + 0px) calc(-120px + 0px)",
                     backgroundRepeat: "no-repeat"
                   }}>
              </div>

              {/* Capsule 2 */}
              <div className="absolute left-[90px] sm:left-[125px] lg:left-[120px] top-[20px] sm:top-[30px] lg:top-9 h-[320px] sm:h-[420px] lg:h-[580px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10"
                   style={{
                     backgroundImage: "url('/homepage/map-lying-wooden-table.jpg')",
                     backgroundSize: "500px 650px",
                     backgroundPosition: "calc(-90px + 0px) calc(-20px + 0px)",
                     backgroundRepeat: "no-repeat"
                   }}>
              </div>

              {/* Capsule 3 */}
              <div className="absolute left-[150px] sm:left-[205px] lg:left-[220px] top-[60px] sm:top-[80px] lg:top-28 h-[300px] sm:h-[400px] lg:h-[560px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10"
                   style={{
                     backgroundImage: "url('/homepage/map-lying-wooden-table.jpg')",
                     backgroundSize: "500px 650px",
                     backgroundPosition: "calc(-150px + 0px) calc(-60px + 0px)",
                     backgroundRepeat: "no-repeat"
                   }}>
              </div>

              {/* Capsule 4 */}
              <div className="absolute left-[210px] sm:left-[285px] lg:left-[320px] top-[40px] sm:top-[50px] lg:top-20 h-[280px] sm:h-[350px] lg:h-[480px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10"
                   style={{
                     backgroundImage: "url('/homepage/map-lying-wooden-table.jpg')",
                     backgroundSize: "500px 650px",
                     backgroundPosition: "calc(-210px + 0px) calc(-40px + 0px)",
                     backgroundRepeat: "no-repeat"
                   }}>
              </div>

              {/* Capsule 5 */}
              <div className="absolute left-[270px] sm:left-[365px] lg:left-[420px] top-[100px] sm:top-[120px] lg:top-40 h-[280px] sm:h-[350px] lg:h-[480px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10"
                   style={{
                     backgroundImage: "url('/homepage/map-lying-wooden-table.jpg')",
                     backgroundSize: "500px 650px",
                     backgroundPosition: "calc(-270px + 0px) calc(-100px + 0px)",
                     backgroundRepeat: "no-repeat"
                   }}>
              </div>
      
              {/* Circular digital marketing badge with complete text around */}
              <div className="absolute top-[40px] sm:top-[60px] lg:top-24 left-2 sm:left-3 lg:left-4 bg-white rounded-full h-20 w-20 sm:h-24 sm:w-24 lg:h-32 lg:w-32 flex items-center justify-center z-30 shadow-md">
                <div className="relative h-full w-full">
                  <svg viewBox="0 0 100 100" width="100%" height="100%">
                    <defs>
                      <path id="circle-path-2" d="M 50,50 m -37,0 a 37,37 0 1,1 74,0 a 37,37 0 1,1 -74,0" />
                    </defs>
                    <text fontSize="11" fill="#0084FF" fontWeight="600" letterSpacing="1" className="hidden lg:block">
                      <textPath xlinkHref="#circle-path-2" startOffset="0%">
                        DIGITAL • MARKETING • SOLUTIONS •
                      </textPath>
                    </text>
                    <text fontSize="9" fill="#0084FF" fontWeight="600" letterSpacing="1" className="block sm:hidden">
                      <textPath xlinkHref="#circle-path-2" startOffset="0%">
                        DIGITAL • MARKETING • SOLUTIONS •
                      </textPath>
                    </text>
                    <text fontSize="10" fill="#0084FF" fontWeight="600" letterSpacing="1" className="hidden sm:block lg:hidden">
                      <textPath xlinkHref="#circle-path-2" startOffset="0%">
                        DIGITAL • MARKETING • SOLUTIONS •
                      </textPath>
                    </text>
                    <circle cx="50" cy="50" r="18" fill="white" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
          
          {/* Right Column - Content */}
          <div className="lg:col-span-6 lg:pl-10 order-1 lg:order-2">            
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-6 sm:mb-8 leading-tight">
              WHY CHOOSE VESA FOR YOUR DIGITAL MARKETING AGENCY?
            </h2>
            
            <div className="space-y-4 sm:space-y-6 mb-6 sm:mb-8">
              <p className="text-gray-700 text-base sm:text-lg leading-relaxed">
                Vesa is a full-service digital marketing agency. We&apos;ve been providing a wide range of services to clients of all industries since 2005. Our digital marketing services include consulting and management options for a variety of online marketing tactics including search engine optimization (SEO), pay-per-click (PPC) ads, Amazon store optimization, copywriting, conversion rate optimization (CRO), and more. We also offer expert web design and development services for both eCommerce and B2B companies. Don&apos;t just partner with any digital marketing agency; work with a company you can trust.
              </p>
            </div>
            
            <h3 className="text-lg sm:text-xl font-bold text-gray-800 mb-3 sm:mb-4">
              Meet our team of digital marketing experts
            </h3>
            
            <p className="text-gray-700 text-base sm:text-lg leading-relaxed mb-8 sm:mb-10">
              You might have a website, but it isn&apos;t doing your business much good if it isn&apos;t being picked up by the search engines. To make sure customers find your site online, we can take your online presence to the next level — your website design, copywriting, keywords, social media presence and more. Working with us, you get the most from your return on marketing investment.
            </p>
            
            <button
              onClick={handleAboutUs}
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-full px-8 sm:px-10 py-3 sm:py-4 text-base sm:text-lg transition-colors shadow-lg w-full sm:w-auto"
            >
              ABOUT US
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseVesaSection;