// SubServiceHero.tsx - Fixed with complete icon support
import React from 'react'
import { IconRenderer } from '@/components/global/IconRender'
import { SubServiceHero as SubServiceHeroType } from '@/types/subService'

interface SubServiceHeroProps {
  data: SubServiceHeroType
}

export const SubServiceHero: React.FC<SubServiceHeroProps> = ({ data }) => {
  return (
    <section className={`bg-gradient-to-r ${data.backgroundGradient || 'from-blue-600 to-blue-800'} py-16`}>
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center">
          {data.badgeText && (
            <div className="inline-flex items-center bg-blue-500 text-white text-sm font-semibold px-4 py-2 rounded-full mb-6">
              <IconRenderer iconName={data.badgeIcon} size={16} className="mr-2" />
              {data.badgeText}
            </div>
          )}

          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            {data.title}
          </h1>

          {data.subtitle && (
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
              {data.subtitle}
            </p>
          )}

          {data.stats && data.stats.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              {data.stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl font-bold text-blue-200 mb-1">
                    {stat.value}
                  </div>
                  <div className="text-blue-100 text-sm">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  )
}