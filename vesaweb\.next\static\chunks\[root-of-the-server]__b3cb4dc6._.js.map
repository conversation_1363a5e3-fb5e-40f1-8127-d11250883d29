{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\r\n\r\ntype SendMessage = (msg: any) => void;\r\nexport type WebSocketMessage =\r\n  | {\r\n      type: \"turbopack-connected\";\r\n    }\r\n  | {\r\n      type: \"turbopack-message\";\r\n      data: Record<string, any>;\r\n    };\r\n\r\n\r\nexport type ClientOptions = {\r\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void;\r\n  sendMessage: SendMessage;\r\n  onUpdateError: (err: unknown) => void;\r\n};\r\n\r\nexport function connect({\r\n  addMessageListener,\r\n  sendMessage,\r\n  onUpdateError = console.error,\r\n}: ClientOptions) {\r\n  addMessageListener((msg) => {\r\n    switch (msg.type) {\r\n      case \"turbopack-connected\":\r\n        handleSocketConnected(sendMessage);\r\n        break;\r\n      default:\r\n        try {\r\n          if (Array.isArray(msg.data)) {\r\n            for (let i = 0; i < msg.data.length; i++) {\r\n              handleSocketMessage(msg.data[i] as ServerMessage);\r\n            }\r\n          } else {\r\n            handleSocketMessage(msg.data as ServerMessage);\r\n          }\r\n          applyAggregatedUpdates();\r\n        } catch (e: unknown) {\r\n          console.warn(\r\n            \"[Fast Refresh] performing full reload\\n\\n\" +\r\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\r\n              \"You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n\" +\r\n              \"Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n\" +\r\n              \"It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n\" +\r\n              \"Fast Refresh requires at least one parent function component in your React tree.\"\r\n          );\r\n          onUpdateError(e);\r\n          location.reload();\r\n        }\r\n        break;\r\n    }\r\n  });\r\n\r\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS;\r\n  if (queued != null && !Array.isArray(queued)) {\r\n    throw new Error(\"A separate HMR handler was already registered\");\r\n  }\r\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\r\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    },\r\n  };\r\n\r\n  if (Array.isArray(queued)) {\r\n    for (const [chunkPath, callback] of queued) {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    }\r\n  }\r\n}\r\n\r\ntype UpdateCallbackSet = {\r\n  callbacks: Set<UpdateCallback>;\r\n  unsubscribe: () => void;\r\n};\r\n\r\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map();\r\n\r\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\r\n  sendMessage(JSON.stringify(message));\r\n}\r\n\r\ntype ResourceKey = string;\r\n\r\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\r\n  return JSON.stringify({\r\n    path: resource.path,\r\n    headers: resource.headers || null,\r\n  });\r\n}\r\n\r\nfunction subscribeToUpdates(\r\n  sendMessage: SendMessage,\r\n  resource: ResourceIdentifier\r\n): () => void {\r\n  sendJSON(sendMessage, {\r\n    type: \"turbopack-subscribe\",\r\n    ...resource,\r\n  });\r\n\r\n  return () => {\r\n    sendJSON(sendMessage, {\r\n      type: \"turbopack-unsubscribe\",\r\n      ...resource,\r\n    });\r\n  };\r\n}\r\n\r\nfunction handleSocketConnected(sendMessage: SendMessage) {\r\n  for (const key of updateCallbackSets.keys()) {\r\n    subscribeToUpdates(sendMessage, JSON.parse(key));\r\n  }\r\n}\r\n\r\n// we aggregate all pending updates until the issues are resolved\r\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\r\n  new Map();\r\n\r\nfunction aggregateUpdates(msg: PartialServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  let aggregated = chunkListsWithPendingUpdates.get(key);\r\n\r\n  if (aggregated) {\r\n    aggregated.instruction = mergeChunkListUpdates(\r\n      aggregated.instruction,\r\n      msg.instruction\r\n    );\r\n  } else {\r\n    chunkListsWithPendingUpdates.set(key, msg);\r\n  }\r\n}\r\n\r\nfunction applyAggregatedUpdates() {\r\n  if (chunkListsWithPendingUpdates.size === 0) return;\r\n  hooks.beforeRefresh();\r\n  for (const msg of chunkListsWithPendingUpdates.values()) {\r\n    triggerUpdate(msg);\r\n  }\r\n  chunkListsWithPendingUpdates.clear();\r\n  finalizeUpdate();\r\n}\r\n\r\nfunction mergeChunkListUpdates(\r\n  updateA: ChunkListUpdate,\r\n  updateB: ChunkListUpdate\r\n): ChunkListUpdate {\r\n  let chunks;\r\n  if (updateA.chunks != null) {\r\n    if (updateB.chunks == null) {\r\n      chunks = updateA.chunks;\r\n    } else {\r\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks);\r\n    }\r\n  } else if (updateB.chunks != null) {\r\n    chunks = updateB.chunks;\r\n  }\r\n\r\n  let merged;\r\n  if (updateA.merged != null) {\r\n    if (updateB.merged == null) {\r\n      merged = updateA.merged;\r\n    } else {\r\n      // Since `merged` is an array of updates, we need to merge them all into\r\n      // one, consistent update.\r\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\r\n      // no need to key on the `type` field.\r\n      let update = updateA.merged[0];\r\n      for (let i = 1; i < updateA.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateA.merged[i]\r\n        );\r\n      }\r\n\r\n      for (let i = 0; i < updateB.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateB.merged[i]\r\n        );\r\n      }\r\n\r\n      merged = [update];\r\n    }\r\n  } else if (updateB.merged != null) {\r\n    merged = updateB.merged;\r\n  }\r\n\r\n  return {\r\n    type: \"ChunkListUpdate\",\r\n    chunks,\r\n    merged,\r\n  };\r\n}\r\n\r\nfunction mergeChunkListChunks(\r\n  chunksA: Record<ChunkPath, ChunkUpdate>,\r\n  chunksB: Record<ChunkPath, ChunkUpdate>\r\n): Record<ChunkPath, ChunkUpdate> {\r\n  const chunks: Record<ChunkPath, ChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB);\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeChunkUpdates(\r\n  updateA: ChunkUpdate,\r\n  updateB: ChunkUpdate\r\n): ChunkUpdate | undefined {\r\n  if (\r\n    (updateA.type === \"added\" && updateB.type === \"deleted\") ||\r\n    (updateA.type === \"deleted\" && updateB.type === \"added\")\r\n  ) {\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"partial\") {\r\n    invariant(updateA.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  if (updateB.type === \"partial\") {\r\n    invariant(updateB.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction mergeChunkListEcmascriptMergedUpdates(\r\n  mergedA: EcmascriptMergedUpdate,\r\n  mergedB: EcmascriptMergedUpdate\r\n): EcmascriptMergedUpdate {\r\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries);\r\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks);\r\n\r\n  return {\r\n    type: \"EcmascriptMergedUpdate\",\r\n    entries,\r\n    chunks,\r\n  };\r\n}\r\n\r\nfunction mergeEcmascriptChunkEntries(\r\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\r\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\r\n): Record<ModuleId, EcmascriptModuleEntry> {\r\n  return { ...entriesA, ...entriesB };\r\n}\r\n\r\nfunction mergeEcmascriptChunksUpdates(\r\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\r\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\r\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\r\n  if (chunksA == null) {\r\n    return chunksB;\r\n  }\r\n\r\n  if (chunksB == null) {\r\n    return chunksA;\r\n  }\r\n\r\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\r\n        chunkUpdateA,\r\n        chunkUpdateB\r\n      );\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  if (Object.keys(chunks).length === 0) {\r\n    return undefined;\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeEcmascriptChunkUpdates(\r\n  updateA: EcmascriptMergedChunkUpdate,\r\n  updateB: EcmascriptMergedChunkUpdate\r\n): EcmascriptMergedChunkUpdate | undefined {\r\n  if (updateA.type === \"added\" && updateB.type === \"deleted\") {\r\n    // These two completely cancel each other out.\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"deleted\" && updateB.type === \"added\") {\r\n    const added = [];\r\n    const deleted = [];\r\n    const deletedModules = new Set(updateA.modules ?? []);\r\n    const addedModules = new Set(updateB.modules ?? []);\r\n\r\n    for (const moduleId of addedModules) {\r\n      if (!deletedModules.has(moduleId)) {\r\n        added.push(moduleId);\r\n      }\r\n    }\r\n\r\n    for (const moduleId of deletedModules) {\r\n      if (!addedModules.has(moduleId)) {\r\n        deleted.push(moduleId);\r\n      }\r\n    }\r\n\r\n    if (added.length === 0 && deleted.length === 0) {\r\n      return undefined;\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added,\r\n      deleted,\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"partial\") {\r\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])]);\r\n    const deleted = new Set([\r\n      ...(updateA.deleted ?? []),\r\n      ...(updateB.deleted ?? []),\r\n    ]);\r\n\r\n    if (updateB.added != null) {\r\n      for (const moduleId of updateB.added) {\r\n        deleted.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    if (updateB.deleted != null) {\r\n      for (const moduleId of updateB.deleted) {\r\n        added.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added: [...added],\r\n      deleted: [...deleted],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"added\" && updateB.type === \"partial\") {\r\n    const modules = new Set([\r\n      ...(updateA.modules ?? []),\r\n      ...(updateB.added ?? []),\r\n    ]);\r\n\r\n    for (const moduleId of updateB.deleted ?? []) {\r\n      modules.delete(moduleId);\r\n    }\r\n\r\n    return {\r\n      type: \"added\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"deleted\") {\r\n    // We could eagerly return `updateB` here, but this would potentially be\r\n    // incorrect if `updateA` has added modules.\r\n\r\n    const modules = new Set(updateB.modules ?? []);\r\n\r\n    if (updateA.added != null) {\r\n      for (const moduleId of updateA.added) {\r\n        modules.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"deleted\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  // Any other update combination is invalid.\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction invariant(_: never, message: string): never {\r\n  throw new Error(`Invariant: ${message}`);\r\n}\r\n\r\nconst CRITICAL = [\"bug\", \"error\", \"fatal\"];\r\n\r\nfunction compareByList(list: any[], a: any, b: any) {\r\n  const aI = list.indexOf(a) + 1 || list.length;\r\n  const bI = list.indexOf(b) + 1 || list.length;\r\n  return aI - bI;\r\n}\r\n\r\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map();\r\n\r\nfunction emitIssues() {\r\n  const issues = [];\r\n  const deduplicationSet = new Set();\r\n\r\n  for (const [_, chunkIssues] of chunksWithIssues) {\r\n    for (const chunkIssue of chunkIssues) {\r\n      if (deduplicationSet.has(chunkIssue.formatted)) continue;\r\n\r\n      issues.push(chunkIssue);\r\n      deduplicationSet.add(chunkIssue.formatted);\r\n    }\r\n  }\r\n\r\n  sortIssues(issues);\r\n\r\n  hooks.issues(issues);\r\n}\r\n\r\nfunction handleIssues(msg: ServerMessage): boolean {\r\n  const key = resourceKey(msg.resource);\r\n  let hasCriticalIssues = false;\r\n\r\n  for (const issue of msg.issues) {\r\n    if (CRITICAL.includes(issue.severity)) {\r\n      hasCriticalIssues = true;\r\n    }\r\n  }\r\n\r\n  if (msg.issues.length > 0) {\r\n    chunksWithIssues.set(key, msg.issues);\r\n  } else if (chunksWithIssues.has(key)) {\r\n    chunksWithIssues.delete(key);\r\n  }\r\n\r\n  emitIssues();\r\n\r\n  return hasCriticalIssues;\r\n}\r\n\r\nconst SEVERITY_ORDER = [\"bug\", \"fatal\", \"error\", \"warning\", \"info\", \"log\"];\r\nconst CATEGORY_ORDER = [\r\n  \"parse\",\r\n  \"resolve\",\r\n  \"code generation\",\r\n  \"rendering\",\r\n  \"typescript\",\r\n  \"other\",\r\n];\r\n\r\nfunction sortIssues(issues: Issue[]) {\r\n  issues.sort((a, b) => {\r\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity);\r\n    if (first !== 0) return first;\r\n    return compareByList(CATEGORY_ORDER, a.category, b.category);\r\n  });\r\n}\r\n\r\nconst hooks = {\r\n  beforeRefresh: () => {},\r\n  refresh: () => {},\r\n  buildOk: () => {},\r\n  issues: (_issues: Issue[]) => {},\r\n};\r\n\r\nexport function setHooks(newHooks: typeof hooks) {\r\n  Object.assign(hooks, newHooks);\r\n}\r\n\r\nfunction handleSocketMessage(msg: ServerMessage) {\r\n  sortIssues(msg.issues);\r\n\r\n  handleIssues(msg);\r\n\r\n  switch (msg.type) {\r\n    case \"issues\":\r\n      // issues are already handled\r\n      break;\r\n    case \"partial\":\r\n      // aggregate updates\r\n      aggregateUpdates(msg);\r\n      break;\r\n    default:\r\n      // run single update\r\n      const runHooks = chunkListsWithPendingUpdates.size === 0;\r\n      if (runHooks) hooks.beforeRefresh();\r\n      triggerUpdate(msg);\r\n      if (runHooks) finalizeUpdate();\r\n      break;\r\n  }\r\n}\r\n\r\nfunction finalizeUpdate() {\r\n  hooks.refresh();\r\n  hooks.buildOk();\r\n\r\n  // This is used by the Next.js integration test suite to notify it when HMR\r\n  // updates have been completed.\r\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\r\n  if (globalThis.__NEXT_HMR_CB) {\r\n    globalThis.__NEXT_HMR_CB();\r\n    globalThis.__NEXT_HMR_CB = null;\r\n  }\r\n}\r\n\r\nfunction subscribeToChunkUpdate(\r\n  chunkListPath: ChunkListPath,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n): () => void {\r\n  return subscribeToUpdate(\r\n    {\r\n      path: chunkListPath,\r\n    },\r\n    sendMessage,\r\n    callback\r\n  );\r\n}\r\n\r\nexport function subscribeToUpdate(\r\n  resource: ResourceIdentifier,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n) {\r\n  const key = resourceKey(resource);\r\n  let callbackSet: UpdateCallbackSet;\r\n  const existingCallbackSet = updateCallbackSets.get(key);\r\n  if (!existingCallbackSet) {\r\n    callbackSet = {\r\n      callbacks: new Set([callback]),\r\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\r\n    };\r\n    updateCallbackSets.set(key, callbackSet);\r\n  } else {\r\n    existingCallbackSet.callbacks.add(callback);\r\n    callbackSet = existingCallbackSet;\r\n  }\r\n\r\n  return () => {\r\n    callbackSet.callbacks.delete(callback);\r\n\r\n    if (callbackSet.callbacks.size === 0) {\r\n      callbackSet.unsubscribe();\r\n      updateCallbackSets.delete(key);\r\n    }\r\n  };\r\n}\r\n\r\nfunction triggerUpdate(msg: ServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  const callbackSet = updateCallbackSets.get(key);\r\n  if (!callbackSet) {\r\n    return;\r\n  }\r\n\r\n  for (const callback of callbackSet.callbacks) {\r\n    callback(msg);\r\n  }\r\n\r\n  if (msg.type === \"notFound\") {\r\n    // This indicates that the resource which we subscribed to either does not exist or\r\n    // has been deleted. In either case, we should clear all update callbacks, so if a\r\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\r\n    // message to the server.\r\n    // No need to send an \"unsubscribe\" message to the server, it will have already\r\n    // dropped the update stream before sending the \"notFound\" message.\r\n    updateCallbackSets.delete(key);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAmBtD,SAAS,QAAQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf;IACd,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM,CAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;QAClB,MAAM,iBAAiB,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QACpD,MAAM,eAAe,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,MAAM,QAAQ,IAAI,IAAI;eAAK,QAAQ,KAAK,IAAI,EAAE;eAAO,QAAQ,KAAK,IAAI,EAAE;SAAE;QAC1E,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,OAAO,IAAI,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,KAAK,IAAI,EAAE;SACxB;QAED,KAAK,MAAM,YAAY,QAAQ,OAAO,IAAI,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS;AACzC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { ChevronDown } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\n\r\ninterface HeaderProps {\r\n  isVisible?: boolean;\r\n}\r\n\r\nconst Header: React.FC<HeaderProps> = ({ isVisible = true }) => {\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);\r\n  const [seoDropdownOpen, setSeoDropdownOpen] = useState<boolean>(false);\r\n  const [webDevDropdownOpen, setWebDevDropdownOpen] = useState<boolean>(false);\r\n  const [digitalMarketingDropdownOpen, setDigitalMarketingDropdownOpen] = useState<boolean>(false);\r\n  const [aboutDropdownOpen, setAboutDropdownOpen] = useState<boolean>(false);\r\n\r\n  const [mounted, setMounted] = useState<boolean>(false);\r\n\r\n  // Ensure component is hydrated before rendering state-dependent UI\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Handle body scroll when mobile menu is open\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) {\r\n      document.body.style.overflow = 'hidden';\r\n    } else {\r\n      document.body.style.overflow = 'unset';\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [mobileMenuOpen]);\r\n\r\n  // Close mobile menu on escape key\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (e.key === 'Escape' && mobileMenuOpen) {\r\n        setMobileMenuOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleEscape);\r\n    return () => document.removeEventListener('keydown', handleEscape);\r\n  }, [mobileMenuOpen]);\r\n\r\n  // SEO Sub-services\r\n  const seoSubServices = [\r\n    { name: 'On-Page SEO Optimization', href: '/on-page-seo', description: 'Optimize content and structure' },\r\n    { name: 'Off-Page SEO & Link Building', href: '/off-page-seo', description: 'Build authority and backlinks' },\r\n    { name: 'Technical SEO Services', href: '/technical-seo', description: 'Optimize technical performance' },\r\n    { name: 'Local SEO Marketing', href: '/local-seo', description: 'Dominate local search results' },\r\n    { name: 'Content Writing', href: '/content-writing', description: 'SEO-optimized content creation' },\r\n    { name: 'SEO Analytics', href: '/seo-analytics', description: 'Comprehensive SEO analysis' }\r\n  ];\r\n\r\n  // Web Development Sub-services\r\n  const webDevSubServices = [\r\n    { name: 'Custom Website Development', href: '/custom-website-development', description: 'Unique, tailored websites' },\r\n    { name: 'E-commerce Development', href: '/ecommerce-development', description: 'Online store solutions' },\r\n    { name: 'Mobile App Development', href: '/mobile-app-development', description: 'iOS & Android apps' },\r\n    { name: 'Website Speed Optimization', href: '/website-speed-optimization', description: 'Lightning-fast websites' },\r\n    { name: 'Web Application Development', href: '/web-application-development', description: 'Custom web applications' },\r\n    { name: 'Website Maintenance & Support', href: '/website-maintenance-support', description: '24/7 website care' }\r\n  ];\r\n\r\n  // Digital Marketing Sub-services\r\n  const digitalMarketingSubServices = [\r\n    { name: 'PPC Advertising', href: '/ppc', description: 'Targeted pay-per-click campaigns' },\r\n    { name: 'Email Marketing', href: '/email-marketing', description: 'Automated campaigns that convert' },\r\n    { name: 'Social Media Marketing', href: '/social-media', description: 'Engage your audience effectively' },\r\n    { name: 'Branding Services', href: '/branding-services', description: 'Build powerful brand identity' },\r\n    { name: 'Conversion Optimization', href: '/conversion-optimization', description: 'Turn visitors into customers' },\r\n    { name: 'Reputation Management', href: '/reputation-management', description: 'Protect and enhance your brand' }\r\n  ];\r\n\r\n  // About Sub-services\r\n  const aboutSubServices = [\r\n    { name: 'Case Studies', href: '/case-studies', description: 'View our successful projects' },\r\n    { name: 'Blog', href: '/blog', description: 'Latest insights and industry news' }\r\n  ];\r\n\r\n  const toggleMobileMenu = () => {\r\n    setMobileMenuOpen(!mobileMenuOpen);\r\n  };\r\n\r\n  const closeMobileMenu = () => {\r\n    setMobileMenuOpen(false);\r\n    // Reset all dropdown states when closing mobile menu\r\n    setSeoDropdownOpen(false);\r\n    setWebDevDropdownOpen(false);\r\n    setDigitalMarketingDropdownOpen(false);\r\n    setAboutDropdownOpen(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <header className={`w-full bg-white/95 backdrop-blur-md border-b border-gray-100 z-50 transition-all duration-500 ease-in-out ${\r\n        isVisible \r\n          ? 'sticky top-0 translate-y-0 opacity-100' \r\n          : 'fixed top-0 -translate-y-full opacity-0 pointer-events-none'\r\n      }`}>\r\n        <div className=\"max-w-[1440px] mx-auto px-4 sm:px-6 py-3 sm:py-4\">\r\n          <div className=\"flex justify-between items-center\">\r\n            {/* Logo */}\r\n            <div className=\"flex items-center group cursor-pointer\">\r\n              <div className=\"relative\">\r\n                <Link href=\"/\">\r\n                  <Image \r\n                    src=\"/VesaLogo.svg\" \r\n                    alt=\"VESA Solutions Logo\" \r\n                    width={94}\r\n                    height={40}\r\n                    priority\r\n                    className=\"transition-transform duration-300 group-hover:scale-105 w-20 h-auto sm:w-[94px]\"\r\n                  />\r\n                </Link>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Desktop Navigation */}\r\n            <nav className=\"hidden lg:flex items-center space-x-12\">\r\n              {/* SEO Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setSeoDropdownOpen(true)}\r\n                onMouseLeave={() => setSeoDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/seo-search-engine-optimization\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-green-600 transition-all duration-300 group\">\r\n                  SEO\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-green-500 to-green-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  seoDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">SEO Services</div>\r\n                      {seoSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100/50 transition-all duration-200 ease-out border border-transparent hover:border-green-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-green-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Web Development Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setWebDevDropdownOpen(true)}\r\n                onMouseLeave={() => setWebDevDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/web-development\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group\">\r\n                  WEB DEVELOPMENT\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  webDevDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">Web Development</div>\r\n                      {webDevSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Digital Marketing Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setDigitalMarketingDropdownOpen(true)}\r\n                onMouseLeave={() => setDigitalMarketingDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/digital-marketing\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-purple-600 transition-all duration-300 group\">\r\n                  DIGITAL MARKETING\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-purple-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  digitalMarketingDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">Digital Marketing</div>\r\n                      {digitalMarketingSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50/50 transition-all duration-200 ease-out border border-transparent hover:border-indigo-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-purple-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-purple-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Services */}\r\n              <Link href=\"/services\" className=\"relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300\">\r\n                SERVICES\r\n                <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n              </Link>\r\n\r\n              {/* About Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setAboutDropdownOpen(true)}\r\n                onMouseLeave={() => setAboutDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/about\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group\">\r\n                  ABOUT\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  aboutDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">About</div>\r\n                      {aboutSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Contact */}\r\n              <Link href=\"/contact-us\" className=\"relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300\">\r\n                CONTACT\r\n                <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n              </Link>\r\n            </nav>\r\n\r\n\r\n            {/* CTA Button and Mobile Menu */}\r\n            <div className=\"flex items-center space-x-3 sm:space-x-4\">\r\n              {/* Desktop CTA */}\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                className=\"hidden sm:block group relative bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-sm font-semibold px-6 lg:px-8 py-2.5 lg:py-3 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-blue-500/25\"\r\n              >\r\n                <span className=\"relative z-10\">GET FREE PROPOSAL</span>\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n              </Link>\r\n\r\n              {/* Mobile Menu Button */}\r\n              <button\r\n                className=\"lg:hidden relative p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-all duration-300 group\"\r\n                onClick={toggleMobileMenu}\r\n                aria-label=\"Toggle mobile menu\"\r\n              >\r\n                <div className=\"w-6 h-6 relative\">\r\n                  <span className={`absolute top-1 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${\r\n                    mobileMenuOpen ? 'rotate-45 translate-y-2' : ''\r\n                  }`}></span>\r\n                  <span className={`absolute top-2.5 left-0 w-6 h-0.5 bg-current transition-all duration-300 ${\r\n                    mobileMenuOpen ? 'opacity-0' : ''\r\n                  }`}></span>\r\n                  <span className={`absolute top-4 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${\r\n                    mobileMenuOpen ? '-rotate-45 -translate-y-2' : ''\r\n                  }`}></span>\r\n                </div>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Mobile Menu */}\r\n      {mounted && mobileMenuOpen && (\r\n        <div className=\"lg:hidden fixed inset-0 z-40 bg-white overflow-y-auto\">\r\n          {/* Mobile Header */}\r\n          <div className=\"flex justify-between items-center p-4 border-b border-gray-200\">\r\n            <Link href=\"/\" onClick={closeMobileMenu}>\r\n              <Image\r\n                src=\"/VesaLogo.svg\"\r\n                alt=\"VESA Solutions Logo\"\r\n                width={80}\r\n                height={34}\r\n                priority\r\n                className=\"w-20 h-auto\"\r\n              />\r\n            </Link>\r\n            <button\r\n              onClick={closeMobileMenu}\r\n              className=\"p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-colors\"\r\n              aria-label=\"Close mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 relative\">\r\n                <span className=\"absolute top-2.5 left-0 w-6 h-0.5 bg-current transform rotate-45\"></span>\r\n                <span className=\"absolute top-2.5 left-0 w-6 h-0.5 bg-current transform -rotate-45\"></span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Mobile Navigation */}\r\n          <div className=\"p-4 space-y-6\">\r\n            {/* SEO Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setSeoDropdownOpen(!seoDropdownOpen)}\r\n              >\r\n                <Link href=\"/seo-search-engine-optimization\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-green-600 transition-colors\">\r\n                  SEO\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {seoDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {seoSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-green-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Web Development Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setWebDevDropdownOpen(!webDevDropdownOpen)}\r\n              >\r\n                <Link href=\"/web-development\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\r\n                  WEB DEVELOPMENT\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {webDevDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {webDevSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-blue-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Digital Marketing Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setDigitalMarketingDropdownOpen(!digitalMarketingDropdownOpen)}\r\n              >\r\n                <Link href=\"/digital-marketing\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-purple-600 transition-colors\">\r\n                  DIGITAL MARKETING\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {digitalMarketingDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {digitalMarketingSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-purple-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Services */}\r\n            <div>\r\n              <Link\r\n                href=\"/services\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\"\r\n              >\r\n                SERVICES\r\n              </Link>\r\n            </div>\r\n\r\n            {/* About Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setAboutDropdownOpen(!aboutDropdownOpen)}\r\n              >\r\n                <Link href=\"/about\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\r\n                  ABOUT\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {aboutDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {aboutSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-blue-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Contact */}\r\n            <div>\r\n              <Link\r\n                href=\"/contact-us\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\"\r\n              >\r\n                CONTACT\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Mobile CTA Button */}\r\n            <div className=\"pt-4\">\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block w-full text-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-6 py-4 rounded-full transition-all duration-300 transform hover:scale-105\"\r\n              >\r\n                GET FREE PROPOSAL\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWA,MAAM,SAAgC,CAAC,EAAE,YAAY,IAAI,EAAE;;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAC1F,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAEpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,mEAAmE;IACnE,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;4BAAE;YACR,WAAW;QACb;2BAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,gBAAgB;gBAClB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA,qBAAqB;YACrB;oCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;2BAAG;QAAC;KAAe;IAEnB,kCAAkC;IAClC,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,YAAY,gBAAgB;wBACxC,kBAAkB;oBACpB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;oCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;2BAAG;QAAC;KAAe;IAEnB,mBAAmB;IACnB,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAA4B,MAAM;YAAgB,aAAa;QAAiC;QACxG;YAAE,MAAM;YAAgC,MAAM;YAAiB,aAAa;QAAgC;QAC5G;YAAE,MAAM;YAA0B,MAAM;YAAkB,aAAa;QAAiC;QACxG;YAAE,MAAM;YAAuB,MAAM;YAAc,aAAa;QAAgC;QAChG;YAAE,MAAM;YAAmB,MAAM;YAAoB,aAAa;QAAiC;QACnG;YAAE,MAAM;YAAiB,MAAM;YAAkB,aAAa;QAA6B;KAC5F;IAED,+BAA+B;IAC/B,MAAM,oBAAoB;QACxB;YAAE,MAAM;YAA8B,MAAM;YAA+B,aAAa;QAA4B;QACpH;YAAE,MAAM;YAA0B,MAAM;YAA0B,aAAa;QAAyB;QACxG;YAAE,MAAM;YAA0B,MAAM;YAA2B,aAAa;QAAqB;QACrG;YAAE,MAAM;YAA8B,MAAM;YAA+B,aAAa;QAA0B;QAClH;YAAE,MAAM;YAA+B,MAAM;YAAgC,aAAa;QAA0B;QACpH;YAAE,MAAM;YAAiC,MAAM;YAAgC,aAAa;QAAoB;KACjH;IAED,iCAAiC;IACjC,MAAM,8BAA8B;QAClC;YAAE,MAAM;YAAmB,MAAM;YAAQ,aAAa;QAAmC;QACzF;YAAE,MAAM;YAAmB,MAAM;YAAoB,aAAa;QAAmC;QACrG;YAAE,MAAM;YAA0B,MAAM;YAAiB,aAAa;QAAmC;QACzG;YAAE,MAAM;YAAqB,MAAM;YAAsB,aAAa;QAAgC;QACtG;YAAE,MAAM;YAA2B,MAAM;YAA4B,aAAa;QAA+B;QACjH;YAAE,MAAM;YAAyB,MAAM;YAA0B,aAAa;QAAiC;KAChH;IAED,qBAAqB;IACrB,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAgB,MAAM;YAAiB,aAAa;QAA+B;QAC3F;YAAE,MAAM;YAAQ,MAAM;YAAS,aAAa;QAAoC;KACjF;IAED,MAAM,mBAAmB;QACvB,kBAAkB,CAAC;IACrB;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,qDAAqD;QACrD,mBAAmB;QACnB,sBAAsB;QACtB,gCAAgC;QAChC,qBAAqB;IACvB;IAEA,qBACE;;0BACE,0JAAC;gBAAO,WAAW,CAAC,0GAA0G,EAC5H,YACI,2CACA,+DACJ;0BACA,cAAA,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,0JAAC,yHAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;;;;;0CAOlB,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC;wCACC,WAAU;wCACV,cAAc,IAAM,mBAAmB;wCACvC,cAAc,IAAM,mBAAmB;;0DAEvC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,WAAU;;oDAA6H;kEAElL,0JAAC,gNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,kBAAkB,eAAe,IAAI;;;;;;kEACjH,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,0JAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,kBAAkB,sCAAsC,sCACxD;0DACA,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,0JAAC,wHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,0JAAC;wCACC,WAAU;wCACV,cAAc,IAAM,sBAAsB;wCAC1C,cAAc,IAAM,sBAAsB;;0DAE1C,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;;oDAA4H;kEAElK,0JAAC,gNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,qBAAqB,eAAe,IAAI;;;;;;kEACpH,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,0JAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,qBAAqB,sCAAsC,sCAC3D;0DACA,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,0JAAC,wHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,0JAAC;wCACC,WAAU;wCACV,cAAc,IAAM,gCAAgC;wCACpD,cAAc,IAAM,gCAAgC;;0DAEpD,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;;oDAA8H;kEAEtK,0JAAC,gNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,+BAA+B,eAAe,IAAI;;;;;;kEAC9H,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,0JAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,+BAA+B,sCAAsC,sCACrE;0DACA,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,4BAA4B,GAAG,CAAC,CAAC,SAAS,sBACzC,0JAAC,wHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;;4CAAmH;0DAElJ,0JAAC;gDAAK,WAAU;;;;;;;;;;;;kDAIlB,0JAAC;wCACC,WAAU;wCACV,cAAc,IAAM,qBAAqB;wCACzC,cAAc,IAAM,qBAAqB;;0DAEzC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;;oDAA4H;kEAExJ,0JAAC,gNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,oBAAoB,eAAe,IAAI;;;;;;kEACnH,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,0JAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,oBAAoB,sCAAsC,sCAC1D;0DACA,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,0JAAC,wHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,0JAAC;wEAAI,WAAU;;0FACb,0JAAC;gFAAI,WAAU;;kGACb,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,0JAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,0JAAC;gFAAI,WAAU;0FACb,cAAA,0JAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;;4CAAmH;0DAEpJ,0JAAC;gDAAK,WAAU;;;;;;;;;;;;;;;;;;0CAMpB,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC,wHAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,0JAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,0JAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,0JAAC;wCACC,WAAU;wCACV,SAAS;wCACT,cAAW;kDAEX,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAK,WAAW,CAAC,+FAA+F,EAC/G,iBAAiB,4BAA4B,IAC7C;;;;;;8DACF,0JAAC;oDAAK,WAAW,CAAC,yEAAyE,EACzF,iBAAiB,cAAc,IAC/B;;;;;;8DACF,0JAAC;oDAAK,WAAW,CAAC,+FAA+F,EAC/G,iBAAiB,8BAA8B,IAC/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASb,WAAW,gCACV,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;;0CACb,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,SAAS;0CACtB,cAAA,0JAAC,yHAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAGd,0JAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAK,WAAU;;;;;;sDAChB,0JAAC;4CAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMtB,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;;kDACC,0JAAC;wCACC,WAAU;wCACV,SAAS,IAAM,mBAAmB,CAAC;;0DAEnC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,SAAS;gDAAiB,WAAU;0DAA6E;;;;;;0DAG9J,0JAAC,gNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,kBAAkB,eAAe,IAAI;;;;;;;;;;;;oCAE7G,iCACC,0JAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,0JAAC;;kDACC,0JAAC;wCACC,WAAU;wCACV,SAAS,IAAM,sBAAsB,CAAC;;0DAEtC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,SAAS;gDAAiB,WAAU;0DAA4E;;;;;;0DAG9I,0JAAC,gNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,qBAAqB,eAAe,IAAI;;;;;;;;;;;;oCAEhH,oCACC,0JAAC;wCAAI,WAAU;kDACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,0JAAC;;kDACC,0JAAC;wCACC,WAAU;wCACV,SAAS,IAAM,gCAAgC,CAAC;;0DAEhD,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,SAAS;gDAAiB,WAAU;0DAA8E;;;;;;0DAGlJ,0JAAC,gNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,+BAA+B,eAAe,IAAI;;;;;;;;;;;;oCAE1H,8CACC,0JAAC;wCAAI,WAAU;kDACZ,4BAA4B,GAAG,CAAC,CAAC,SAAS,sBACzC,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,0JAAC;0CACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;0CAMH,0JAAC;;kDACC,0JAAC;wCACC,WAAU;wCACV,SAAS,IAAM,qBAAqB,CAAC;;0DAErC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,SAAS;gDAAiB,WAAU;0DAA4E;;;;;;0DAGpI,0JAAC,gNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,oBAAoB,eAAe,IAAI;;;;;;;;;;;;oCAE/G,mCACC,0JAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,0JAAC,wHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,0JAAC;0CACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;0CAMH,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAlgBM;KAAA;uCAogBS", "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Footer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { Mail, Phone, MapPin, ArrowRight, Facebook, Instagram, Linkedin, Twitter, MessageCircle, Globe } from 'lucide-react';\r\n\r\nconst Footer: React.FC = () => {\r\n  return (\r\n    <footer className=\"relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden min-h-screen flex flex-col justify-center\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0 opacity-5\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/20 to-transparent\"></div>\r\n        <div className=\"absolute top-20 right-0 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute bottom-0 left-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative max-w-7xl mx-auto px-6 py-16\">\r\n        {/* Top Section - CTA */}\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent\">\r\n            Ready to Grow Your Business?\r\n          </h2>\r\n          <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\r\n            Let&apos;s create something amazing together. Get your free consultation today.\r\n          </p>\r\n          <Link href=\"/free-estimate\">\r\n            <button className=\"group inline-flex items-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl shadow-blue-500/25\">\r\n              Get Free Proposal\r\n              <ArrowRight size={20} className=\"ml-2 transition-transform duration-300 group-hover:translate-x-1\" />\r\n            </button>\r\n          </Link>\r\n        </div>\r\n\r\n        {/* Main Content */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12\">\r\n          {/* Company Info */}\r\n          <div className=\"lg:col-span-2\">\r\n            <div className=\"flex items-center mb-6\">\r\n              <Link href=\"/\">\r\n                <Image \r\n                  src=\"/VesaLogo.svg\" \r\n                  alt=\"VESA Solutions Logo\" \r\n                  width={120} \r\n                  height={46}\r\n                  className=\"w-30 h-auto transition-transform duration-300 hover:scale-105\"\r\n                />\r\n              </Link>\r\n            </div>\r\n            <p className=\"text-gray-300 text-lg leading-relaxed mb-8\">\r\n              Full-service digital marketing agency helping businesses attract, impress, and convert more leads online since 2015.\r\n            </p>\r\n            \r\n            {/* Contact Info */}\r\n            <div className=\"space-y-4\">\r\n              <a\r\n                href=\"mailto:<EMAIL>\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Mail size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span><EMAIL></span>\r\n              </a>\r\n              <a\r\n                href=\"tel:+***********\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Phone size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span>****** 628 3793</span>\r\n              </a>\r\n              <a\r\n                href=\"tel:+355694046408\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Phone size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span>+355 69 404 6408</span>\r\n              </a>\r\n              <a \r\n                href=\"https://share.google/T9q3WjqOOmMHrBnJY\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"flex items-start text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <MapPin size={18} className=\"mr-3 text-blue-400 mt-0.5 flex-shrink-0 group-hover:scale-110 transition-transform\" />\r\n                <span>Bulevardi Dyrrah, Pallati 394, Kati 4-t<br />2001, Durrës, Albania</span>\r\n              </a>\r\n              <div className=\"flex items-center text-gray-300\">\r\n                <div className=\"w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse\"></div>\r\n                <span className=\"text-green-400 font-medium\">Open 24 Hours</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Services Grid */}\r\n          <div className=\"lg:col-span-3 grid grid-cols-1 md:grid-cols-4 gap-8\">\r\n            {/* SEO Services */}\r\n            <div>\r\n              <Link href=\"/seo-search-engine-optimization\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-green-400 mb-6 relative transition-colors duration-300\">\r\n                  SEO Services\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-green-400 to-green-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/on-page-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  On-Page SEO\r\n                </Link></li>\r\n                <li><Link href=\"/off-page-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Off-Page SEO\r\n                </Link></li>\r\n                <li><Link href=\"/technical-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Technical SEO\r\n                </Link></li>\r\n                <li><Link href=\"/local-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Local SEO\r\n                </Link></li>\r\n                <li><Link href=\"/content-writing\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Content Writing\r\n                </Link></li>\r\n                <li><Link href=\"/seo-analytics\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  SEO Analytics\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Web Development */}\r\n            <div>\r\n              <Link href=\"/web-development\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-blue-400 mb-6 relative transition-colors duration-300\">\r\n                  Web Development\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/custom-website-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Custom Websites\r\n                </Link></li>\r\n                <li><Link href=\"/ecommerce-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  E-commerce Dev\r\n                </Link></li>\r\n                <li><Link href=\"/mobile-app-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Mobile Apps\r\n                </Link></li>\r\n                <li><Link href=\"/website-speed-optimization\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 flex-shrink-0 transition-all duration-300 group-hover:w-2\"></span>\r\n                  <span className=\"whitespace-nowrap\">Speed Optimization</span>\r\n                </Link></li>\r\n                <li><Link href=\"/web-application-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Web Applications\r\n                </Link></li>\r\n                <li><Link href=\"/website-maintenance-support\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Maintenance\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Digital Marketing */}\r\n            <div>\r\n              <Link href=\"/digital-marketing\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-purple-400 mb-6 relative transition-colors duration-300\">\r\n                  Digital Marketing\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-purple-400 to-purple-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/ppc\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  PPC Advertising\r\n                </Link></li>\r\n                <li><Link href=\"/email-marketing\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Email Marketing\r\n                </Link></li>\r\n                <li><Link href=\"/social-media\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Social Media\r\n                </Link></li>\r\n                <li><Link href=\"/branding-services\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Branding Services\r\n                </Link></li>\r\n                <li><Link href=\"/conversion-optimization\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Conversion Optimization\r\n                </Link></li>\r\n                <li><Link href=\"/reputation-management\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Reputation Management\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Company */}\r\n            <div>\r\n              <h3 className=\"text-lg font-bold text-white mb-6 relative\">\r\n                Company\r\n                <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600\"></span>\r\n              </h3>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/services\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Services\r\n                </Link></li>\r\n                <li><Link href=\"/case-studies\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Case Studies\r\n                </Link></li>\r\n                <li><Link href=\"/about\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  About Us\r\n                </Link></li>\r\n                <li><Link href=\"/contact-us\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Contact Us\r\n                </Link></li>\r\n                <li><Link href=\"/blog\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Blog\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Social Media & Bottom Section */}\r\n        <div className=\"flex flex-col lg:flex-row justify-between items-center pt-8 border-t border-gray-700\">\r\n          <div className=\"mb-6 lg:mb-0\">\r\n            <h4 className=\"text-white font-semibold mb-4\">Follow Our Journey</h4>\r\n            <div className=\"flex space-x-4\">\r\n              <a\r\n                href=\"https://m.facebook.com/VesaSolutions/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Facebook\"\r\n              >\r\n                <Facebook size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://www.instagram.com/vesasolutions/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-gradient-to-r hover:from-pink-500 hover:to-purple-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Instagram\"\r\n              >\r\n                <Instagram size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://al.linkedin.com/company/vesasolutions\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-700 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on LinkedIn\"\r\n              >\r\n                <Linkedin size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"#\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-500 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Twitter\"\r\n              >\r\n                <Twitter size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://wa.me/***********\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-green-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Chat with us on WhatsApp\"\r\n              >\r\n                <MessageCircle size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://vesasolutions.com/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-indigo-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Visit our website\"\r\n              >\r\n                <Globe size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"text-center lg:text-right\">\r\n            <div className=\"text-2xl font-bold text-white mb-2\">Growing Businesses Since 2015</div>\r\n            <div className=\"text-gray-400 text-sm\">Trusted by 200+ companies worldwide</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Copyright */}\r\n        <div className=\"mt-12 pt-8 border-t border-gray-700 text-center\">\r\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\r\n            <p className=\"text-gray-400 text-sm\">\r\n              © 2025 Vesa Solutions Marketing Agency. All rights reserved.\r\n            </p>\r\n            <div className=\"flex flex-wrap justify-center md:justify-end space-x-6 text-sm\">\r\n              <Link href=\"/privacy-policy\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Privacy Policy</Link>\r\n              <Link href=\"/terms-of-service\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Terms of Service</Link>\r\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Sitemap</Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAOA,MAAM,SAAmB;IACvB,qBACE,0JAAC;QAAO,WAAU;;0BAEhB,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAG,WAAU;0CAA4G;;;;;;0CAG1H,0JAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,0JAAC;oCAAO,WAAU;;wCAA2P;sDAE3Q,0JAAC,8MAAA,CAAA,aAAU;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMtC,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,0JAAC,yHAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;kDAIhB,0JAAC;wCAAE,WAAU;kDAA6C;;;;;;kDAK1D,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,0JAAC,8LAAA,CAAA,OAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC1B,0JAAC;kEAAK;;;;;;;;;;;;0DAER,0JAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,0JAAC,gMAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC3B,0JAAC;kEAAK;;;;;;;;;;;;0DAER,0JAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,0JAAC,gMAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC3B,0JAAC;kEAAK;;;;;;;;;;;;0DAER,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,0JAAC,sMAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC5B,0JAAC;;4DAAK;0EAAuC,0JAAC;;;;;4DAAK;;;;;;;;;;;;;0DAErD,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAI,WAAU;;;;;;kEACf,0JAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;0CAMnD,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC;;0DACC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,WAAU;0DACrD,cAAA,0JAAC;oDAAG,WAAU;;wDAAiG;sEAE7G,0JAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,0JAAC;gDAAG,WAAU;;kEACZ,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAe,WAAU;;8EACtC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EACxC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAa,WAAU;;8EACpC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;;8EAC1C,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EACxC,0JAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;;;;;;;;;;;;;kDAOlH,0JAAC;;0DACC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;0DACtC,cAAA,0JAAC;oDAAG,WAAU;;wDAAgG;sEAE5G,0JAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,0JAAC;gDAAG,WAAU;;kEACZ,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA8B,WAAU;;8EACrD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAyB,WAAU;;8EAChD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA0B,WAAU;;8EACjD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA8B,WAAU;;8EACrD,0JAAC;oEAAK,WAAU;;;;;;8EAChB,0JAAC;oEAAK,WAAU;8EAAoB;;;;;;;;;;;;;;;;;kEAEtC,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA+B,WAAU;;8EACtD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA+B,WAAU;;8EACtD,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;;;;;;;;;;;;;kDAOjH,0JAAC;;0DACC,0JAAC,wHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DACxC,cAAA,0JAAC;oDAAG,WAAU;;wDAAkG;sEAE9G,0JAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,0JAAC;gDAAG,WAAU;;kEACZ,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAO,WAAU;;8EAC9B,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;;8EAC1C,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAqB,WAAU;;8EAC5C,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA2B,WAAU;;8EAClD,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAyB,WAAU;;8EAChD,0JAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;;;;;;;;;;;;;kDAOnH,0JAAC;;0DACC,0JAAC;gDAAG,WAAU;;oDAA6C;kEAEzD,0JAAC;wDAAK,WAAU;;;;;;;;;;;;0DAElB,0JAAC;gDAAG,WAAU;;kEACZ,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAY,WAAU;;8EACnC,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;;8EAChC,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAc,WAAU;;8EACrC,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,0JAAC;kEAAG,cAAA,0JAAC,wHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAQ,WAAU;;8EAC/B,0JAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrH,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,sMAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEhC,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,wMAAA,CAAA,YAAS;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEjC,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,sMAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEhC,0JAAC;gDACC,MAAK;gDACL,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,oMAAA,CAAA,UAAO;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAE/B,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,oNAAA,CAAA,gBAAa;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAErC,0JAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,0JAAC,gMAAA,CAAA,QAAK;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAKjC,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,0JAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAK3C,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;;8CACb,0JAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,0JAAC;oCAAI,WAAU;;sDACb,0JAAC,wHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAkB,WAAU;sDAAsD;;;;;;sDAC7F,0JAAC,wHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAoB,WAAU;sDAAsD;;;;;;sDAC/F,0JAAC,wHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpG;KArTM;uCAuTS", "debugId": null}}, {"offset": {"line": 2973, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/home/<USER>"], "sourcesContent": ["__turbopack_context__.v({\n  \"column3\": \"HeroSection-module___UGK9W__column3\",\n  \"column4\": \"HeroSection-module___UGK9W__column4\",\n  \"column5\": \"HeroSection-module___UGK9W__column5\",\n  \"columnsContainer\": \"HeroSection-module___UGK9W__columnsContainer\",\n  \"heroMockup\": \"HeroSection-module___UGK9W__heroMockup\",\n  \"infiniteScrollY\": \"HeroSection-module___UGK9W__infiniteScrollY\",\n  \"scrollWrapper\": \"HeroSection-module___UGK9W__scrollWrapper\",\n  \"scrollY\": \"HeroSection-module___UGK9W__scrollY\",\n  \"smoothScroll\": \"HeroSection-module___UGK9W__smoothScroll\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2990, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/router';\r\nimport Image from 'next/image';\r\nimport styles from './HeroSection.module.css';\r\n\r\ninterface HeroSectionProps {\r\n  className?: string;\r\n}\r\n\r\nconst HeroSection: React.FC<HeroSectionProps> = (props) => {\r\n  const className = props.className || '';\r\n  const router = useRouter();\r\n  const [animationsReady, setAnimationsReady] = useState(false);\r\n  const [websiteUrl, setWebsiteUrl] = useState('');\r\n\r\n  // Small delay to prevent weird reload positioning\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setAnimationsReady(true);\r\n    }, 100); // Very small delay\r\n\r\n    return () => clearTimeout(timer);\r\n  }, []);\r\n\r\n  // Handle form submission\r\n  const handleGetProposal = () => {\r\n    // Store the website URL in sessionStorage to pass to free-estimate page\r\n    if (websiteUrl.trim()) {\r\n      sessionStorage.setItem('websiteUrl', websiteUrl.trim());\r\n    }\r\n    // Navigate to free-estimate page\r\n    router.push('/free-estimate');\r\n  };\r\n  \r\n  // Images for the carousel\r\n  const images = React.useMemo(() => [\r\n    '/aic.jpg', '/argon.jpg', '/caffee.jpg', '/elixir.jpg', '/harbour.jpg',\r\n    '/icecream.jpg', '/newhorizon.jpg', '/pink.jpg', '/afterhours.jpg',\r\n    '/disti.jpg', '/liquortogo.jpg'\r\n  ], []);\r\n\r\n  return (\r\n    <div className={`relative w-full h-screen flex flex-col overflow-hidden ${className}`}>\r\n      {/* Deep gradient background */}\r\n      <div className=\"absolute inset-0 bg-indigo-900 bg-opacity-90 z-0\"></div>\r\n      \r\n      {/* Scrolling Mockups Background */}\r\n      <div className=\"absolute inset-0 overflow-hidden\">\r\n        <div className=\"absolute right-0 top-0 w-full h-full opacity-60\">\r\n          <div className={`flex h-full ${styles.columnsContainer}`}>\r\n            {/* Create multiple columns of scrolling content - responsive */}\r\n            {Array.from({ length: 5 }, (_, colIndex) => {\r\n              const isReverse = colIndex % 2 === 0;\r\n              const duration = 70 + (colIndex * 8);\r\n              const offsetX = colIndex * 6;\r\n              const startOffsetY = (colIndex % 3) * 10;\r\n              \r\n              // Add responsive classes\r\n              let columnClass = styles.scrollWrapper;\r\n              if (colIndex === 2) columnClass += ` ${styles.column3}`;\r\n              if (colIndex === 3) columnClass += ` ${styles.column4}`; \r\n              if (colIndex === 4) columnClass += ` ${styles.column5}`;\r\n              \r\n              return (\r\n                <div \r\n                  key={`col-${colIndex}`} \r\n                  className={columnClass}\r\n                  style={{ transform: `translateX(${offsetX}px)` }}\r\n                >\r\n                  <div\r\n                    className={`${styles.smoothScroll} ${animationsReady ? styles.infiniteScrollY : ''}`}\r\n                    style={{ \r\n                      animationDuration: `${duration}s`,\r\n                      animationDirection: isReverse ? 'reverse' : 'normal',\r\n                      marginTop: `${startOffsetY}%`\r\n                    }}\r\n                  >\r\n                    {/* Generate multiple sets of images */}\r\n                    {Array.from({ length: 3 }, (_, setIndex) => (\r\n                      <div key={`set-${colIndex}-${setIndex}`} className=\"flex flex-col gap-6\">\r\n                        {images.map((image, imgIndex) => {\r\n                          const rotate = ((colIndex + imgIndex) % 3 - 1) * 1;\r\n                          \r\n                          return (\r\n                            <div \r\n                              key={`mockup-${colIndex}-${setIndex}-${imgIndex}`}\r\n                              className=\"transform hover:scale-105 transition-transform duration-500\"\r\n                              style={{ transform: `rotate(${rotate}deg)` }}\r\n                            >\r\n                              <div className={styles.heroMockup}>\r\n                                <Image\r\n                                  src={image}\r\n                                  alt={`Hero Section ${imgIndex}`}\r\n                                  fill\r\n                                  className=\"object-cover\"\r\n                                  priority\r\n                                />\r\n                                <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent\"></div>\r\n                              </div>\r\n                            </div>\r\n                          );\r\n                        })}\r\n                      </div>\r\n                    ))}\r\n                    \r\n                    {/* Duplicate set for seamless looping */}\r\n                    {Array.from({ length: 3 }, (_, setIndex) => (\r\n                      <div key={`dup-set-${colIndex}-${setIndex}`} className=\"flex flex-col gap-6\" aria-hidden=\"true\">\r\n                        {images.map((image, imgIndex) => {\r\n                          const rotate = ((colIndex + imgIndex) % 3 - 1) * 1;\r\n                          \r\n                          return (\r\n                            <div \r\n                              key={`mockup-dup-${colIndex}-${setIndex}-${imgIndex}`}\r\n                              className=\"transform hover:scale-105 transition-transform duration-500\"\r\n                              style={{ transform: `rotate(${rotate}deg)` }}\r\n                            >\r\n                              <div className={styles.heroMockup}>\r\n                                <Image\r\n                                  src={image}\r\n                                  alt={`Hero Section ${imgIndex}`}\r\n                                  fill\r\n                                  className=\"object-cover\"\r\n                                  priority\r\n                                />\r\n                                <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent\"></div>\r\n                              </div>\r\n                            </div>\r\n                          );\r\n                        })}\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Gradient overlay for readability */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-r from-indigo-900 via-indigo-900/75 to-indigo-900/5 z-10\"></div>\r\n      \r\n      {/* Hero Content */}\r\n      <div className=\"relative z-20 flex-1 container mx-auto flex items-center\">\r\n        <div className=\"w-full md:w-3/5 px-6 md:px-12\">\r\n          <div className=\"max-w-2xl\">\r\n            {/* Main Heading */}\r\n            <h1 className=\"text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight mb-6\">\r\n              DIGITAL MARKETING<br />\r\n              THAT <span className=\"text-blue-400\">ACTUALLY</span><br />\r\n              WORKS\r\n            </h1>\r\n            \r\n            {/* Subheading */}\r\n            <p className=\"text-xl md:text-2xl text-blue-100 mb-8\">\r\n              More leads, more conversions, more sales.<br />\r\n              We guarantee it.\r\n            </p>\r\n            \r\n            {/* Form */}\r\n            <div className=\"flex flex-col md:flex-row gap-4\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Enter Website Address\"\r\n                value={websiteUrl}\r\n                onChange={(e) => setWebsiteUrl(e.target.value)}\r\n                className=\"border-2 border-blue-300 bg-transparent rounded-full px-6 py-3 flex-1 text-base text-white placeholder-blue-200 focus:border-blue-400 focus:outline-none transition-colors\"\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleGetProposal}\r\n                className=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-full px-8 py-3 whitespace-nowrap text-base transition-colors\"\r\n              >\r\n                GET MY FREE PROPOSAL →\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HeroSection;"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;AAMA,MAAM,cAA0C,CAAC;;IAC/C,MAAM,YAAY,MAAM,SAAS,IAAI;IACrC,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,kDAAkD;IAClD,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,QAAQ;+CAAW;oBACvB,mBAAmB;gBACrB;8CAAG,MAAM,mBAAmB;YAE5B;yCAAO,IAAM,aAAa;;QAC5B;gCAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,oBAAoB;QACxB,wEAAwE;QACxE,IAAI,WAAW,IAAI,IAAI;YACrB,eAAe,OAAO,CAAC,cAAc,WAAW,IAAI;QACtD;QACA,iCAAiC;QACjC,OAAO,IAAI,CAAC;IACd;IAEA,0BAA0B;IAC1B,MAAM,SAAS,0HAAA,CAAA,UAAK,CAAC,OAAO;uCAAC,IAAM;gBACjC;gBAAY;gBAAc;gBAAe;gBAAe;gBACxD;gBAAiB;gBAAmB;gBAAa;gBACjD;gBAAc;aACf;sCAAE,EAAE;IAEL,qBACE,0JAAC;QAAI,WAAW,CAAC,uDAAuD,EAAE,WAAW;;0BAEnF,0JAAC;gBAAI,WAAU;;;;;;0BAGf,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAW,CAAC,YAAY,EAAE,yIAAA,CAAA,UAAM,CAAC,gBAAgB,EAAE;kCAErD,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,CAAC,GAAG;4BAC7B,MAAM,YAAY,WAAW,MAAM;4BACnC,MAAM,WAAW,KAAM,WAAW;4BAClC,MAAM,UAAU,WAAW;4BAC3B,MAAM,eAAe,AAAC,WAAW,IAAK;4BAEtC,yBAAyB;4BACzB,IAAI,cAAc,yIAAA,CAAA,UAAM,CAAC,aAAa;4BACtC,IAAI,aAAa,GAAG,eAAe,CAAC,CAAC,EAAE,yIAAA,CAAA,UAAM,CAAC,OAAO,EAAE;4BACvD,IAAI,aAAa,GAAG,eAAe,CAAC,CAAC,EAAE,yIAAA,CAAA,UAAM,CAAC,OAAO,EAAE;4BACvD,IAAI,aAAa,GAAG,eAAe,CAAC,CAAC,EAAE,yIAAA,CAAA,UAAM,CAAC,OAAO,EAAE;4BAEvD,qBACE,0JAAC;gCAEC,WAAW;gCACX,OAAO;oCAAE,WAAW,CAAC,WAAW,EAAE,QAAQ,GAAG,CAAC;gCAAC;0CAE/C,cAAA,0JAAC;oCACC,WAAW,GAAG,yIAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,kBAAkB,yIAAA,CAAA,UAAM,CAAC,eAAe,GAAG,IAAI;oCACpF,OAAO;wCACL,mBAAmB,GAAG,SAAS,CAAC,CAAC;wCACjC,oBAAoB,YAAY,YAAY;wCAC5C,WAAW,GAAG,aAAa,CAAC,CAAC;oCAC/B;;wCAGC,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAE,GAAG,CAAC,GAAG,yBAC7B,0JAAC;gDAAwC,WAAU;0DAChD,OAAO,GAAG,CAAC,CAAC,OAAO;oDAClB,MAAM,SAAS,CAAC,CAAC,WAAW,QAAQ,IAAI,IAAI,CAAC,IAAI;oDAEjD,qBACE,0JAAC;wDAEC,WAAU;wDACV,OAAO;4DAAE,WAAW,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;wDAAC;kEAE3C,cAAA,0JAAC;4DAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;;8EAC/B,0JAAC,yHAAA,CAAA,UAAK;oEACJ,KAAK;oEACL,KAAK,CAAC,aAAa,EAAE,UAAU;oEAC/B,IAAI;oEACJ,WAAU;oEACV,QAAQ;;;;;;8EAEV,0JAAC;oEAAI,WAAU;;;;;;;;;;;;uDAZZ,CAAC,OAAO,EAAE,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU;;;;;gDAgBvD;+CAtBQ,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,UAAU;;;;;wCA2BxC,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAE,GAAG,CAAC,GAAG,yBAC7B,0JAAC;gDAA4C,WAAU;gDAAsB,eAAY;0DACtF,OAAO,GAAG,CAAC,CAAC,OAAO;oDAClB,MAAM,SAAS,CAAC,CAAC,WAAW,QAAQ,IAAI,IAAI,CAAC,IAAI;oDAEjD,qBACE,0JAAC;wDAEC,WAAU;wDACV,OAAO;4DAAE,WAAW,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;wDAAC;kEAE3C,cAAA,0JAAC;4DAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;;8EAC/B,0JAAC,yHAAA,CAAA,UAAK;oEACJ,KAAK;oEACL,KAAK,CAAC,aAAa,EAAE,UAAU;oEAC/B,IAAI;oEACJ,WAAU;oEACV,QAAQ;;;;;;8EAEV,0JAAC;oEAAI,WAAU;;;;;;;;;;;;uDAZZ,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU;;;;;gDAgB3D;+CAtBQ,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,UAAU;;;;;;;;;;;+BA1C1C,CAAC,IAAI,EAAE,UAAU;;;;;wBAsE5B;;;;;;;;;;;;;;;;0BAMN,0JAAC;gBAAI,WAAU;;;;;;0BAGf,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAG,WAAU;;oCAA2E;kDACtE,0JAAC;;;;;oCAAK;kDAClB,0JAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAAe,0JAAC;;;;;oCAAK;;;;;;;0CAK5D,0JAAC;gCAAE,WAAU;;oCAAyC;kDACX,0JAAC;;;;;oCAAK;;;;;;;0CAKjD,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;kDAEZ,0JAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA7KM;;QAEW,0HAAA,CAAA,YAAS;;;KAFpB;uCA+KS", "debugId": null}}, {"offset": {"line": 3346, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/home/<USER>"], "sourcesContent": ["import React from 'react';\r\n\r\ninterface ReputationManagementSectionProps {\r\n  className?: string;\r\n}\r\n\r\nconst ReputationManagementSection: React.FC<ReputationManagementSectionProps> = ({ className = '' }) => {\r\n  const services: string[] = [\r\n    \"Monitor and Respond to Feedback\",\r\n    \"Enhance Positive Content Visibility\", \r\n    \"Leverage Social Proof\",\r\n    \"Engage in Reputation Management Campaigns\"\r\n  ];\r\n\r\n  const handleBookCall = (): void => {\r\n    window.location.href = '/contact-us';\r\n  };\r\n\r\n  const handleViewWork = (): void => {\r\n    window.location.href = '/case-studies';\r\n  };\r\n\r\n  return (\r\n    <section className={`w-full bg-blue-50 min-h-screen flex items-center ${className}`}>\r\n      <div className=\"container mx-auto px-4 md:px-0 py-8 sm:py-16\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-12 items-center\">\r\n          {/* Left Column - Text Content */}\r\n          <div className=\"lg:col-span-4 order-1\">\r\n            <h2 className=\"text-2xl sm:text-3xl font-bold text-gray-800 mb-4 sm:mb-6\">\r\n              Expert Online Reputation Management Services to Boost Your Digital Presence\r\n            </h2>\r\n            \r\n            <p className=\"text-base sm:text-lg text-gray-700 mb-4 sm:mb-6\">\r\n              At Vesa Solutions, we specialize in online reputation management services designed to enhance your digital presence and boost keyword rankings in Canada and Albania. Our tailored strategies include monitoring and responding to online reviews, creating high-quality, SEO-optimized content, and managing social media engagement to build trust and credibility.\r\n            </p>\r\n            \r\n            <p className=\"text-base sm:text-lg text-gray-700 mb-4 sm:mb-6\">\r\n              By leveraging advanced SEO techniques and data-driven insights, we ensure your business stands out in search results, helping you attract more customers and maintain a positive online image.\r\n            </p>\r\n            \r\n            <p className=\"text-base sm:text-lg text-gray-700\">\r\n              Whether you&apos;re looking to recover from negative feedback or improve your brand visibility, our expert team is here to elevate your digital reputation.\r\n            </p>\r\n          </div>\r\n          \r\n          {/* Middle Column - Capsule Image Display */}\r\n          <div className=\"lg:col-span-4 flex justify-center order-3 lg:order-2\">\r\n            <div className=\"relative h-[400px] sm:h-[500px] lg:h-[650px] w-full max-w-sm sm:max-w-md lg:max-w-md\">\r\n\r\n              {/* Capsule 1 */}\r\n              <div className=\"absolute left-[30px] sm:left-[45px] lg:left-[20px] top-[120px] sm:top-[150px] lg:top-44 h-[200px] sm:h-[280px] lg:h-[380px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10\"\r\n                   style={{\r\n                     backgroundImage: \"url('/homepage/colleagues-working-desk.jpg')\",\r\n                     backgroundSize: \"500px 650px\",\r\n                     backgroundPosition: \"calc(-30px + 0px) calc(-120px + 0px)\",\r\n                     backgroundRepeat: \"no-repeat\"\r\n                   }}>\r\n              </div>\r\n\r\n              {/* Capsule 2 */}\r\n              <div className=\"absolute left-[90px] sm:left-[125px] lg:left-[120px] top-[20px] sm:top-[30px] lg:top-9 h-[320px] sm:h-[420px] lg:h-[580px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10\"\r\n                   style={{\r\n                     backgroundImage: \"url('/homepage/colleagues-working-desk.jpg')\",\r\n                     backgroundSize: \"500px 650px\",\r\n                     backgroundPosition: \"calc(-90px + 0px) calc(-20px + 0px)\",\r\n                     backgroundRepeat: \"no-repeat\"\r\n                   }}>\r\n              </div>\r\n\r\n              {/* Capsule 3 */}\r\n              <div className=\"absolute left-[150px] sm:left-[205px] lg:left-[220px] top-[60px] sm:top-[80px] lg:top-28 h-[300px] sm:h-[400px] lg:h-[560px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10\"\r\n                   style={{\r\n                     backgroundImage: \"url('/homepage/colleagues-working-desk.jpg')\",\r\n                     backgroundSize: \"500px 650px\",\r\n                     backgroundPosition: \"calc(-150px + 0px) calc(-60px + 0px)\",\r\n                     backgroundRepeat: \"no-repeat\"\r\n                   }}>\r\n              </div>\r\n\r\n              {/* Capsule 4 */}\r\n              <div className=\"absolute left-[210px] sm:left-[285px] lg:left-[320px] top-[40px] sm:top-[50px] lg:top-20 h-[280px] sm:h-[350px] lg:h-[480px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10\"\r\n                   style={{\r\n                     backgroundImage: \"url('/homepage/colleagues-working-desk.jpg')\",\r\n                     backgroundSize: \"500px 650px\",\r\n                     backgroundPosition: \"calc(-210px + 0px) calc(-40px + 0px)\",\r\n                     backgroundRepeat: \"no-repeat\"\r\n                   }}>\r\n              </div>\r\n\r\n              {/* Capsule 5 */}\r\n              <div className=\"absolute left-[270px] sm:left-[365px] lg:left-[420px] top-[100px] sm:top-[120px] lg:top-40 h-[280px] sm:h-[350px] lg:h-[480px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10\"\r\n                   style={{\r\n                     backgroundImage: \"url('/homepage/colleagues-working-desk.jpg')\",\r\n                     backgroundSize: \"500px 650px\",\r\n                     backgroundPosition: \"calc(-270px + 0px) calc(-100px + 0px)\",\r\n                     backgroundRepeat: \"no-repeat\"\r\n                   }}>\r\n              </div>\r\n\r\n              {/* Circular reputation management badge */}\r\n              <div className=\"absolute top-[40px] sm:top-[60px] lg:top-24 left-2 sm:left-3 lg:left-4 bg-white rounded-full h-20 w-20 sm:h-24 sm:w-24 lg:h-32 lg:w-32 flex items-center justify-center z-30 shadow-md\">\r\n                <div className=\"relative h-full w-full\">\r\n                  <svg viewBox=\"0 0 100 100\" width=\"100%\" height=\"100%\">\r\n                    <defs>\r\n                      <path id=\"circle-path\" d=\"M 50,50 m -37,0 a 37,37 0 1,1 74,0 a 37,37 0 1,1 -74,0\" />\r\n                    </defs>\r\n                    <text fontSize=\"11\" fill=\"#0084FF\" fontWeight=\"600\" letterSpacing=\"1\" className=\"hidden lg:block\">\r\n                      <textPath xlinkHref=\"#circle-path\" startOffset=\"0%\">\r\n                        REPUTATION • MANAGEMENT • SERVICES •\r\n                      </textPath>\r\n                    </text>\r\n                    <text fontSize=\"9\" fill=\"#0084FF\" fontWeight=\"600\" letterSpacing=\"1\" className=\"block sm:hidden\">\r\n                      <textPath xlinkHref=\"#circle-path\" startOffset=\"0%\">\r\n                        REPUTATION • MANAGEMENT •\r\n                      </textPath>\r\n                    </text>\r\n                    <text fontSize=\"10\" fill=\"#0084FF\" fontWeight=\"600\" letterSpacing=\"1\" className=\"hidden sm:block lg:hidden\">\r\n                      <textPath xlinkHref=\"#circle-path\" startOffset=\"0%\">\r\n                        REPUTATION • MANAGEMENT • SERVICES •\r\n                      </textPath>\r\n                    </text>\r\n                    <circle cx=\"50\" cy=\"50\" r=\"18\" fill=\"white\" />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Right Column - Services & CTA */}\r\n          <div className=\"lg:col-span-4 lg:pl-10 order-2 lg:order-3\">\r\n            <p className=\"text-blue-700 font-medium text-base sm:text-lg mb-3\">WE&apos;RE HERE TO HELP</p>\r\n            \r\n            <h2 className=\"text-2xl sm:text-3xl font-bold text-gray-800 mb-6 sm:mb-8\">\r\n              REPAIR YOUR ONLINE REPUTATION\r\n            </h2>\r\n            \r\n            <ul className=\"mb-8 sm:mb-10 space-y-4 sm:space-y-5\">\r\n              {services.map((service, index) => (\r\n                <li key={index} className=\"flex items-center\">\r\n                  <svg className=\"h-5 w-5 sm:h-6 sm:w-6 text-blue-500 mr-3 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\"></path>\r\n                  </svg>\r\n                  <span className=\"text-gray-700 text-base sm:text-lg\">{service}</span>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n            \r\n            <div className=\"flex flex-col sm:flex-row lg:flex-col xl:flex-row gap-4 sm:gap-5\">\r\n              <button\r\n                onClick={handleBookCall}\r\n                className=\"bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-full px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg transition duration-300 w-full sm:w-auto\"\r\n              >\r\n                BOOK YOUR CALL\r\n              </button>\r\n              \r\n              <button \r\n                onClick={handleViewWork}\r\n                className=\"bg-blue-100 hover:bg-blue-200 text-blue-800 font-medium rounded-full px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg transition duration-300 w-full sm:w-auto\"\r\n              >\r\n                VIEW OUR WORK\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ReputationManagementSection;"], "names": [], "mappings": ";;;;;AAMA,MAAM,8BAA0E,CAAC,EAAE,YAAY,EAAE,EAAE;IACjG,MAAM,WAAqB;QACzB;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,iBAAiB;QACrB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,0JAAC;QAAQ,WAAW,CAAC,iDAAiD,EAAE,WAAW;kBACjF,cAAA,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAI1E,0JAAC;gCAAE,WAAU;0CAAkD;;;;;;0CAI/D,0JAAC;gCAAE,WAAU;0CAAkD;;;;;;0CAI/D,0JAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;kCAMpD,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;;8CAGb,0JAAC;oCAAI,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;wCAChB,oBAAoB;wCACpB,kBAAkB;oCACpB;;;;;;8CAIL,0JAAC;oCAAI,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;wCAChB,oBAAoB;wCACpB,kBAAkB;oCACpB;;;;;;8CAIL,0JAAC;oCAAI,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;wCAChB,oBAAoB;wCACpB,kBAAkB;oCACpB;;;;;;8CAIL,0JAAC;oCAAI,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;wCAChB,oBAAoB;wCACpB,kBAAkB;oCACpB;;;;;;8CAIL,0JAAC;oCAAI,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;wCAChB,oBAAoB;wCACpB,kBAAkB;oCACpB;;;;;;8CAIL,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAI,SAAQ;4CAAc,OAAM;4CAAO,QAAO;;8DAC7C,0JAAC;8DACC,cAAA,0JAAC;wDAAK,IAAG;wDAAc,GAAE;;;;;;;;;;;8DAE3B,0JAAC;oDAAK,UAAS;oDAAK,MAAK;oDAAU,YAAW;oDAAM,eAAc;oDAAI,WAAU;8DAC9E,cAAA,0JAAC;wDAAS,WAAU;wDAAe,aAAY;kEAAK;;;;;;;;;;;8DAItD,0JAAC;oDAAK,UAAS;oDAAI,MAAK;oDAAU,YAAW;oDAAM,eAAc;oDAAI,WAAU;8DAC7E,cAAA,0JAAC;wDAAS,WAAU;wDAAe,aAAY;kEAAK;;;;;;;;;;;8DAItD,0JAAC;oDAAK,UAAS;oDAAK,MAAK;oDAAU,YAAW;oDAAM,eAAc;oDAAI,WAAU;8DAC9E,cAAA,0JAAC;wDAAS,WAAU;wDAAe,aAAY;kEAAK;;;;;;;;;;;8DAItD,0JAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9C,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAE,WAAU;0CAAsD;;;;;;0CAEnE,0JAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAI1E,0JAAC;gCAAG,WAAU;0CACX,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,0JAAC;wCAAe,WAAU;;0DACxB,0JAAC;gDAAI,WAAU;gDAAyD,MAAK;gDAAe,SAAQ;0DAClG,cAAA,0JAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;0DAE3J,0JAAC;gDAAK,WAAU;0DAAsC;;;;;;;uCAJ/C;;;;;;;;;;0CASb,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAID,0JAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KAjKM;uCAmKS", "debugId": null}}, {"offset": {"line": 3726, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/home/<USER>"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { ChevronRight } from 'lucide-react';\r\nimport { Slide } from '@/types';\r\n\r\ninterface DigitalStrategySliderSectionProps {\r\n  className?: string;\r\n}\r\n\r\nconst DigitalStrategySliderSection: React.FC<DigitalStrategySliderSectionProps> = ({ className = '' }) => {\r\n  const [activeSlide, setActiveSlide] = useState<number>(0);\r\n\r\n  const getSlideImage = (index: number): string => {\r\n    const images = [\r\n      '/homepage/understanding your business.jpg',\r\n      '/homepage/developing custom digital marketing plan.png',\r\n      '/homepage/placing digital presence with precision.png',\r\n      '/homepage/optimizing based off search results.png'\r\n    ];\r\n    return images[index] || '/api/placeholder/800/600';\r\n  };\r\n\r\n  const slides: Slide[] = [\r\n    {\r\n      number: \"01\",\r\n      topTitle: \"THE FOUNDATION\",\r\n      title: \"Understanding Your Business to Build a Tailored Strategy\",\r\n      content: [\r\n        \"To lay the groundwork for success, we need to understand the intricacies of your business. This step allows us to understand the challenges so we can pave a clear path forward. From that point on, you can consider us your most devoted groupie (but please don't sing to us).\",\r\n        \"Then comes the idea; the piece that will turn heads and spark conversation. We embrace the notion that not every idea will work, and that's okay!\",\r\n        \"Even though it may be a stellar concept, if it doesn't align with your brand, we will put our egos aside and find an idea that's a perfect match.\"\r\n      ],\r\n      imageDesc: \"Business team discussing strategy in a modern meeting room, with hands raised in agreement\"\r\n    },\r\n    {\r\n      number: \"02\",\r\n      topTitle: \"THE STRATEGY\",\r\n      title: \"Developing Your Custom Digital Marketing Plan\",\r\n      content: [\r\n        \"With a deep understanding of your business objectives, we craft a comprehensive digital strategy that aligns with your goals and resonates with your target audience.\",\r\n        \"Our approach combines data-driven insights with creative excellence to identify the most effective channels, messaging, and tactics for your brand.\",\r\n        \"We establish clear KPIs and success metrics that will guide our efforts and allow us to measure the impact of our strategic initiatives.\"\r\n      ],\r\n      imageDesc: \"Digital marketing specialists collaborating at whiteboard with diagrams and flowcharts\"\r\n    },\r\n    {\r\n      number: \"03\",\r\n      topTitle: \"THE EXECUTION\",\r\n      title: \"Implementing Your Digital Presence with Precision\",\r\n      content: [\r\n        \"This is where plans transform into action. Our team of specialists deploys your custom strategy across all relevant digital platforms with meticulous attention to detail.\",\r\n        \"From content creation and website optimization to paid campaigns and social media management, we execute each element with precision and care.\",\r\n        \"Throughout the implementation phase, we maintain transparent communication and provide regular updates on progress and initial results.\"\r\n      ],\r\n      imageDesc: \"Team working on implementation with multiple screens showing website designs and analytics\"\r\n    },\r\n    {\r\n      number: \"04\",\r\n      topTitle: \"THE REFINEMENT\",\r\n      title: \"Optimizing Your Strategy Based on Real Results\",\r\n      content: [\r\n        \"Digital marketing is never static—and neither are we. We continuously analyze performance data to identify opportunities for optimization and growth.\",\r\n        \"Through A/B testing, user feedback, and performance analytics, we refine your strategy to enhance results and maximize your return on investment.\",\r\n        \"Our iterative approach ensures that your digital presence evolves with market trends, consumer behavior, and your business objectives.\"\r\n      ],\r\n      imageDesc: \"Data analysis meeting with growth charts and performance metrics on screens\"\r\n    }\r\n  ];\r\n\r\n  const handleNext = (): void => {\r\n    setActiveSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));\r\n  };\r\n\r\n  const handleDotClick = (index: number): void => {\r\n    setActiveSlide(index); \r\n  };\r\n\r\n  return (\r\n    <section className={`w-full bg-white min-h-screen flex flex-col ${className}`}>\r\n      {/* Section Header - Centered */}\r\n      <div className=\"container mx-auto px-4 sm:px-6 text-center pt-8 sm:pt-16 pb-6 sm:pb-8\">\r\n        <p className=\"text-blue-700 font-medium text-base sm:text-lg mb-3\">TAILORED DIGITAL STRATEGY FOR SUCCESS</p>\r\n        <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 max-w-4xl mx-auto\">\r\n          BUILDING A DIGITAL PRESENCE STRATEGY TAILORED TO YOUR BUSINESS\r\n        </h2>\r\n      </div>\r\n      \r\n      {/* Slider Content */}\r\n      <div className=\"flex-grow container mx-auto px-4 sm:px-6 lg:px-12 relative flex items-center\">\r\n        {slides.map((slide, index) => (\r\n          <div \r\n            key={index} \r\n            className={`w-full transition-opacity duration-500 ${activeSlide === index ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}\r\n            style={{ display: activeSlide === index ? 'block' : 'none' }}\r\n          >\r\n            <div className=\"relative flex flex-col md:flex-row gap-6 sm:gap-10 lg:gap-16 items-center\">\r\n              {/* Left Column - Image */}\r\n              <div className=\"relative w-full md:w-1/2 order-2 md:order-1\">\r\n                {/* Main Image Container */}\r\n                <div className=\"relative rounded-lg overflow-hidden shadow-xl\">\r\n                  {/* Business Meeting Image */}\r\n                  <div className=\"relative pt-[75%]\"> {/* 4:3 aspect ratio */}\r\n                    <div\r\n                      className=\"absolute inset-0 bg-cover bg-center\"\r\n                      style={{\r\n                        backgroundImage: `url('${getSlideImage(index)}')`,\r\n                      }}\r\n                      role=\"img\"\r\n                      aria-label={slide.imageDesc}\r\n                    >\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {/* World Map Background */}\r\n                  <div className=\"absolute bottom-0 left-0 right-0 h-32 sm:h-48 overflow-hidden\">\r\n                    <div className=\"absolute inset-0 bg-blue-50 opacity-30\">\r\n                      <svg viewBox=\"0 0 1200 600\" width=\"100%\" height=\"100%\" preserveAspectRatio=\"none\" className=\"opacity-50\">\r\n                        <path d=\"M300,150 Q400,100 500,150 T700,120 T900,150\" fill=\"none\" stroke=\"#d0d0d0\" strokeWidth=\"1\"/>\r\n                        <path d=\"M200,200 Q300,180 400,200 T600,190 T800,210\" fill=\"none\" stroke=\"#d0d0d0\" strokeWidth=\"1\"/>\r\n                        <path d=\"M250,250 Q350,230 450,250 T650,240 T850,260\" fill=\"none\" stroke=\"#d0d0d0\" strokeWidth=\"1\"/>\r\n                        <path d=\"M150,120 L250,120 L300,170 L200,190 Z\" fill=\"#e6e6e6\" stroke=\"#d0d0d0\" strokeWidth=\"0.5\"/>\r\n                        <path d=\"M400,140 L500,130 L550,180 L430,200 Z\" fill=\"#e6e6e6\" stroke=\"#d0d0d0\" strokeWidth=\"0.5\"/>\r\n                        <path d=\"M700,130 L800,120 L820,160 L730,190 Z\" fill=\"#e6e6e6\" stroke=\"#d0d0d0\" strokeWidth=\"0.5\"/>\r\n                        <path d=\"M300,220 L370,210 L390,240 L320,260 Z\" fill=\"#e6e6e6\" stroke=\"#d0d0d0\" strokeWidth=\"0.5\"/>\r\n                        <path d=\"M600,220 L670,210 L690,250 L620,270 Z\" fill=\"#e6e6e6\" stroke=\"#d0d0d0\" strokeWidth=\"0.5\"/>\r\n                      </svg>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              {/* Large Number - Responsive positioning */}\r\n              <div className=\"absolute md:right-1/2 top-0 md:top-0 z-10 md:transform md:translate-x-12 md:-translate-y-24 right-4 sm:right-8\">\r\n                <span\r\n                  className=\"text-[120px] sm:text-[200px] md:text-[280px] font-black leading-none\"\r\n                  style={{\r\n                    color: 'rgba(59, 130, 246, 0.15)',\r\n                    WebkitTextStroke: '2px rgba(37, 99, 235, 0.3)',\r\n                    WebkitTextFillColor: 'rgba(59, 130, 246, 0.08)',\r\n                    textShadow: '0 0 20px rgba(59, 130, 246, 0.1)'\r\n                  }}\r\n                >\r\n                  {slide.number}\r\n                </span>\r\n              </div>\r\n              \r\n              {/* Right Column - Content */}\r\n              <div className=\"w-full md:w-1/2 order-1 md:order-2\">\r\n                <p className=\"text-blue-700 font-medium text-lg sm:text-xl mb-3\">{slide.topTitle}</p>\r\n                <h3 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-4 sm:mb-6\">{slide.title}</h3>\r\n                \r\n                <div className=\"space-y-4 sm:space-y-5\">\r\n                  {slide.content.map((paragraph, i) => (\r\n                    <p key={i} className=\"text-gray-700 text-base sm:text-lg\">{paragraph}</p>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n        \r\n        {/* Navigation Dots - Responsive positioning */}\r\n        <div className=\"absolute left-2 sm:left-0 top-1/2 transform -translate-y-1/2 flex flex-col space-y-4 sm:space-y-6 z-30\">\r\n          {slides.map((_, index) => (\r\n            <button \r\n              key={index} \r\n              onClick={() => handleDotClick(index)}\r\n              className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-colors ${activeSlide === index ? 'bg-blue-500' : 'bg-gray-300'}`}\r\n              aria-label={`Go to slide ${index + 1}`}\r\n            />\r\n          ))}\r\n        </div>\r\n        \r\n        {/* Right Arrow - Responsive positioning */}\r\n        <div className=\"absolute right-2 sm:-right-4 top-1/2 transform -translate-y-1/2 z-30\">\r\n          <button \r\n            onClick={handleNext}\r\n            className=\"w-8 h-8 sm:w-10 sm:h-10 bg-blue-500 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-blue-600 transition-colors\"\r\n            aria-label=\"Next slide\"\r\n          >\r\n            <ChevronRight size={16} className=\"sm:w-5 sm:h-5\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Bottom padding area */}\r\n      <div className=\"py-4 sm:py-8\"></div>\r\n    </section>\r\n  );\r\n};\r\nexport default DigitalStrategySliderSection;"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAOA,MAAM,+BAA4E,CAAC,EAAE,YAAY,EAAE,EAAE;;IACnG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAU;IAEvD,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS;YACb;YACA;YACA;YACA;SACD;QACD,OAAO,MAAM,CAAC,MAAM,IAAI;IAC1B;IAEA,MAAM,SAAkB;QACtB;YACE,QAAQ;YACR,UAAU;YACV,OAAO;YACP,SAAS;gBACP;gBACA;gBACA;aACD;YACD,WAAW;QACb;QACA;YACE,QAAQ;YACR,UAAU;YACV,OAAO;YACP,SAAS;gBACP;gBACA;gBACA;aACD;YACD,WAAW;QACb;QACA;YACE,QAAQ;YACR,UAAU;YACV,OAAO;YACP,SAAS;gBACP;gBACA;gBACA;aACD;YACD,WAAW;QACb;QACA;YACE,QAAQ;YACR,UAAU;YACV,OAAO;YACP,SAAS;gBACP;gBACA;gBACA;aACD;YACD,WAAW;QACb;KACD;IAED,MAAM,aAAa;QACjB,eAAe,CAAC,OAAU,SAAS,OAAO,MAAM,GAAG,IAAI,IAAI,OAAO;IACpE;IAEA,MAAM,iBAAiB,CAAC;QACtB,eAAe;IACjB;IAEA,qBACE,0JAAC;QAAQ,WAAW,CAAC,2CAA2C,EAAE,WAAW;;0BAE3E,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAE,WAAU;kCAAsD;;;;;;kCACnE,0JAAC;wBAAG,WAAU;kCAA6E;;;;;;;;;;;;0BAM7F,0JAAC;gBAAI,WAAU;;oBACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,0JAAC;4BAEC,WAAW,CAAC,uCAAuC,EAAE,gBAAgB,QAAQ,gBAAgB,8BAA8B;4BAC3H,OAAO;gCAAE,SAAS,gBAAgB,QAAQ,UAAU;4BAAO;sCAE3D,cAAA,0JAAC;gCAAI,WAAU;;kDAEb,0JAAC;wCAAI,WAAU;kDAEb,cAAA,0JAAC;4CAAI,WAAU;;8DAEb,0JAAC;oDAAI,WAAU;;wDAAoB;sEACjC,0JAAC;4DACC,WAAU;4DACV,OAAO;gEACL,iBAAiB,CAAC,KAAK,EAAE,cAAc,OAAO,EAAE,CAAC;4DACnD;4DACA,MAAK;4DACL,cAAY,MAAM,SAAS;;;;;;;;;;;;8DAM/B,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC;4DAAI,SAAQ;4DAAe,OAAM;4DAAO,QAAO;4DAAO,qBAAoB;4DAAO,WAAU;;8EAC1F,0JAAC;oEAAK,GAAE;oEAA8C,MAAK;oEAAO,QAAO;oEAAU,aAAY;;;;;;8EAC/F,0JAAC;oEAAK,GAAE;oEAA8C,MAAK;oEAAO,QAAO;oEAAU,aAAY;;;;;;8EAC/F,0JAAC;oEAAK,GAAE;oEAA8C,MAAK;oEAAO,QAAO;oEAAU,aAAY;;;;;;8EAC/F,0JAAC;oEAAK,GAAE;oEAAwC,MAAK;oEAAU,QAAO;oEAAU,aAAY;;;;;;8EAC5F,0JAAC;oEAAK,GAAE;oEAAwC,MAAK;oEAAU,QAAO;oEAAU,aAAY;;;;;;8EAC5F,0JAAC;oEAAK,GAAE;oEAAwC,MAAK;oEAAU,QAAO;oEAAU,aAAY;;;;;;8EAC5F,0JAAC;oEAAK,GAAE;oEAAwC,MAAK;oEAAU,QAAO;oEAAU,aAAY;;;;;;8EAC5F,0JAAC;oEAAK,GAAE;oEAAwC,MAAK;oEAAU,QAAO;oEAAU,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQtG,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CACC,WAAU;4CACV,OAAO;gDACL,OAAO;gDACP,kBAAkB;gDAClB,qBAAqB;gDACrB,YAAY;4CACd;sDAEC,MAAM,MAAM;;;;;;;;;;;kDAKjB,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDAAE,WAAU;0DAAqD,MAAM,QAAQ;;;;;;0DAChF,0JAAC;gDAAG,WAAU;0DAAyE,MAAM,KAAK;;;;;;0DAElG,0JAAC;gDAAI,WAAU;0DACZ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,WAAW,kBAC7B,0JAAC;wDAAU,WAAU;kEAAsC;uDAAnD;;;;;;;;;;;;;;;;;;;;;;2BA9DX;;;;;kCAuET,0JAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,0JAAC;gCAEC,SAAS,IAAM,eAAe;gCAC9B,WAAW,CAAC,qDAAqD,EAAE,gBAAgB,QAAQ,gBAAgB,eAAe;gCAC1H,cAAY,CAAC,YAAY,EAAE,QAAQ,GAAG;+BAHjC;;;;;;;;;;kCASX,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAW;sCAEX,cAAA,0JAAC,kNAAA,CAAA,eAAY;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMxC,0JAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GApLM;KAAA;uCAqLS", "debugId": null}}, {"offset": {"line": 4130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\nimport React, { useState } from 'react';\r\nimport Image from 'next/image';\r\nimport { Testimonial } from '@/types';\r\n\r\ninterface ClientTestimonialsSectionProps {\r\n  className?: string;\r\n}\r\n\r\nconst ClientTestimonialsSection: React.FC<ClientTestimonialsSectionProps> = ({ className = '' }) => {\r\n  const [activeTestimonial, setActiveTestimonial] = useState<number>(0);\r\n\r\n  const testimonials: Testimonial[] = [\r\n    {\r\n      id: 1,\r\n      name: \"<PERSON><PERSON>\",\r\n      title: \"Executive Director | AIC\",\r\n      image: \"/homepage/elira kokona.png\",\r\n      heading: \"How Vesa Solutions Elevated Our Digital Presence\",\r\n      content: \"Working with Vesa Solutions has been a game-changer for our digital presence. They expertly built our website from the ground up, ensuring it's not only visually appealing but also optimized for performance and SEO. Thanks to their comprehensive approach, we've seen significant improvements in our online visibility and search engine rankings.\",\r\n      additionalContent: \"Their team's professionalism, creativity, and dedication have made them a trusted partner in our digital growth. Highly recommended for any business looking to elevate their online presence!\",\r\n      stars: 5,\r\n      signature: \"<PERSON><PERSON>\",\r\n      position: \"Executive Director at Albanian Investment Corporation\"\r\n    },\r\n    {\r\n      id: 2,\r\n      name: \"<PERSON><PERSON>\",\r\n      title: \"Founder | QellzA\",\r\n      image: \"/homepage/Sonila-Mustafa.png\",\r\n      heading: \"Transformed Our Brand's Digital Presence\",\r\n      content: \"Vesa Solutions completely revolutionized our online presence. Their strategic approach to website design and digital marketing has helped us stand out in a competitive market. The team took time to understand our business needs and delivered solutions that exceeded our expectations.\",\r\n      additionalContent: \"Our website traffic and conversion rates have increased significantly since working with them. I highly recommend their services to any business looking for digital transformation.\",\r\n      stars: 5,\r\n      signature: \"Sonila Mustafa\",\r\n      position: \"Founder at QellzA\"\r\n    },\r\n    {\r\n  id: 3,\r\n  name: \"Durana Tech Park CEO\",\r\n  title: \"Founder | Durana Tech Park\",\r\n  image: \"/homepage/Durana.png\",\r\n  heading: \"A Strategic Digital Launchpad for Albania’s Leading Tech Hub\",\r\n  content: \"As the founder of Durana Tech Park, I knew we needed a digital identity that could match the ambition of Albania’s first dedicated technology and innovation ecosystem. Our platform had to clearly communicate our mission: to attract startups, global tech companies, researchers, and investors to a thriving hub between Tirana and Durrës.\",\r\n  additionalContent: \"Vesa Solutions rose to the occasion. They delivered a sleek, scalable, and informative website that showcases our infrastructure, investor incentives, and global opportunities. We've seen an uptick in interest from local and international tech players, and the site has become a key tool for onboarding and partner outreach. Their continued support has been essential to our digital strategy.\",\r\n  stars: 5,\r\n  signature: \"Durana Tech Park Management\",\r\n  position: \"Executive Board\"\r\n},\r\n\r\n    {\r\n      id: 4,\r\n      name: \"Florian Duci\",\r\n      title: \"Founder | Future's Past Events\",\r\n      image: \"/homepage/florian duci.png\",\r\n      heading: \"Strategic Digital Approach for Events Business\",\r\n      content: \"Vesa Solutions understood the unique challenges of marketing an events business online. They created a website that beautifully showcases our past events while making it easy for potential clients to inquire about our services.\",\r\n      additionalContent: \"Their ongoing SEO and social media strategy has helped us reach new clients and build our brand reputation. Working with them has been a pleasure from day one.\",\r\n      stars: 5,\r\n      signature: \"Florian Duci\",\r\n      position: \"Founder at Future's Past Events\"\r\n    }\r\n  ];\r\n\r\n  const QuoteIcon: React.FC = () => (\r\n    <svg \r\n      xmlns=\"http://www.w3.org/2000/svg\" \r\n      width=\"36\" \r\n      height=\"36\"\r\n      className=\"sm:w-12 sm:h-12\" \r\n      viewBox=\"0 0 24 24\"\r\n      fill=\"rgba(59, 130, 246, 0.2)\"\r\n    >\r\n      <path d=\"M9.983 3v7.391c0 5.704-3.731 9.57-8.983 10.609l-.995-2.151c2.432-.917 3.995-3.638 3.995-5.849h-4v-10h9.983zm14.017 0v7.391c0 5.704-3.748 9.571-9 10.609l-.996-2.151c2.433-.917 3.996-3.638 3.996-5.849h-3.983v-10h9.983z\"/>\r\n    </svg>\r\n  );\r\n\r\n  const renderStars = (count: number) => {\r\n    return [...Array(count)].map((_, i) => (\r\n      <svg key={i} className=\"w-4 h-4 sm:w-5 sm:h-5 text-yellow-400 fill-current\" viewBox=\"0 0 24 24\">\r\n        <path d=\"M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z\" />\r\n      </svg>\r\n    ));\r\n  };\r\n\r\n  return (\r\n    <section className={`w-full bg-white pb-12 sm:pb-24 ${className}`}>\r\n      <div className=\"max-w-[1440px] mx-auto px-4 sm:px-6 py-8 sm:py-12\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-10 sm:mb-16\">\r\n          <h3 className=\"text-blue-700 font-medium text-base sm:text-lg mb-2\">CLIENT SUCCESS STORIES</h3>\r\n          <h2 className=\"text-gray-800 text-xl sm:text-2xl font-bold\">WHAT OUR CLIENTS SAY</h2>\r\n        </div>\r\n        \r\n        {/* Main content */}\r\n        <div className=\"flex flex-col lg:flex-row lg:gap-24 lg:justify-between\">\r\n          {/* Left column with profiles */}\r\n          <div className=\"w-full lg:w-2/5 relative lg:pl-0 lg:mt-16 mb-8 lg:mb-0\">\r\n            {/* Mobile horizontal scroll */}\r\n            <div className=\"lg:hidden flex overflow-x-auto space-x-4 pb-4 mb-6\">\r\n              {testimonials.map((testimonial, index) => (\r\n                <div \r\n                  key={testimonial.id} \r\n                  className=\"flex-shrink-0\"\r\n                  onClick={() => setActiveTestimonial(index)}\r\n                >\r\n                  <div \r\n                    className={`flex items-center shadow-md cursor-pointer rounded-2xl transition-all duration-300 w-64 ${\r\n                      index === activeTestimonial ? 'bg-blue-500 scale-105' : 'bg-white hover:shadow-lg'\r\n                    }`}\r\n                  >\r\n                    <div className=\"p-3\">\r\n                      <div className=\"w-12 h-12 rounded-full border-2 border-white overflow-hidden\">\r\n                        <Image\r\n                          src={testimonial.image}\r\n                          alt={testimonial.name}\r\n                          width={48}\r\n                          height={48}\r\n                          className=\"w-full h-full object-cover\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"p-3\">\r\n                      <div\r\n                        className={`text-lg font-medium ${\r\n                          index === activeTestimonial ? 'text-white' : 'text-blue-700'\r\n                        }`}\r\n                        style={{ fontFamily: 'cursive' }}\r\n                      >\r\n                        {testimonial.name}\r\n                      </div>\r\n                      <div \r\n                        className={`text-xs ${\r\n                          index === activeTestimonial ? 'text-white' : 'text-gray-600'\r\n                        }`}\r\n                      >\r\n                        {testimonial.title}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Desktop vertical layout */}\r\n            <div className=\"hidden lg:block relative\">\r\n              {/* Vertical timeline line */}\r\n              <div className=\"absolute right-0 top-0 bottom-0 w-px bg-gray-200\"></div>\r\n              \r\n              {testimonials.map((testimonial, index) => (\r\n                <div \r\n                  key={testimonial.id} \r\n                  className=\"relative mb-14\"\r\n                  onClick={() => setActiveTestimonial(index)}\r\n                >\r\n                  {/* Profile card */}\r\n                  <div \r\n                    className={`flex items-center shadow-md cursor-pointer rounded-2xl transition-all duration-300 ${\r\n                      index === activeTestimonial ? 'bg-blue-500 scale-105' : 'bg-white hover:shadow-lg'\r\n                    }`}\r\n                    style={{ width: '90%' }}\r\n                  >\r\n                    <div className=\"p-3\">\r\n                      <div className=\"w-16 h-16 rounded-full border-2 border-white overflow-hidden\">\r\n                        <Image\r\n                          src={testimonial.image}\r\n                          alt={testimonial.name}\r\n                          width={64}\r\n                          height={64}\r\n                          className=\"w-full h-full object-cover\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"p-3\">\r\n                      <div\r\n                        className={`text-xl font-medium ${\r\n                          index === activeTestimonial ? 'text-white' : 'text-blue-700'\r\n                        }`}\r\n                        style={{ fontFamily: 'cursive' }}\r\n                      >\r\n                        {testimonial.name}\r\n                      </div>\r\n                      <div \r\n                        className={`text-sm ${\r\n                          index === activeTestimonial ? 'text-white' : 'text-gray-600'\r\n                        }`}\r\n                      >\r\n                        {testimonial.title}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {/* Horizontal connector line */}\r\n                  <div \r\n                    className={`absolute top-1/2 h-px transform -translate-y-1/2 transition-colors ${\r\n                      index === activeTestimonial ? 'bg-blue-500' : 'bg-gray-200'\r\n                    }`}\r\n                    style={{ \r\n                      left: '90%', \r\n                      width: '10%'\r\n                    }}\r\n                  ></div>\r\n                  \r\n                  {/* Timeline dot */}\r\n                  <div \r\n                    className={`absolute top-1/2 right-0 w-3 h-3 rounded-full transform translate-x-1.5 -translate-y-1/2 transition-colors ${\r\n                      index === activeTestimonial ? 'bg-blue-500' : 'bg-gray-300'\r\n                    }`}\r\n                  ></div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Right column with testimonial content */}\r\n          <div className=\"w-full lg:w-3/5 relative\">\r\n            \r\n            {/* Testimonial content */}\r\n            {testimonials.map((testimonial, index) => (\r\n              <div \r\n                key={testimonial.id}\r\n                className={`transition-all duration-300 ${\r\n                  index === activeTestimonial ? 'block opacity-100' : 'hidden opacity-0'\r\n                }`}\r\n              >\r\n                <div className=\"bg-blue-50 rounded-lg p-6 sm:p-12 pt-8 sm:pt-16 mt-8 sm:mt-20 shadow-md relative\">\r\n                  {/* Quote icon */}\r\n                  <div className=\"absolute top-4 sm:top-6 left-4 sm:left-6\">\r\n                    <QuoteIcon />\r\n                  </div>\r\n                  \r\n                  {/* Testimonial content */}\r\n                  <div className=\"relative z-10 pl-8 sm:pl-16\">\r\n                    <h3 className=\"text-blue-700 text-lg sm:text-xl font-bold mb-4 sm:mb-6\">{testimonial.heading}</h3>\r\n                    <p className=\"text-gray-700 mb-3 sm:mb-4 leading-relaxed text-sm sm:text-base\">{testimonial.content}</p>\r\n                    <p className=\"text-gray-700 mb-4 sm:mb-6 leading-relaxed text-sm sm:text-base\">{testimonial.additionalContent}</p>\r\n                    \r\n                    {/* Star rating */}\r\n                    <div className=\"flex mb-3 sm:mb-4\">\r\n                      {renderStars(testimonial.stars)}\r\n                    </div>\r\n                    \r\n                    {/* Signature */}\r\n                    <div className=\"flex flex-col sm:flex-row sm:items-center border-t border-gray-200 pt-4 gap-2 sm:gap-0\">\r\n                      <div className=\"text-blue-700 text-xl sm:text-2xl sm:mr-2\" style={{ fontFamily: 'cursive' }}>\r\n                        {testimonial.signature}\r\n                      </div>\r\n                      <div className=\"text-gray-600 text-xs sm:text-sm sm:border-l sm:border-gray-300 sm:pl-2\">\r\n                        {testimonial.position}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ClientTestimonialsSection;"], "names": [], "mappings": "AAAA,gDAAgD;;;;;AAChD;AACA;;;;;AAOA,MAAM,4BAAsE,CAAC,EAAE,YAAY,EAAE,EAAE;;IAC7F,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAU;IAEnE,MAAM,eAA8B;QAClC;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,mBAAmB;YACnB,OAAO;YACP,WAAW;YACX,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,mBAAmB;YACnB,OAAO;YACP,WAAW;YACX,UAAU;QACZ;QACA;YACF,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,mBAAmB;YACnB,OAAO;YACP,WAAW;YACX,UAAU;QACZ;QAEI;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,mBAAmB;YACnB,OAAO;YACP,WAAW;YACX,UAAU;QACZ;KACD;IAED,MAAM,YAAsB,kBAC1B,0JAAC;YACC,OAAM;YACN,OAAM;YACN,QAAO;YACP,WAAU;YACV,SAAQ;YACR,MAAK;sBAEL,cAAA,0JAAC;gBAAK,GAAE;;;;;;;;;;;IAIZ,MAAM,cAAc,CAAC;QACnB,OAAO;eAAI,MAAM;SAAO,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC/B,0JAAC;gBAAY,WAAU;gBAAqD,SAAQ;0BAClF,cAAA,0JAAC;oBAAK,GAAE;;;;;;eADA;;;;;IAId;IAEA,qBACE,0JAAC;QAAQ,WAAW,CAAC,+BAA+B,EAAE,WAAW;kBAC/D,cAAA,0JAAC;YAAI,WAAU;;8BAEb,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAG,WAAU;sCAAsD;;;;;;sCACpE,0JAAC;4BAAG,WAAU;sCAA8C;;;;;;;;;;;;8BAI9D,0JAAC;oBAAI,WAAU;;sCAEb,0JAAC;4BAAI,WAAU;;8CAEb,0JAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,0JAAC;4CAEC,WAAU;4CACV,SAAS,IAAM,qBAAqB;sDAEpC,cAAA,0JAAC;gDACC,WAAW,CAAC,wFAAwF,EAClG,UAAU,oBAAoB,0BAA0B,4BACxD;;kEAEF,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC;4DAAI,WAAU;sEACb,cAAA,0JAAC,yHAAA,CAAA,UAAK;gEACJ,KAAK,YAAY,KAAK;gEACtB,KAAK,YAAY,IAAI;gEACrB,OAAO;gEACP,QAAQ;gEACR,WAAU;;;;;;;;;;;;;;;;kEAIhB,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEACC,WAAW,CAAC,oBAAoB,EAC9B,UAAU,oBAAoB,eAAe,iBAC7C;gEACF,OAAO;oEAAE,YAAY;gEAAU;0EAE9B,YAAY,IAAI;;;;;;0EAEnB,0JAAC;gEACC,WAAW,CAAC,QAAQ,EAClB,UAAU,oBAAoB,eAAe,iBAC7C;0EAED,YAAY,KAAK;;;;;;;;;;;;;;;;;;2CAlCnB,YAAY,EAAE;;;;;;;;;;8CA2CzB,0JAAC;oCAAI,WAAU;;sDAEb,0JAAC;4CAAI,WAAU;;;;;;wCAEd,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,0JAAC;gDAEC,WAAU;gDACV,SAAS,IAAM,qBAAqB;;kEAGpC,0JAAC;wDACC,WAAW,CAAC,mFAAmF,EAC7F,UAAU,oBAAoB,0BAA0B,4BACxD;wDACF,OAAO;4DAAE,OAAO;wDAAM;;0EAEtB,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC;oEAAI,WAAU;8EACb,cAAA,0JAAC,yHAAA,CAAA,UAAK;wEACJ,KAAK,YAAY,KAAK;wEACtB,KAAK,YAAY,IAAI;wEACrB,OAAO;wEACP,QAAQ;wEACR,WAAU;;;;;;;;;;;;;;;;0EAIhB,0JAAC;gEAAI,WAAU;;kFACb,0JAAC;wEACC,WAAW,CAAC,oBAAoB,EAC9B,UAAU,oBAAoB,eAAe,iBAC7C;wEACF,OAAO;4EAAE,YAAY;wEAAU;kFAE9B,YAAY,IAAI;;;;;;kFAEnB,0JAAC;wEACC,WAAW,CAAC,QAAQ,EAClB,UAAU,oBAAoB,eAAe,iBAC7C;kFAED,YAAY,KAAK;;;;;;;;;;;;;;;;;;kEAMxB,0JAAC;wDACC,WAAW,CAAC,mEAAmE,EAC7E,UAAU,oBAAoB,gBAAgB,eAC9C;wDACF,OAAO;4DACL,MAAM;4DACN,OAAO;wDACT;;;;;;kEAIF,0JAAC;wDACC,WAAW,CAAC,2GAA2G,EACrH,UAAU,oBAAoB,gBAAgB,eAC9C;;;;;;;+CAxDC,YAAY,EAAE;;;;;;;;;;;;;;;;;sCAgE3B,0JAAC;4BAAI,WAAU;sCAGZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,0JAAC;oCAEC,WAAW,CAAC,4BAA4B,EACtC,UAAU,oBAAoB,sBAAsB,oBACpD;8CAEF,cAAA,0JAAC;wCAAI,WAAU;;0DAEb,0JAAC;gDAAI,WAAU;0DACb,cAAA,0JAAC;;;;;;;;;;0DAIH,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;wDAAG,WAAU;kEAA2D,YAAY,OAAO;;;;;;kEAC5F,0JAAC;wDAAE,WAAU;kEAAmE,YAAY,OAAO;;;;;;kEACnG,0JAAC;wDAAE,WAAU;kEAAmE,YAAY,iBAAiB;;;;;;kEAG7G,0JAAC;wDAAI,WAAU;kEACZ,YAAY,YAAY,KAAK;;;;;;kEAIhC,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;gEAA4C,OAAO;oEAAE,YAAY;gEAAU;0EACvF,YAAY,SAAS;;;;;;0EAExB,0JAAC;gEAAI,WAAU;0EACZ,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;mCA5BxB,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCnC;GA3PM;KAAA;uCA6PS", "debugId": null}}, {"offset": {"line": 4596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\nimport React from 'react';\r\n\r\ninterface WhyChooseVesaSectionProps {\r\n  className?: string;\r\n}\r\n\r\nconst WhyChooseVesaSection: React.FC<WhyChooseVesaSectionProps> = ({ className = '' }) => {\r\n  const handleAboutUs = (): void => {\r\n    window.location.href = '/about';\r\n  };\r\n\r\n  return (\r\n    <section className={`w-full bg-blue-50 min-h-screen flex items-center ${className}`}>\r\n      <div className=\"container mx-auto px-4 md:px-0 py-8 sm:py-16\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-12 items-center\">\r\n          {/* Left Column - Capsule Image Display */}\r\n          <div className=\"lg:col-span-6 flex justify-center order-2 lg:order-1\">\r\n            <div className=\"relative h-[400px] sm:h-[500px] lg:h-[650px] w-full max-w-sm sm:max-w-md lg:max-w-lg\">\r\n\r\n              \r\n              {/* Capsule 1 */}\r\n              <div className=\"absolute left-[30px] sm:left-[45px] lg:left-[20px] top-[120px] sm:top-[150px] lg:top-44 h-[200px] sm:h-[280px] lg:h-[380px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10\"\r\n                   style={{\r\n                     backgroundImage: \"url('/homepage/map-lying-wooden-table.jpg')\",\r\n                     backgroundSize: \"500px 650px\",\r\n                     backgroundPosition: \"calc(-30px + 0px) calc(-120px + 0px)\",\r\n                     backgroundRepeat: \"no-repeat\"\r\n                   }}>\r\n              </div>\r\n\r\n              {/* Capsule 2 */}\r\n              <div className=\"absolute left-[90px] sm:left-[125px] lg:left-[120px] top-[20px] sm:top-[30px] lg:top-9 h-[320px] sm:h-[420px] lg:h-[580px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10\"\r\n                   style={{\r\n                     backgroundImage: \"url('/homepage/map-lying-wooden-table.jpg')\",\r\n                     backgroundSize: \"500px 650px\",\r\n                     backgroundPosition: \"calc(-90px + 0px) calc(-20px + 0px)\",\r\n                     backgroundRepeat: \"no-repeat\"\r\n                   }}>\r\n              </div>\r\n\r\n              {/* Capsule 3 */}\r\n              <div className=\"absolute left-[150px] sm:left-[205px] lg:left-[220px] top-[60px] sm:top-[80px] lg:top-28 h-[300px] sm:h-[400px] lg:h-[560px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10\"\r\n                   style={{\r\n                     backgroundImage: \"url('/homepage/map-lying-wooden-table.jpg')\",\r\n                     backgroundSize: \"500px 650px\",\r\n                     backgroundPosition: \"calc(-150px + 0px) calc(-60px + 0px)\",\r\n                     backgroundRepeat: \"no-repeat\"\r\n                   }}>\r\n              </div>\r\n\r\n              {/* Capsule 4 */}\r\n              <div className=\"absolute left-[210px] sm:left-[285px] lg:left-[320px] top-[40px] sm:top-[50px] lg:top-20 h-[280px] sm:h-[350px] lg:h-[480px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10\"\r\n                   style={{\r\n                     backgroundImage: \"url('/homepage/map-lying-wooden-table.jpg')\",\r\n                     backgroundSize: \"500px 650px\",\r\n                     backgroundPosition: \"calc(-210px + 0px) calc(-40px + 0px)\",\r\n                     backgroundRepeat: \"no-repeat\"\r\n                   }}>\r\n              </div>\r\n\r\n              {/* Capsule 5 */}\r\n              <div className=\"absolute left-[270px] sm:left-[365px] lg:left-[420px] top-[100px] sm:top-[120px] lg:top-40 h-[280px] sm:h-[350px] lg:h-[480px] w-[50px] sm:w-[70px] lg:w-[90px] border-2 lg:border-4 border-white rounded-full overflow-hidden shadow-lg z-10\"\r\n                   style={{\r\n                     backgroundImage: \"url('/homepage/map-lying-wooden-table.jpg')\",\r\n                     backgroundSize: \"500px 650px\",\r\n                     backgroundPosition: \"calc(-270px + 0px) calc(-100px + 0px)\",\r\n                     backgroundRepeat: \"no-repeat\"\r\n                   }}>\r\n              </div>\r\n      \r\n              {/* Circular digital marketing badge with complete text around */}\r\n              <div className=\"absolute top-[40px] sm:top-[60px] lg:top-24 left-2 sm:left-3 lg:left-4 bg-white rounded-full h-20 w-20 sm:h-24 sm:w-24 lg:h-32 lg:w-32 flex items-center justify-center z-30 shadow-md\">\r\n                <div className=\"relative h-full w-full\">\r\n                  <svg viewBox=\"0 0 100 100\" width=\"100%\" height=\"100%\">\r\n                    <defs>\r\n                      <path id=\"circle-path-2\" d=\"M 50,50 m -37,0 a 37,37 0 1,1 74,0 a 37,37 0 1,1 -74,0\" />\r\n                    </defs>\r\n                    <text fontSize=\"11\" fill=\"#0084FF\" fontWeight=\"600\" letterSpacing=\"1\" className=\"hidden lg:block\">\r\n                      <textPath xlinkHref=\"#circle-path-2\" startOffset=\"0%\">\r\n                        DIGITAL • MARKETING • SOLUTIONS •\r\n                      </textPath>\r\n                    </text>\r\n                    <text fontSize=\"9\" fill=\"#0084FF\" fontWeight=\"600\" letterSpacing=\"1\" className=\"block sm:hidden\">\r\n                      <textPath xlinkHref=\"#circle-path-2\" startOffset=\"0%\">\r\n                        DIGITAL • MARKETING • SOLUTIONS •\r\n                      </textPath>\r\n                    </text>\r\n                    <text fontSize=\"10\" fill=\"#0084FF\" fontWeight=\"600\" letterSpacing=\"1\" className=\"hidden sm:block lg:hidden\">\r\n                      <textPath xlinkHref=\"#circle-path-2\" startOffset=\"0%\">\r\n                        DIGITAL • MARKETING • SOLUTIONS •\r\n                      </textPath>\r\n                    </text>\r\n                    <circle cx=\"50\" cy=\"50\" r=\"18\" fill=\"white\" />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Right Column - Content */}\r\n          <div className=\"lg:col-span-6 lg:pl-10 order-1 lg:order-2\">            \r\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-6 sm:mb-8 leading-tight\">\r\n              WHY CHOOSE VESA FOR YOUR DIGITAL MARKETING AGENCY?\r\n            </h2>\r\n            \r\n            <div className=\"space-y-4 sm:space-y-6 mb-6 sm:mb-8\">\r\n              <p className=\"text-gray-700 text-base sm:text-lg leading-relaxed\">\r\n                Vesa is a full-service digital marketing agency. We&apos;ve been providing a wide range of services to clients of all industries since 2005. Our digital marketing services include consulting and management options for a variety of online marketing tactics including search engine optimization (SEO), pay-per-click (PPC) ads, Amazon store optimization, copywriting, conversion rate optimization (CRO), and more. We also offer expert web design and development services for both eCommerce and B2B companies. Don&apos;t just partner with any digital marketing agency; work with a company you can trust.\r\n              </p>\r\n            </div>\r\n            \r\n            <h3 className=\"text-lg sm:text-xl font-bold text-gray-800 mb-3 sm:mb-4\">\r\n              Meet our team of digital marketing experts\r\n            </h3>\r\n            \r\n            <p className=\"text-gray-700 text-base sm:text-lg leading-relaxed mb-8 sm:mb-10\">\r\n              You might have a website, but it isn&apos;t doing your business much good if it isn&apos;t being picked up by the search engines. To make sure customers find your site online, we can take your online presence to the next level — your website design, copywriting, keywords, social media presence and more. Working with us, you get the most from your return on marketing investment.\r\n            </p>\r\n            \r\n            <button\r\n              onClick={handleAboutUs}\r\n              className=\"bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-full px-8 sm:px-10 py-3 sm:py-4 text-base sm:text-lg transition-colors shadow-lg w-full sm:w-auto\"\r\n            >\r\n              ABOUT US\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default WhyChooseVesaSection;"], "names": [], "mappings": "AAAA,2CAA2C;;;;;;AAO3C,MAAM,uBAA4D,CAAC,EAAE,YAAY,EAAE,EAAE;IACnF,MAAM,gBAAgB;QACpB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,0JAAC;QAAQ,WAAW,CAAC,iDAAiD,EAAE,WAAW;kBACjF,cAAA,0JAAC;YAAI,WAAU;sBACb,cAAA,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;;8CAIb,0JAAC;oCAAI,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;wCAChB,oBAAoB;wCACpB,kBAAkB;oCACpB;;;;;;8CAIL,0JAAC;oCAAI,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;wCAChB,oBAAoB;wCACpB,kBAAkB;oCACpB;;;;;;8CAIL,0JAAC;oCAAI,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;wCAChB,oBAAoB;wCACpB,kBAAkB;oCACpB;;;;;;8CAIL,0JAAC;oCAAI,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;wCAChB,oBAAoB;wCACpB,kBAAkB;oCACpB;;;;;;8CAIL,0JAAC;oCAAI,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;wCAChB,oBAAoB;wCACpB,kBAAkB;oCACpB;;;;;;8CAIL,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAI,SAAQ;4CAAc,OAAM;4CAAO,QAAO;;8DAC7C,0JAAC;8DACC,cAAA,0JAAC;wDAAK,IAAG;wDAAgB,GAAE;;;;;;;;;;;8DAE7B,0JAAC;oDAAK,UAAS;oDAAK,MAAK;oDAAU,YAAW;oDAAM,eAAc;oDAAI,WAAU;8DAC9E,cAAA,0JAAC;wDAAS,WAAU;wDAAiB,aAAY;kEAAK;;;;;;;;;;;8DAIxD,0JAAC;oDAAK,UAAS;oDAAI,MAAK;oDAAU,YAAW;oDAAM,eAAc;oDAAI,WAAU;8DAC7E,cAAA,0JAAC;wDAAS,WAAU;wDAAiB,aAAY;kEAAK;;;;;;;;;;;8DAIxD,0JAAC;oDAAK,UAAS;oDAAK,MAAK;oDAAU,YAAW;oDAAM,eAAc;oDAAI,WAAU;8DAC9E,cAAA,0JAAC;wDAAS,WAAU;wDAAiB,aAAY;kEAAK;;;;;;;;;;;8DAIxD,0JAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9C,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAG,WAAU;0CAAsF;;;;;;0CAIpG,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAE,WAAU;8CAAqD;;;;;;;;;;;0CAKpE,0JAAC;gCAAG,WAAU;0CAA0D;;;;;;0CAIxE,0JAAC;gCAAE,WAAU;0CAAmE;;;;;;0CAIhF,0JAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KA5HM;uCA8HS", "debugId": null}}, {"offset": {"line": 4890, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\nimport React, { useEffect, useRef, useState } from 'react';\r\nimport Image from 'next/image';\r\n// ChevronRight import removed as buttons were removed\r\nimport { WhyChooseStep } from '@/types';\r\n\r\ninterface HorizontalScrollSectionProps {\r\n  className?: string;\r\n}\r\n\r\nconst HorizontalScrollSection: React.FC<HorizontalScrollSectionProps> = ({ className = '' }) => {\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const sectionsRef = useRef<HTMLDivElement>(null);\r\n  const progressRef = useRef<HTMLDivElement>(null);\r\n  const [isMobile, setIsMobile] = useState<boolean>(false);\r\n\r\n  // Horizontal scroll steps data\r\n  const whyChooseSteps: WhyChooseStep[] = [\r\n    {\r\n      id: \"01\",\r\n      title: \"We Have Proven Results\",\r\n      description: \"Why would you work with a digital marketing agency that doesn't provide you with the results you are looking for? Stop throwing away your hard-earned money on a strategy that isn't converting. <PERSON><PERSON><PERSON> takes the time to listen to your business' goals and needs, and then builds a custom strategy to reach these goals.\",\r\n      image: \"/homepage/proven results.jpg\"\r\n    },\r\n    {\r\n      id: \"02\",\r\n      title: \"We Are Honest & Ethical\",\r\n      description: \"At Vesa, we believe in honesty, integrity, and respecting our clients. We don't believe in 'selling' a service that you don't need or applying a one-size-fits-all approach to each client. We dedicate our time to achieving higher rankings, increased traffic and conversions, and a higher ROI for our clients.\",\r\n      image: \"/homepage/honest and ethical.avif\"\r\n    },\r\n    {\r\n      id: \"03\",\r\n      title: \"We Know Digital Marketing\",\r\n      description: \"Working with a digital marketing company that actually knows digital marketing seems obvious, but unfortunately, many firms fail to deliver positive results. Our strategies are proven to work. In fact, the majority of our clients pay for their entire website in the first month from new business leads as a direct result of our online marketing and SEO efforts.\",\r\n      image: \"/homepage/digital marketing.jpg\"\r\n    },\r\n    {\r\n      id: \"04\",\r\n      title: \"We Put Clients First\",\r\n      description: \"Maintaining a client-first mentality is a priority at Vesa, helping our clients grow and follow through on business promises. What makes Vesa different from other agencies is that we show up, we're present and respectful, and we always go above and beyond for our clients to deliver exceptional results.\",\r\n      image: \"/homepage/client first.avif\"\r\n    }\r\n  ];\r\n\r\n  // Check for mobile screen size\r\n  useEffect(() => {\r\n    const checkMobile = () => {\r\n      setIsMobile(window.innerWidth < 1024); // lg breakpoint\r\n    };\r\n    \r\n    checkMobile();\r\n    window.addEventListener('resize', checkMobile);\r\n    \r\n    return () => window.removeEventListener('resize', checkMobile);\r\n  }, []);\r\n  \r\n  // Function to handle manual navigation in horizontal scroll\r\n  const scrollToStep = (): void => {\r\n    if (sectionsRef.current && containerRef.current) {\r\n      window.scrollTo({\r\n        top: containerRef.current.offsetTop,\r\n        behavior: 'smooth'\r\n      });\r\n    }\r\n  };\r\n\r\n  // GSAP Horizontal Scroll Animation - Desktop Only\r\n  useEffect(() => {\r\n    // Only run on client side and desktop\r\n    if (typeof window === 'undefined' || isMobile) {\r\n      // If switching to mobile, kill all ScrollTriggers to prevent conflicts\r\n      if (typeof window !== 'undefined') {\r\n        import('gsap/ScrollTrigger').then(({ ScrollTrigger }) => {\r\n          ScrollTrigger.killAll();\r\n        }).catch(() => {});\r\n      }\r\n      return;\r\n    }\r\n\r\n    const container = containerRef.current;\r\n    const sections = sectionsRef.current;\r\n    const progressBar = progressRef.current;\r\n    \r\n    if (!container || !sections || !progressBar) return;\r\n\r\n    let ctx: { revert: () => void } | null = null;\r\n    let isDestroyed = false;\r\n\r\n    // Dynamic import of GSAP to avoid SSR issues\r\n    const loadGSAP = async () => {\r\n      try {\r\n        const { gsap } = await import('gsap');\r\n        const { ScrollTrigger } = await import('gsap/ScrollTrigger');\r\n        \r\n        // Check if component is still mounted\r\n        if (isDestroyed) return;\r\n        \r\n        // Register ScrollTrigger plugin\r\n        gsap.registerPlugin(ScrollTrigger);\r\n        \r\n        // Kill any existing ScrollTriggers first\r\n        ScrollTrigger.killAll();\r\n        \r\n        // Create a ScrollTrigger context for cleanup\r\n        ctx = gsap.context(() => {\r\n          // Double check elements still exist\r\n          if (!container || !sections || !progressBar || isDestroyed) return;\r\n          \r\n          // Calculate the total width needed for horizontal scroll\r\n          const scrollWidth = sections.scrollWidth - container.offsetWidth;\r\n\r\n          // Create the horizontal scroll animation\r\n          const scrollTween = gsap.to(sections, {\r\n            x: -scrollWidth,\r\n            ease: \"none\",\r\n          });\r\n\r\n          // Animate progress bar\r\n          gsap.to(progressBar, {\r\n            scaleX: 1,\r\n            ease: \"none\",\r\n            scrollTrigger: {\r\n              trigger: container,\r\n              start: \"top top\",\r\n              end: () => `+=${scrollWidth}`,\r\n              scrub: 1,\r\n            }\r\n          });\r\n\r\n          // Create ScrollTrigger\r\n          ScrollTrigger.create({\r\n            trigger: container,\r\n            start: \"top top\",\r\n            end: () => `+=${scrollWidth}`,\r\n            scrub: 1,\r\n            pin: true,\r\n            animation: scrollTween,\r\n            invalidateOnRefresh: true\r\n          });\r\n\r\n          // Animate cards\r\n          const cards = gsap.utils.toArray('.step-card');\r\n          cards.forEach((card) => {\r\n            if (isDestroyed) return;\r\n            // Card entrance animation\r\n            gsap.fromTo(card as HTMLElement, \r\n              { \r\n                opacity: 0, \r\n                y: 50,\r\n              },\r\n              {\r\n                opacity: 1,\r\n                y: 0,\r\n                duration: 0.8,\r\n                ease: \"power2.out\",\r\n                scrollTrigger: {\r\n                  trigger: card as HTMLElement,\r\n                  start: \"left 70%\",\r\n                  end: \"left 30%\",\r\n                  scrub: 1,\r\n                  horizontal: true,\r\n                  containerAnimation: scrollTween,\r\n                }\r\n              }\r\n            );\r\n          });\r\n        }, container);\r\n\r\n        // Handle resize to maintain responsiveness\r\n        const handleResize = () => {\r\n          if (!isDestroyed) {\r\n            ScrollTrigger.refresh();\r\n          }\r\n        };\r\n        \r\n        window.addEventListener('resize', handleResize);\r\n        \r\n        // Clean up function\r\n        return () => {\r\n          window.removeEventListener('resize', handleResize);\r\n          if (ctx) {\r\n            ctx.revert();\r\n          }\r\n          ScrollTrigger.killAll();\r\n        };\r\n      } catch (error) {\r\n        console.warn('GSAP could not be loaded:', error);\r\n        return () => {};\r\n      }\r\n    };\r\n\r\n    // Load GSAP and set up animations\r\n    const cleanupPromise = loadGSAP();\r\n    \r\n    // Return cleanup function\r\n    return () => {\r\n      isDestroyed = true;\r\n      if (ctx) {\r\n        ctx.revert();\r\n        ctx = null;\r\n      }\r\n      cleanupPromise.then(cleanupFn => {\r\n        if (cleanupFn && typeof cleanupFn === 'function') {\r\n          cleanupFn();\r\n        }\r\n      }).catch(() => {});\r\n      \r\n      // Additional cleanup for ScrollTrigger\r\n      if (typeof window !== 'undefined') {\r\n        import('gsap/ScrollTrigger').then(({ ScrollTrigger }) => {\r\n          ScrollTrigger.killAll();\r\n        }).catch(() => {});\r\n      }\r\n    };\r\n  }, [isMobile]);\r\n\r\n  // Mobile version - natural vertical scrolling\r\n  if (isMobile) {\r\n    return (\r\n      <section className={`w-full bg-white ${className}`}>\r\n        {/* Section Header */}\r\n        <div className=\"text-center py-8 sm:py-12 px-4\">\r\n          <p className=\"text-blue-700 font-medium text-base sm:text-lg mb-3\">WHY CHOOSE US</p>\r\n          <h2 className=\"text-2xl sm:text-3xl font-bold text-gray-800\">\r\n            FOUR REASONS VESA IS YOUR IDEAL PARTNER\r\n          </h2>\r\n        </div>\r\n\r\n        {/* Vertical Stack of Steps */}\r\n        <div className=\"space-y-8 sm:space-y-12 px-4 pb-12\">\r\n          {whyChooseSteps.map((step, index) => (\r\n            <div key={step.id} className=\"max-w-2xl mx-auto\">\r\n              {/* Step Card */}\r\n              <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden\">\r\n                {/* Visual Section */}\r\n                <div className=\"relative\">\r\n                  {/* Large Number in Front - Right Side */}\r\n                  <div className=\"absolute -top-8 -right-4 text-6xl sm:text-8xl font-bold text-blue-500/20 select-none z-10\">\r\n                    {step.id}\r\n                  </div>\r\n\r\n                  <div className=\"aspect-[4/3] sm:aspect-[16/10]\">\r\n                    <Image\r\n                      src={step.image}\r\n                      alt={step.title}\r\n                      fill\r\n                      className=\"object-cover\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Content Section */}\r\n                <div className=\"p-6 sm:p-8\">\r\n                  {/* Step Number and Title Line */}\r\n                  <div className=\"flex items-center space-x-4 mb-4\">\r\n                    <div className=\"w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-blue-600 flex items-center justify-center shadow-lg\">\r\n                      <span className=\"text-lg sm:text-xl font-bold text-white\">{step.id}</span>\r\n                    </div>\r\n                    <div className=\"h-px flex-1 bg-gradient-to-r from-blue-400 to-transparent\" />\r\n                  </div>\r\n\r\n                  {/* Title */}\r\n                  <h3 className=\"text-xl sm:text-2xl font-bold text-gray-800 mb-4 leading-tight\">\r\n                    {step.title}\r\n                  </h3>\r\n\r\n                  {/* Description */}\r\n                  <p className=\"text-gray-700 leading-relaxed mb-6 text-sm sm:text-base\">\r\n                    {step.description}\r\n                  </p>\r\n\r\n\r\n                </div>\r\n              </div>\r\n\r\n              {/* Progress Indicator */}\r\n              {index < whyChooseSteps.length - 1 && (\r\n                <div className=\"flex justify-center mt-6\">\r\n                  <div className=\"w-px h-8 bg-gradient-to-b from-blue-300 to-transparent\"></div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Bottom Summary */}\r\n        <div className=\"bg-blue-50 py-8 px-4\">\r\n          <div className=\"max-w-2xl mx-auto text-center\">\r\n            <h3 className=\"text-lg sm:text-xl font-bold text-gray-800 mb-3\">\r\n              Ready to Get Started?\r\n            </h3>\r\n            <p className=\"text-gray-700 text-sm sm:text-base\">\r\n              Experience the difference with Vesa&apos;s proven approach to digital marketing success.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    );\r\n  }\r\n\r\n  // Desktop version - horizontal scroll (unchanged)\r\n  return (\r\n    <section className={`w-full bg-white overflow-x-hidden ${className}`}>\r\n      {/* Progress Bar */}\r\n      <div className=\"sticky top-0 left-0 w-full h-1 bg-gray-100 z-40\">\r\n        <div \r\n          ref={progressRef}\r\n          className=\"h-full bg-blue-500 origin-left scale-x-0\"\r\n        />\r\n      </div>\r\n\r\n      {/* Section Header */}\r\n      <div className=\"text-center py-16\">\r\n        <p className=\"text-blue-700 font-medium text-lg mb-3\">WHY CHOOSE US</p>\r\n        <h2 className=\"text-3xl md:text-4xl font-bold text-gray-800 max-w-4xl mx-auto\">\r\n          FOUR REASONS VESA IS YOUR IDEAL PARTNER\r\n        </h2>\r\n      </div>\r\n\r\n      {/* Horizontal Scroll Container */}\r\n      <div ref={containerRef} className=\"relative h-screen w-full\">\r\n        <div ref={sectionsRef} className=\"flex h-full\" style={{ width: '400vw' }}>\r\n          {whyChooseSteps.map((step, index) => (\r\n            <div\r\n              key={step.id}\r\n              className=\"w-screen h-full flex items-center justify-center px-8 md:px-12\"\r\n            >\r\n              <div className=\"step-card max-w-6xl mx-auto\">\r\n                <div className=\"grid md:grid-cols-2 gap-16 items-center\">\r\n                  \r\n                  {/* Content Side */}\r\n                  <div className=\"space-y-8\">\r\n                    {/* Step Number and Title */}\r\n                    <div className=\"flex items-center space-x-6\">\r\n                      <div className=\"w-16 h-16 rounded-full bg-blue-600 flex items-center justify-center shadow-lg\">\r\n                        <span className=\"text-2xl font-bold text-white\">{step.id}</span>\r\n                      </div>\r\n                      <div className=\"h-px flex-1 bg-gradient-to-r from-blue-400 to-transparent\" />\r\n                    </div>\r\n\r\n                    {/* Title */}\r\n                    <h3 className=\"text-3xl md:text-4xl font-bold text-gray-800 leading-tight\">\r\n                      {step.title}\r\n                    </h3>\r\n\r\n                    {/* Description */}\r\n                    <p className=\"text-lg text-gray-700 leading-relaxed\">\r\n                      {step.description}\r\n                    </p>\r\n\r\n\r\n                  </div>\r\n\r\n                  {/* Visual Side */}\r\n                  <div className=\"relative\">\r\n                    {/* Large Number in Front - Right Side */}\r\n                    <div className=\"absolute -top-20 -right-8 text-9xl font-bold text-blue-500/20 select-none z-10\">\r\n                      {step.id}\r\n                    </div>\r\n\r\n                    {/* Main Visual Card */}\r\n                    <div className=\"relative rounded-2xl overflow-hidden shadow-xl border border-gray-100\">\r\n                      <div className=\"aspect-square\">\r\n                        <Image\r\n                          src={step.image}\r\n                          alt={step.title}\r\n                          fill\r\n                          className=\"object-cover\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Navigation Dots */}\r\n                <div className=\"mt-16 flex justify-center\">\r\n                  <div className=\"flex space-x-3\">\r\n                    {whyChooseSteps.map((_, i) => (\r\n                      <button\r\n                        key={i}\r\n                        onClick={scrollToStep}\r\n                        className={`w-3 h-3 rounded-full transition-all duration-300 ${\r\n                          i === index \r\n                            ? 'bg-blue-500 scale-125' \r\n                            : 'bg-gray-300 hover:bg-gray-400'\r\n                        }`}\r\n                        aria-label={`Go to step ${i + 1}`}\r\n                      />\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default HorizontalScrollSection;"], "names": [], "mappings": "AAAA,8CAA8C;;;;;AAC9C;AACA;;;;;AAQA,MAAM,0BAAkE,CAAC,EAAE,YAAY,EAAE,EAAE;;IACzF,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAElD,+BAA+B;IAC/B,MAAM,iBAAkC;QACtC;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,+BAA+B;IAC/B,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;6CAAE;YACR,MAAM;iEAAc;oBAClB,YAAY,OAAO,UAAU,GAAG,OAAO,gBAAgB;gBACzD;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC;qDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;4CAAG,EAAE;IAEL,4DAA4D;IAC5D,MAAM,eAAe;QACnB,IAAI,YAAY,OAAO,IAAI,aAAa,OAAO,EAAE;YAC/C,OAAO,QAAQ,CAAC;gBACd,KAAK,aAAa,OAAO,CAAC,SAAS;gBACnC,UAAU;YACZ;QACF;IACF;IAEA,kDAAkD;IAClD,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;6CAAE;YACR,sCAAsC;YACtC,IAAI,aAAkB,eAAe,UAAU;gBAC7C,uEAAuE;gBACvE,wCAAmC;oBACjC,qIAA6B,IAAI;6DAAC,CAAC,EAAE,aAAa,EAAE;4BAClD,cAAc,OAAO;wBACvB;4DAAG,KAAK;6DAAC,KAAO;;gBAClB;gBACA;YACF;YAEA,MAAM,YAAY,aAAa,OAAO;YACtC,MAAM,WAAW,YAAY,OAAO;YACpC,MAAM,cAAc,YAAY,OAAO;YAEvC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,aAAa;YAE7C,IAAI,MAAqC;YACzC,IAAI,cAAc;YAElB,6CAA6C;YAC7C,MAAM;8DAAW;oBACf,IAAI;wBACF,MAAM,EAAE,IAAI,EAAE,GAAG;wBACjB,MAAM,EAAE,aAAa,EAAE,GAAG;wBAE1B,sCAAsC;wBACtC,IAAI,aAAa;wBAEjB,gCAAgC;wBAChC,KAAK,cAAc,CAAC;wBAEpB,yCAAyC;wBACzC,cAAc,OAAO;wBAErB,6CAA6C;wBAC7C,MAAM,KAAK,OAAO;0EAAC;gCACjB,oCAAoC;gCACpC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,aAAa;gCAE5D,yDAAyD;gCACzD,MAAM,cAAc,SAAS,WAAW,GAAG,UAAU,WAAW;gCAEhE,yCAAyC;gCACzC,MAAM,cAAc,KAAK,EAAE,CAAC,UAAU;oCACpC,GAAG,CAAC;oCACJ,MAAM;gCACR;gCAEA,uBAAuB;gCACvB,KAAK,EAAE,CAAC,aAAa;oCACnB,QAAQ;oCACR,MAAM;oCACN,eAAe;wCACb,SAAS;wCACT,OAAO;wCACP,GAAG;0FAAE,IAAM,CAAC,EAAE,EAAE,aAAa;;wCAC7B,OAAO;oCACT;gCACF;gCAEA,uBAAuB;gCACvB,cAAc,MAAM,CAAC;oCACnB,SAAS;oCACT,OAAO;oCACP,GAAG;sFAAE,IAAM,CAAC,EAAE,EAAE,aAAa;;oCAC7B,OAAO;oCACP,KAAK;oCACL,WAAW;oCACX,qBAAqB;gCACvB;gCAEA,gBAAgB;gCAChB,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC;gCACjC,MAAM,OAAO;kFAAC,CAAC;wCACb,IAAI,aAAa;wCACjB,0BAA0B;wCAC1B,KAAK,MAAM,CAAC,MACV;4CACE,SAAS;4CACT,GAAG;wCACL,GACA;4CACE,SAAS;4CACT,GAAG;4CACH,UAAU;4CACV,MAAM;4CACN,eAAe;gDACb,SAAS;gDACT,OAAO;gDACP,KAAK;gDACL,OAAO;gDACP,YAAY;gDACZ,oBAAoB;4CACtB;wCACF;oCAEJ;;4BACF;yEAAG;wBAEH,2CAA2C;wBAC3C,MAAM;uFAAe;gCACnB,IAAI,CAAC,aAAa;oCAChB,cAAc,OAAO;gCACvB;4BACF;;wBAEA,OAAO,gBAAgB,CAAC,UAAU;wBAElC,oBAAoB;wBACpB;0EAAO;gCACL,OAAO,mBAAmB,CAAC,UAAU;gCACrC,IAAI,KAAK;oCACP,IAAI,MAAM;gCACZ;gCACA,cAAc,OAAO;4BACvB;;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,IAAI,CAAC,6BAA6B;wBAC1C;0EAAO,KAAO;;oBAChB;gBACF;;YAEA,kCAAkC;YAClC,MAAM,iBAAiB;YAEvB,0BAA0B;YAC1B;qDAAO;oBACL,cAAc;oBACd,IAAI,KAAK;wBACP,IAAI,MAAM;wBACV,MAAM;oBACR;oBACA,eAAe,IAAI;6DAAC,CAAA;4BAClB,IAAI,aAAa,OAAO,cAAc,YAAY;gCAChD;4BACF;wBACF;4DAAG,KAAK;6DAAC,KAAO;;oBAEhB,uCAAuC;oBACvC,wCAAmC;wBACjC,qIAA6B,IAAI;iEAAC,CAAC,EAAE,aAAa,EAAE;gCAClD,cAAc,OAAO;4BACvB;gEAAG,KAAK;iEAAC,KAAO;;oBAClB;gBACF;;QACF;4CAAG;QAAC;KAAS;IAEb,8CAA8C;IAC9C,IAAI,UAAU;QACZ,qBACE,0JAAC;YAAQ,WAAW,CAAC,gBAAgB,EAAE,WAAW;;8BAEhD,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAE,WAAU;sCAAsD;;;;;;sCACnE,0JAAC;4BAAG,WAAU;sCAA+C;;;;;;;;;;;;8BAM/D,0JAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,0JAAC;4BAAkB,WAAU;;8CAE3B,0JAAC;oCAAI,WAAU;;sDAEb,0JAAC;4CAAI,WAAU;;8DAEb,0JAAC;oDAAI,WAAU;8DACZ,KAAK,EAAE;;;;;;8DAGV,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC,yHAAA,CAAA,UAAK;wDACJ,KAAK,KAAK,KAAK;wDACf,KAAK,KAAK,KAAK;wDACf,IAAI;wDACJ,WAAU;;;;;;;;;;;;;;;;;sDAMhB,0JAAC;4CAAI,WAAU;;8DAEb,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAI,WAAU;sEACb,cAAA,0JAAC;gEAAK,WAAU;0EAA2C,KAAK,EAAE;;;;;;;;;;;sEAEpE,0JAAC;4DAAI,WAAU;;;;;;;;;;;;8DAIjB,0JAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAIb,0JAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;gCAQtB,QAAQ,eAAe,MAAM,GAAG,mBAC/B,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC;wCAAI,WAAU;;;;;;;;;;;;2BA/CX,KAAK,EAAE;;;;;;;;;;8BAuDrB,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAGhE,0JAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;;;;;;;;;;;;IAO5D;IAEA,kDAAkD;IAClD,qBACE,0JAAC;QAAQ,WAAW,CAAC,kCAAkC,EAAE,WAAW;;0BAElE,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBACC,KAAK;oBACL,WAAU;;;;;;;;;;;0BAKd,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAE,WAAU;kCAAyC;;;;;;kCACtD,0JAAC;wBAAG,WAAU;kCAAiE;;;;;;;;;;;;0BAMjF,0JAAC;gBAAI,KAAK;gBAAc,WAAU;0BAChC,cAAA,0JAAC;oBAAI,KAAK;oBAAa,WAAU;oBAAc,OAAO;wBAAE,OAAO;oBAAQ;8BACpE,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,0JAAC;4BAEC,WAAU;sCAEV,cAAA,0JAAC;gCAAI,WAAU;;kDACb,0JAAC;wCAAI,WAAU;;0DAGb,0JAAC;gDAAI,WAAU;;kEAEb,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEAAI,WAAU;0EACb,cAAA,0JAAC;oEAAK,WAAU;8EAAiC,KAAK,EAAE;;;;;;;;;;;0EAE1D,0JAAC;gEAAI,WAAU;;;;;;;;;;;;kEAIjB,0JAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAIb,0JAAC;wDAAE,WAAU;kEACV,KAAK,WAAW;;;;;;;;;;;;0DAOrB,0JAAC;gDAAI,WAAU;;kEAEb,0JAAC;wDAAI,WAAU;kEACZ,KAAK,EAAE;;;;;;kEAIV,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC;4DAAI,WAAU;sEACb,cAAA,0JAAC,yHAAA,CAAA,UAAK;gEACJ,KAAK,KAAK,KAAK;gEACf,KAAK,KAAK,KAAK;gEACf,IAAI;gEACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQpB,0JAAC;wCAAI,WAAU;kDACb,cAAA,0JAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,GAAG,kBACtB,0JAAC;oDAEC,SAAS;oDACT,WAAW,CAAC,iDAAiD,EAC3D,MAAM,QACF,0BACA,iCACJ;oDACF,cAAY,CAAC,WAAW,EAAE,IAAI,GAAG;mDAP5B;;;;;;;;;;;;;;;;;;;;;2BAvDV,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AA0E1B;GApYM;KAAA;uCAsYS", "debugId": null}}, {"offset": {"line": 5546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\nimport React, { useState } from 'react';\r\nimport Image from 'next/image';\r\nimport { ChevronLeft, ChevronRight, User, Mail, Clock } from 'lucide-react';\r\nimport { CalendarDay } from '@/types';\r\nimport { format } from 'date-fns';\r\n\r\n\r\ninterface SchedulingSectionProps {\r\n  className?: string;\r\n}\r\n\r\nconst SchedulingSection: React.FC<SchedulingSectionProps> = ({ className = '' }) => {\r\n  const [selectedDate, setSelectedDate] = useState<Date | null>(null);\r\n  const [showForm, setShowForm] = useState<boolean>(false);\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    email: ''\r\n  });\r\n  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);\r\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\r\n  const [statusMessage, setStatusMessage] = useState<string>('');\r\n  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());\r\n\r\n\r\n\r\n\r\n\r\n  // Handle form submission\r\n  const handleFormSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!selectedDate || !formData.name || !formData.email) {\r\n      setSubmitStatus('error');\r\n      setStatusMessage('Please fill in all fields and select a date.');\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    setSubmitStatus('idle');\r\n\r\n    try {\r\n      const formattedDate = format(selectedDate, 'MMMM d, yyyy');\r\n\r\n      // Prepare data for API\r\n      const scheduleData = {\r\n        name: formData.name,\r\n        email: formData.email,\r\n        date: formattedDate\r\n      };\r\n\r\n      // Send to API\r\n      const response = await fetch('/api/schedule-consultation', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(scheduleData),\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (response.ok && result.success) {\r\n        setSubmitStatus('success');\r\n        setStatusMessage(result.message || 'Consultation scheduled successfully! You will receive a confirmation email shortly.');\r\n\r\n        // Reset form\r\n        setFormData({ name: '', email: '' });\r\n        setSelectedDate(null);\r\n        setShowForm(false);\r\n      } else {\r\n        setSubmitStatus('error');\r\n        setStatusMessage(result.message || 'Failed to schedule consultation. Please try again.');\r\n      }\r\n\r\n    } catch {\r\n      setSubmitStatus('error');\r\n      setStatusMessage('Failed to schedule consultation. Please check your connection and try again.');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n\r\n      // Clear status message after 8 seconds\r\n      setTimeout(() => {\r\n        setSubmitStatus('idle');\r\n        setStatusMessage('');\r\n      }, 8000);\r\n    }\r\n  };\r\n\r\n\r\n\r\n\r\n\r\n  // Generate calendar data for the current month view\r\n  const generateCalendarDays = (): CalendarDay[] => {\r\n    const today = new Date();\r\n    const days: CalendarDay[] = [];\r\n\r\n    // Get first day of the current month\r\n    const firstDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);\r\n    const lastDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);\r\n\r\n    // Get the day of week for the first day (0 = Sunday, 1 = Monday, etc.)\r\n    // Convert to Monday = 0, Sunday = 6\r\n    const firstDayOfWeek = (firstDayOfMonth.getDay() + 6) % 7;\r\n\r\n    // Add empty cells for days before the first day of the month\r\n    for (let i = 0; i < firstDayOfWeek; i++) {\r\n      const prevDate = new Date(firstDayOfMonth);\r\n      prevDate.setDate(prevDate.getDate() - (firstDayOfWeek - i));\r\n\r\n      days.push({\r\n        date: prevDate.getDate(),\r\n        available: false,\r\n        fullDate: prevDate,\r\n        isToday: false,\r\n        isNextMonth: true // These are from previous month\r\n      });\r\n    }\r\n\r\n    // Add all days of the current month\r\n    for (let day = 1; day <= lastDayOfMonth.getDate(); day++) {\r\n      const dayDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);\r\n      const isToday = dayDate.toDateString() === today.toDateString();\r\n      const isPastDate = dayDate < today && !isToday;\r\n\r\n      days.push({\r\n        date: day,\r\n        available: !isPastDate, // Available if not in the past\r\n        fullDate: dayDate,\r\n        isToday: isToday,\r\n        isNextMonth: false\r\n      });\r\n    }\r\n\r\n    // Fill remaining cells to complete the 6-week grid (42 cells)\r\n    const remainingCells = 42 - days.length;\r\n    for (let i = 1; i <= remainingCells; i++) {\r\n      const nextDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, i);\r\n\r\n      days.push({\r\n        date: i,\r\n        available: true, // Future month dates are always available\r\n        fullDate: nextDate,\r\n        isToday: false,\r\n        isNextMonth: true\r\n      });\r\n    }\r\n\r\n    return days;\r\n  };\r\n\r\n  // Navigation functions\r\n  const goToPreviousMonth = () => {\r\n    const today = new Date();\r\n    const prevMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1);\r\n\r\n    // Don't allow going to months before the current month\r\n    if (prevMonth.getFullYear() > today.getFullYear() ||\r\n        (prevMonth.getFullYear() === today.getFullYear() && prevMonth.getMonth() >= today.getMonth())) {\r\n      setCurrentMonth(prevMonth);\r\n    }\r\n  };\r\n\r\n  const goToNextMonth = () => {\r\n    const nextMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1);\r\n    const today = new Date();\r\n    const maxMonth = new Date(today.getFullYear() + 1, today.getMonth(), 1); // Allow up to 1 year ahead\r\n\r\n    // Don't allow going more than 1 year into the future\r\n    if (nextMonth <= maxMonth) {\r\n      setCurrentMonth(nextMonth);\r\n    }\r\n  };\r\n\r\n  const calendarDays = generateCalendarDays();\r\n\r\n  const weekDays: string[] = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];\r\n\r\n  return (\r\n    <section className={`w-full bg-blue-50 py-24 ${className}`}>\r\n      <div className=\"container mx-auto px-4\">\r\n        <div className=\"text-center mb-16\">\r\n          <p className=\"text-blue-700 font-medium text-lg mb-3\">BOOK A CONSULTATION</p>\r\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-800 max-w-4xl mx-auto\">\r\n            SCHEDULE YOUR FREE ONLINE REPUTATION CONSULTATION\r\n          </h2>\r\n          <p className=\"text-gray-700 text-lg mt-4 max-w-3xl mx-auto\">\r\n            Take the first step towards a better online presence. Our experts will analyze your current reputation and suggest strategies to improve it.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"max-w-6xl mx-auto\">\r\n          <div className=\"bg-white rounded-2xl shadow-lg overflow-hidden\">\r\n            <div className=\"flex flex-col lg:flex-row\">\r\n              {/* Left side - Professional Image */}\r\n              <div className=\"lg:w-1/3 relative\">\r\n                <Image\r\n                  src=\"https://img.freepik.com/free-photo/man-signs-documents-manager-working-office_1157-42026.jpg\"\r\n                  alt=\"Professional consultant\"\r\n                  fill\r\n                  className=\"object-cover\"\r\n                />\r\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"></div>\r\n              </div>\r\n\r\n              {/* Center - Calendar */}\r\n              <div className=\"lg:w-1/3 p-6 bg-gray-50\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-6 text-center\">\r\n                  Select a Date\r\n                </h3>\r\n\r\n                {/* Month Navigation */}\r\n                <div className=\"flex items-center justify-between mb-6\">\r\n                  <button\r\n                    onClick={goToPreviousMonth}\r\n                    className=\"p-1 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                    disabled={currentMonth.getFullYear() === new Date().getFullYear() && currentMonth.getMonth() === new Date().getMonth()}\r\n                    aria-label=\"Go to previous month\"\r\n                  >\r\n                    <ChevronLeft className=\"w-5 h-5 text-blue-600\" />\r\n                  </button>\r\n                  <span className=\"text-base font-medium text-gray-700\">\r\n                    {format(currentMonth, 'MMMM yyyy')}\r\n                  </span>\r\n                  <button\r\n                    onClick={goToNextMonth}\r\n                    className=\"p-1 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                    disabled={(() => {\r\n                      const today = new Date();\r\n                      const maxMonth = new Date(today.getFullYear() + 1, today.getMonth(), 1);\r\n                      const nextMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1);\r\n                      return nextMonth > maxMonth;\r\n                    })()}\r\n                    aria-label=\"Go to next month\"\r\n                  >\r\n                    <ChevronRight className=\"w-5 h-5 text-blue-600\" />\r\n                  </button>\r\n                </div>\r\n\r\n                {/* Week Days */}\r\n                <div className=\"grid grid-cols-7 gap-1 mb-3\">\r\n                  {weekDays.map((day) => (\r\n                    <div\r\n                      key={day}\r\n                      className=\"text-xs font-medium text-gray-500 text-center py-2\"\r\n                    >\r\n                      {day}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n\r\n                {/* Calendar Grid */}\r\n                <div className=\"grid grid-cols-7 gap-1\">\r\n                  {calendarDays.map((day, index) => {\r\n                    const dayKey = day.fullDate ? day.fullDate.toISOString() : `${day.date}-${index}`;\r\n                    const isSelected = selectedDate && day.fullDate && selectedDate.toDateString() === day.fullDate.toDateString();\r\n\r\n                    return (\r\n                      <button\r\n                        key={dayKey}\r\n                        onClick={() => {\r\n                          if (day.available && day.fullDate) {\r\n                            setSelectedDate(day.fullDate);\r\n                            setShowForm(true);\r\n                          }\r\n                        }}\r\n                        disabled={!day.available}\r\n                        className={`\r\n                          aspect-square flex items-center justify-center text-sm font-medium rounded-lg\r\n                          transition-all duration-200 relative\r\n                          ${\r\n                            day.available\r\n                              ? day.isNextMonth\r\n                                ? 'text-gray-500 hover:bg-gray-100 cursor-pointer border border-gray-100'\r\n                                : 'text-blue-600 hover:bg-blue-100 cursor-pointer border border-gray-200'\r\n                              : 'text-gray-300 cursor-not-allowed bg-gray-50'\r\n                          }\r\n                          ${\r\n                            isSelected\r\n                              ? 'bg-blue-600 text-white hover:bg-blue-700 border-blue-600'\r\n                              : ''\r\n                          }\r\n                          ${day.isToday ? 'ring-2 ring-blue-300 ring-offset-1' : ''}\r\n                          ${day.isNextMonth ? 'opacity-70' : ''}\r\n                        `}\r\n                        title={day.fullDate ? format(day.fullDate, 'EEEE, MMMM d, yyyy') : ''}\r\n                        aria-label={day.fullDate ? `Select ${format(day.fullDate, 'EEEE, MMMM d, yyyy')}` : `Day ${day.date}`}\r\n                      >\r\n                        {day.date}\r\n                        {day.isToday && (\r\n                          <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full\"></div>\r\n                        )}\r\n                      </button>\r\n                    );\r\n                  })}\r\n                </div>\r\n\r\n\r\n\r\n\r\n              </div>\r\n\r\n              {/* Right side - Consultation Details & Form */}\r\n              <div className=\"lg:w-1/3 p-8 flex flex-col justify-center\">\r\n                {!showForm ? (\r\n                  <>\r\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\r\n                      Online Reputation Management Consultation\r\n                    </h2>\r\n\r\n                    <div className=\"flex items-center gap-2 text-gray-600 mb-6\">\r\n                      <Clock className=\"w-5 h-5\" />\r\n                      <span className=\"text-base\">30 min</span>\r\n                    </div>\r\n\r\n                    <p className=\"text-gray-700 mb-6 leading-relaxed\">\r\n                      Schedule a free online reputation management consultation with Vesa Solutions.\r\n                    </p>\r\n\r\n                    <p className=\"text-gray-600 text-sm mb-8 leading-relaxed\">\r\n                      Your custom online reputation management strategy will be outlined for you step-by-step during our call. If we believe we can help you, we will outline what it will look like if we work together.\r\n                    </p>\r\n\r\n                    <div className=\"text-center\">\r\n                      <p className=\"text-gray-600 text-lg\">\r\n                        Select a date from the calendar to get started\r\n                      </p>\r\n                    </div>\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\r\n                      Complete Your Booking\r\n                    </h2>\r\n\r\n                    <div className=\"mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200\">\r\n                      <h4 className=\"font-medium text-blue-800 mb-2\">Appointment Details</h4>\r\n                      <div className=\"text-blue-700 text-sm space-y-1\">\r\n                        <div>📅 {selectedDate ? format(selectedDate, 'EEEE, MMMM d, yyyy') : 'Invalid date'}</div>\r\n                        <div>⏱️ 30 minutes consultation</div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <form onSubmit={handleFormSubmit} className=\"space-y-4\">\r\n                      <div>\r\n                        <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                          <User className=\"w-4 h-4 inline mr-1\" />\r\n                          Full Name *\r\n                        </label>\r\n                        <input\r\n                          type=\"text\"\r\n                          id=\"name\"\r\n                          value={formData.name}\r\n                          onChange={(e) => setFormData({ ...formData, name: e.target.value })}\r\n                          required\r\n                          disabled={isSubmitting}\r\n                          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50\"\r\n                          placeholder=\"Enter your full name\"\r\n                        />\r\n                      </div>\r\n\r\n                      <div>\r\n                        <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                          <Mail className=\"w-4 h-4 inline mr-1\" />\r\n                          Email Address *\r\n                        </label>\r\n                        <input\r\n                          type=\"email\"\r\n                          id=\"email\"\r\n                          value={formData.email}\r\n                          onChange={(e) => setFormData({ ...formData, email: e.target.value })}\r\n                          required\r\n                          disabled={isSubmitting}\r\n                          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50\"\r\n                          placeholder=\"Enter your email address\"\r\n                        />\r\n                      </div>\r\n\r\n                      <div className=\"flex gap-3 pt-4\">\r\n                        <button\r\n                          type=\"button\"\r\n                          onClick={() => setShowForm(false)}\r\n                          disabled={isSubmitting}\r\n                          className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50\"\r\n                        >\r\n                          Back\r\n                        </button>\r\n                        <button\r\n                          type=\"submit\"\r\n                          disabled={isSubmitting || !formData.name || !formData.email}\r\n                          className=\"flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 disabled:cursor-not-allowed\"\r\n                        >\r\n                          {isSubmitting ? 'Scheduling...' : 'Schedule Consultation'}\r\n                        </button>\r\n                      </div>\r\n                    </form>\r\n                  </>\r\n                )}\r\n\r\n                {/* Status Message */}\r\n                {statusMessage && (\r\n                  <div className={`mt-4 p-3 rounded-lg text-sm ${\r\n                    submitStatus === 'success'\r\n                      ? 'bg-green-50 text-green-700 border border-green-200'\r\n                      : submitStatus === 'error'\r\n                      ? 'bg-red-50 text-red-700 border border-red-200'\r\n                      : ''\r\n                  }`}>\r\n                    {statusMessage}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default SchedulingSection;"], "names": [], "mappings": "AAAA,wCAAwC;;;;;AACxC;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;;;;;;;AAOA,MAAM,oBAAsD,CAAC,EAAE,YAAY,EAAE,EAAE;;IAC7E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;IACT;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAW;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAgC;IAC/E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAM3D,yBAAyB;IACzB,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE;YACtD,gBAAgB;YAChB,iBAAiB;YACjB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,MAAM,gBAAgB,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;YAE3C,uBAAuB;YACvB,MAAM,eAAe;gBACnB,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;gBACrB,MAAM;YACR;YAEA,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,8BAA8B;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,IAAI,OAAO,OAAO,EAAE;gBACjC,gBAAgB;gBAChB,iBAAiB,OAAO,OAAO,IAAI;gBAEnC,aAAa;gBACb,YAAY;oBAAE,MAAM;oBAAI,OAAO;gBAAG;gBAClC,gBAAgB;gBAChB,YAAY;YACd,OAAO;gBACL,gBAAgB;gBAChB,iBAAiB,OAAO,OAAO,IAAI;YACrC;QAEF,EAAE,OAAM;YACN,gBAAgB;YAChB,iBAAiB;QACnB,SAAU;YACR,gBAAgB;YAEhB,uCAAuC;YACvC,WAAW;gBACT,gBAAgB;gBAChB,iBAAiB;YACnB,GAAG;QACL;IACF;IAMA,oDAAoD;IACpD,MAAM,uBAAuB;QAC3B,MAAM,QAAQ,IAAI;QAClB,MAAM,OAAsB,EAAE;QAE9B,qCAAqC;QACrC,MAAM,kBAAkB,IAAI,KAAK,aAAa,WAAW,IAAI,aAAa,QAAQ,IAAI;QACtF,MAAM,iBAAiB,IAAI,KAAK,aAAa,WAAW,IAAI,aAAa,QAAQ,KAAK,GAAG;QAEzF,uEAAuE;QACvE,oCAAoC;QACpC,MAAM,iBAAiB,CAAC,gBAAgB,MAAM,KAAK,CAAC,IAAI;QAExD,6DAA6D;QAC7D,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,IAAK;YACvC,MAAM,WAAW,IAAI,KAAK;YAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK,CAAC,iBAAiB,CAAC;YAEzD,KAAK,IAAI,CAAC;gBACR,MAAM,SAAS,OAAO;gBACtB,WAAW;gBACX,UAAU;gBACV,SAAS;gBACT,aAAa,KAAK,gCAAgC;YACpD;QACF;QAEA,oCAAoC;QACpC,IAAK,IAAI,MAAM,GAAG,OAAO,eAAe,OAAO,IAAI,MAAO;YACxD,MAAM,UAAU,IAAI,KAAK,aAAa,WAAW,IAAI,aAAa,QAAQ,IAAI;YAC9E,MAAM,UAAU,QAAQ,YAAY,OAAO,MAAM,YAAY;YAC7D,MAAM,aAAa,UAAU,SAAS,CAAC;YAEvC,KAAK,IAAI,CAAC;gBACR,MAAM;gBACN,WAAW,CAAC;gBACZ,UAAU;gBACV,SAAS;gBACT,aAAa;YACf;QACF;QAEA,8DAA8D;QAC9D,MAAM,iBAAiB,KAAK,KAAK,MAAM;QACvC,IAAK,IAAI,IAAI,GAAG,KAAK,gBAAgB,IAAK;YACxC,MAAM,WAAW,IAAI,KAAK,aAAa,WAAW,IAAI,aAAa,QAAQ,KAAK,GAAG;YAEnF,KAAK,IAAI,CAAC;gBACR,MAAM;gBACN,WAAW;gBACX,UAAU;gBACV,SAAS;gBACT,aAAa;YACf;QACF;QAEA,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,QAAQ,IAAI;QAClB,MAAM,YAAY,IAAI,KAAK,aAAa,WAAW,IAAI,aAAa,QAAQ,KAAK,GAAG;QAEpF,uDAAuD;QACvD,IAAI,UAAU,WAAW,KAAK,MAAM,WAAW,MAC1C,UAAU,WAAW,OAAO,MAAM,WAAW,MAAM,UAAU,QAAQ,MAAM,MAAM,QAAQ,IAAK;YACjG,gBAAgB;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,YAAY,IAAI,KAAK,aAAa,WAAW,IAAI,aAAa,QAAQ,KAAK,GAAG;QACpF,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,IAAI,KAAK,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,IAAI,IAAI,2BAA2B;QAEpG,qDAAqD;QACrD,IAAI,aAAa,UAAU;YACzB,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe;IAErB,MAAM,WAAqB;QAAC;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;KAAM;IAE5E,qBACE,0JAAC;QAAQ,WAAW,CAAC,wBAAwB,EAAE,WAAW;kBACxD,cAAA,0JAAC;YAAI,WAAU;;8BACb,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAE,WAAU;sCAAyC;;;;;;sCACtD,0JAAC;4BAAG,WAAU;sCAAiE;;;;;;sCAG/E,0JAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAK9D,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;;8CAEb,0JAAC;oCAAI,WAAU;;sDACb,0JAAC,yHAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;;;;;;sDAEZ,0JAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIjB,0JAAC;oCAAI,WAAU;;sDACb,0JAAC;4CAAG,WAAU;sDAAuD;;;;;;sDAKrE,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDACC,SAAS;oDACT,WAAU;oDACV,UAAU,aAAa,WAAW,OAAO,IAAI,OAAO,WAAW,MAAM,aAAa,QAAQ,OAAO,IAAI,OAAO,QAAQ;oDACpH,cAAW;8DAEX,cAAA,0JAAC,gNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAEzB,0JAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;;;;;;8DAExB,0JAAC;oDACC,SAAS;oDACT,WAAU;oDACV,UAAU,CAAC;wDACT,MAAM,QAAQ,IAAI;wDAClB,MAAM,WAAW,IAAI,KAAK,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,IAAI;wDACrE,MAAM,YAAY,IAAI,KAAK,aAAa,WAAW,IAAI,aAAa,QAAQ,KAAK,GAAG;wDACpF,OAAO,YAAY;oDACrB,CAAC;oDACD,cAAW;8DAEX,cAAA,0JAAC,kNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAK5B,0JAAC;4CAAI,WAAU;sDACZ,SAAS,GAAG,CAAC,CAAC,oBACb,0JAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;sDASX,0JAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,KAAK;gDACtB,MAAM,SAAS,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,WAAW,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO;gDACjF,MAAM,aAAa,gBAAgB,IAAI,QAAQ,IAAI,aAAa,YAAY,OAAO,IAAI,QAAQ,CAAC,YAAY;gDAE5G,qBACE,0JAAC;oDAEC,SAAS;wDACP,IAAI,IAAI,SAAS,IAAI,IAAI,QAAQ,EAAE;4DACjC,gBAAgB,IAAI,QAAQ;4DAC5B,YAAY;wDACd;oDACF;oDACA,UAAU,CAAC,IAAI,SAAS;oDACxB,WAAW,CAAC;;;0BAGV,EACE,IAAI,SAAS,GACT,IAAI,WAAW,GACb,0EACA,0EACF,8CACL;0BACD,EACE,aACI,6DACA,GACL;0BACD,EAAE,IAAI,OAAO,GAAG,uCAAuC,GAAG;0BAC1D,EAAE,IAAI,WAAW,GAAG,eAAe,GAAG;wBACxC,CAAC;oDACD,OAAO,IAAI,QAAQ,GAAG,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ,EAAE,wBAAwB;oDACnE,cAAY,IAAI,QAAQ,GAAG,CAAC,OAAO,EAAE,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ,EAAE,uBAAuB,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE;;wDAEpG,IAAI,IAAI;wDACR,IAAI,OAAO,kBACV,0JAAC;4DAAI,WAAU;;;;;;;mDA/BZ;;;;;4CAmCX;;;;;;;;;;;;8CASJ,0JAAC;oCAAI,WAAU;;wCACZ,CAAC,yBACA;;8DACE,0JAAC;oDAAG,WAAU;8DAAwC;;;;;;8DAItD,0JAAC;oDAAI,WAAU;;sEACb,0JAAC,gMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,0JAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;8DAG9B,0JAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAIlD,0JAAC;oDAAE,WAAU;8DAA6C;;;;;;8DAI1D,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;yEAMzC;;8DACE,0JAAC;oDAAG,WAAU;8DAAwC;;;;;;8DAItD,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;;wEAAI;wEAAI,eAAe,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,cAAc,wBAAwB;;;;;;;8EACrE,0JAAC;8EAAI;;;;;;;;;;;;;;;;;;8DAIT,0JAAC;oDAAK,UAAU;oDAAkB,WAAU;;sEAC1C,0JAAC;;8EACC,0JAAC;oEAAM,SAAQ;oEAAO,WAAU;;sFAC9B,0JAAC,8LAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAwB;;;;;;;8EAG1C,0JAAC;oEACC,MAAK;oEACL,IAAG;oEACH,OAAO,SAAS,IAAI;oEACpB,UAAU,CAAC,IAAM,YAAY;4EAAE,GAAG,QAAQ;4EAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wEAAC;oEACjE,QAAQ;oEACR,UAAU;oEACV,WAAU;oEACV,aAAY;;;;;;;;;;;;sEAIhB,0JAAC;;8EACC,0JAAC;oEAAM,SAAQ;oEAAQ,WAAU;;sFAC/B,0JAAC,8LAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAwB;;;;;;;8EAG1C,0JAAC;oEACC,MAAK;oEACL,IAAG;oEACH,OAAO,SAAS,KAAK;oEACrB,UAAU,CAAC,IAAM,YAAY;4EAAE,GAAG,QAAQ;4EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wEAAC;oEAClE,QAAQ;oEACR,UAAU;oEACV,WAAU;oEACV,aAAY;;;;;;;;;;;;sEAIhB,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEACC,MAAK;oEACL,SAAS,IAAM,YAAY;oEAC3B,UAAU;oEACV,WAAU;8EACX;;;;;;8EAGD,0JAAC;oEACC,MAAK;oEACL,UAAU,gBAAgB,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK;oEAC3D,WAAU;8EAET,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;wCAQ3C,+BACC,0JAAC;4CAAI,WAAW,CAAC,4BAA4B,EAC3C,iBAAiB,YACb,uDACA,iBAAiB,UACjB,iDACA,IACJ;sDACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB;GAvZM;KAAA;uCAyZS", "debugId": null}}, {"offset": {"line": 6216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\nimport React, { useState } from 'react';\r\nimport Image from 'next/image';\r\n\r\nimport { Service } from '@/types';\r\n\r\ninterface DigitalMarketingServicesSectionProps {\r\n  className?: string;\r\n}\r\n\r\nconst DigitalMarketingServicesSection: React.FC<DigitalMarketingServicesSectionProps> = ({ className = '' }) => {\r\n  const [activeService, setActiveService] = useState<number>(1);\r\n  const [hoverIndex, setHoverIndex] = useState<number | null>(null);\r\n  \r\n  // Digital Marketing Services Data\r\n  const services: Service[] = [\r\n    {\r\n      id: 0,\r\n      name: \"<PERSON>AR<PERSON> ENGINE OPTIMIZATION\",\r\n      title: \"BOOST YOUR SEARCH RANKINGS WITH EXPERT SEO\",\r\n      description: [\r\n        \"Our comprehensive SEO strategies help your website rank higher in search engine results, driving more organic traffic to your business. We use proven techniques including keyword research, on-page optimization, and technical SEO audits.\",\r\n        \"From local SEO to enterprise-level optimization, we create customized strategies that align with your business goals. Our data-driven approach ensures measurable results and improved online visibility.\",\r\n        \"Partner with our SEO experts to dominate search results and stay ahead of your competition. We provide detailed reporting and continuous optimization to maintain your competitive edge.\"\r\n      ],\r\n      images: [\r\n        \"/homepage/seo.jpeg\",\r\n      ]\r\n    },\r\n    {\r\n      id: 1,\r\n      name: \"WEBSITE DESIGN\",\r\n      title: \"STUNNING WEBSITE DESIGN TO ELEVATE YOUR BRAND\",\r\n      description: [\r\n        \"Our professional web design team creates beautiful, functional websites that capture your brand's essence and convert visitors into customers. We understand that your website is often the first interaction potential customers have with your brand.\",\r\n        \"We take a comprehensive approach to web design, considering everything from user experience and interface design to search engine optimization. Our designs are responsive across all devices, from desktop to mobile.\",\r\n        \"At Vesa Solutions, we don't just design websites - we create experiences. By incorporating the latest design trends and technologies, we ensure your site stays ahead of the competition.\"\r\n      ],\r\n      images: [\r\n        \"/homepage/web design.webp\",\r\n      ]\r\n    },\r\n    {\r\n      id: 2,\r\n      name: \"SOCIAL MEDIA MARKETING\",\r\n      title: \"AMPLIFY YOUR BRAND THROUGH SOCIAL MEDIA\",\r\n      description: [\r\n        \"Our social media marketing strategies help you connect with your audience across all major platforms. We create engaging content that builds brand awareness and drives meaningful engagement with your customers.\",\r\n        \"From content creation to community management, we handle every aspect of your social media presence. Our data-driven approach ensures your campaigns reach the right audience at the right time.\",\r\n        \"Transform your social media channels into powerful marketing tools that generate leads and build lasting customer relationships. We provide comprehensive analytics and optimization for maximum ROI.\"\r\n      ],\r\n      images: [\r\n        \"/homepage/social media marketing.jpg\",\r\n      ]\r\n    },\r\n    {\r\n      id: 3,\r\n      name: \"PAY PER CLICK - PPC\",\r\n      title: \"MAXIMIZE ROI WITH TARGETED PPC CAMPAIGNS\",\r\n      description: [\r\n        \"Our PPC experts create and manage high-converting ad campaigns across Google Ads, Facebook, and other platforms. We focus on maximizing your return on investment through strategic targeting and optimization.\",\r\n        \"From keyword research to ad copy creation, we handle every aspect of your PPC campaigns. Our continuous monitoring and optimization ensure your ads perform at their peak potential.\",\r\n        \"Drive immediate traffic and conversions with our data-driven PPC strategies. We provide transparent reporting and regular campaign optimization to ensure your advertising budget delivers maximum results.\"\r\n      ],\r\n      images: [\r\n        \"/homepage/ppc.jpg\",\r\n      ]\r\n    },\r\n    {\r\n      id: 4,\r\n      name: \"CONTENT WRITING\",\r\n      title: \"ENGAGING CONTENT THAT CONVERTS READERS\",\r\n      description: [\r\n        \"Our expert content writers create compelling, SEO-optimized content that engages your audience and drives action. From blog posts to website copy, we craft content that tells your brand story effectively.\",\r\n        \"Every piece of content is strategically designed to support your marketing goals, whether that's building brand awareness, generating leads, or improving search rankings. We maintain your brand voice across all content.\",\r\n        \"Transform your content marketing with professionally written material that resonates with your target audience. Our content drives engagement, builds trust, and converts readers into customers.\"\r\n      ],\r\n      images: [\r\n        \"/homepage/content writing.avif\",\r\n      ]\r\n    },\r\n    {\r\n      id: 5,\r\n      name: \"CONVERSION OPTIMIZATION\",\r\n      title: \"OPTIMIZE YOUR FUNNEL FOR MAXIMUM CONVERSIONS\",\r\n      description: [\r\n        \"Our conversion optimization specialists analyze your website and marketing funnels to identify opportunities for improvement. We use A/B testing and data analysis to increase your conversion rates.\",\r\n        \"From landing page optimization to checkout process improvement, we focus on every touchpoint in your customer journey. Our systematic approach ensures sustainable growth in your conversion rates.\",\r\n        \"Transform more visitors into customers with our proven conversion optimization strategies. We provide detailed insights and continuous testing to maximize the value of your existing traffic.\"\r\n      ],\r\n      images: [\r\n        \"/homepage/conversion optimzation.webp\",\r\n      ]\r\n    }\r\n  ];\r\n\r\n  const currentService = services[activeService];\r\n\r\n  // Digital Marketing Handlers\r\n  const handleServiceClick = (index: number): void => {\r\n    setActiveService(index);\r\n  };\r\n\r\n  return (\r\n    <div className={`relative min-h-screen bg-cover bg-center bg-no-repeat overflow-hidden ${className}`} \r\n         style={{backgroundImage: 'url(https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=1920&h=1080&fit=crop)'}}>\r\n      {/* Light Blue Overlay */}\r\n      <div className=\"absolute inset-0 bg-blue-900/70 backdrop-blur-[1px]\"></div>\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0 opacity-10\">\r\n        <div className=\"absolute top-20 left-10 w-32 h-32 border border-white/20 rounded-lg transform rotate-12\"></div>\r\n        <div className=\"absolute top-40 right-20 w-24 h-24 border border-white/20 rounded-lg transform -rotate-12\"></div>\r\n        <div className=\"absolute bottom-32 left-32 w-16 h-16 border border-white/20 rounded-lg transform rotate-45\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 h-full\">\r\n        {/* Title Section */}\r\n        <div className=\"text-center py-8 sm:py-12 lg:py-16 px-4 sm:px-8\">\r\n          <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-2 sm:mb-4 tracking-wide\">\r\n            TAILORED DIGITAL STRATEGY FOR SUCCESS\r\n          </h2>\r\n          <p className=\"text-base sm:text-lg md:text-xl lg:text-2xl text-blue-100 font-light tracking-wide\">\r\n            BUILDING A DIGITAL PRESENCE STRATEGY TAILORED TO YOUR BUSINESS\r\n          </p>\r\n        </div>\r\n\r\n        {/* Mobile Layout */}\r\n        <div className=\"lg:hidden\">\r\n          {/* Mobile Services Menu */}\r\n          <div className=\"px-4 mb-6\">\r\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-4\">\r\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-2\">\r\n                {services.map((service, index) => (\r\n                  <button\r\n                    key={service.id}\r\n                    onClick={() => handleServiceClick(index)}\r\n                    className={`p-3 rounded-xl text-left transition-all duration-300 ${\r\n                      activeService === index\r\n                        ? 'bg-white text-blue-900 shadow-lg transform scale-105'\r\n                        : 'bg-white/10 text-white hover:bg-white/20'\r\n                    }`}\r\n                  >\r\n                    <h3 className=\"font-semibold text-sm leading-tight\">\r\n                      {service.name}\r\n                    </h3>\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Mobile Content */}\r\n          <div className=\"px-4 pb-8\">\r\n            <div className=\"bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-2xl\">\r\n              {/* Title */}\r\n              <h2 className=\"text-xl sm:text-2xl font-bold text-blue-900 text-center mb-4 leading-tight\">\r\n                {currentService.title}\r\n              </h2>\r\n\r\n              {/* Single Image */}\r\n              <div className=\"relative mb-6\">\r\n                <div className=\"relative h-48 sm:h-56 rounded-lg overflow-hidden\">\r\n                  <Image\r\n                    src={currentService.images[0]}\r\n                    alt={`${currentService.name} example`}\r\n                    fill\r\n                    className=\"object-cover\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              {/* Description */}\r\n              <div className=\"space-y-3 text-gray-700 leading-relaxed text-sm\">\r\n                {currentService.description.map((paragraph, index) => (\r\n                  <p key={index} className=\"transition-all duration-700 ease-out\"\r\n                     style={{ transitionDelay: `${index * 100}ms` }}>\r\n                    {paragraph}\r\n                  </p>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Desktop Layout - Unchanged */}\r\n        <div className=\"hidden lg:grid lg:grid-cols-2 gap-0 h-screen\">\r\n          {/* Left Column - Services Menu */}\r\n          <div className=\"relative flex items-center justify-end p-8 lg:p-16\">\r\n            <div className=\"w-full max-w-2xl space-y-8 text-right\">\r\n              {services.map((service, index) => (\r\n                <div \r\n                  key={service.id} \r\n                  className=\"relative h-20 flex items-center justify-end\"\r\n                  onMouseEnter={() => setHoverIndex(index)}\r\n                  onMouseLeave={() => setHoverIndex(null)}\r\n                >\r\n                  {/* White line under text only */}\r\n                  <div className=\"absolute bottom-4 left-0 right-0 flex items-center z-0\">\r\n                    <div \r\n                      className={`h-0.5 transition-all duration-700 ease-out ml-auto ${\r\n                        activeService === index ? 'bg-white' : 'bg-transparent'\r\n                      }`}\r\n                      style={{\r\n                        width: activeService === index ? '100%' : '0%'\r\n                      }}\r\n                    />\r\n                  </div>\r\n                  \r\n                  <div \r\n                    onClick={() => handleServiceClick(index)}\r\n                    className={`cursor-pointer transition-all duration-300 p-6 block relative z-20 ${\r\n                      activeService === index\r\n                        ? \"text-white transform -translate-x-4\" \r\n                        : \"text-blue-200 hover:text-white\"\r\n                    }`}\r\n                  >\r\n                    <div className=\"relative overflow-hidden\">\r\n                      <h3 className={`font-semibold text-xl lg:text-2xl xl:text-3xl tracking-wide transition-colors duration-200 ${\r\n                        activeService === index ? 'text-white' : \r\n                        hoverIndex === index ? 'text-white' : 'text-blue-200'\r\n                      }`}>\r\n                        {service.name}\r\n                      </h3>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right Column - Full White Content Area */}\r\n          <div className=\"relative pl-6 pb-12\">\r\n            <div className=\"h-full bg-white/95 backdrop-blur-sm shadow-2xl p-4 sm:p-6 lg:p-8 xl:p-12 relative overflow-hidden rounded-l-[2rem]\">\r\n              {/* Fixed Title Area - More Responsive */}\r\n              <div className=\"absolute top-4 sm:top-6 lg:top-8 xl:top-12 left-4 right-4 sm:left-6 sm:right-6 lg:left-8 lg:right-8 xl:left-12 xl:right-12 h-16 sm:h-18 lg:h-20 xl:h-24 flex items-center justify-center\">\r\n                <h2 className={`text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-3xl 2xl:text-4xl font-bold text-blue-900 text-center leading-tight transition-opacity duration-500 ${activeService !== null ? 'opacity-100' : 'opacity-0'}`}>\r\n                  {currentService.title}\r\n                </h2>\r\n              </div>\r\n              \r\n              {/* Fixed Description Area - More Responsive */}\r\n              <div className=\"absolute top-24 sm:top-28 lg:top-32 xl:top-40 left-4 right-4 sm:left-6 sm:right-6 lg:left-8 lg:right-8 xl:left-12 xl:right-12 h-48 sm:h-52 md:h-56 lg:h-60 xl:h-64 overflow-hidden\">\r\n                <div className={`space-y-3 sm:space-y-4 lg:space-y-5 xl:space-y-6 text-gray-700 leading-relaxed text-sm sm:text-base lg:text-lg text-center transition-opacity duration-500 ${activeService !== null ? 'opacity-100' : 'opacity-0'}`}>\r\n                  {currentService.description.map((paragraph, index) => (\r\n                    <p key={index} className=\"transition-all duration-700 ease-out\"\r\n                       style={{ transitionDelay: `${index * 100}ms` }}>\r\n                      {paragraph}\r\n                    </p>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Fixed Single Image Area - More Responsive */}\r\n              <div className=\"absolute bottom-4 sm:bottom-6 lg:bottom-8 xl:bottom-12 left-4 right-4 sm:left-6 sm:right-6 lg:left-8 lg:right-8 xl:left-12 xl:right-12\">\r\n                <div className={`relative transition-opacity duration-500 ${activeService !== null ? 'opacity-100' : 'opacity-0'}`}>\r\n                  <div className=\"relative h-48 sm:h-56 md:h-64 lg:h-72 xl:h-80 rounded-lg overflow-hidden\">\r\n                    <Image\r\n                      src={currentService.images[0]}\r\n                      alt={`${currentService.name} example`}\r\n                      fill\r\n                      className=\"object-cover\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DigitalMarketingServicesSection;"], "names": [], "mappings": "AAAA,sDAAsD;;;;;AACtD;AACA;;;;;AAQA,MAAM,kCAAkF,CAAC,EAAE,YAAY,EAAE,EAAE;;IACzG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,kCAAkC;IAClC,MAAM,WAAsB;QAC1B;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;gBACX;gBACA;gBACA;aACD;YACD,QAAQ;gBACN;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;gBACX;gBACA;gBACA;aACD;YACD,QAAQ;gBACN;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;gBACX;gBACA;gBACA;aACD;YACD,QAAQ;gBACN;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;gBACX;gBACA;gBACA;aACD;YACD,QAAQ;gBACN;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;gBACX;gBACA;gBACA;aACD;YACD,QAAQ;gBACN;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;gBACX;gBACA;gBACA;aACD;YACD,QAAQ;gBACN;aACD;QACH;KACD;IAED,MAAM,iBAAiB,QAAQ,CAAC,cAAc;IAE9C,6BAA6B;IAC7B,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB;IACnB;IAEA,qBACE,0JAAC;QAAI,WAAW,CAAC,sEAAsE,EAAE,WAAW;QAC/F,OAAO;YAAC,iBAAiB;QAA0F;;0BAEtH,0JAAC;gBAAI,WAAU;;;;;;0BAEf,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAI,WAAU;;;;;;kCACf,0JAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAG,WAAU;0CAA2G;;;;;;0CAGzH,0JAAC;gCAAE,WAAU;0CAAqF;;;;;;;;;;;;kCAMpG,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,0JAAC;gDAEC,SAAS,IAAM,mBAAmB;gDAClC,WAAW,CAAC,qDAAqD,EAC/D,kBAAkB,QACd,yDACA,4CACJ;0DAEF,cAAA,0JAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI;;;;;;+CATV,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;0CAkBzB,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;;sDAEb,0JAAC;4CAAG,WAAU;sDACX,eAAe,KAAK;;;;;;sDAIvB,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAI,WAAU;0DACb,cAAA,0JAAC,yHAAA,CAAA,UAAK;oDACJ,KAAK,eAAe,MAAM,CAAC,EAAE;oDAC7B,KAAK,GAAG,eAAe,IAAI,CAAC,QAAQ,CAAC;oDACrC,IAAI;oDACJ,WAAU;;;;;;;;;;;;;;;;sDAMhB,0JAAC;4CAAI,WAAU;sDACZ,eAAe,WAAW,CAAC,GAAG,CAAC,CAAC,WAAW,sBAC1C,0JAAC;oDAAc,WAAU;oDACtB,OAAO;wDAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;oDAAC;8DAC7C;mDAFK;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWlB,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,0JAAC;4CAEC,WAAU;4CACV,cAAc,IAAM,cAAc;4CAClC,cAAc,IAAM,cAAc;;8DAGlC,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDACC,WAAW,CAAC,mDAAmD,EAC7D,kBAAkB,QAAQ,aAAa,kBACvC;wDACF,OAAO;4DACL,OAAO,kBAAkB,QAAQ,SAAS;wDAC5C;;;;;;;;;;;8DAIJ,0JAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAW,CAAC,mEAAmE,EAC7E,kBAAkB,QACd,wCACA,kCACJ;8DAEF,cAAA,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC;4DAAG,WAAW,CAAC,2FAA2F,EACzG,kBAAkB,QAAQ,eAC1B,eAAe,QAAQ,eAAe,iBACtC;sEACC,QAAQ,IAAI;;;;;;;;;;;;;;;;;2CA9Bd,QAAQ,EAAE;;;;;;;;;;;;;;;0CAwCvB,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCAAI,WAAU;;sDAEb,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAG,WAAW,CAAC,sJAAsJ,EAAE,kBAAkB,OAAO,gBAAgB,aAAa;0DAC3N,eAAe,KAAK;;;;;;;;;;;sDAKzB,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAI,WAAW,CAAC,2JAA2J,EAAE,kBAAkB,OAAO,gBAAgB,aAAa;0DACjO,eAAe,WAAW,CAAC,GAAG,CAAC,CAAC,WAAW,sBAC1C,0JAAC;wDAAc,WAAU;wDACtB,OAAO;4DAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;wDAAC;kEAC7C;uDAFK;;;;;;;;;;;;;;;sDASd,0JAAC;4CAAI,WAAU;sDACb,cAAA,0JAAC;gDAAI,WAAW,CAAC,yCAAyC,EAAE,kBAAkB,OAAO,gBAAgB,aAAa;0DAChH,cAAA,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC,yHAAA,CAAA,UAAK;wDACJ,KAAK,eAAe,MAAM,CAAC,EAAE;wDAC7B,KAAK,GAAG,eAAe,IAAI,CAAC,QAAQ,CAAC;wDACrC,IAAI;wDACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhC;GArQM;KAAA;uCAuQS", "debugId": null}}, {"offset": {"line": 6688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/home/<USER>"], "sourcesContent": ["import React from 'react';\r\nimport Image from 'next/image';\r\n\r\ninterface Platform {\r\n  name: string;\r\n  logo: string;\r\n}\r\n\r\ninterface PlatformLogosSectionProps {\r\n  className?: string;\r\n}\r\n\r\nconst PlatformLogosSection: React.FC<PlatformLogosSectionProps> = ({ className = '' }) => {\r\n  const platforms: Platform[] = [\r\n    {\r\n      name: 'SEMrush',\r\n      logo: '/homepage/semrush.png'\r\n    },\r\n    {\r\n      name: 'WordPress',\r\n      logo: '/homepage/wordpress logo.png'\r\n    },\r\n    {\r\n      name: 'Shopify',\r\n      logo: '/homepage/shopify.png'\r\n    },\r\n    {\r\n      name: 'WooCommerce',\r\n      logo: '/homepage/WooCommerce-Logo.jpg'\r\n    },\r\n    {\r\n      name: 'Google Ads',\r\n      logo: '/homepage/google ads.png'\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <section className={`w-full relative overflow-hidden ${className}`} style={{\r\n      background: 'linear-gradient(to bottom, #ffffff, rgba(239, 246, 255, 0.3))'\r\n    }}>\r\n      {/* Background decorative elements */}\r\n      <div className=\"absolute inset-0\" style={{ opacity: 0.05 }}>\r\n        <div className=\"absolute w-32 h-32 rounded-full\" style={{\r\n          top: '80px',\r\n          left: '40px',\r\n          border: '1px solid #DBEAFE'\r\n        }}></div>\r\n        <div className=\"absolute w-24 h-24 rounded-lg transform rotate-45\" style={{\r\n          bottom: '80px',\r\n          right: '40px',\r\n          border: '1px solid #DBEAFE'\r\n        }}></div>\r\n        <div className=\"absolute w-16 h-16 rounded-lg transform -rotate-12\" style={{\r\n          top: '50%',\r\n          left: '25%',\r\n          border: '1px solid #DBEAFE'\r\n        }}></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20\">\r\n        {/* Enhanced Header */}\r\n        <div className=\"text-center mb-12 sm:mb-16 lg:mb-20\">\r\n          <p className=\"font-medium text-base sm:text-lg mb-3 tracking-wide\" style={{\r\n            color: '#3B82F6'\r\n          }}>\r\n            OUR TECHNOLOGY PARTNERS\r\n          </p>\r\n          <h2 className=\"text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight mb-4 sm:mb-6\" style={{\r\n            color: '#1F2937'\r\n          }}>\r\n            WE PROVIDE EXCEPTIONAL EXPERTISE IN<br className=\"hidden sm:block\" />\r\n            <span className=\"sm:hidden\"> </span>SUPPORTING DIVERSE PLATFORMS\r\n          </h2>\r\n          <p className=\"text-base sm:text-lg lg:text-xl max-w-4xl mx-auto leading-relaxed\" style={{\r\n            color: '#4B5563'\r\n          }}>\r\n            From SEO optimization to e-commerce solutions, we leverage industry-leading tools and platforms to deliver exceptional results for your digital presence.\r\n          </p>\r\n        </div>\r\n        \r\n        {/* Platform Logos Container */}\r\n        <div className=\"relative\">\r\n          {/* Background card for logos */}\r\n          <div className=\"rounded-2xl lg:rounded-3xl p-6 sm:p-8 lg:p-12\" style={{\r\n            backgroundColor: 'rgba(255, 255, 255, 0.6)',\r\n            backdropFilter: 'blur(8px)',\r\n            boxShadow: '0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\r\n            border: '1px solid #F3F4F6'\r\n          }}>\r\n            {/* Platform Logos Grid */}\r\n            <div className=\"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-6 sm:gap-8 lg:gap-12 items-center justify-items-center\">\r\n              {platforms.map((platform, index) => (\r\n                <div\r\n                  key={platform.name}\r\n                  className=\"group flex items-center justify-center w-full h-16 sm:h-20 lg:h-24\"\r\n                  style={{ animationDelay: `${index * 100}ms` }}\r\n                >\r\n                  <div \r\n                    className=\"relative w-full h-full flex items-center justify-center p-2 rounded-xl\"\r\n                    style={{\r\n                      transition: 'all 0.3s ease'\r\n                    }}\r\n                    onMouseEnter={(e) => {\r\n                      e.currentTarget.style.backgroundColor = 'rgba(239, 246, 255, 0.5)';\r\n                      e.currentTarget.style.transform = 'scale(1.05)';\r\n                      e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';\r\n                    }}\r\n                    onMouseLeave={(e) => {\r\n                      e.currentTarget.style.backgroundColor = 'transparent';\r\n                      e.currentTarget.style.transform = 'scale(1)';\r\n                      e.currentTarget.style.boxShadow = 'none';\r\n                    }}\r\n                  >\r\n                    <Image\r\n                      src={platform.logo}\r\n                      alt={platform.name}\r\n                      width={200}\r\n                      height={64}\r\n                      className=\"h-10 sm:h-12 lg:h-16 w-auto max-w-[140px] sm:max-w-[160px] lg:max-w-[200px] object-contain transition-all duration-300 hover:scale-110\"\r\n                    />\r\n                    \r\n                    {/* Hover effect overlay */}\r\n                    <div \r\n                      className=\"absolute inset-0 rounded-xl border-2\"\r\n                      style={{\r\n                        borderColor: 'transparent',\r\n                        transition: 'all 0.3s ease',\r\n                        opacity: 0\r\n                      }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.borderColor = '#DBEAFE';\r\n                        e.currentTarget.style.opacity = '1';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.borderColor = 'transparent';\r\n                        e.currentTarget.style.opacity = '0';\r\n                      }}\r\n                    ></div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Subtle bottom accent */}\r\n          <div className=\"absolute left-1/2 transform -translate-x-1/2 w-32 sm:w-48 lg:w-64 h-1 rounded-full\" style={{\r\n            bottom: '-4px',\r\n            background: 'linear-gradient(to right, transparent, #DBEAFE, transparent)'\r\n          }}></div>\r\n        </div>\r\n\r\n        {/* Call to action text */}\r\n        <div className=\"text-center mt-12 sm:mt-16 lg:mt-20\">\r\n          <p className=\"text-sm sm:text-base lg:text-lg italic\" style={{\r\n            color: '#4B5563'\r\n          }}>\r\n            Ready to leverage these powerful platforms for your business?\r\n          </p>\r\n          <button\r\n            className=\"mt-4 sm:mt-6 font-medium rounded-full px-6 sm:px-8 py-3 sm:py-4 text-sm sm:text-base lg:text-lg\"\r\n            style={{\r\n              backgroundColor: '#2563EB',\r\n              color: '#FFFFFF',\r\n              transition: 'all 0.3s ease',\r\n              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\r\n              transform: 'translateY(0)'\r\n            }}\r\n            onClick={() => window.location.href = '/free-estimate'}\r\n            onMouseEnter={(e) => {\r\n              e.currentTarget.style.backgroundColor = '#1d4ed8';\r\n              e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';\r\n              e.currentTarget.style.transform = 'translateY(-4px)';\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              e.currentTarget.style.backgroundColor = '#2563EB';\r\n              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';\r\n              e.currentTarget.style.transform = 'translateY(0)';\r\n            }}\r\n          >\r\n            Get Started Today\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default PlatformLogosSection;"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,uBAA4D,CAAC,EAAE,YAAY,EAAE,EAAE;IACnF,MAAM,YAAwB;QAC5B;YACE,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,MAAM;QACR;KACD;IAED,qBACE,0JAAC;QAAQ,WAAW,CAAC,gCAAgC,EAAE,WAAW;QAAE,OAAO;YACzE,YAAY;QACd;;0BAEE,0JAAC;gBAAI,WAAU;gBAAmB,OAAO;oBAAE,SAAS;gBAAK;;kCACvD,0JAAC;wBAAI,WAAU;wBAAkC,OAAO;4BACtD,KAAK;4BACL,MAAM;4BACN,QAAQ;wBACV;;;;;;kCACA,0JAAC;wBAAI,WAAU;wBAAoD,OAAO;4BACxE,QAAQ;4BACR,OAAO;4BACP,QAAQ;wBACV;;;;;;kCACA,0JAAC;wBAAI,WAAU;wBAAqD,OAAO;4BACzE,KAAK;4BACL,MAAM;4BACN,QAAQ;wBACV;;;;;;;;;;;;0BAGF,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAE,WAAU;gCAAsD,OAAO;oCACxE,OAAO;gCACT;0CAAG;;;;;;0CAGH,0JAAC;gCAAG,WAAU;gCAAoF,OAAO;oCACvG,OAAO;gCACT;;oCAAG;kDACkC,0JAAC;wCAAG,WAAU;;;;;;kDACjD,0JAAC;wCAAK,WAAU;kDAAY;;;;;;oCAAQ;;;;;;;0CAEtC,0JAAC;gCAAE,WAAU;gCAAoE,OAAO;oCACtF,OAAO;gCACT;0CAAG;;;;;;;;;;;;kCAML,0JAAC;wBAAI,WAAU;;0CAEb,0JAAC;gCAAI,WAAU;gCAAgD,OAAO;oCACpE,iBAAiB;oCACjB,gBAAgB;oCAChB,WAAW;oCACX,QAAQ;gCACV;0CAEE,cAAA,0JAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,0JAAC;4CAEC,WAAU;4CACV,OAAO;gDAAE,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;4CAAC;sDAE5C,cAAA,0JAAC;gDACC,WAAU;gDACV,OAAO;oDACL,YAAY;gDACd;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gDACpC;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gDACpC;;kEAEA,0JAAC,yHAAA,CAAA,UAAK;wDACJ,KAAK,SAAS,IAAI;wDAClB,KAAK,SAAS,IAAI;wDAClB,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAIZ,0JAAC;wDACC,WAAU;wDACV,OAAO;4DACL,aAAa;4DACb,YAAY;4DACZ,SAAS;wDACX;wDACA,cAAc,CAAC;4DACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4DACpC,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;wDAClC;wDACA,cAAc,CAAC;4DACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4DACpC,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;wDAClC;;;;;;;;;;;;2CA3CC,SAAS,IAAI;;;;;;;;;;;;;;;0CAoD1B,0JAAC;gCAAI,WAAU;gCAAqF,OAAO;oCACzG,QAAQ;oCACR,YAAY;gCACd;;;;;;;;;;;;kCAIF,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAE,WAAU;gCAAyC,OAAO;oCAC3D,OAAO;gCACT;0CAAG;;;;;;0CAGH,0JAAC;gCACC,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,OAAO;oCACP,YAAY;oCACZ,WAAW;oCACX,WAAW;gCACb;gCACA,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;gCACtC,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oCAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gCACpC;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oCAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gCACpC;0CACD;;;;;;;;;;;;;;;;;;;;;;;;AAOX;KA7KM;uCA+KS", "debugId": null}}, {"offset": {"line": 7010, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/lib/sanity-blog.ts"], "sourcesContent": ["// lib/sanity-blog.ts - Sanity client utilities for blog system\nimport { createClient } from '@sanity/client'\nimport imageUrlBuilder from '@sanity/image-url'\nimport { BlogPost, BlogListItem, BlogCategory, BlogTag, BlogFilters, SanityImageObject } from '@/types/blog'\n\nexport const client = createClient({\n  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,\n  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,\n  apiVersion: '2023-05-03',\n  useCdn: false,\n})\n\n// Image URL builder\nconst builder = imageUrlBuilder(client)\n\nexport function urlForImage(source: SanityImageObject) {\n  return builder.image(source)\n}\n\n// GROQ Queries\nconst blogPostFields = `\n  _id,\n  title,\n  slug,\n  excerpt,\n  featuredImage,\n  content,\n  publishedAt,\n  categories[]->{\n    _id,\n    title,\n    slug,\n    description,\n    color\n  },\n  tags[]->{\n    _id,\n    title,\n    slug,\n    description\n  },\n  featured,\n  readingTime,\n  seo\n`\n\nconst blogListFields = `\n  _id,\n  title,\n  slug,\n  excerpt,\n  featuredImage,\n  publishedAt,\n  categories[]->{\n    _id,\n    title,\n    slug,\n    color\n  },\n  tags[]->{\n    _id,\n    title,\n    slug\n  },\n  featured,\n  readingTime\n`\n\n// Get all blog posts with pagination and filtering\nexport async function getBlogPosts(filters: BlogFilters = {}): Promise<{\n  posts: BlogListItem[];\n  totalPosts: number;\n  hasMore: boolean;\n}> {\n  const { category, tag, search, page = 1, limit = 12 } = filters\n  const offset = (page - 1) * limit\n\n  let filterQuery = '*[_type == \"blogPost\"'\n  const filterConditions: string[] = []\n\n  if (category) {\n    filterConditions.push(`\"${category}\" in categories[]->slug.current`)\n  }\n  \n  if (tag) {\n    filterConditions.push(`\"${tag}\" in tags[]->slug.current`)\n  }\n  \n  if (search) {\n    filterConditions.push(`(title match \"${search}*\" || excerpt match \"${search}*\")`)\n  }\n\n  if (filterConditions.length > 0) {\n    filterQuery += ` && (${filterConditions.join(' && ')})`\n  }\n\n  filterQuery += ']'\n\n  const query = `{\n    \"posts\": ${filterQuery} | order(publishedAt desc) [${offset}...${offset + limit}] {\n      ${blogListFields}\n    },\n    \"totalPosts\": count(${filterQuery})\n  }`\n\n  const result = await client.fetch(query)\n  \n  return {\n    posts: result.posts || [],\n    totalPosts: result.totalPosts || 0,\n    hasMore: result.totalPosts > offset + limit\n  }\n}\n\n// Get featured blog posts\nexport async function getFeaturedBlogPosts(limit: number = 3): Promise<BlogListItem[]> {\n  const query = `*[_type == \"blogPost\" && featured == true] | order(publishedAt desc) [0...${limit}] {\n    ${blogListFields}\n  }`\n  \n  return await client.fetch(query)\n}\n\n// Get single blog post by slug\nexport async function getBlogPost(slug: string): Promise<BlogPost | null> {\n  const query = `*[_type == \"blogPost\" && slug.current == $slug][0] {\n    ${blogPostFields}\n  }`\n  \n  return await client.fetch(query, { slug })\n}\n\n// Get related blog posts\nexport async function getRelatedBlogPosts(postId: string, categories: string[], limit: number = 3): Promise<BlogListItem[]> {\n  const query = `*[_type == \"blogPost\" && _id != $postId && count(categories[]->slug.current[@ in $categories]) > 0] | order(publishedAt desc) [0...${limit}] {\n    ${blogListFields}\n  }`\n  \n  return await client.fetch(query, { postId, categories })\n}\n\n// Get all blog categories\nexport async function getBlogCategories(): Promise<BlogCategory[]> {\n  const query = `*[_type == \"blogCategory\"] | order(title asc) {\n    _id,\n    title,\n    slug,\n    description,\n    color\n  }`\n  \n  return await client.fetch(query)\n}\n\n// Get all blog tags\nexport async function getBlogTags(): Promise<BlogTag[]> {\n  const query = `*[_type == \"blogTag\"] | order(title asc) {\n    _id,\n    title,\n    slug,\n    description\n  }`\n  \n  return await client.fetch(query)\n}\n\n\n\n// Get blog post slugs for static generation\nexport async function getBlogPostSlugs(): Promise<string[]> {\n  const query = `*[_type == \"blogPost\" && defined(slug.current)].slug.current`\n  return await client.fetch(query)\n}\n\n// Get recent blog posts for homepage or sidebar\nexport async function getRecentBlogPosts(limit: number = 5): Promise<BlogListItem[]> {\n  const query = `*[_type == \"blogPost\"] | order(publishedAt desc) [0...${limit}] {\n    ${blogListFields}\n  }`\n  \n  return await client.fetch(query)\n}\n"], "names": [], "mappings": "AAAA,+DAA+D;;;;;;;;;;;;;AAMlD;AALb;AACA;;;AAGO,MAAM,SAAS,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE;IACjC,SAAS;IACT,OAAO;IACP,YAAY;IACZ,QAAQ;AACV;AAEA,oBAAoB;AACpB,MAAM,UAAU,CAAA,GAAA,6KAAA,CAAA,UAAe,AAAD,EAAE;AAEzB,SAAS,YAAY,MAAyB;IACnD,OAAO,QAAQ,KAAK,CAAC;AACvB;AAEA,eAAe;AACf,MAAM,iBAAiB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAwBxB,CAAC;AAED,MAAM,iBAAiB,CAAC;;;;;;;;;;;;;;;;;;;;AAoBxB,CAAC;AAGM,eAAe,aAAa,UAAuB,CAAC,CAAC;IAK1D,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG;IACxD,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;IAE5B,IAAI,cAAc;IAClB,MAAM,mBAA6B,EAAE;IAErC,IAAI,UAAU;QACZ,iBAAiB,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,+BAA+B,CAAC;IACrE;IAEA,IAAI,KAAK;QACP,iBAAiB,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,yBAAyB,CAAC;IAC1D;IAEA,IAAI,QAAQ;QACV,iBAAiB,IAAI,CAAC,CAAC,cAAc,EAAE,OAAO,qBAAqB,EAAE,OAAO,GAAG,CAAC;IAClF;IAEA,IAAI,iBAAiB,MAAM,GAAG,GAAG;QAC/B,eAAe,CAAC,KAAK,EAAE,iBAAiB,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzD;IAEA,eAAe;IAEf,MAAM,QAAQ,CAAC;aACJ,EAAE,YAAY,4BAA4B,EAAE,OAAO,GAAG,EAAE,SAAS,MAAM;MAC9E,EAAE,eAAe;;wBAEC,EAAE,YAAY;GACnC,CAAC;IAEF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC;IAElC,OAAO;QACL,OAAO,OAAO,KAAK,IAAI,EAAE;QACzB,YAAY,OAAO,UAAU,IAAI;QACjC,SAAS,OAAO,UAAU,GAAG,SAAS;IACxC;AACF;AAGO,eAAe,qBAAqB,QAAgB,CAAC;IAC1D,MAAM,QAAQ,CAAC,0EAA0E,EAAE,MAAM;IAC/F,EAAE,eAAe;GAClB,CAAC;IAEF,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAGO,eAAe,YAAY,IAAY;IAC5C,MAAM,QAAQ,CAAC;IACb,EAAE,eAAe;GAClB,CAAC;IAEF,OAAO,MAAM,OAAO,KAAK,CAAC,OAAO;QAAE;IAAK;AAC1C;AAGO,eAAe,oBAAoB,MAAc,EAAE,UAAoB,EAAE,QAAgB,CAAC;IAC/F,MAAM,QAAQ,CAAC,mIAAmI,EAAE,MAAM;IACxJ,EAAE,eAAe;GAClB,CAAC;IAEF,OAAO,MAAM,OAAO,KAAK,CAAC,OAAO;QAAE;QAAQ;IAAW;AACxD;AAGO,eAAe;IACpB,MAAM,QAAQ,CAAC;;;;;;GAMd,CAAC;IAEF,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAGO,eAAe;IACpB,MAAM,QAAQ,CAAC;;;;;GAKd,CAAC;IAEF,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAKO,eAAe;IACpB,MAAM,QAAQ,CAAC,4DAA4D,CAAC;IAC5E,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B;AAGO,eAAe,mBAAmB,QAAgB,CAAC;IACxD,MAAM,QAAQ,CAAC,sDAAsD,EAAE,MAAM;IAC3E,EAAE,eAAe;GAClB,CAAC;IAEF,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 7178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { BlogListItem } from '@/types/blog';\r\nimport { urlForImage } from '@/lib/sanity-blog';\r\n\r\ninterface DigitalStrategyArticlesSectionProps {\r\n  className?: string;\r\n  blogPosts?: BlogListItem[];\r\n}\r\n\r\nconst DigitalStrategyArticlesSection: React.FC<DigitalStrategyArticlesSectionProps> = ({\r\n  className = '',\r\n  blogPosts = []\r\n}) => {\r\n  // Newsletter subscription state\r\n  const [email, setEmail] = useState('');\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\r\n  const [statusMessage, setStatusMessage] = useState('');\r\n\r\n  // Get the featured post (first post) and sidebar posts (remaining posts)\r\n  const featuredPost = blogPosts[0];\r\n  const sidebarPosts = blogPosts.slice(1, 4); // Get up to 3 sidebar posts\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric'\r\n    });\r\n  };\r\n\r\n  // Newsletter subscription handler\r\n  const handleNewsletterSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!email || !email.includes('@')) {\r\n      setSubmitStatus('error');\r\n      setStatusMessage('Please enter a valid email address');\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    setSubmitStatus('idle');\r\n    setStatusMessage('');\r\n\r\n    try {\r\n      const response = await fetch('/api/newsletter', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ email }),\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (response.ok && result.success) {\r\n        setSubmitStatus('success');\r\n        setStatusMessage(result.message || 'Successfully subscribed to newsletter!');\r\n        setEmail(''); // Clear the form\r\n      } else {\r\n        setSubmitStatus('error');\r\n        setStatusMessage(result.message || 'Failed to subscribe. Please try again.');\r\n      }\r\n    } catch (error) {\r\n      setSubmitStatus('error');\r\n      setStatusMessage('Network error. Please check your connection and try again.');\r\n      console.error('Newsletter subscription error:', error);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n\r\n      // Clear status message after 5 seconds\r\n      setTimeout(() => {\r\n        setSubmitStatus('idle');\r\n        setStatusMessage('');\r\n      }, 5000);\r\n    }\r\n  };\r\n\r\n  // Show loading state if no blog posts are available\r\n  if (!blogPosts || blogPosts.length === 0) {\r\n    return (\r\n      <section className={`w-full min-h-screen py-8 sm:py-12 lg:py-16 ${className}`} style={{\r\n        backgroundColor: '#f9fafb'\r\n      }}>\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          {/* Header */}\r\n          <div className=\"mb-8 sm:mb-12 lg:mb-16\">\r\n            <h3 className=\"text-sm font-semibold tracking-wider uppercase mb-3 sm:mb-4\" style={{\r\n              color: '#1d4ed8'\r\n            }}>\r\n              TAILORED DIGITAL STRATEGY FOR SUCCESS\r\n            </h3>\r\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold leading-tight\" style={{\r\n              color: '#1f2937'\r\n            }}>\r\n              BUILDING A DIGITAL PRESENCE STRATEGY<br className=\"hidden sm:block\" />\r\n              <span className=\"sm:hidden\"> </span>TAILORED TO YOUR BUSINESS\r\n            </h2>\r\n          </div>\r\n\r\n          {/* Loading/No Content State */}\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10\">\r\n            <div className=\"lg:col-span-2\">\r\n              <div\r\n                className=\"rounded-2xl h-64 sm:h-80 md:h-96 lg:h-[480px] flex items-center justify-center\"\r\n                style={{\r\n                  backgroundColor: '#ffffff',\r\n                  border: '1px solid #e5e7eb',\r\n                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'\r\n                }}\r\n              >\r\n                <div className=\"text-center\">\r\n                  <div\r\n                    className=\"animate-spin rounded-full h-12 w-12 mx-auto mb-4\"\r\n                    style={{\r\n                      borderWidth: '2px',\r\n                      borderStyle: 'solid',\r\n                      borderColor: '#e5e7eb',\r\n                      borderBottomColor: '#3b82f6'\r\n                    }}\r\n                  ></div>\r\n                  <p style={{ color: '#4b5563' }}>Loading latest insights...</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"space-y-4 sm:space-y-6\">\r\n              {[1, 2, 3].map((i) => (\r\n                <div\r\n                  key={i}\r\n                  className=\"rounded-xl p-4 sm:p-6\"\r\n                  style={{\r\n                    backgroundColor: '#ffffff',\r\n                    border: '1px solid #e5e7eb',\r\n                    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'\r\n                  }}\r\n                >\r\n                  <div className=\"animate-pulse\">\r\n                    <div className=\"h-4 rounded w-3/4 mb-2\" style={{ backgroundColor: '#e5e7eb' }}></div>\r\n                    <div className=\"h-3 rounded w-1/2\" style={{ backgroundColor: '#e5e7eb' }}></div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <section className={`w-full min-h-screen py-8 sm:py-12 lg:py-16 ${className}`} style={{\r\n      backgroundColor: '#f9fafb'\r\n    }}>\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        {/* Header */}\r\n        <div className=\"mb-8 sm:mb-12 lg:mb-16\">\r\n          <h3 className=\"text-sm font-semibold tracking-wider uppercase mb-3 sm:mb-4\" style={{\r\n            color: '#1d4ed8'\r\n          }}>\r\n            TAILORED DIGITAL STRATEGY FOR SUCCESS\r\n          </h3>\r\n          <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold leading-tight\" style={{\r\n            color: '#1f2937'\r\n          }}>\r\n            BUILDING A DIGITAL PRESENCE STRATEGY<br className=\"hidden sm:block\" />\r\n            <span className=\"sm:hidden\"> </span>TAILORED TO YOUR BUSINESS\r\n          </h2>\r\n        </div>\r\n        \r\n        {/* Content Grid */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10\">\r\n          {/* Featured Article - Large Card */}\r\n          <div className=\"lg:col-span-2\">\r\n            {featuredPost ? (\r\n              <Link href={`/blog/${featuredPost.slug.current}`}>\r\n                <div\r\n                  className=\"relative rounded-2xl overflow-hidden h-64 sm:h-80 md:h-96 lg:h-[480px] group cursor-pointer transition-all duration-300\"\r\n                  style={{\r\n                    backgroundColor: '#ffffff',\r\n                    border: '1px solid #e5e7eb',\r\n                    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';\r\n                  }}\r\n                >\r\n                  {/* Main Article Image */}\r\n                  {featuredPost.featuredImage ? (\r\n                    <Image\r\n                      src={urlForImage(featuredPost.featuredImage).width(800).height(480).url()}\r\n                      alt={featuredPost.title}\r\n                      fill\r\n                      className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\r\n                    />\r\n                  ) : (\r\n                    <div\r\n                      className=\"absolute inset-0 flex items-center justify-center group-hover:scale-105 transition-transform duration-300\"\r\n                      style={{\r\n                        background: 'linear-gradient(to bottom right, #2563eb, #1d4ed8)'\r\n                      }}\r\n                    >\r\n                      <div className=\"text-center\" style={{ color: '#ffffff' }}>\r\n                        <div className=\"text-4xl font-bold mb-2\">VESA</div>\r\n                        <div className=\"text-lg\">Digital Strategy</div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Article Info Overlay */}\r\n                  <div\r\n                    className=\"absolute bottom-0 left-0 right-0 p-4 sm:p-6\"\r\n                    style={{\r\n                      background: 'linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.4), transparent)'\r\n                    }}\r\n                  >\r\n                    <h3 className=\"text-lg sm:text-xl md:text-2xl font-bold mb-2 leading-tight\" style={{\r\n                      color: '#ffffff'\r\n                    }}>\r\n                      {featuredPost.title}\r\n                    </h3>\r\n                    <div className=\"flex items-center space-x-4\">\r\n                      <p className=\"text-sm font-medium\" style={{\r\n                        color: '#93c5fd'\r\n                      }}>\r\n                        {featuredPost.readingTime ? `${featuredPost.readingTime} min read` : 'Quick read'}\r\n                      </p>\r\n                      <div className=\"hidden sm:flex items-center space-x-2\">\r\n                        <div className=\"w-2 h-2 rounded-full\" style={{\r\n                          backgroundColor: '#93c5fd'\r\n                        }}></div>\r\n                        <p className=\"text-sm\" style={{\r\n                          color: '#93c5fd'\r\n                        }}>{formatDate(featuredPost.publishedAt)}</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Hover overlay */}\r\n                  <div className=\"absolute inset-0 bg-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                </div>\r\n              </Link>\r\n            ) : (\r\n              <div\r\n                className=\"relative rounded-2xl overflow-hidden h-64 sm:h-80 md:h-96 lg:h-[480px] group\"\r\n                style={{\r\n                  backgroundColor: '#ffffff',\r\n                  border: '1px solid #e5e7eb',\r\n                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"absolute inset-0 flex items-center justify-center\"\r\n                  style={{\r\n                    background: 'linear-gradient(to bottom right, #2563eb, #1d4ed8)'\r\n                  }}\r\n                >\r\n                  <div className=\"text-center\" style={{ color: '#ffffff' }}>\r\n                    <div className=\"text-4xl font-bold mb-2\">VESA</div>\r\n                    <div className=\"text-lg\">Digital Strategy</div>\r\n                    <div className=\"text-sm mt-2\" style={{ opacity: 0.75 }}>No featured articles yet</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n          \r\n          {/* Sidebar Articles */}\r\n          <div className=\"space-y-4 sm:space-y-6 lg:space-y-8\">\r\n            {sidebarPosts.length > 0 ? (\r\n              sidebarPosts.map((post) => (\r\n                <Link key={post._id} href={`/blog/${post.slug.current}`}>\r\n                  <div\r\n                    className=\"group cursor-pointer rounded-xl transition-all duration-300\"\r\n                    style={{\r\n                      backgroundColor: '#ffffff',\r\n                      border: '1px solid #f3f4f6'\r\n                    }}\r\n                    onMouseEnter={(e) => {\r\n                      e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';\r\n                      e.currentTarget.style.borderColor = '#bfdbfe';\r\n                    }}\r\n                    onMouseLeave={(e) => {\r\n                      e.currentTarget.style.boxShadow = 'none';\r\n                      e.currentTarget.style.borderColor = '#f3f4f6';\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4\">\r\n                      {/* Article Image */}\r\n                      <div className=\"flex-shrink-0 group-hover:scale-105 transition-transform duration-300\">\r\n                        {post.featuredImage ? (\r\n                          <Image\r\n                            src={urlForImage(post.featuredImage).width(112).height(80).url()}\r\n                            alt={post.title}\r\n                            width={112}\r\n                            height={80}\r\n                            className=\"w-20 h-14 sm:w-24 sm:h-16 md:w-28 md:h-20 rounded-lg object-cover shadow-md\"\r\n                          />\r\n                        ) : (\r\n                          <div\r\n                            className=\"w-20 h-14 sm:w-24 sm:h-16 md:w-28 md:h-20 rounded-lg flex items-center justify-center shadow-md\"\r\n                            style={{\r\n                              background: 'linear-gradient(to bottom right, #dbeafe, #bfdbfe)'\r\n                            }}\r\n                          >\r\n                            <div className=\"text-center\" style={{ color: '#2563eb' }}>\r\n                              <div className=\"text-xs font-bold\">VESA</div>\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n\r\n                      {/* Content */}\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <h4\r\n                          className=\"font-bold text-base sm:text-lg md:text-xl leading-tight mb-2 sm:mb-3 transition-colors duration-300 line-clamp-2\"\r\n                          style={{ color: '#1f2937' }}\r\n                          onMouseEnter={(e) => {\r\n                            e.currentTarget.style.color = '#2563eb';\r\n                          }}\r\n                          onMouseLeave={(e) => {\r\n                            e.currentTarget.style.color = '#1f2937';\r\n                          }}\r\n                        >\r\n                          {post.title}\r\n                        </h4>\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <p className=\"text-sm font-medium\" style={{ color: '#3b82f6' }}>\r\n                            {post.readingTime ? `${post.readingTime} min read` : formatDate(post.publishedAt)}\r\n                          </p>\r\n                          <div\r\n                            className=\"transition-colors duration-300\"\r\n                            style={{ color: '#60a5fa' }}\r\n                            onMouseEnter={(e) => {\r\n                              e.currentTarget.style.color = '#2563eb';\r\n                            }}\r\n                            onMouseLeave={(e) => {\r\n                              e.currentTarget.style.color = '#60a5fa';\r\n                            }}\r\n                          >\r\n                            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\r\n                            </svg>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </Link>\r\n              ))\r\n            ) : (\r\n              // Fallback when no blog posts are available\r\n              Array.from({ length: 3 }).map((_, index) => (\r\n                <div key={index} className=\"bg-white rounded-xl border border-gray-100\">\r\n                  <div className=\"flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4\">\r\n                    <div className=\"w-20 h-14 sm:w-24 sm:h-16 md:w-28 md:h-20 rounded-lg bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center shadow-md\">\r\n                      <div className=\"text-blue-600 text-center\">\r\n                        <div className=\"text-xs font-bold\">VESA</div>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <h4 className=\"font-bold text-base sm:text-lg md:text-xl leading-tight mb-2 sm:mb-3\" style={{\r\n                        color: '#1f2937'\r\n                      }}>\r\n                        Coming Soon: Digital Marketing Insights\r\n                      </h4>\r\n                      <p className=\"text-sm font-medium\" style={{\r\n                        color: '#3b82f6'\r\n                      }}>\r\n                        Stay tuned\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))\r\n            )}\r\n\r\n            {/* View All Articles Link */}\r\n            <div className=\"mt-6 lg:mt-8\">\r\n              <Link href=\"/blog\">\r\n                <button\r\n                  className=\"w-full font-medium rounded-xl py-3 sm:py-4 px-4 sm:px-6 transition-all duration-300 group\"\r\n                  style={{\r\n                    backgroundColor: '#eff6ff',\r\n                    color: '#2563eb',\r\n                    border: '1px solid #bfdbfe'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.currentTarget.style.backgroundColor = '#dbeafe';\r\n                    e.currentTarget.style.borderColor = '#93c5fd';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.currentTarget.style.backgroundColor = '#eff6ff';\r\n                    e.currentTarget.style.borderColor = '#bfdbfe';\r\n                  }}\r\n                >\r\n                  <div className=\"flex items-center justify-center space-x-2\">\r\n                    <span>View All Articles</span>\r\n                    <svg className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\r\n                    </svg>\r\n                  </div>\r\n                </button>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom Call to Action */}\r\n        <div className=\"mt-12 sm:mt-16 lg:mt-20 text-center\">\r\n          <div className=\"rounded-2xl p-6 sm:p-8 lg:p-12 shadow-lg\" style={{\r\n            backgroundColor: '#ffffff',\r\n            border: '1px solid #e5e7eb'\r\n          }}>\r\n            <h3 className=\"text-xl sm:text-2xl lg:text-3xl font-bold mb-3 sm:mb-4\" style={{\r\n              color: '#1f2937'\r\n            }}>\r\n              Stay Updated with Digital Marketing Insights\r\n            </h3>\r\n            <p className=\"text-base sm:text-lg mb-6 sm:mb-8 max-w-2xl mx-auto\" style={{\r\n              color: '#4b5563'\r\n            }}>\r\n              Get the latest strategies, trends, and expert insights delivered to your inbox.\r\n            </p>\r\n\r\n            {/* Newsletter Subscription Form */}\r\n            <form onSubmit={handleNewsletterSubmit} className=\"max-w-md mx-auto\">\r\n              <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4\">\r\n                <input\r\n                  type=\"email\"\r\n                  value={email}\r\n                  onChange={(e) => setEmail(e.target.value)}\r\n                  placeholder=\"Your email address\"\r\n                  required\r\n                  disabled={isSubmitting}\r\n                  className=\"flex-1 px-4 py-3 rounded-lg focus:outline-none transition-all duration-300 whitespace-nowrap min-w-[120px] flex items-center justify-center\"\r\n                  style={{\r\n                    border: '1px solid #d1d5db',\r\n                    backgroundColor: '#ffffff',\r\n                    color: '#374151'\r\n                  }}\r\n                  onFocus={(e) => {\r\n                    e.currentTarget.style.outline = '2px solid #3b82f6';\r\n                    e.currentTarget.style.outlineOffset = '2px';\r\n                    e.currentTarget.style.borderColor = 'transparent';\r\n                  }}\r\n                  onBlur={(e) => {\r\n                    e.currentTarget.style.outline = 'none';\r\n                    e.currentTarget.style.borderColor = '#d1d5db';\r\n                  }}\r\n                />\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={isSubmitting || !email}\r\n                  className=\"font-medium px-6 py-3 rounded-lg transition-all duration-300 whitespace-nowrap min-w-[120px] flex items-center justify-center\"\r\n                  style={{\r\n                    backgroundColor: isSubmitting || !email ? '#9ca3af' : '#3b82f6',\r\n                    color: '#ffffff',\r\n                    cursor: isSubmitting || !email ? 'not-allowed' : 'pointer',\r\n                    opacity: isSubmitting || !email ? 0.5 : 1\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    if (!isSubmitting && email) {\r\n                      e.currentTarget.style.backgroundColor = '#2563eb';\r\n                    }\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    if (!isSubmitting && email) {\r\n                      e.currentTarget.style.backgroundColor = '#3b82f6';\r\n                    }\r\n                  }}\r\n                >\r\n                  {isSubmitting ? (\r\n                    <>\r\n                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                      </svg>\r\n                      Subscribing...\r\n                    </>\r\n                  ) : (\r\n                    'Subscribe'\r\n                  )}\r\n                </button>\r\n              </div>\r\n\r\n              {/* Status Message */}\r\n              {statusMessage && (\r\n                <div\r\n                  className=\"mt-4 p-3 rounded-lg text-sm\"\r\n                  style={{\r\n                    backgroundColor: submitStatus === 'success' ? '#f0fdf4' : submitStatus === 'error' ? '#fef2f2' : '#f9fafb',\r\n                    color: submitStatus === 'success' ? '#15803d' : submitStatus === 'error' ? '#dc2626' : '#374151',\r\n                    border: `1px solid ${submitStatus === 'success' ? '#bbf7d0' : submitStatus === 'error' ? '#fecaca' : '#e5e7eb'}`\r\n                  }}\r\n                >\r\n                  {statusMessage}\r\n                </div>\r\n              )}\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default DigitalStrategyArticlesSection;"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AACrD;AACA;AACA;AAEA;;;;;;;AAOA,MAAM,iCAAgF,CAAC,EACrF,YAAY,EAAE,EACd,YAAY,EAAE,EACf;;IACC,gCAAgC;IAChC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAgC;IAC/E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,yEAAyE;IACzE,MAAM,eAAe,SAAS,CAAC,EAAE;IACjC,MAAM,eAAe,UAAU,KAAK,CAAC,GAAG,IAAI,4BAA4B;IAExE,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,kCAAkC;IAClC,MAAM,yBAAyB,OAAO;QACpC,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM;YAClC,gBAAgB;YAChB,iBAAiB;YACjB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QAEjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,IAAI,OAAO,OAAO,EAAE;gBACjC,gBAAgB;gBAChB,iBAAiB,OAAO,OAAO,IAAI;gBACnC,SAAS,KAAK,iBAAiB;YACjC,OAAO;gBACL,gBAAgB;gBAChB,iBAAiB,OAAO,OAAO,IAAI;YACrC;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;YAChB,iBAAiB;YACjB,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,gBAAgB;YAEhB,uCAAuC;YACvC,WAAW;gBACT,gBAAgB;gBAChB,iBAAiB;YACnB,GAAG;QACL;IACF;IAEA,oDAAoD;IACpD,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;QACxC,qBACE,0JAAC;YAAQ,WAAW,CAAC,2CAA2C,EAAE,WAAW;YAAE,OAAO;gBACpF,iBAAiB;YACnB;sBACE,cAAA,0JAAC;gBAAI,WAAU;;kCAEb,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAG,WAAU;gCAA8D,OAAO;oCACjF,OAAO;gCACT;0CAAG;;;;;;0CAGH,0JAAC;gCAAG,WAAU;gCAAuE,OAAO;oCAC1F,OAAO;gCACT;;oCAAG;kDACmC,0JAAC;wCAAG,WAAU;;;;;;kDAClD,0JAAC;wCAAK,WAAU;kDAAY;;;;;;oCAAQ;;;;;;;;;;;;;kCAKxC,0JAAC;wBAAI,WAAU;;0CACb,0JAAC;gCAAI,WAAU;0CACb,cAAA,0JAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,QAAQ;wCACR,WAAW;oCACb;8CAEA,cAAA,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,WAAU;gDACV,OAAO;oDACL,aAAa;oDACb,aAAa;oDACb,aAAa;oDACb,mBAAmB;gDACrB;;;;;;0DAEF,0JAAC;gDAAE,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;;;;;;;;;;;;;;;;;0CAItC,0JAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,kBACd,0JAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,QAAQ;4CACR,WAAW;wCACb;kDAEA,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;oDAAyB,OAAO;wDAAE,iBAAiB;oDAAU;;;;;;8DAC5E,0JAAC;oDAAI,WAAU;oDAAoB,OAAO;wDAAE,iBAAiB;oDAAU;;;;;;;;;;;;uCAVpE;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmBrB;IAEA,qBACE,0JAAC;QAAQ,WAAW,CAAC,2CAA2C,EAAE,WAAW;QAAE,OAAO;YACpF,iBAAiB;QACnB;kBACE,cAAA,0JAAC;YAAI,WAAU;;8BAEb,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAG,WAAU;4BAA8D,OAAO;gCACjF,OAAO;4BACT;sCAAG;;;;;;sCAGH,0JAAC;4BAAG,WAAU;4BAAuE,OAAO;gCAC1F,OAAO;4BACT;;gCAAG;8CACmC,0JAAC;oCAAG,WAAU;;;;;;8CAClD,0JAAC;oCAAK,WAAU;8CAAY;;;;;;gCAAQ;;;;;;;;;;;;;8BAKxC,0JAAC;oBAAI,WAAU;;sCAEb,0JAAC;4BAAI,WAAU;sCACZ,6BACC,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,MAAM,EAAE,aAAa,IAAI,CAAC,OAAO,EAAE;0CAC9C,cAAA,0JAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,QAAQ;wCACR,WAAW;oCACb;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oCACpC;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oCACpC;;wCAGC,aAAa,aAAa,iBACzB,0JAAC,yHAAA,CAAA,UAAK;4CACJ,KAAK,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD,EAAE,aAAa,aAAa,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG;4CACvE,KAAK,aAAa,KAAK;4CACvB,IAAI;4CACJ,WAAU;;;;;iEAGZ,0JAAC;4CACC,WAAU;4CACV,OAAO;gDACL,YAAY;4CACd;sDAEA,cAAA,0JAAC;gDAAI,WAAU;gDAAc,OAAO;oDAAE,OAAO;gDAAU;;kEACrD,0JAAC;wDAAI,WAAU;kEAA0B;;;;;;kEACzC,0JAAC;wDAAI,WAAU;kEAAU;;;;;;;;;;;;;;;;;sDAM/B,0JAAC;4CACC,WAAU;4CACV,OAAO;gDACL,YAAY;4CACd;;8DAEA,0JAAC;oDAAG,WAAU;oDAA8D,OAAO;wDACjF,OAAO;oDACT;8DACG,aAAa,KAAK;;;;;;8DAErB,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAE,WAAU;4DAAsB,OAAO;gEACxC,OAAO;4DACT;sEACG,aAAa,WAAW,GAAG,GAAG,aAAa,WAAW,CAAC,SAAS,CAAC,GAAG;;;;;;sEAEvE,0JAAC;4DAAI,WAAU;;8EACb,0JAAC;oEAAI,WAAU;oEAAuB,OAAO;wEAC3C,iBAAiB;oEACnB;;;;;;8EACA,0JAAC;oEAAE,WAAU;oEAAU,OAAO;wEAC5B,OAAO;oEACT;8EAAI,WAAW,aAAa,WAAW;;;;;;;;;;;;;;;;;;;;;;;;sDAM7C,0JAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;qDAInB,0JAAC;gCACC,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,QAAQ;oCACR,WAAW;gCACb;0CAEA,cAAA,0JAAC;oCACC,WAAU;oCACV,OAAO;wCACL,YAAY;oCACd;8CAEA,cAAA,0JAAC;wCAAI,WAAU;wCAAc,OAAO;4CAAE,OAAO;wCAAU;;0DACrD,0JAAC;gDAAI,WAAU;0DAA0B;;;;;;0DACzC,0JAAC;gDAAI,WAAU;0DAAU;;;;;;0DACzB,0JAAC;gDAAI,WAAU;gDAAe,OAAO;oDAAE,SAAS;gDAAK;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,0JAAC;4BAAI,WAAU;;gCACZ,aAAa,MAAM,GAAG,IACrB,aAAa,GAAG,CAAC,CAAC,qBAChB,0JAAC,wHAAA,CAAA,UAAI;wCAAgB,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE;kDACrD,cAAA,0JAAC;4CACC,WAAU;4CACV,OAAO;gDACL,iBAAiB;gDACjB,QAAQ;4CACV;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4CACtC;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4CACtC;sDAEA,cAAA,0JAAC;gDAAI,WAAU;;kEAEb,0JAAC;wDAAI,WAAU;kEACZ,KAAK,aAAa,iBACjB,0JAAC,yHAAA,CAAA,UAAK;4DACJ,KAAK,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,aAAa,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,IAAI,GAAG;4DAC9D,KAAK,KAAK,KAAK;4DACf,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;iFAGZ,0JAAC;4DACC,WAAU;4DACV,OAAO;gEACL,YAAY;4DACd;sEAEA,cAAA,0JAAC;gEAAI,WAAU;gEAAc,OAAO;oEAAE,OAAO;gEAAU;0EACrD,cAAA,0JAAC;oEAAI,WAAU;8EAAoB;;;;;;;;;;;;;;;;;;;;;kEAO3C,0JAAC;wDAAI,WAAU;;0EACb,0JAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO;gEAAU;gEAC1B,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gEAChC;gEACA,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gEAChC;0EAEC,KAAK,KAAK;;;;;;0EAEb,0JAAC;gEAAI,WAAU;;kFACb,0JAAC;wEAAE,WAAU;wEAAsB,OAAO;4EAAE,OAAO;wEAAU;kFAC1D,KAAK,WAAW,GAAG,GAAG,KAAK,WAAW,CAAC,SAAS,CAAC,GAAG,WAAW,KAAK,WAAW;;;;;;kFAElF,0JAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,OAAO;wEAAU;wEAC1B,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;wEAChC;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;wEAChC;kFAEA,cAAA,0JAAC;4EAAI,WAAU;4EAAU,MAAK;4EAAO,QAAO;4EAAe,SAAQ;sFACjE,cAAA,0JAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAtExE,KAAK,GAAG;;;;gDAgFrB,4CAA4C;gCAC5C,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,0JAAC;wCAAgB,WAAU;kDACzB,cAAA,0JAAC;4CAAI,WAAU;;8DACb,0JAAC;oDAAI,WAAU;8DACb,cAAA,0JAAC;wDAAI,WAAU;kEACb,cAAA,0JAAC;4DAAI,WAAU;sEAAoB;;;;;;;;;;;;;;;;8DAGvC,0JAAC;oDAAI,WAAU;;sEACb,0JAAC;4DAAG,WAAU;4DAAuE,OAAO;gEAC1F,OAAO;4DACT;sEAAG;;;;;;sEAGH,0JAAC;4DAAE,WAAU;4DAAsB,OAAO;gEACxC,OAAO;4DACT;sEAAG;;;;;;;;;;;;;;;;;;uCAfC;;;;;8CAyBd,0JAAC;oCAAI,WAAU;8CACb,cAAA,0JAAC,wHAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,0JAAC;4CACC,WAAU;4CACV,OAAO;gDACL,iBAAiB;gDACjB,OAAO;gDACP,QAAQ;4CACV;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4CACtC;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4CACtC;sDAEA,cAAA,0JAAC;gDAAI,WAAU;;kEACb,0JAAC;kEAAK;;;;;;kEACN,0JAAC;wDAAI,WAAU;wDAAsE,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC7H,cAAA,0JAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUnF,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAC/D,iBAAiB;4BACjB,QAAQ;wBACV;;0CACE,0JAAC;gCAAG,WAAU;gCAAyD,OAAO;oCAC5E,OAAO;gCACT;0CAAG;;;;;;0CAGH,0JAAC;gCAAE,WAAU;gCAAsD,OAAO;oCACxE,OAAO;gCACT;0CAAG;;;;;;0CAKH,0JAAC;gCAAK,UAAU;gCAAwB,WAAU;;kDAChD,0JAAC;wCAAI,WAAU;;0DACb,0JAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,aAAY;gDACZ,QAAQ;gDACR,UAAU;gDACV,WAAU;gDACV,OAAO;oDACL,QAAQ;oDACR,iBAAiB;oDACjB,OAAO;gDACT;gDACA,SAAS,CAAC;oDACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;oDAChC,EAAE,aAAa,CAAC,KAAK,CAAC,aAAa,GAAG;oDACtC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;gDACtC;gDACA,QAAQ,CAAC;oDACP,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;oDAChC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;gDACtC;;;;;;0DAEF,0JAAC;gDACC,MAAK;gDACL,UAAU,gBAAgB,CAAC;gDAC3B,WAAU;gDACV,OAAO;oDACL,iBAAiB,gBAAgB,CAAC,QAAQ,YAAY;oDACtD,OAAO;oDACP,QAAQ,gBAAgB,CAAC,QAAQ,gBAAgB;oDACjD,SAAS,gBAAgB,CAAC,QAAQ,MAAM;gDAC1C;gDACA,cAAc,CAAC;oDACb,IAAI,CAAC,gBAAgB,OAAO;wDAC1B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDAC1C;gDACF;gDACA,cAAc,CAAC;oDACb,IAAI,CAAC,gBAAgB,OAAO;wDAC1B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDAC1C;gDACF;0DAEC,6BACC;;sEACE,0JAAC;4DAAI,WAAU;4DAA6C,OAAM;4DAA6B,MAAK;4DAAO,SAAQ;;8EACjH,0JAAC;oEAAO,WAAU;oEAAa,IAAG;oEAAK,IAAG;oEAAK,GAAE;oEAAK,QAAO;oEAAe,aAAY;;;;;;8EACxF,0JAAC;oEAAK,WAAU;oEAAa,MAAK;oEAAe,GAAE;;;;;;;;;;;;wDAC/C;;mEAIR;;;;;;;;;;;;oCAML,+BACC,0JAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB,iBAAiB,YAAY,YAAY,iBAAiB,UAAU,YAAY;4CACjG,OAAO,iBAAiB,YAAY,YAAY,iBAAiB,UAAU,YAAY;4CACvF,QAAQ,CAAC,UAAU,EAAE,iBAAiB,YAAY,YAAY,iBAAiB,UAAU,YAAY,WAAW;wCAClH;kDAEC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnB;GAnfM;KAAA;uCAqfS", "debugId": null}}, {"offset": {"line": 8225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\nimport React from 'react';\r\n\r\ninterface ContactCTASectionProps {\r\n  className?: string;\r\n}\r\n\r\nconst ContactCTASection: React.FC<ContactCTASectionProps> = ({ className = '' }) => {\r\n  const handleContactUs = (): void => {\r\n    // Navigate to contact page\r\n    window.location.href = '/contact-us';\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <style jsx>{`\r\n        @keyframes scroll {\r\n          from { transform: translateX(0%); }\r\n          to { transform: translateX(-50%); }\r\n        }\r\n\r\n        .scroll-container {\r\n          display: flex;\r\n          position: absolute;\r\n          bottom: -65px;\r\n          left: 0;\r\n          align-items: flex-end;\r\n          justify-content: flex-start;\r\n          width: 100%;\r\n          height: calc(100% + 65px);\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .scroll-wrapper {\r\n          display: flex;\r\n          animation: scroll 8s linear infinite;\r\n        }\r\n\r\n        .scroll-text {\r\n          font-size: clamp(3rem, 12vw, 16rem);\r\n          font-weight: 900;\r\n          color: transparent;\r\n          -webkit-text-stroke: clamp(1px, 0.5vw, 3px) rgba(255, 255, 255, 0.2);\r\n          text-transform: uppercase;\r\n          letter-spacing: 0.05em;\r\n          margin: 0;\r\n        }\r\n\r\n        /* Mobile - bigger text and faster animation */\r\n        @media (max-width: 639px) {\r\n          .scroll-wrapper {\r\n            animation: scroll 6s linear infinite;\r\n          }\r\n          .scroll-text {\r\n            font-size: clamp(6rem, 20vw, 12rem);\r\n            -webkit-text-stroke: 2px rgba(255, 255, 255, 0.25);\r\n          }\r\n          .scroll-container {\r\n            bottom: -20px;\r\n            height: calc(100% + 20px);\r\n          }\r\n        }\r\n\r\n        /* Contact button hover effect */\r\n        .contact-btn {\r\n          position: relative;\r\n          overflow: hidden;\r\n        }\r\n\r\n        .contact-btn:hover {\r\n          background: transparent;\r\n          color: transparent;\r\n          -webkit-text-stroke: 2px white;\r\n          text-stroke: 2px white;\r\n          border: 2px solid white;\r\n          transform: translateY(-2px);\r\n          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\r\n        }\r\n\r\n        /* Tablet */\r\n        @media (min-width: 640px) and (max-width: 767px) {\r\n          .scroll-wrapper {\r\n            animation: scroll 10s linear infinite;\r\n          }\r\n        }\r\n\r\n        /* Desktop */\r\n        @media (min-width: 768px) and (max-width: 1023px) {\r\n          .scroll-wrapper {\r\n            animation: scroll 14s linear infinite;\r\n          }\r\n        }\r\n\r\n        /* Large desktop */\r\n        @media (min-width: 1024px) and (max-width: 1279px) {\r\n          .scroll-wrapper {\r\n            animation: scroll 18s linear infinite;\r\n          }\r\n        }\r\n\r\n        /* Extra large desktop */\r\n        @media (min-width: 1280px) {\r\n          .scroll-wrapper {\r\n            animation: scroll 22s linear infinite;\r\n          }\r\n        }\r\n      `}</style>\r\n\r\n      <section className={`relative bg-gradient-to-br from-blue-500 to-blue-700 text-white min-h-[400px] sm:min-h-[500px] overflow-hidden ${className}`}>\r\n        {/* Main Content */}\r\n        <div className=\"relative z-10 max-w-6xl mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-8 xl:py-12\">\r\n          <h2 className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-6 sm:mb-8 leading-tight\">\r\n            Wanna get in touch?\r\n          </h2>\r\n\r\n          <p className=\"text-base sm:text-lg md:text-xl leading-relaxed mb-8 sm:mb-10 max-w-2xl opacity-90\">\r\n            We offer exceptional services tailored to a wide range of businesses that want to improve the\r\n            effectiveness of their digital marketing activities with discernible returns on investment.\r\n            We aim to get back to all enquiries rapidly.\r\n          </p>\r\n\r\n          <button\r\n            onClick={handleContactUs}\r\n            className=\"contact-btn bg-white text-blue-600 px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-xs sm:text-sm uppercase tracking-wider focus:outline-none focus:ring-4 focus:ring-white focus:ring-opacity-50 transition-all duration-300\"\r\n          >\r\n            Contact Us\r\n          </button>\r\n        </div>\r\n\r\n        {/* Infinite Scrolling Text - Positioned at bottom like original */}\r\n        <div className=\"absolute bottom-0 left-0 w-full h-32 sm:h-40 lg:h-48 overflow-hidden z-0\">\r\n          <div className=\"scroll-container\">\r\n            <div className=\"scroll-wrapper\">\r\n              <div className=\"scroll-text\">START A PROJECT&nbsp;</div>\r\n              <div className=\"scroll-text\">START A PROJECT&nbsp;</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ContactCTASection;"], "names": [], "mappings": "AAAA,wCAAwC;;;;;;;;AAOxC,MAAM,oBAAsD,CAAC,EAAE,YAAY,EAAE,EAAE;IAC7E,MAAM,kBAAkB;QACtB,2BAA2B;QAC3B,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE;;;;;;0BA8FE,0JAAC;yDAAmB,CAAC,+GAA+G,EAAE,WAAW;;kCAE/I,0JAAC;iEAAc;;0CACb,0JAAC;yEAAa;0CAAoF;;;;;;0CAIlG,0JAAC;yEAAY;0CAAqF;;;;;;0CAMlG,0JAAC;gCACC,SAAS;yEACC;0CACX;;;;;;;;;;;;kCAMH,0JAAC;iEAAc;kCACb,cAAA,0JAAC;qEAAc;sCACb,cAAA,0JAAC;yEAAc;;kDACb,0JAAC;iFAAc;kDAAc;;;;;;kDAC7B,0JAAC;iFAAc;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;KAtIM;uCAwIS", "debugId": null}}, {"offset": {"line": 8342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/home/<USER>"], "sourcesContent": ["import React from 'react';\r\nimport Image from 'next/image';\r\n\r\ninterface Partner {\r\n  id: number;\r\n  name: string;\r\n  logo: string;\r\n}\r\n\r\ninterface PartnersProps {\r\n  className?: string;\r\n  speed?: number;\r\n}\r\n\r\nconst Partners: React.FC<PartnersProps> = ({ \r\n  className = '', \r\n  speed = 30\r\n}) => {\r\n  // Partner logos data\r\n  const partners: Partner[] = [\r\n    { id: 1, name: \"<PERSON><PERSON>\", logo: \"/logo/AIClogo.png\" },\r\n    { id: 2, name: \"After Hours\", logo: \"/logo/afterhourslogo.png\" },\r\n    { id: 3, name: \"Argon\", logo: \"/logo/argon logo.png\" },\r\n    { id: 4, name: \"Dorchester\", logo: \"/logo/dorchester logo.png\" },\r\n    { id: 5, name: \"Harbour\", logo: \"/logo/harbour logo.png\" },\r\n    { id: 6, name: \"Ice Cream Truck\", logo: \"/logo/ice cream truck logo.png\" },\r\n    { id: 7, name: \"JN<PERSON>\", logo: \"/logo/jnj logo.png\" },\r\n    { id: 8, name: \"Kiddie Couch\", logo: \"/logo/kiddie couch logo.png\" },\r\n    { id: 9, name: \"Liquor To Go\", logo: \"/logo/liquor to go logo.png\" },\r\n    { id: 10, name: \"Miami\", logo: \"/logo/miami logo.png\" }\r\n  ];\r\n\r\n  return (\r\n    <div className={`relative bg-blue-600 py-8 overflow-hidden ${className}`}>\r\n      <style jsx>{`\r\n        @keyframes scroll {\r\n          0% { transform: translateX(0); }\r\n          100% { transform: translateX(calc(-200px * ${partners.length})); }\r\n        }\r\n        \r\n        @media (min-width: 768px) {\r\n          @keyframes scroll {\r\n            0% { transform: translateX(0); }\r\n            100% { transform: translateX(calc(-250px * ${partners.length})); }\r\n          }\r\n        }\r\n        \r\n        .slider {\r\n          position: relative;\r\n        }\r\n        \r\n        .slider::before,\r\n        .slider::after {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 0;\r\n          width: 60px;\r\n          height: 100%;\r\n          z-index: 10;\r\n          pointer-events: none;\r\n        }\r\n        \r\n        .slider::before {\r\n          left: 0;\r\n          background: linear-gradient(to right, rgb(37, 99, 235) 0%, rgba(37, 99, 235, 0) 100%);\r\n        }\r\n        \r\n        .slider::after {\r\n          right: 0;\r\n          background: linear-gradient(to left, rgb(37, 99, 235) 0%, rgba(37, 99, 235, 0) 100%);\r\n        }\r\n        \r\n        .slide-track {\r\n          animation: scroll ${speed}s linear infinite;\r\n          display: flex;\r\n          width: calc(200px * ${partners.length * 2});\r\n        }\r\n        \r\n        @media (min-width: 768px) {\r\n          .slide-track {\r\n            width: calc(250px * ${partners.length * 2});\r\n          }\r\n        }\r\n        \r\n        .slide {\r\n          height: 100px;\r\n          width: 200px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          flex-shrink: 0;\r\n          padding: 0 20px;\r\n        }\r\n        \r\n        @media (min-width: 768px) {\r\n          .slide {\r\n            width: 250px;\r\n          }\r\n        }\r\n      `}</style>\r\n      \r\n      <div className=\"slider\">\r\n        <div className=\"slide-track\">\r\n          {/* First set */}\r\n          {partners.map((partner) => (\r\n            <div key={partner.id} className=\"slide\">\r\n              <Image\r\n                src={partner.logo}\r\n                alt={partner.name}\r\n                width={160}\r\n                height={80}\r\n                className=\"h-12 md:h-16 object-contain opacity-90 w-auto\"\r\n              />\r\n            </div>\r\n          ))}\r\n          {/* Duplicate for seamless loop */}\r\n          {partners.map((partner) => (\r\n            <div key={`dup-${partner.id}`} className=\"slide\">\r\n              <Image\r\n                src={partner.logo}\r\n                alt={partner.name}\r\n                width={160}\r\n                height={80}\r\n                className=\"h-12 md:h-16 object-contain opacity-90 w-auto\"\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Partners;"], "names": [], "mappings": ";;;;;AACA;;;;AAaA,MAAM,WAAoC,CAAC,EACzC,YAAY,EAAE,EACd,QAAQ,EAAE,EACX;IACC,qBAAqB;IACrB,MAAM,WAAsB;QAC1B;YAAE,IAAI;YAAG,MAAM;YAAO,MAAM;QAAoB;QAChD;YAAE,IAAI;YAAG,MAAM;YAAe,MAAM;QAA2B;QAC/D;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;QAAuB;QACrD;YAAE,IAAI;YAAG,MAAM;YAAc,MAAM;QAA4B;QAC/D;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;QAAyB;QACzD;YAAE,IAAI;YAAG,MAAM;YAAmB,MAAM;QAAiC;QACzE;YAAE,IAAI;YAAG,MAAM;YAAO,MAAM;QAAqB;QACjD;YAAE,IAAI;YAAG,MAAM;YAAgB,MAAM;QAA8B;QACnE;YAAE,IAAI;YAAG,MAAM;YAAgB,MAAM;QAA8B;QACnE;YAAE,IAAI;YAAI,MAAM;YAAS,MAAM;QAAuB;KACvD;IAED,qBACE,0JAAC;;;;;oBAIkD,SAAS,MAAM;oBAMb,SAAS,MAAM;oBA8B1C;oBAEE,SAAS,MAAM,GAAG;oBAKhB,SAAS,MAAM,GAAG;;;mBA/ChC,CAAC,0CAA0C,EAAE,WAAW;;;;;oBAIrB,SAAS,MAAM;oBAMb,SAAS,MAAM;oBA8B1C;oBAEE,SAAS,MAAM,GAAG;oBAKhB,SAAS,MAAM,GAAG;;6GA3CG,SAAS,MAAM,6GAMb,SAAS,MAAM,ygBAgCxC,SAAS,MAAM,GAAG,0DAFpB,wGAOI,SAAS,MAAM,GAAG;;0BAqB9C,0JAAC;;;;;4BAhEgD,SAAS,MAAM;4BAMb,SAAS,MAAM;4BA8B1C;4BAEE,SAAS,MAAM,GAAG;4BAKhB,SAAS,MAAM,GAAG;;;2BAqB/B;0BACb,cAAA,0JAAC;;;;;gCAjE8C,SAAS,MAAM;gCAMb,SAAS,MAAM;gCA8B1C;gCAEE,SAAS,MAAM,GAAG;gCAKhB,SAAS,MAAM,GAAG;;;+BAsB7B;;wBAEZ,SAAS,GAAG,CAAC,CAAC,wBACb,0JAAC;;;;;4CApE0C,SAAS,MAAM;4CAMb,SAAS,MAAM;4CA8B1C;4CAEE,SAAS,MAAM,GAAG;4CAKhB,SAAS,MAAM,GAAG;;;2CAyBR;0CAC9B,cAAA,0JAAC,yHAAA,CAAA,UAAK;oCACJ,KAAK,QAAQ,IAAI;oCACjB,KAAK,QAAQ,IAAI;oCACjB,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;+BANJ,QAAQ,EAAE;;;;;wBAWrB,SAAS,GAAG,CAAC,CAAC,wBACb,0JAAC;;;;;4CAhF0C,SAAS,MAAM;4CAMb,SAAS,MAAM;4CA8B1C;4CAEE,SAAS,MAAM,GAAG;4CAKhB,SAAS,MAAM,GAAG;;;2CAqCC;0CACvC,cAAA,0JAAC,yHAAA,CAAA,UAAK;oCACJ,KAAK,QAAQ,IAAI;oCACjB,KAAK,QAAQ,IAAI;oCACjB,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;+BANJ,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;AAczC;KArHM;uCAuHS", "debugId": null}}, {"offset": {"line": 8546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\nexport { default as HeroSection } from './HeroSection';\r\nexport { default as ReputationManagementSection } from './ReputationManagementSection';\r\nexport { default as DigitalStrategySliderSection } from './DigitalStrategySliderSection';\r\nexport { default as ClientTestimonialsSection } from './ClientTestimonialsSection';\r\nexport { default as WhyChooseVesaSection } from './WhyChooseVesaSection';\r\nexport { default as HorizontalScrollSection } from './HorizontalScrollSection';\r\nexport { default as SchedulingSection } from './SchedulingSection';\r\nexport { default as DigitalMarketingServicesSection } from './DigitalMarketingServicesSection';\r\nexport { default as PlatformLogosSection } from './PlatformLogosSection';\r\nexport { default as DigitalStrategyArticlesSection } from './DigitalStrategyArticlesSection';\r\nexport { default as ContactCTASection } from './ContactCTASection';\r\nexport { default as PartnerCarousel } from './PartnerCarousel';"], "names": [], "mappings": "AAAA,2BAA2B;;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 8721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/seo/HomepageSchema.tsx"], "sourcesContent": ["import React from 'react';\nimport Head from 'next/head';\n\nconst HomepageSchema: React.FC = () => {\n  // Complete Organization Schema with all services\n  const organizationSchema = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Organization\",\n    \"name\": \"Vesa Solutions\",\n    \"alternateName\": [\"VesaSolutions\", \"VESA Solutions\"],\n    \"url\": \"https://vesasolutions.com\",\n    \"logo\": {\n      \"@type\": \"ImageObject\",\n      \"url\": \"https://vesasolutions.com/VesaLogo.svg\",\n      \"width\": \"200\",\n      \"height\": \"80\"\n    },\n    \"image\": {\n      \"@type\": \"ImageObject\",\n      \"url\": \"https://vesasolutions.com/VesaLogo.svg\",\n      \"width\": \"200\",\n      \"height\": \"80\"\n    },\n    \"description\": \"Full-service digital marketing agency helping businesses attract, impress, and convert more leads online since 2015. Specializing in SEO, web development, and digital marketing services.\",\n    \"foundingDate\": \"2015\",\n    \"email\": \"<EMAIL>\",\n    \"telephone\": [\"+***********\", \"+355694046408\"],\n    \"faxNumber\": null,\n    \"address\": {\n      \"@type\": \"PostalAddress\",\n      \"streetAddress\": \"Bulevardi Dyrrah, Pallati 394, Kati 4-t\",\n      \"addressLocality\": \"Durrës\",\n      \"addressRegion\": \"Durrës County\",\n      \"postalCode\": \"2001\",\n      \"addressCountry\": \"AL\"\n    },\n\n    \"contactPoint\": [\n      {\n        \"@type\": \"ContactPoint\",\n        \"telephone\": \"+***********\",\n        \"contactType\": \"customer service\",\n        \"areaServed\": \"US\",\n        \"availableLanguage\": \"English\"\n      },\n      {\n        \"@type\": \"ContactPoint\", \n        \"telephone\": \"+355694046408\",\n        \"contactType\": \"customer service\",\n        \"areaServed\": \"AL\",\n        \"availableLanguage\": [\"English\", \"Albanian\"]\n      },\n      {\n        \"@type\": \"ContactPoint\",\n        \"email\": \"<EMAIL>\",\n        \"contactType\": \"customer service\",\n        \"areaServed\": \"Worldwide\"\n      }\n    ],\n    \"sameAs\": [\n      \"https://www.facebook.com/vesasolutions\",\n      \"https://www.linkedin.com/company/vesasolutions\",\n      \"https://twitter.com/vesasolutions\",\n      \"https://www.instagram.com/vesasolutions\"\n    ],\n    \"serviceArea\": {\n      \"@type\": \"Place\",\n      \"name\": \"Worldwide\"\n    },\n    \"knowsAbout\": [\n      \"Search Engine Optimization\",\n      \"Web Development\", \n      \"Digital Marketing\",\n      \"PPC Advertising\",\n      \"Social Media Marketing\",\n      \"Email Marketing\",\n      \"Content Marketing\",\n      \"Local SEO\",\n      \"Technical SEO\",\n      \"E-commerce Development\",\n      \"Website Design\",\n      \"Branding\"\n    ],\n    \"makesOffer\": [\n      {\n        \"@type\": \"Offer\",\n        \"itemOffered\": {\n          \"@type\": \"Service\",\n          \"name\": \"SEO Services\",\n          \"description\": \"Comprehensive search engine optimization services to improve organic rankings and traffic.\"\n        },\n        \"areaServed\": \"Worldwide\"\n      },\n      {\n        \"@type\": \"Offer\", \n        \"itemOffered\": {\n          \"@type\": \"Service\",\n          \"name\": \"Web Development Services\",\n          \"description\": \"Custom website development, e-commerce solutions, and web applications.\"\n        },\n        \"areaServed\": \"Worldwide\"\n      },\n      {\n        \"@type\": \"Offer\",\n        \"itemOffered\": {\n          \"@type\": \"Service\", \n          \"name\": \"Digital Marketing Services\",\n          \"description\": \"Complete digital marketing solutions including PPC, social media, and email marketing.\"\n        },\n        \"areaServed\": \"Worldwide\"\n      }\n    ]\n  };\n\n  // Local Business Schema\n  const localBusinessSchema = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"LocalBusiness\",\n    \"name\": \"Vesa Solutions\",\n    \"image\": \"https://vesasolutions.com/VesaLogo.svg\",\n    \"url\": \"https://vesasolutions.com\",\n    \"telephone\": \"+***********\",\n    \"email\": \"<EMAIL>\",\n    \"address\": {\n      \"@type\": \"PostalAddress\",\n      \"streetAddress\": \"Bulevardi Dyrrah, Pallati 394, Kati 4-t\",\n      \"addressLocality\": \"Durrës\",\n      \"addressRegion\": \"Durrës County\", \n      \"postalCode\": \"2001\",\n      \"addressCountry\": \"AL\"\n    },\n    \"geo\": {\n      \"@type\": \"GeoCoordinates\",\n      \"latitude\": \"41.3317\",\n      \"longitude\": \"19.4414\"\n    },\n    \"openingHoursSpecification\": {\n      \"@type\": \"OpeningHoursSpecification\",\n      \"dayOfWeek\": [\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\", \"Sunday\"],\n      \"opens\": \"00:00\",\n      \"closes\": \"23:59\"\n    },\n    \"priceRange\": \"$$\",\n    \"aggregateRating\": {\n      \"@type\": \"AggregateRating\",\n      \"ratingValue\": \"4.9\",\n      \"reviewCount\": \"127\",\n      \"bestRating\": \"5\",\n      \"worstRating\": \"1\"\n    },\n    \"review\": [\n      {\n        \"@type\": \"Review\",\n        \"author\": {\n          \"@type\": \"Person\",\n          \"name\": \"Elira Kokona\"\n        },\n        \"reviewRating\": {\n          \"@type\": \"Rating\",\n          \"ratingValue\": \"5\",\n          \"bestRating\": \"5\"\n        },\n        \"reviewBody\": \"Working with Vesa Solutions has been a game-changer for our digital presence. They expertly built our website from the ground up, ensuring it's not only visually appealing but also optimized for performance and SEO. Thanks to their comprehensive approach, we've seen significant improvements in our online visibility and search engine rankings.\"\n      },\n      {\n        \"@type\": \"Review\",\n        \"author\": {\n          \"@type\": \"Person\",\n          \"name\": \"Sonila Mustafa\"\n        },\n        \"reviewRating\": {\n          \"@type\": \"Rating\",\n          \"ratingValue\": \"5\",\n          \"bestRating\": \"5\"\n        },\n        \"reviewBody\": \"Vesa Solutions completely revolutionized our online presence. Their strategic approach to website design and digital marketing has helped us stand out in a competitive market. The team took time to understand our business needs and delivered solutions that exceeded our expectations.\"\n      },\n      {\n        \"@type\": \"Review\",\n        \"author\": {\n          \"@type\": \"Organization\",\n          \"name\": \"Durana Tech Park\"\n        },\n        \"reviewRating\": {\n          \"@type\": \"Rating\",\n          \"ratingValue\": \"5\",\n          \"bestRating\": \"5\"\n        },\n        \"reviewBody\": \"As the founder of Durana Tech Park, I knew we needed a digital identity that could match the ambition of Albania's first dedicated technology and innovation ecosystem. Vesa Solutions rose to the occasion. They delivered a sleek, scalable, and informative website that showcases our infrastructure, investor incentives, and global opportunities.\"\n      },\n      {\n        \"@type\": \"Review\",\n        \"author\": {\n          \"@type\": \"Person\",\n          \"name\": \"Florian Duci\"\n        },\n        \"reviewRating\": {\n          \"@type\": \"Rating\",\n          \"ratingValue\": \"5\",\n          \"bestRating\": \"5\"\n        },\n        \"reviewBody\": \"Vesa Solutions understood the unique challenges of marketing an events business online. They created a website that beautifully showcases our past events while making it easy for potential clients to inquire about our services. Their ongoing SEO and social media strategy has helped us reach new clients and build our brand reputation.\"\n      }\n    ]\n  };\n\n  // Website Schema\n  const websiteSchema = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"WebSite\",\n    \"name\": \"Vesa Solutions\",\n    \"alternateName\": \"VesaSolutions\",\n    \"url\": \"https://vesasolutions.com\",\n    \"description\": \"Full-service digital marketing agency helping businesses attract, impress, and convert more leads online since 2015.\",\n    \"publisher\": {\n      \"@type\": \"Organization\",\n      \"name\": \"Vesa Solutions\",\n      \"logo\": {\n        \"@type\": \"ImageObject\",\n        \"url\": \"https://vesasolutions.com/VesaLogo.svg\"\n      }\n    },\n    \"potentialAction\": {\n      \"@type\": \"SearchAction\",\n      \"target\": {\n        \"@type\": \"EntryPoint\",\n        \"urlTemplate\": \"https://vesasolutions.com/search?q={search_term_string}\"\n      },\n      \"query-input\": \"required name=search_term_string\"\n    },\n    \"mainEntity\": {\n      \"@type\": \"Organization\",\n      \"name\": \"Vesa Solutions\"\n    }\n  };\n\n  // Service Schema (cleaner version without warnings)\n  const serviceSchema = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Service\",\n    \"name\": \"Digital Marketing Services\",\n    \"url\": \"https://vesasolutions.com\",\n    \"description\": \"Professional digital marketing services including SEO, web development, PPC advertising, social media marketing, and branding solutions.\",\n    \"provider\": {\n      \"@type\": \"Organization\",\n      \"name\": \"Vesa Solutions\",\n      \"url\": \"https://vesasolutions.com\"\n    },\n    \"areaServed\": {\n      \"@type\": \"Place\",\n      \"name\": \"Worldwide\"\n    },\n    \"serviceType\": \"Digital Marketing\"\n  };\n\n  return (\n    <Head>\n      {/* Organization Schema */}\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify(organizationSchema)\n        }}\n      />\n\n      {/* Local Business Schema */}\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify(localBusinessSchema)\n        }}\n      />\n\n      {/* Website Schema */}\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify(websiteSchema)\n        }}\n      />\n\n      {/* Service Schema */}\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify(serviceSchema)\n        }}\n      />\n    </Head>\n  );\n};\n\nexport default HomepageSchema;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,iBAA2B;IAC/B,iDAAiD;IACjD,MAAM,qBAAqB;QACzB,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,iBAAiB;YAAC;YAAiB;SAAiB;QACpD,OAAO;QACP,QAAQ;YACN,SAAS;YACT,OAAO;YACP,SAAS;YACT,UAAU;QACZ;QACA,SAAS;YACP,SAAS;YACT,OAAO;YACP,SAAS;YACT,UAAU;QACZ;QACA,eAAe;QACf,gBAAgB;QAChB,SAAS;QACT,aAAa;YAAC;YAAgB;SAAgB;QAC9C,aAAa;QACb,WAAW;YACT,SAAS;YACT,iBAAiB;YACjB,mBAAmB;YACnB,iBAAiB;YACjB,cAAc;YACd,kBAAkB;QACpB;QAEA,gBAAgB;YACd;gBACE,SAAS;gBACT,aAAa;gBACb,eAAe;gBACf,cAAc;gBACd,qBAAqB;YACvB;YACA;gBACE,SAAS;gBACT,aAAa;gBACb,eAAe;gBACf,cAAc;gBACd,qBAAqB;oBAAC;oBAAW;iBAAW;YAC9C;YACA;gBACE,SAAS;gBACT,SAAS;gBACT,eAAe;gBACf,cAAc;YAChB;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,eAAe;YACb,SAAS;YACT,QAAQ;QACV;QACA,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;gBACE,SAAS;gBACT,eAAe;oBACb,SAAS;oBACT,QAAQ;oBACR,eAAe;gBACjB;gBACA,cAAc;YAChB;YACA;gBACE,SAAS;gBACT,eAAe;oBACb,SAAS;oBACT,QAAQ;oBACR,eAAe;gBACjB;gBACA,cAAc;YAChB;YACA;gBACE,SAAS;gBACT,eAAe;oBACb,SAAS;oBACT,QAAQ;oBACR,eAAe;gBACjB;gBACA,cAAc;YAChB;SACD;IACH;IAEA,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,SAAS;QACT,OAAO;QACP,aAAa;QACb,SAAS;QACT,WAAW;YACT,SAAS;YACT,iBAAiB;YACjB,mBAAmB;YACnB,iBAAiB;YACjB,cAAc;YACd,kBAAkB;QACpB;QACA,OAAO;YACL,SAAS;YACT,YAAY;YACZ,aAAa;QACf;QACA,6BAA6B;YAC3B,SAAS;YACT,aAAa;gBAAC;gBAAU;gBAAW;gBAAa;gBAAY;gBAAU;gBAAY;aAAS;YAC3F,SAAS;YACT,UAAU;QACZ;QACA,cAAc;QACd,mBAAmB;YACjB,SAAS;YACT,eAAe;YACf,eAAe;YACf,cAAc;YACd,eAAe;QACjB;QACA,UAAU;YACR;gBACE,SAAS;gBACT,UAAU;oBACR,SAAS;oBACT,QAAQ;gBACV;gBACA,gBAAgB;oBACd,SAAS;oBACT,eAAe;oBACf,cAAc;gBAChB;gBACA,cAAc;YAChB;YACA;gBACE,SAAS;gBACT,UAAU;oBACR,SAAS;oBACT,QAAQ;gBACV;gBACA,gBAAgB;oBACd,SAAS;oBACT,eAAe;oBACf,cAAc;gBAChB;gBACA,cAAc;YAChB;YACA;gBACE,SAAS;gBACT,UAAU;oBACR,SAAS;oBACT,QAAQ;gBACV;gBACA,gBAAgB;oBACd,SAAS;oBACT,eAAe;oBACf,cAAc;gBAChB;gBACA,cAAc;YAChB;YACA;gBACE,SAAS;gBACT,UAAU;oBACR,SAAS;oBACT,QAAQ;gBACV;gBACA,gBAAgB;oBACd,SAAS;oBACT,eAAe;oBACf,cAAc;gBAChB;gBACA,cAAc;YAChB;SACD;IACH;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,iBAAiB;QACjB,OAAO;QACP,eAAe;QACf,aAAa;YACX,SAAS;YACT,QAAQ;YACR,QAAQ;gBACN,SAAS;gBACT,OAAO;YACT;QACF;QACA,mBAAmB;YACjB,SAAS;YACT,UAAU;gBACR,SAAS;gBACT,eAAe;YACjB;YACA,eAAe;QACjB;QACA,cAAc;YACZ,SAAS;YACT,QAAQ;QACV;IACF;IAEA,oDAAoD;IACpD,MAAM,gBAAgB;QACpB,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,OAAO;QACP,eAAe;QACf,YAAY;YACV,SAAS;YACT,QAAQ;YACR,OAAO;QACT;QACA,cAAc;YACZ,SAAS;YACT,QAAQ;QACV;QACA,eAAe;IACjB;IAEA,qBACE,0JAAC,wHAAA,CAAA,UAAI;;0BAEH,0JAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;gBACzB;;;;;;0BAIF,0JAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;gBACzB;;;;;;0BAIF,0JAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;gBACzB;;;;;;0BAIF,0JAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;gBACzB;;;;;;;;;;;;AAIR;KA/RM;uCAiSS", "debugId": null}}, {"offset": {"line": 9054, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/pages/index.tsx"], "sourcesContent": ["// pages/index.tsx\nimport React, { useRef } from 'react';\nimport { GetStaticProps } from 'next';\nimport Head from 'next/head';\nimport Header from '@/components/global/Header';\nimport Footer from '@/components/global/Footer';\nimport {\n  HeroSection,\n  ReputationManagementSection,\n  DigitalStrategySliderSection,\n  ClientTestimonialsSection,\n  WhyChooseVesaSection,\n  HorizontalScrollSection,\n  SchedulingSection,\n  DigitalMarketingServicesSection,\n  PlatformLogosSection,\n  DigitalStrategyArticlesSection,\n  ContactCTASection,\n  PartnerCarousel\n} from '@/components/home';\nimport { getRecentBlogPosts } from '@/lib/sanity-blog';\nimport { BlogListItem } from '@/types/blog';\nimport HomepageSchema from '@/components/seo/HomepageSchema';\n\ninterface HomeProps {\n  blogPosts: BlogListItem[];\n}\n\nconst Home: React.FC<HomeProps> = ({ blogPosts }) => {\n  const footerRef = useRef<HTMLDivElement>(null);\n\n  return (\n    <>\n      <Head>\n        <title>Vesa Solutions - Digital Marketing That Actually Works</title>\n        <meta name=\"description\" content=\"Expert digital marketing services including SEO, web design, social media marketing, and online reputation management. Get more leads, conversions, and sales.\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n      </Head>\n\n      {/* Schema Markup for Homepage */}\n      <HomepageSchema />\n\n      <Header />\n      \n      <main>\n        <HeroSection />\n        <PartnerCarousel />\n        <ReputationManagementSection />\n        <DigitalStrategySliderSection />\n        <ClientTestimonialsSection />\n        <WhyChooseVesaSection />\n        <HorizontalScrollSection />\n        <SchedulingSection />\n        <DigitalMarketingServicesSection />\n        <PlatformLogosSection />\n        <DigitalStrategyArticlesSection blogPosts={blogPosts} />\n        <ContactCTASection />\n      </main>\n\n      <div ref={footerRef}>\n        <Footer />\n      </div>\n    </>\n  );\n};\n\nexport const getStaticProps: GetStaticProps = async () => {\n  try {\n    // Get recent blog posts for the homepage\n    const blogPosts = await getRecentBlogPosts(4);\n\n    return {\n      props: {\n        blogPosts\n      },\n      revalidate: 300 // Revalidate every 5 minutes\n    };\n  } catch (error) {\n    console.error('Error fetching blog posts for homepage:', error);\n    return {\n      props: {\n        blogPosts: []\n      },\n      revalidate: 300\n    };\n  }\n};\n\nexport default Home;"], "names": [], "mappings": "AAAA,kBAAkB;;;;;;AAClB;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;;;;;;;;;AAMA,MAAM,OAA4B,CAAC,EAAE,SAAS,EAAE;;IAC9C,MAAM,YAAY,CAAA,GAAA,0HAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,qBACE;;0BACE,0JAAC,wHAAA,CAAA,UAAI;;kCACH,0JAAC;kCAAM;;;;;;kCACP,0JAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,0JAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,0JAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;;;;;;;0BAIxB,0JAAC,gIAAA,CAAA,UAAc;;;;;0BAEf,0JAAC,2HAAA,CAAA,UAAM;;;;;0BAEP,0JAAC;;kCACC,0JAAC,wKAAA,CAAA,cAAW;;;;;kCACZ,0JAAC,gLAAA,CAAA,kBAAe;;;;;kCAChB,0JAAC,wMAAA,CAAA,8BAA2B;;;;;kCAC5B,0JAAC,0MAAA,CAAA,+BAA4B;;;;;kCAC7B,0JAAC,oMAAA,CAAA,4BAAyB;;;;;kCAC1B,0JAAC,0LAAA,CAAA,uBAAoB;;;;;kCACrB,0JAAC,gMAAA,CAAA,0BAAuB;;;;;kCACxB,0JAAC,oLAAA,CAAA,oBAAiB;;;;;kCAClB,0JAAC,gNAAA,CAAA,kCAA+B;;;;;kCAChC,0JAAC,0LAAA,CAAA,uBAAoB;;;;;kCACrB,0JAAC,8MAAA,CAAA,iCAA8B;wBAAC,WAAW;;;;;;kCAC3C,0JAAC,oLAAA,CAAA,oBAAiB;;;;;;;;;;;0BAGpB,0JAAC;gBAAI,KAAK;0BACR,cAAA,0JAAC,2HAAA,CAAA,UAAM;;;;;;;;;;;;AAIf;GArCM;KAAA;;uCA6DS", "debugId": null}}, {"offset": {"line": 9239, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/\";\n\n/// <reference types=\"next/client\" />\r\n\r\n// inserted by rust code\r\ndeclare const PAGE_PATH: string\r\n\r\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\r\n;(window.__NEXT_P = window.__NEXT_P || []).push([\r\n  PAGE_PATH,\r\n  () => {\r\n    return require('PAGE')\r\n  },\r\n])\r\n// @ts-expect-error module.hot exists\r\nif (module.hot) {\r\n  // @ts-expect-error module.hot exists\r\n  module.hot.dispose(function () {\r\n    window.__NEXT_P.push([PAGE_PATH])\r\n  })\r\n}\r\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}