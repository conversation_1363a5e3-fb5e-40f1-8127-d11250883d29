// pages/api/schedule-consultation.ts - Schedule consultation API endpoint
import { NextApiRequest, NextApiResponse } from 'next';
import nodemailer from 'nodemailer';

interface ScheduleData {
  name: string;
  email: string;
  date: string;
}

// Helper function to get error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) return error.message;
  return String(error);
}

// Helper function to validate email
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Helper function to create email transporter
function createTransporter() {
  const businessEmail = process.env.BUSINESS_EMAIL;
  const appPass = process.env.APP_PASS;

  if (!businessEmail || !appPass) {
    throw new Error('Email configuration missing. Please check BUSINESS_EMAIL and APP_PASS environment variables.');
  }

  return nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: businessEmail,
      pass: appPass
    }
  });
}

// Helper function to send notification emails to team
async function sendTeamNotification(scheduleData: ScheduleData, transporter: nodemailer.Transporter) {
  const { name, email, date } = scheduleData;
  
  const teamEmails = ['<EMAIL>', '<EMAIL>'];
  
  const subject = `New Consultation Scheduled - ${name}`;
  
  const htmlContent = `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 650px; margin: 0 auto; padding: 20px; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
      <div style="background-color: #ffffff; padding: 40px; border-radius: 16px; box-shadow: 0 10px 25px rgba(0,0,0,0.1); border: 1px solid #e2e8f0;">

        <!-- Header -->
        <div style="text-align: center; margin-bottom: 35px; padding-bottom: 25px; border-bottom: 2px solid #f1f5f9;">
          <div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 15px 25px; border-radius: 50px; display: inline-block; margin-bottom: 20px;">
            <h1 style="margin: 0; font-size: 20px; font-weight: 600;">🎯 New Consultation Scheduled</h1>
          </div>
          <p style="color: #64748b; margin: 0; font-size: 16px;">A new client has scheduled a consultation through Vesa Solutions</p>
        </div>

        <!-- Client Information -->
        <div style="background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%); padding: 25px; border-radius: 12px; border-left: 5px solid #3b82f6; margin-bottom: 25px;">
          <h2 style="color: #1e40af; margin: 0 0 20px 0; font-size: 18px; display: flex; align-items: center;">
            👤 Client Information
          </h2>
          <div style="display: grid; gap: 12px;">
            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #bfdbfe;">
              <strong style="color: #1e40af;">Name:</strong>
              <span style="color: #374151; margin-left: 10px; font-size: 16px;">${name}</span>
            </div>
            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #bfdbfe;">
              <strong style="color: #1e40af;">Email:</strong>
              <a href="mailto:${email}" style="color: #3b82f6; margin-left: 10px; text-decoration: none; font-size: 16px;">${email}</a>
            </div>
          </div>
        </div>

        <!-- Appointment Details -->
        <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 25px; border-radius: 12px; border-left: 5px solid #10b981; margin-bottom: 25px;">
          <h2 style="color: #065f46; margin: 0 0 20px 0; font-size: 18px; display: flex; align-items: center;">
            📅 Appointment Details
          </h2>
          <div style="display: grid; gap: 12px;">
            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #bbf7d0;">
              <strong style="color: #065f46;">📅 Date:</strong>
              <span style="color: #374151; margin-left: 10px; font-size: 16px;">${date}</span>
            </div>
            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #bbf7d0;">
              <strong style="color: #065f46;">⏱️ Duration:</strong>
              <span style="color: #374151; margin-left: 10px; font-size: 16px;">30 minutes consultation</span>
            </div>
            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #bbf7d0;">
              <strong style="color: #065f46;">📝 Note:</strong>
              <span style="color: #374151; margin-left: 10px; font-size: 16px;">Please contact the client to schedule a specific time</span>
            </div>
          </div>
        </div>



        <!-- Footer -->
        <div style="text-align: center; margin-top: 35px; padding-top: 25px; border-top: 2px solid #f1f5f9;">
          <div style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); padding: 20px; border-radius: 12px; border: 1px solid #e2e8f0;">
            <p style="color: #64748b; font-size: 14px; margin: 0; line-height: 1.6;">
              🌐 This consultation was scheduled through the <strong style="color: #3b82f6;">Vesa Solutions</strong> website.<br>
              📞 For urgent matters, contact us at <a href="mailto:<EMAIL>" style="color: #3b82f6; text-decoration: none;"><EMAIL></a>
            </p>
          </div>
        </div>
      </div>
    </div>
  `;

  // Send to both team emails
  for (const teamEmail of teamEmails) {
    await transporter.sendMail({
      from: `"Vesa Solutions" <${process.env.BUSINESS_EMAIL}>`,
      to: teamEmail,
      subject: subject,
      html: htmlContent
    });
  }
}

// Helper function to send confirmation email to client
async function sendClientConfirmation(scheduleData: ScheduleData, transporter: nodemailer.Transporter) {
  const { name, email, date } = scheduleData;

  const subject = 'Consultation Scheduled - Vesa Solutions';
  
  const htmlContent = `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 650px; margin: 0 auto; padding: 15px; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
      <style>
        @media only screen and (max-width: 600px) {
          .container { padding: 10px !important; max-width: 100% !important; }
          .content { padding: 20px !important; }
          .header-badge { padding: 10px 15px !important; font-size: 16px !important; }
          .section { padding: 15px !important; margin-bottom: 15px !important; }
          .card { padding: 10px !important; margin-bottom: 8px !important; }
          .text-large { font-size: 16px !important; }
          .text-medium { font-size: 14px !important; }
          .text-small { font-size: 12px !important; }
          .grid-item { display: block !important; margin-bottom: 8px !important; }
          .icon { font-size: 16px !important; margin-right: 8px !important; }
        }
      </style>
      <div class="content" style="background-color: #ffffff; padding: 25px; border-radius: 16px; box-shadow: 0 10px 25px rgba(0,0,0,0.1); border: 1px solid #e2e8f0;">

        <!-- Header -->
        <div style="text-align: center; margin-bottom: 25px; padding-bottom: 20px; border-bottom: 2px solid #f1f5f9;">
          <div class="header-badge" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 12px 20px; border-radius: 50px; display: inline-block; margin-bottom: 15px;">
            <h1 class="text-large" style="margin: 0; font-size: 18px; font-weight: 600;">✅ Consultation Confirmed!</h1>
          </div>
          <p class="text-medium" style="color: #64748b; margin: 0; font-size: 14px; line-height: 1.5;">Your appointment with Vesa Solutions has been successfully scheduled</p>
        </div>

        <!-- Personal Greeting -->
        <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); padding: 25px; border-radius: 12px; border-left: 5px solid #f59e0b; margin-bottom: 30px;">
          <p style="color: #92400e; font-size: 18px; margin: 0 0 15px 0; font-weight: 600;">
            Dear ${name},
          </p>
          <p style="color: #374151; line-height: 1.7; margin: 0; font-size: 16px;">
            Thank you for scheduling a consultation with <strong style="color: #3b82f6;">Vesa Solutions</strong>! We're excited to discuss your online reputation management needs and help you build a stronger digital presence.
          </p>
        </div>

        <!-- Appointment Details -->
        <div style="background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%); padding: 25px; border-radius: 12px; border-left: 5px solid #3b82f6; margin-bottom: 30px;">
          <h2 style="color: #1e40af; margin: 0 0 20px 0; font-size: 18px; display: flex; align-items: center;">
            📅 Your Appointment Details
          </h2>
          <div style="display: grid; gap: 12px;">
            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #bfdbfe; display: flex; align-items: center;">
              <span style="font-size: 20px; margin-right: 12px;">📅</span>
              <div>
                <strong style="color: #1e40af;">Date:</strong>
                <span style="color: #374151; margin-left: 8px; font-size: 16px;">${date}</span>
              </div>
            </div>
            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #bfdbfe; display: flex; align-items: center;">
              <span style="font-size: 20px; margin-right: 12px;">📝</span>
              <div>
                <strong style="color: #1e40af;">Next Step:</strong>
                <span style="color: #374151; margin-left: 8px; font-size: 16px;">We will contact you to schedule a specific time</span>
              </div>
            </div>
            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #bfdbfe; display: flex; align-items: center;">
              <span style="font-size: 20px; margin-right: 12px;">⏱️</span>
              <div>
                <strong style="color: #1e40af;">Duration:</strong>
                <span style="color: #374151; margin-left: 8px; font-size: 16px;">30 minutes</span>
              </div>
            </div>
            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #bfdbfe; display: flex; align-items: center;">
              <span style="font-size: 20px; margin-right: 12px;">📧</span>
              <div>
                <strong style="color: #1e40af;">Contact:</strong>
                <a href="mailto:<EMAIL>" style="color: #3b82f6; margin-left: 8px; text-decoration: none; font-size: 16px;"><EMAIL></a>
              </div>
            </div>
          </div>
        </div>

        <!-- What to Expect -->
        <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 25px; border-radius: 12px; border-left: 5px solid #10b981; margin-bottom: 30px;">
          <h2 style="color: #065f46; margin: 0 0 20px 0; font-size: 18px; display: flex; align-items: center;">
            🎯 What to Expect During Your Consultation
          </h2>
          <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #bbf7d0;">
            <ul style="margin: 0; padding-left: 0; list-style: none; color: #374151;">
              <li style="margin: 12px 0; padding: 12px; background: #f0fdf4; border-radius: 6px; border-left: 3px solid #10b981; display: flex; align-items: flex-start;">
                <span style="font-size: 18px; margin-right: 12px; margin-top: 2px;">🔍</span>
                <span style="line-height: 1.6;">Comprehensive analysis of your current online reputation</span>
              </li>
              <li style="margin: 12px 0; padding: 12px; background: #f0fdf4; border-radius: 6px; border-left: 3px solid #10b981; display: flex; align-items: flex-start;">
                <span style="font-size: 18px; margin-right: 12px; margin-top: 2px;">📋</span>
                <span style="line-height: 1.6;">Customized strategy recommendations tailored to your business</span>
              </li>
              <li style="margin: 12px 0; padding: 12px; background: #f0fdf4; border-radius: 6px; border-left: 3px solid #10b981; display: flex; align-items: flex-start;">
                <span style="font-size: 18px; margin-right: 12px; margin-top: 2px;">💬</span>
                <span style="line-height: 1.6;">Discussion of our services and how we can help you succeed</span>
              </li>
              <li style="margin: 12px 0; padding: 12px; background: #f0fdf4; border-radius: 6px; border-left: 3px solid #10b981; display: flex; align-items: flex-start;">
                <span style="font-size: 18px; margin-right: 12px; margin-top: 2px;">❓</span>
                <span style="line-height: 1.6;">Q&A session to address all your questions and concerns</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- Next Steps -->
        <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); padding: 25px; border-radius: 12px; border-left: 5px solid #f59e0b; margin-bottom: 30px;">
          <p style="color: #374151; line-height: 1.7; margin: 0; font-size: 16px; text-align: center;">
            📅 Our team will send you a <strong>calendar invite</strong> shortly with the meeting link.<br><br>
            📞 If you have any questions or need to reschedule, please contact us at
            <a href="mailto:<EMAIL>" style="color: #3b82f6; text-decoration: none; font-weight: 600;"><EMAIL></a>
          </p>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 35px; padding-top: 25px; border-top: 2px solid #f1f5f9;">
          <div style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); padding: 25px; border-radius: 12px; border: 1px solid #e2e8f0;">
            <p style="color: #64748b; font-size: 16px; margin: 0 0 15px 0; line-height: 1.6;">
              🚀 Looking forward to speaking with you and helping your business thrive online!
            </p>
            <p style="color: #3b82f6; font-size: 18px; font-weight: 600; margin: 0;">
              The Vesa Solutions Team
            </p>
          </div>
        </div>
      </div>
    </div>
  `;

  await transporter.sendMail({
    from: `"Vesa Solutions" <<EMAIL>>`,
    to: email,
    subject: subject,
    html: htmlContent
  });
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Add CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  console.log('Consultation scheduling request received');

  try {
    const { name, email, date }: ScheduleData = req.body;

    // Validate required fields
    if (!name || !email || !date) {
      return res.status(400).json({
        message: 'All fields are required',
        success: false
      });
    }

    // Validate email
    if (!isValidEmail(email)) {
      return res.status(400).json({ 
        message: 'Valid email address is required',
        success: false 
      });
    }

    console.log('Processing consultation scheduling for:', { name, email, date });

    // Create email transporter
    const transporter = createTransporter();

    // Prepare schedule data
    const scheduleData: ScheduleData = {
      name,
      email,
      date
    };

    // Send emails
    await Promise.all([
      sendTeamNotification(scheduleData, transporter),
      sendClientConfirmation(scheduleData, transporter)
    ]);

    console.log('Consultation scheduled successfully for:', email);

    res.status(200).json({ 
      message: 'Consultation scheduled successfully! You will receive a confirmation email shortly.',
      success: true 
    });

  } catch (error) {
    const errorMessage = getErrorMessage(error);
    console.error('Consultation scheduling error:', errorMessage);
    
    res.status(500).json({ 
      message: 'Failed to schedule consultation. Please try again or contact us directly.',
      success: false,
      debug: process.env.NODE_ENV === 'development' ? errorMessage : undefined
    });
  }
}
