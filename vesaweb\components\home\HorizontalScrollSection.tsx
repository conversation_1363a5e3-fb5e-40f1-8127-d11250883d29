// components/home/<USER>
import React, { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
// ChevronRight import removed as buttons were removed
import { WhyChooseStep } from '@/types';

interface HorizontalScrollSectionProps {
  className?: string;
}

const HorizontalScrollSection: React.FC<HorizontalScrollSectionProps> = ({ className = '' }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sectionsRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const [isMobile, setIsMobile] = useState<boolean>(false);

  // Horizontal scroll steps data
  const whyChooseSteps: WhyChooseStep[] = [
    {
      id: "01",
      title: "We Have Proven Results",
      description: "Why would you work with a digital marketing agency that doesn't provide you with the results you are looking for? Stop throwing away your hard-earned money on a strategy that isn't converting. <PERSON><PERSON><PERSON> takes the time to listen to your business' goals and needs, and then builds a custom strategy to reach these goals.",
      image: "/homepage/proven results.jpg"
    },
    {
      id: "02",
      title: "We Are Honest & Ethical",
      description: "At Vesa, we believe in honesty, integrity, and respecting our clients. We don't believe in 'selling' a service that you don't need or applying a one-size-fits-all approach to each client. We dedicate our time to achieving higher rankings, increased traffic and conversions, and a higher ROI for our clients.",
      image: "/homepage/honest and ethical.avif"
    },
    {
      id: "03",
      title: "We Know Digital Marketing",
      description: "Working with a digital marketing company that actually knows digital marketing seems obvious, but unfortunately, many firms fail to deliver positive results. Our strategies are proven to work. In fact, the majority of our clients pay for their entire website in the first month from new business leads as a direct result of our online marketing and SEO efforts.",
      image: "/homepage/digital marketing.jpg"
    },
    {
      id: "04",
      title: "We Put Clients First",
      description: "Maintaining a client-first mentality is a priority at Vesa, helping our clients grow and follow through on business promises. What makes Vesa different from other agencies is that we show up, we're present and respectful, and we always go above and beyond for our clients to deliver exceptional results.",
      image: "/homepage/client first.avif"
    }
  ];

  // Check for mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  // Function to handle manual navigation in horizontal scroll
  const scrollToStep = (): void => {
    if (sectionsRef.current && containerRef.current) {
      window.scrollTo({
        top: containerRef.current.offsetTop,
        behavior: 'smooth'
      });
    }
  };

  // GSAP Horizontal Scroll Animation - Desktop Only
  useEffect(() => {
    // Only run on client side and desktop
    if (typeof window === 'undefined' || isMobile) {
      // If switching to mobile, kill all ScrollTriggers to prevent conflicts
      if (typeof window !== 'undefined') {
        import('gsap/ScrollTrigger').then(({ ScrollTrigger }) => {
          ScrollTrigger.killAll();
        }).catch(() => {});
      }
      return;
    }

    const container = containerRef.current;
    const sections = sectionsRef.current;
    const progressBar = progressRef.current;
    
    if (!container || !sections || !progressBar) return;

    let ctx: { revert: () => void } | null = null;
    let isDestroyed = false;

    // Dynamic import of GSAP to avoid SSR issues
    const loadGSAP = async () => {
      try {
        const { gsap } = await import('gsap');
        const { ScrollTrigger } = await import('gsap/ScrollTrigger');
        
        // Check if component is still mounted
        if (isDestroyed) return;
        
        // Register ScrollTrigger plugin
        gsap.registerPlugin(ScrollTrigger);
        
        // Kill any existing ScrollTriggers first
        ScrollTrigger.killAll();
        
        // Create a ScrollTrigger context for cleanup
        ctx = gsap.context(() => {
          // Double check elements still exist
          if (!container || !sections || !progressBar || isDestroyed) return;
          
          // Calculate the total width needed for horizontal scroll
          const scrollWidth = sections.scrollWidth - container.offsetWidth;

          // Create the horizontal scroll animation
          const scrollTween = gsap.to(sections, {
            x: -scrollWidth,
            ease: "none",
          });

          // Animate progress bar
          gsap.to(progressBar, {
            scaleX: 1,
            ease: "none",
            scrollTrigger: {
              trigger: container,
              start: "top top",
              end: () => `+=${scrollWidth}`,
              scrub: 1,
            }
          });

          // Create ScrollTrigger
          ScrollTrigger.create({
            trigger: container,
            start: "top top",
            end: () => `+=${scrollWidth}`,
            scrub: 1,
            pin: true,
            animation: scrollTween,
            invalidateOnRefresh: true
          });

          // Animate cards
          const cards = gsap.utils.toArray('.step-card');
          cards.forEach((card) => {
            if (isDestroyed) return;
            // Card entrance animation
            gsap.fromTo(card as HTMLElement, 
              { 
                opacity: 0, 
                y: 50,
              },
              {
                opacity: 1,
                y: 0,
                duration: 0.8,
                ease: "power2.out",
                scrollTrigger: {
                  trigger: card as HTMLElement,
                  start: "left 70%",
                  end: "left 30%",
                  scrub: 1,
                  horizontal: true,
                  containerAnimation: scrollTween,
                }
              }
            );
          });
        }, container);

        // Handle resize to maintain responsiveness
        const handleResize = () => {
          if (!isDestroyed) {
            ScrollTrigger.refresh();
          }
        };
        
        window.addEventListener('resize', handleResize);
        
        // Clean up function
        return () => {
          window.removeEventListener('resize', handleResize);
          if (ctx) {
            ctx.revert();
          }
          ScrollTrigger.killAll();
        };
      } catch (error) {
        console.warn('GSAP could not be loaded:', error);
        return () => {};
      }
    };

    // Load GSAP and set up animations
    const cleanupPromise = loadGSAP();
    
    // Return cleanup function
    return () => {
      isDestroyed = true;
      if (ctx) {
        ctx.revert();
        ctx = null;
      }
      cleanupPromise.then(cleanupFn => {
        if (cleanupFn && typeof cleanupFn === 'function') {
          cleanupFn();
        }
      }).catch(() => {});
      
      // Additional cleanup for ScrollTrigger
      if (typeof window !== 'undefined') {
        import('gsap/ScrollTrigger').then(({ ScrollTrigger }) => {
          ScrollTrigger.killAll();
        }).catch(() => {});
      }
    };
  }, [isMobile]);

  // Mobile version - natural vertical scrolling
  if (isMobile) {
    return (
      <section className={`w-full bg-white ${className}`}>
        {/* Section Header */}
        <div className="text-center py-8 sm:py-12 px-4">
          <p className="text-blue-700 font-medium text-base sm:text-lg mb-3">WHY CHOOSE US</p>
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-800">
            FOUR REASONS VESA IS YOUR IDEAL PARTNER
          </h2>
        </div>

        {/* Vertical Stack of Steps */}
        <div className="space-y-8 sm:space-y-12 px-4 pb-12">
          {whyChooseSteps.map((step, index) => (
            <div key={step.id} className="max-w-2xl mx-auto">
              {/* Step Card */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                {/* Visual Section */}
                <div className="relative">
                  {/* Large Number in Front - Right Side */}
                  <div className="absolute -top-8 -right-4 text-6xl sm:text-8xl font-bold text-blue-500/20 select-none z-10">
                    {step.id}
                  </div>

                  <div className="aspect-[4/3] sm:aspect-[16/10]">
                    <Image
                      src={step.image}
                      alt={step.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>

                {/* Content Section */}
                <div className="p-6 sm:p-8">
                  {/* Step Number and Title Line */}
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-blue-600 flex items-center justify-center shadow-lg">
                      <span className="text-lg sm:text-xl font-bold text-white">{step.id}</span>
                    </div>
                    <div className="h-px flex-1 bg-gradient-to-r from-blue-400 to-transparent" />
                  </div>

                  {/* Title */}
                  <h3 className="text-xl sm:text-2xl font-bold text-gray-800 mb-4 leading-tight">
                    {step.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-700 leading-relaxed mb-6 text-sm sm:text-base">
                    {step.description}
                  </p>


                </div>
              </div>

              {/* Progress Indicator */}
              {index < whyChooseSteps.length - 1 && (
                <div className="flex justify-center mt-6">
                  <div className="w-px h-8 bg-gradient-to-b from-blue-300 to-transparent"></div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Bottom Summary */}
        <div className="bg-blue-50 py-8 px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h3 className="text-lg sm:text-xl font-bold text-gray-800 mb-3">
              Ready to Get Started?
            </h3>
            <p className="text-gray-700 text-sm sm:text-base">
              Experience the difference with Vesa&apos;s proven approach to digital marketing success.
            </p>
          </div>
        </div>
      </section>
    );
  }

  // Desktop version - horizontal scroll (unchanged)
  return (
    <section className={`w-full bg-white overflow-x-hidden ${className}`}>
      {/* Progress Bar */}
      <div className="sticky top-0 left-0 w-full h-1 bg-gray-100 z-40">
        <div 
          ref={progressRef}
          className="h-full bg-blue-500 origin-left scale-x-0"
        />
      </div>

      {/* Section Header */}
      <div className="text-center py-16">
        <p className="text-blue-700 font-medium text-lg mb-3">WHY CHOOSE US</p>
        <h2 className="text-3xl md:text-4xl font-bold text-gray-800 max-w-4xl mx-auto">
          FOUR REASONS VESA IS YOUR IDEAL PARTNER
        </h2>
      </div>

      {/* Horizontal Scroll Container */}
      <div ref={containerRef} className="relative h-screen w-full">
        <div ref={sectionsRef} className="flex h-full" style={{ width: '400vw' }}>
          {whyChooseSteps.map((step, index) => (
            <div
              key={step.id}
              className="w-screen h-full flex items-center justify-center px-8 md:px-12"
            >
              <div className="step-card max-w-6xl mx-auto">
                <div className="grid md:grid-cols-2 gap-16 items-center">
                  
                  {/* Content Side */}
                  <div className="space-y-8">
                    {/* Step Number and Title */}
                    <div className="flex items-center space-x-6">
                      <div className="w-16 h-16 rounded-full bg-blue-600 flex items-center justify-center shadow-lg">
                        <span className="text-2xl font-bold text-white">{step.id}</span>
                      </div>
                      <div className="h-px flex-1 bg-gradient-to-r from-blue-400 to-transparent" />
                    </div>

                    {/* Title */}
                    <h3 className="text-3xl md:text-4xl font-bold text-gray-800 leading-tight">
                      {step.title}
                    </h3>

                    {/* Description */}
                    <p className="text-lg text-gray-700 leading-relaxed">
                      {step.description}
                    </p>


                  </div>

                  {/* Visual Side */}
                  <div className="relative">
                    {/* Large Number in Front - Right Side */}
                    <div className="absolute -top-20 -right-8 text-9xl font-bold text-blue-500/20 select-none z-10">
                      {step.id}
                    </div>

                    {/* Main Visual Card */}
                    <div className="relative rounded-2xl overflow-hidden shadow-xl border border-gray-100">
                      <div className="aspect-square">
                        <Image
                          src={step.image}
                          alt={step.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Navigation Dots */}
                <div className="mt-16 flex justify-center">
                  <div className="flex space-x-3">
                    {whyChooseSteps.map((_, i) => (
                      <button
                        key={i}
                        onClick={scrollToStep}
                        className={`w-3 h-3 rounded-full transition-all duration-300 ${
                          i === index 
                            ? 'bg-blue-500 scale-125' 
                            : 'bg-gray-300 hover:bg-gray-400'
                        }`}
                        aria-label={`Go to step ${i + 1}`}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HorizontalScrollSection;