(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/react-phone-number-input/modules/libphonenumber/formatPhoneNumber.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>formatPhoneNumber),
    "formatPhoneNumberIntl": (()=>formatPhoneNumberIntl)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$parsePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/parsePhoneNumber.js [client] (ecmascript)");
function _typeof(o) {
    "@babel/helpers - typeof";
    return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o) {
        return typeof o;
    } : function(o) {
        return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;
    }, _typeof(o);
}
;
function formatPhoneNumber(value, format, metadata) {
    if (!metadata) {
        if (_typeof(format) === 'object') {
            metadata = format;
            format = 'NATIONAL';
        }
    }
    if (!value) {
        return '';
    }
    var phoneNumber = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$parsePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(value, metadata);
    if (!phoneNumber) {
        return '';
    }
    // Deprecated.
    // Legacy `format`s.
    switch(format){
        case 'National':
            format = 'NATIONAL';
            break;
        case 'International':
            format = 'INTERNATIONAL';
            break;
    }
    return phoneNumber.format(format);
}
function formatPhoneNumberIntl(value, metadata) {
    return formatPhoneNumber(value, 'INTERNATIONAL', metadata);
} //# sourceMappingURL=formatPhoneNumber.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/libphonenumber/formatPhoneNumber.js [client] (ecmascript) <export default as formatPhoneNumber>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "formatPhoneNumber": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$libphonenumber$2f$formatPhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$libphonenumber$2f$formatPhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/libphonenumber/formatPhoneNumber.js [client] (ecmascript)");
}}),
"[project]/node_modules/react-phone-number-input/locale/en.json.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const __TURBOPACK__default__export__ = {
    "ext": "ext.",
    "country": "Phone number country",
    "phone": "Phone",
    "AB": "Abkhazia",
    "AC": "Ascension Island",
    "AD": "Andorra",
    "AE": "United Arab Emirates",
    "AF": "Afghanistan",
    "AG": "Antigua and Barbuda",
    "AI": "Anguilla",
    "AL": "Albania",
    "AM": "Armenia",
    "AO": "Angola",
    "AQ": "Antarctica",
    "AR": "Argentina",
    "AS": "American Samoa",
    "AT": "Austria",
    "AU": "Australia",
    "AW": "Aruba",
    "AX": "Åland Islands",
    "AZ": "Azerbaijan",
    "BA": "Bosnia and Herzegovina",
    "BB": "Barbados",
    "BD": "Bangladesh",
    "BE": "Belgium",
    "BF": "Burkina Faso",
    "BG": "Bulgaria",
    "BH": "Bahrain",
    "BI": "Burundi",
    "BJ": "Benin",
    "BL": "Saint Barthélemy",
    "BM": "Bermuda",
    "BN": "Brunei Darussalam",
    "BO": "Bolivia",
    "BQ": "Bonaire, Sint Eustatius and Saba",
    "BR": "Brazil",
    "BS": "Bahamas",
    "BT": "Bhutan",
    "BV": "Bouvet Island",
    "BW": "Botswana",
    "BY": "Belarus",
    "BZ": "Belize",
    "CA": "Canada",
    "CC": "Cocos (Keeling) Islands",
    "CD": "Congo, Democratic Republic of the",
    "CF": "Central African Republic",
    "CG": "Congo",
    "CH": "Switzerland",
    "CI": "Cote d'Ivoire",
    "CK": "Cook Islands",
    "CL": "Chile",
    "CM": "Cameroon",
    "CN": "China",
    "CO": "Colombia",
    "CR": "Costa Rica",
    "CU": "Cuba",
    "CV": "Cape Verde",
    "CW": "Curaçao",
    "CX": "Christmas Island",
    "CY": "Cyprus",
    "CZ": "Czech Republic",
    "DE": "Germany",
    "DJ": "Djibouti",
    "DK": "Denmark",
    "DM": "Dominica",
    "DO": "Dominican Republic",
    "DZ": "Algeria",
    "EC": "Ecuador",
    "EE": "Estonia",
    "EG": "Egypt",
    "EH": "Western Sahara",
    "ER": "Eritrea",
    "ES": "Spain",
    "ET": "Ethiopia",
    "FI": "Finland",
    "FJ": "Fiji",
    "FK": "Falkland Islands",
    "FM": "Federated States of Micronesia",
    "FO": "Faroe Islands",
    "FR": "France",
    "GA": "Gabon",
    "GB": "United Kingdom",
    "GD": "Grenada",
    "GE": "Georgia",
    "GF": "French Guiana",
    "GG": "Guernsey",
    "GH": "Ghana",
    "GI": "Gibraltar",
    "GL": "Greenland",
    "GM": "Gambia",
    "GN": "Guinea",
    "GP": "Guadeloupe",
    "GQ": "Equatorial Guinea",
    "GR": "Greece",
    "GS": "South Georgia and the South Sandwich Islands",
    "GT": "Guatemala",
    "GU": "Guam",
    "GW": "Guinea-Bissau",
    "GY": "Guyana",
    "HK": "Hong Kong",
    "HM": "Heard Island and McDonald Islands",
    "HN": "Honduras",
    "HR": "Croatia",
    "HT": "Haiti",
    "HU": "Hungary",
    "ID": "Indonesia",
    "IE": "Ireland",
    "IL": "Israel",
    "IM": "Isle of Man",
    "IN": "India",
    "IO": "British Indian Ocean Territory",
    "IQ": "Iraq",
    "IR": "Iran",
    "IS": "Iceland",
    "IT": "Italy",
    "JE": "Jersey",
    "JM": "Jamaica",
    "JO": "Jordan",
    "JP": "Japan",
    "KE": "Kenya",
    "KG": "Kyrgyzstan",
    "KH": "Cambodia",
    "KI": "Kiribati",
    "KM": "Comoros",
    "KN": "Saint Kitts and Nevis",
    "KP": "North Korea",
    "KR": "South Korea",
    "KW": "Kuwait",
    "KY": "Cayman Islands",
    "KZ": "Kazakhstan",
    "LA": "Laos",
    "LB": "Lebanon",
    "LC": "Saint Lucia",
    "LI": "Liechtenstein",
    "LK": "Sri Lanka",
    "LR": "Liberia",
    "LS": "Lesotho",
    "LT": "Lithuania",
    "LU": "Luxembourg",
    "LV": "Latvia",
    "LY": "Libya",
    "MA": "Morocco",
    "MC": "Monaco",
    "MD": "Moldova",
    "ME": "Montenegro",
    "MF": "Saint Martin (French Part)",
    "MG": "Madagascar",
    "MH": "Marshall Islands",
    "MK": "North Macedonia",
    "ML": "Mali",
    "MM": "Myanmar",
    "MN": "Mongolia",
    "MO": "Macao",
    "MP": "Northern Mariana Islands",
    "MQ": "Martinique",
    "MR": "Mauritania",
    "MS": "Montserrat",
    "MT": "Malta",
    "MU": "Mauritius",
    "MV": "Maldives",
    "MW": "Malawi",
    "MX": "Mexico",
    "MY": "Malaysia",
    "MZ": "Mozambique",
    "NA": "Namibia",
    "NC": "New Caledonia",
    "NE": "Niger",
    "NF": "Norfolk Island",
    "NG": "Nigeria",
    "NI": "Nicaragua",
    "NL": "Netherlands",
    "NO": "Norway",
    "NP": "Nepal",
    "NR": "Nauru",
    "NU": "Niue",
    "NZ": "New Zealand",
    "OM": "Oman",
    "OS": "South Ossetia",
    "PA": "Panama",
    "PE": "Peru",
    "PF": "French Polynesia",
    "PG": "Papua New Guinea",
    "PH": "Philippines",
    "PK": "Pakistan",
    "PL": "Poland",
    "PM": "Saint Pierre and Miquelon",
    "PN": "Pitcairn",
    "PR": "Puerto Rico",
    "PS": "Palestine",
    "PT": "Portugal",
    "PW": "Palau",
    "PY": "Paraguay",
    "QA": "Qatar",
    "RE": "Reunion",
    "RO": "Romania",
    "RS": "Serbia",
    "RU": "Russia",
    "RW": "Rwanda",
    "SA": "Saudi Arabia",
    "SB": "Solomon Islands",
    "SC": "Seychelles",
    "SD": "Sudan",
    "SE": "Sweden",
    "SG": "Singapore",
    "SH": "Saint Helena",
    "SI": "Slovenia",
    "SJ": "Svalbard and Jan Mayen",
    "SK": "Slovakia",
    "SL": "Sierra Leone",
    "SM": "San Marino",
    "SN": "Senegal",
    "SO": "Somalia",
    "SR": "Suriname",
    "SS": "South Sudan",
    "ST": "Sao Tome and Principe",
    "SV": "El Salvador",
    "SX": "Sint Maarten",
    "SY": "Syria",
    "SZ": "Swaziland",
    "TA": "Tristan da Cunha",
    "TC": "Turks and Caicos Islands",
    "TD": "Chad",
    "TF": "French Southern Territories",
    "TG": "Togo",
    "TH": "Thailand",
    "TJ": "Tajikistan",
    "TK": "Tokelau",
    "TL": "Timor-Leste",
    "TM": "Turkmenistan",
    "TN": "Tunisia",
    "TO": "Tonga",
    "TR": "Turkey",
    "TT": "Trinidad and Tobago",
    "TV": "Tuvalu",
    "TW": "Taiwan",
    "TZ": "Tanzania",
    "UA": "Ukraine",
    "UG": "Uganda",
    "UM": "United States Minor Outlying Islands",
    "US": "United States",
    "UY": "Uruguay",
    "UZ": "Uzbekistan",
    "VA": "Holy See (Vatican City State)",
    "VC": "Saint Vincent and the Grenadines",
    "VE": "Venezuela",
    "VG": "Virgin Islands, British",
    "VI": "Virgin Islands, U.S.",
    "VN": "Vietnam",
    "VU": "Vanuatu",
    "WF": "Wallis and Futuna",
    "WS": "Samoa",
    "XK": "Kosovo",
    "YE": "Yemen",
    "YT": "Mayotte",
    "ZA": "South Africa",
    "ZM": "Zambia",
    "ZW": "Zimbabwe",
    "ZZ": "International"
};
}}),
"[project]/node_modules/react-phone-number-input/modules/PropTypes.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "labels": (()=>labels),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prop-types/index.js [client] (ecmascript)");
;
var metadata = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].shape({
    country_calling_codes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].object.isRequired,
    countries: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].object.isRequired
});
var labels = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].objectOf(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string); //# sourceMappingURL=PropTypes.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/helpers/inputValuePrefix.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getPrefixForFormattingValueAsPhoneNumber": (()=>getPrefixForFormattingValueAsPhoneNumber),
    "removePrefixFromFormattedPhoneNumber": (()=>removePrefixFromFormattedPhoneNumber)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/metadata.js [client] (ecmascript)");
;
function getPrefixForFormattingValueAsPhoneNumber(_ref) {
    var inputFormat = _ref.inputFormat, country = _ref.country, metadata = _ref.metadata;
    return inputFormat === 'NATIONAL_PART_OF_INTERNATIONAL' ? "+".concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getCountryCallingCode"])(country, metadata)) : '';
}
function removePrefixFromFormattedPhoneNumber(value, prefix) {
    if (prefix) {
        value = value.slice(prefix.length);
        if (value[0] === ' ') {
            value = value.slice(1);
        }
    }
    return value;
} //# sourceMappingURL=inputValuePrefix.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/helpers/parsePhoneNumberCharacter.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>parsePhoneNumberCharacter_)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$parseIncompletePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/parseIncompletePhoneNumber.js [client] (ecmascript)");
;
function parsePhoneNumberCharacter_(character, prevParsedCharacters, context) {
    // `context` argument was added as a third argument of `parse()` function
    // in `input-format` package on Dec 26th, 2023. So it could potentially be
    // `undefined` here if a 3rd-party app somehow ends up with this newer version
    // of `react-phone-number-input` and an older version of `input-format`.
    // Dunno how, but just in case, it could be `undefined` here and it wouldn't break.
    // Maybe it's not required to handle `undefined` case here.
    //
    // The addition of the `context` argument was to fix the slightly-weird behavior
    // of parsing an input string when the user inputs something like `"2+7"
    // https://github.com/catamphetamine/react-phone-number-input/issues/437
    //
    // If the parser encounters an unexpected `+` in a string being parsed
    // then it simply discards that out-of-place `+` and any following characters.
    //
    if (context && context.ignoreRest) {
        return;
    }
    var emitEvent = function emitEvent(eventName) {
        if (context) {
            switch(eventName){
                case 'end':
                    context.ignoreRest = true;
                    break;
            }
        }
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$parseIncompletePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__["parsePhoneNumberCharacter"])(character, prevParsedCharacters, emitEvent);
} //# sourceMappingURL=parsePhoneNumberCharacter.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/useInputKeyDownHandler.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useInputKeyDownHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
;
function useInputKeyDownHandler(_ref) {
    var onKeyDown = _ref.onKeyDown, inputFormat = _ref.inputFormat;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useInputKeyDownHandler.useCallback": function(event) {
            // Usability:
            // Don't allow the user to erase a leading "+" character when "international" input mode is forced.
            // That indicates to the user that they can't possibly enter the phone number in a non-international format.
            if (event.keyCode === BACKSPACE_KEY_CODE && inputFormat === 'INTERNATIONAL') {
                // It checks `event.target` here for being an `<input/>` element
                // because "keydown" events may bubble from arbitrary child elements
                // so there's no guarantee that `event.target` represents an `<input/>` element.
                // Also, since `inputComponent` is not neceesarily an `<input/>`, this check is required too.
                if (event.target instanceof HTMLInputElement) {
                    if (getCaretPosition(event.target) === LEADING_PLUS.length) {
                        event.preventDefault();
                        return;
                    }
                }
            }
            if (onKeyDown) {
                onKeyDown(event);
            }
        }
    }["useInputKeyDownHandler.useCallback"], [
        onKeyDown,
        inputFormat
    ]);
}
// Gets the caret position in an `<input/>` field.
// The caret position starts with `0` which means "before the first character".
function getCaretPosition(element) {
    return element.selectionStart;
}
var BACKSPACE_KEY_CODE = 8;
var LEADING_PLUS = '+'; //# sourceMappingURL=useInputKeyDownHandler.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/InputSmart.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createInput": (()=>createInput),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prop-types/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$input$2d$format$2f$modules$2f$react$2f$Input$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/input-format/modules/react/Input.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$AsYouType$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AsYouType$3e$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/AsYouType.js [client] (ecmascript) <export default as AsYouType>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$inputValuePrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/helpers/inputValuePrefix.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$parsePhoneNumberCharacter$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/helpers/parsePhoneNumberCharacter.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$useInputKeyDownHandler$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/useInputKeyDownHandler.js [client] (ecmascript)");
var _excluded = [
    "onKeyDown",
    "country",
    "inputFormat",
    "metadata",
    "international",
    "withCountryCallingCode"
];
function _extends() {
    _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined);
    return _extends.apply(this, arguments);
}
function _objectWithoutProperties(source, excluded) {
    if (source == null) return {};
    var target = _objectWithoutPropertiesLoose(source, excluded);
    var key, i;
    if (Object.getOwnPropertySymbols) {
        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
        for(i = 0; i < sourceSymbolKeys.length; i++){
            key = sourceSymbolKeys[i];
            if (excluded.indexOf(key) >= 0) continue;
            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
            target[key] = source[key];
        }
    }
    return target;
}
function _objectWithoutPropertiesLoose(source, excluded) {
    if (source == null) return {};
    var target = {};
    var sourceKeys = Object.keys(source);
    var key, i;
    for(i = 0; i < sourceKeys.length; i++){
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
    }
    return target;
}
;
;
;
;
;
;
;
function createInput(defaultMetadata) {
    /**
   * `InputSmart` is a "smarter" implementation of a `Component`
   * that can be passed to `<PhoneInput/>`. It parses and formats
   * the user's and maintains the caret's position in the process.
   * The caret positioning is maintained using `input-format` library.
   * Relies on being run in a DOM environment for calling caret positioning functions.
   */ function InputSmart(_ref, ref) {
        var onKeyDown = _ref.onKeyDown, country = _ref.country, inputFormat = _ref.inputFormat, _ref$metadata = _ref.metadata, metadata = _ref$metadata === void 0 ? defaultMetadata : _ref$metadata, international = _ref.international, withCountryCallingCode = _ref.withCountryCallingCode, rest = _objectWithoutProperties(_ref, _excluded);
        var format = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["useCallback"])({
            "createInput.InputSmart.useCallback[format]": function(value) {
                // "As you type" formatter.
                var formatter = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$AsYouType$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AsYouType$3e$__["AsYouType"](country, metadata);
                var prefix = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$inputValuePrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getPrefixForFormattingValueAsPhoneNumber"])({
                    inputFormat: inputFormat,
                    country: country,
                    metadata: metadata
                });
                // Format the number.
                var text = formatter.input(prefix + value);
                var template = formatter.getTemplate();
                if (prefix) {
                    text = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$inputValuePrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["removePrefixFromFormattedPhoneNumber"])(text, prefix);
                    // `AsYouType.getTemplate()` can be `undefined`.
                    if (template) {
                        template = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$inputValuePrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["removePrefixFromFormattedPhoneNumber"])(template, prefix);
                    }
                }
                return {
                    text: text,
                    template: template
                };
            }
        }["createInput.InputSmart.useCallback[format]"], [
            country,
            metadata
        ]);
        var _onKeyDown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$useInputKeyDownHandler$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])({
            onKeyDown: onKeyDown,
            inputFormat: inputFormat
        });
        return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$input$2d$format$2f$modules$2f$react$2f$Input$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"], _extends({}, rest, {
            ref: ref,
            parse: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$parsePhoneNumberCharacter$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"],
            format: format,
            onKeyDown: _onKeyDown
        }));
    }
    InputSmart = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].forwardRef(InputSmart);
    InputSmart.propTypes = {
        /**
     * The parsed phone number.
     * "Parsed" not in a sense of "E.164"
     * but rather in a sense of "having only
     * digits and possibly a leading plus character".
     * Examples: `""`, `"+"`, `"+123"`, `"123"`.
     */ value: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string.isRequired,
        /**
     * A function of `value: string`.
     * Updates the `value` property.
     */ onChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].func.isRequired,
        /**
     * A function of `event: Event`.
     * Handles `keydown` events.
     */ onKeyDown: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].func,
        /**
     * A two-letter country code for formatting `value`
     * as a national phone number (e.g. `(800) 555 35 35`).
     * E.g. "US", "RU", etc.
     * If no `country` is passed then `value`
     * is formatted as an international phone number.
     * (e.g. `****** 555 35 35`)
     * This property should've been called `defaultCountry`
     * because it only applies when the user inputs a phone number in a national format
     * and is completely ignored when the user inputs a phone number in an international format.
     */ country: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string,
        /**
     * The format that the input field value is being input/output in.
     */ inputFormat: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].oneOf([
            'INTERNATIONAL',
            'NATIONAL_PART_OF_INTERNATIONAL',
            'NATIONAL',
            'INTERNATIONAL_OR_NATIONAL'
        ]).isRequired,
        /**
     * `libphonenumber-js` metadata.
     */ metadata: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].object
    };
    return InputSmart;
}
const __TURBOPACK__default__export__ = createInput();
 //# sourceMappingURL=InputSmart.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/InputBasic.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createInput": (()=>createInput),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prop-types/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$parseIncompletePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__parseIncompletePhoneNumber$3e$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/parseIncompletePhoneNumber.js [client] (ecmascript) <export default as parseIncompletePhoneNumber>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$formatIncompletePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__formatIncompletePhoneNumber$3e$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/formatIncompletePhoneNumber.js [client] (ecmascript) <export default as formatIncompletePhoneNumber>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$inputValuePrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/helpers/inputValuePrefix.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$useInputKeyDownHandler$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/useInputKeyDownHandler.js [client] (ecmascript)");
var _excluded = [
    "value",
    "onChange",
    "onKeyDown",
    "country",
    "inputFormat",
    "metadata",
    "inputComponent",
    "international",
    "withCountryCallingCode"
];
function _extends() {
    _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined);
    return _extends.apply(this, arguments);
}
function _objectWithoutProperties(source, excluded) {
    if (source == null) return {};
    var target = _objectWithoutPropertiesLoose(source, excluded);
    var key, i;
    if (Object.getOwnPropertySymbols) {
        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
        for(i = 0; i < sourceSymbolKeys.length; i++){
            key = sourceSymbolKeys[i];
            if (excluded.indexOf(key) >= 0) continue;
            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
            target[key] = source[key];
        }
    }
    return target;
}
function _objectWithoutPropertiesLoose(source, excluded) {
    if (source == null) return {};
    var target = {};
    var sourceKeys = Object.keys(source);
    var key, i;
    for(i = 0; i < sourceKeys.length; i++){
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
    }
    return target;
}
;
;
;
;
;
function createInput(defaultMetadata) {
    /**
   * `InputBasic` is the most basic implementation of a `Component`
   * that can be passed to `<PhoneInput/>`. It parses and formats
   * the user's input but doesn't control the caret in the process:
   * when erasing or inserting digits in the middle of a phone number
   * the caret usually jumps to the end (this is the expected behavior).
   * Why does `InputBasic` exist when there's `InputSmart`?
   * One reason is working around the [Samsung Galaxy smart caret positioning bug]
   * (https://github.com/catamphetamine/react-phone-number-input/issues/75).
   * Another reason is that, unlike `InputSmart`, it doesn't require DOM environment.
   */ function InputBasic(_ref, ref) {
        var value = _ref.value, onChange = _ref.onChange, onKeyDown = _ref.onKeyDown, country = _ref.country, inputFormat = _ref.inputFormat, _ref$metadata = _ref.metadata, metadata = _ref$metadata === void 0 ? defaultMetadata : _ref$metadata, _ref$inputComponent = _ref.inputComponent, Input = _ref$inputComponent === void 0 ? 'input' : _ref$inputComponent, international = _ref.international, withCountryCallingCode = _ref.withCountryCallingCode, rest = _objectWithoutProperties(_ref, _excluded);
        var prefix = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$inputValuePrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getPrefixForFormattingValueAsPhoneNumber"])({
            inputFormat: inputFormat,
            country: country,
            metadata: metadata
        });
        var _onChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["useCallback"])({
            "createInput.InputBasic.useCallback[_onChange]": function(event) {
                var newValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$parseIncompletePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__parseIncompletePhoneNumber$3e$__["parseIncompletePhoneNumber"])(event.target.value);
                // By default, if a value is something like `"(123)"`
                // then Backspace would only erase the rightmost brace
                // becoming something like `"(123"`
                // which would give the same `"123"` value
                // which would then be formatted back to `"(123)"`
                // and so a user wouldn't be able to erase the phone number.
                //
                // This issue is worked around with this simple hack:
                // when "old" and "new" parsed values are the same,
                // it checks if the "new" formatted value could be obtained
                // from the "old" formatted value by erasing some (or no) characters at the right side.
                // If it could then it's likely that the user has hit a Backspace key
                // and what they really intended was to erase a rightmost digit rather than
                // a rightmost punctuation character.
                //
                if (newValue === value) {
                    var newValueFormatted = format(prefix, newValue, country, metadata);
                    if (newValueFormatted.indexOf(event.target.value) === 0) {
                        // Trim the last digit (or plus sign).
                        newValue = newValue.slice(0, -1);
                    }
                }
                onChange(newValue);
            }
        }["createInput.InputBasic.useCallback[_onChange]"], [
            prefix,
            value,
            onChange,
            country,
            metadata
        ]);
        var _onKeyDown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$useInputKeyDownHandler$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])({
            onKeyDown: onKeyDown,
            inputFormat: inputFormat
        });
        return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(Input, _extends({}, rest, {
            ref: ref,
            value: format(prefix, value, country, metadata),
            onChange: _onChange,
            onKeyDown: _onKeyDown
        }));
    }
    InputBasic = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].forwardRef(InputBasic);
    InputBasic.propTypes = {
        /**
     * The parsed phone number.
     * "Parsed" not in a sense of "E.164"
     * but rather in a sense of "having only
     * digits and possibly a leading plus character".
     * Examples: `""`, `"+"`, `"+123"`, `"123"`.
     */ value: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string.isRequired,
        /**
     * A function of `value: string`.
     * Updates the `value` property.
     */ onChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].func.isRequired,
        /**
     * A function of `event: Event`.
     * Handles `keydown` events.
     */ onKeyDown: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].func,
        /**
     * A two-letter country code for formatting `value`
     * as a national phone number (e.g. `(800) 555 35 35`).
     * E.g. "US", "RU", etc.
     * If no `country` is passed then `value`
     * is formatted as an international phone number.
     * (e.g. `****** 555 35 35`)
     * This property should've been called `defaultCountry`
     * because it only applies when the user inputs a phone number in a national format
     * and is completely ignored when the user inputs a phone number in an international format.
     */ country: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string,
        /**
     * The format that the input field value is being input/output in.
     */ inputFormat: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].oneOf([
            'INTERNATIONAL',
            'NATIONAL_PART_OF_INTERNATIONAL',
            'NATIONAL',
            'INTERNATIONAL_OR_NATIONAL'
        ]).isRequired,
        /**
     * `libphonenumber-js` metadata.
     */ metadata: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].object,
        /**
     * The `<input/>` component.
     */ inputComponent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].elementType
    };
    return InputBasic;
}
const __TURBOPACK__default__export__ = createInput();
function format(prefix, value, country, metadata) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$inputValuePrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["removePrefixFromFormattedPhoneNumber"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$formatIncompletePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__formatIncompletePhoneNumber$3e$__["formatIncompletePhoneNumber"])(prefix + value, country, metadata), prefix);
} //# sourceMappingURL=InputBasic.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/CountrySelect.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CountrySelectWithIcon": (()=>CountrySelectWithIcon),
    "default": (()=>CountrySelect)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prop-types/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$country$2d$flag$2d$icons$2f$modules$2f$unicode$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/country-flag-icons/modules/unicode.js [client] (ecmascript)");
var _excluded = [
    "value",
    "onChange",
    "options",
    "disabled",
    "readOnly"
], _excluded2 = [
    "value",
    "options",
    "className",
    "iconComponent",
    "getIconAspectRatio",
    "arrowComponent",
    "unicodeFlags"
];
function _createForOfIteratorHelperLoose(o, allowArrayLike) {
    var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
    if (it) return (it = it.call(o)).next.bind(it);
    if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {
        if (it) o = it;
        var i = 0;
        return function() {
            if (i >= o.length) return {
                done: true
            };
            return {
                done: false,
                value: o[i++]
            };
        };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _unsupportedIterableToArray(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _arrayLikeToArray(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(o);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
}
function _arrayLikeToArray(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _extends() {
    _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined);
    return _extends.apply(this, arguments);
}
function _objectWithoutProperties(source, excluded) {
    if (source == null) return {};
    var target = _objectWithoutPropertiesLoose(source, excluded);
    var key, i;
    if (Object.getOwnPropertySymbols) {
        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
        for(i = 0; i < sourceSymbolKeys.length; i++){
            key = sourceSymbolKeys[i];
            if (excluded.indexOf(key) >= 0) continue;
            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
            target[key] = source[key];
        }
    }
    return target;
}
function _objectWithoutPropertiesLoose(source, excluded) {
    if (source == null) return {};
    var target = {};
    var sourceKeys = Object.keys(source);
    var key, i;
    for(i = 0; i < sourceKeys.length; i++){
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
    }
    return target;
}
;
;
;
;
function CountrySelect(_ref) {
    var value = _ref.value, onChange = _ref.onChange, options = _ref.options, disabled = _ref.disabled, readOnly = _ref.readOnly, rest = _objectWithoutProperties(_ref, _excluded);
    var onChange_ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CountrySelect.useCallback[onChange_]": function(event) {
            var value = event.target.value;
            onChange(value === 'ZZ' ? undefined : value);
        }
    }["CountrySelect.useCallback[onChange_]"], [
        onChange
    ]);
    var selectedOption = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CountrySelect.useMemo[selectedOption]": function() {
            return getSelectedOption(options, value);
        }
    }["CountrySelect.useMemo[selectedOption]"], [
        options,
        value
    ]);
    // "ZZ" means "International".
    // (HTML requires each `<option/>` have some string `value`).
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("select", _extends({}, rest, {
        disabled: disabled || readOnly,
        readOnly: readOnly,
        value: value || 'ZZ',
        onChange: onChange_
    }), options.map(function(_ref2) {
        var value = _ref2.value, label = _ref2.label, divider = _ref2.divider;
        return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("option", {
            key: divider ? '|' : value || 'ZZ',
            value: divider ? '|' : value || 'ZZ',
            disabled: divider ? true : false,
            style: divider ? DIVIDER_STYLE : undefined
        }, label);
    }));
}
CountrySelect.propTypes = {
    /**
   * A two-letter country code.
   * Example: "US", "RU", etc.
   */ value: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string,
    /**
   * A function of `value: string`.
   * Updates the `value` property.
   */ onChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].func.isRequired,
    // `<select/>` options.
    options: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].arrayOf(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].shape({
        value: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string,
        label: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string,
        divider: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].bool
    })).isRequired,
    // `readonly` attribute doesn't work on a `<select/>`.
    // https://github.com/catamphetamine/react-phone-number-input/issues/419#issuecomment-1764384480
    // https://www.delftstack.com/howto/html/html-select-readonly/
    // To work around that, if `readOnly: true` property is passed
    // to this component, it behaves analogous to `disabled: true`.
    disabled: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].bool,
    readOnly: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].bool
};
var DIVIDER_STYLE = {
    fontSize: '1px',
    backgroundColor: 'currentColor',
    color: 'inherit'
};
function CountrySelectWithIcon(_ref3) {
    var value = _ref3.value, options = _ref3.options, className = _ref3.className, Icon = _ref3.iconComponent, getIconAspectRatio = _ref3.getIconAspectRatio, _ref3$arrowComponent = _ref3.arrowComponent, Arrow = _ref3$arrowComponent === void 0 ? DefaultArrowComponent : _ref3$arrowComponent, unicodeFlags = _ref3.unicodeFlags, rest = _objectWithoutProperties(_ref3, _excluded2);
    var selectedOption = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CountrySelectWithIcon.useMemo[selectedOption]": function() {
            return getSelectedOption(options, value);
        }
    }["CountrySelectWithIcon.useMemo[selectedOption]"], [
        options,
        value
    ]);
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "PhoneInputCountry"
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(CountrySelect, _extends({}, rest, {
        value: value,
        options: options,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])('PhoneInputCountrySelect', className)
    })), selectedOption && (unicodeFlags && value ? /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "PhoneInputCountryIconUnicode"
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$country$2d$flag$2d$icons$2f$modules$2f$unicode$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(value)) : /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(Icon, {
        "aria-hidden": true,
        country: value,
        label: selectedOption.label,
        aspectRatio: unicodeFlags ? 1 : undefined
    })), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(Arrow, null));
}
CountrySelectWithIcon.propTypes = {
    // Country flag component.
    iconComponent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].elementType,
    // Select arrow component.
    arrowComponent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].elementType,
    // Set to `true` to render Unicode flag icons instead of SVG images.
    unicodeFlags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].bool
};
function DefaultArrowComponent() {
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "PhoneInputCountrySelectArrow"
    });
}
function getSelectedOption(options, value) {
    for(var _iterator = _createForOfIteratorHelperLoose(options), _step; !(_step = _iterator()).done;){
        var option = _step.value;
        if (!option.divider) {
            if (isSameOptionValue(option.value, value)) {
                return option;
            }
        }
    }
}
function isSameOptionValue(value1, value2) {
    // `undefined` is identical to `null`: both mean "no country selected".
    if (value1 === undefined || value1 === null) {
        return value2 === undefined || value2 === null;
    }
    return value1 === value2;
} //# sourceMappingURL=CountrySelect.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/Flag.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>FlagComponent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prop-types/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [client] (ecmascript)");
var _excluded = [
    "country",
    "countryName",
    "flags",
    "flagUrl"
];
function _extends() {
    _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined);
    return _extends.apply(this, arguments);
}
function _objectWithoutProperties(source, excluded) {
    if (source == null) return {};
    var target = _objectWithoutPropertiesLoose(source, excluded);
    var key, i;
    if (Object.getOwnPropertySymbols) {
        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
        for(i = 0; i < sourceSymbolKeys.length; i++){
            key = sourceSymbolKeys[i];
            if (excluded.indexOf(key) >= 0) continue;
            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
            target[key] = source[key];
        }
    }
    return target;
}
function _objectWithoutPropertiesLoose(source, excluded) {
    if (source == null) return {};
    var target = {};
    var sourceKeys = Object.keys(source);
    var key, i;
    for(i = 0; i < sourceKeys.length; i++){
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
    }
    return target;
}
;
;
;
function FlagComponent(_ref) {
    var country = _ref.country, countryName = _ref.countryName, flags = _ref.flags, flagUrl = _ref.flagUrl, rest = _objectWithoutProperties(_ref, _excluded);
    if (flags && flags[country]) {
        return flags[country]({
            title: countryName
        });
    }
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("img", _extends({}, rest, {
        alt: countryName,
        role: countryName ? undefined : "presentation",
        src: flagUrl.replace('{XX}', country).replace('{xx}', country.toLowerCase())
    }));
}
FlagComponent.propTypes = {
    // The country to be selected by default.
    // Two-letter country code ("ISO 3166-1 alpha-2").
    country: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string.isRequired,
    // Will be HTML `title` attribute of the `<img/>`.
    countryName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string.isRequired,
    // Country flag icon components.
    // By default flag icons are inserted as `<img/>`s
    // with their `src` pointed to `country-flag-icons` gitlab pages website.
    // There might be cases (e.g. an offline application)
    // where having a large (3 megabyte) `<svg/>` flags
    // bundle is more appropriate.
    // `import flags from 'react-phone-number-input/flags'`.
    flags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].objectOf(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].elementType),
    // A URL for a country flag icon.
    // By default it points to `country-flag-icons` gitlab pages website.
    flagUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string.isRequired
}; //# sourceMappingURL=Flag.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/InternationalIcon.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>InternationalIcon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prop-types/index.js [client] (ecmascript)");
var _excluded = [
    "aspectRatio"
], _excluded2 = [
    "title"
], _excluded3 = [
    "title"
];
function _extends() {
    _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined);
    return _extends.apply(this, arguments);
}
function _objectWithoutProperties(source, excluded) {
    if (source == null) return {};
    var target = _objectWithoutPropertiesLoose(source, excluded);
    var key, i;
    if (Object.getOwnPropertySymbols) {
        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
        for(i = 0; i < sourceSymbolKeys.length; i++){
            key = sourceSymbolKeys[i];
            if (excluded.indexOf(key) >= 0) continue;
            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
            target[key] = source[key];
        }
    }
    return target;
}
function _objectWithoutPropertiesLoose(source, excluded) {
    if (source == null) return {};
    var target = {};
    var sourceKeys = Object.keys(source);
    var key, i;
    for(i = 0; i < sourceKeys.length; i++){
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
    }
    return target;
}
;
;
function InternationalIcon(_ref) {
    var aspectRatio = _ref.aspectRatio, rest = _objectWithoutProperties(_ref, _excluded);
    if (aspectRatio === 1) {
        return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(InternationalIcon1x1, rest);
    } else {
        return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(InternationalIcon3x2, rest);
    }
}
InternationalIcon.propTypes = {
    title: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string.isRequired,
    aspectRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].number
};
// 3x2.
// Using `<title/>` in `<svg/>`s:
// https://developer.mozilla.org/en-US/docs/Web/SVG/Element/title
function InternationalIcon3x2(_ref2) {
    var title = _ref2.title, rest = _objectWithoutProperties(_ref2, _excluded2);
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", _extends({}, rest, {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 75 50"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("title", null, title), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("g", {
        className: "PhoneInputInternationalIconGlobe",
        stroke: "currentColor",
        fill: "none",
        strokeWidth: "2",
        strokeMiterlimit: "10"
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        strokeLinecap: "round",
        d: "M47.2,36.1C48.1,36,49,36,50,36c7.4,0,14,1.7,18.5,4.3"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M68.6,9.6C64.2,12.3,57.5,14,50,14c-7.4,0-14-1.7-18.5-4.3"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("line", {
        x1: "26",
        y1: "25",
        x2: "74",
        y2: "25"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("line", {
        x1: "50",
        y1: "1",
        x2: "50",
        y2: "49"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        strokeLinecap: "round",
        d: "M46.3,48.7c1.2,0.2,2.5,0.3,3.7,0.3c13.3,0,24-10.7,24-24S63.3,1,50,1S26,11.7,26,25c0,2,0.3,3.9,0.7,5.8"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        strokeLinecap: "round",
        d: "M46.8,48.2c1,0.6,2.1,0.8,3.2,0.8c6.6,0,12-10.7,12-24S56.6,1,50,1S38,11.7,38,25c0,1.4,0.1,2.7,0.2,4c0,0.1,0,0.2,0,0.2"
    })), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        className: "PhoneInputInternationalIconPhone",
        stroke: "none",
        fill: "currentColor",
        d: "M12.4,17.9c2.9-2.9,5.4-4.8,0.3-11.2S4.1,5.2,1.3,8.1C-2,11.4,1.1,23.5,13.1,35.6s24.3,15.2,27.5,11.9c2.8-2.8,7.8-6.3,1.4-11.5s-8.3-2.6-11.2,0.3c-2,2-7.2-2.2-11.7-6.7S10.4,19.9,12.4,17.9z"
    }));
}
InternationalIcon3x2.propTypes = {
    title: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string.isRequired
};
// 1x1.
// Using `<title/>` in `<svg/>`s:
// https://developer.mozilla.org/en-US/docs/Web/SVG/Element/title
function InternationalIcon1x1(_ref3) {
    var title = _ref3.title, rest = _objectWithoutProperties(_ref3, _excluded3);
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", _extends({}, rest, {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 50 50"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("title", null, title), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("g", {
        className: "PhoneInputInternationalIconGlobe",
        stroke: "currentColor",
        fill: "none",
        strokeWidth: "2",
        strokeLinecap: "round"
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M8.45,13A21.44,21.44,0,1,1,37.08,41.56"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M19.36,35.47a36.9,36.9,0,0,1-2.28-13.24C17.08,10.39,21.88.85,27.8.85s10.72,9.54,10.72,21.38c0,6.48-1.44,12.28-3.71,16.21"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M17.41,33.4A39,39,0,0,1,27.8,32.06c6.62,0,12.55,1.5,16.48,3.86"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M44.29,8.53c-3.93,2.37-9.86,3.88-16.49,3.88S15.25,10.9,11.31,8.54"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("line", {
        x1: "27.8",
        y1: "0.85",
        x2: "27.8",
        y2: "34.61"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("line", {
        x1: "15.2",
        y1: "22.23",
        x2: "49.15",
        y2: "22.23"
    })), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        className: "PhoneInputInternationalIconPhone",
        stroke: "transparent",
        fill: "currentColor",
        d: "M9.42,26.64c2.22-2.22,4.15-3.59.22-8.49S3.08,17,.93,19.17c-2.49,2.48-.13,11.74,9,20.89s18.41,11.5,20.89,9c2.15-2.15,5.91-4.77,1-8.71s-6.27-2-8.49.22c-1.55,1.55-5.48-1.69-8.86-5.08S7.87,28.19,9.42,26.64Z"
    }));
}
InternationalIcon1x1.propTypes = {
    title: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string.isRequired
}; //# sourceMappingURL=InternationalIcon.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/helpers/isE164Number.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Tells if `value: string` is an `E.164` phone number.
//
// Returns a boolean.
//
// It doesn't validate that the minimum national (significant) number length
// is at least 2 characters.
//
__turbopack_context__.s({
    "default": (()=>isE164Number),
    "validateE164Number": (()=>validateE164Number)
});
function isE164Number(value) {
    if (value.length < 2) {
        return false;
    }
    if (value[0] !== '+') {
        return false;
    }
    var i = 1;
    while(i < value.length){
        var character = value.charCodeAt(i);
        if (character >= 48 && character <= 57) {
        // Is a digit.
        } else {
            return false;
        }
        i++;
    }
    return true;
}
function validateE164Number(value) {
    if (!isE164Number(value)) {
        console.error('[react-phone-number-input] Expected the initial `value` to be a E.164 phone number. Got', value);
    }
} //# sourceMappingURL=isE164Number.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/helpers/countries.js [client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getSupportedCountries": (()=>getSupportedCountries),
    "getSupportedCountryOptions": (()=>getSupportedCountryOptions),
    "isCountrySupportedWithError": (()=>isCountrySupportedWithError),
    "sortCountryOptions": (()=>sortCountryOptions)
});
// Ignores weird istanbul error: "else path not taken".
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/metadata.js [client] (ecmascript)");
function _createForOfIteratorHelperLoose(o, allowArrayLike) {
    var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
    if (it) return (it = it.call(o)).next.bind(it);
    if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {
        if (it) o = it;
        var i = 0;
        return function() {
            if (i >= o.length) return {
                done: true
            };
            return {
                done: false,
                value: o[i++]
            };
        };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _unsupportedIterableToArray(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _arrayLikeToArray(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(o);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
}
function _arrayLikeToArray(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
;
;
function sortCountryOptions(options, order) {
    if (!order) {
        return options;
    }
    var optionsOnTop = [];
    var optionsOnBottom = [];
    var appendTo = optionsOnTop;
    var _loop = function _loop() {
        var element = _step.value;
        if (element === '|') {
            appendTo.push({
                divider: true
            });
        } else if (element === '...' || element === '…') {
            appendTo = optionsOnBottom;
        } else {
            var countryCode;
            if (element === '🌐') {
                countryCode = undefined;
            } else {
                countryCode = element;
            }
            // Find the position of the option.
            var index = options.indexOf(options.filter(function(option) {
                return option.value === countryCode;
            })[0]);
            // Get the option.
            var option = options[index];
            // Remove the option from its default position.
            options.splice(index, 1);
            // Add the option on top.
            appendTo.push(option);
        }
    };
    for(var _iterator = _createForOfIteratorHelperLoose(order), _step; !(_step = _iterator()).done;){
        _loop();
    }
    return optionsOnTop.concat(options).concat(optionsOnBottom);
}
function getSupportedCountryOptions(countryOptions, metadata) {
    if (countryOptions) {
        countryOptions = countryOptions.filter(function(option) {
            switch(option){
                case '🌐':
                case '|':
                case '...':
                case '…':
                    return true;
                default:
                    return isCountrySupportedWithError(option, metadata);
            }
        });
        if (countryOptions.length > 0) {
            return countryOptions;
        }
    }
}
function isCountrySupportedWithError(country, metadata) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__["isSupportedCountry"])(country, metadata)) {
        return true;
    } else {
        console.error("Country not found: ".concat(country));
        return false;
    }
}
function getSupportedCountries(countries, metadata) {
    if (countries) {
        countries = countries.filter(function(country) {
            return isCountrySupportedWithError(country, metadata);
        });
        if (countries.length === 0) {
            countries = undefined;
        }
    }
    return countries;
} //# sourceMappingURL=countries.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/CountryIcon.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createCountryIconComponent": (()=>createCountryIconComponent),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prop-types/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$InternationalIcon$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/InternationalIcon.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$Flag$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/Flag.js [client] (ecmascript)");
var _excluded = [
    "country",
    "label",
    "aspectRatio"
];
function _extends() {
    _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined);
    return _extends.apply(this, arguments);
}
function _objectWithoutProperties(source, excluded) {
    if (source == null) return {};
    var target = _objectWithoutPropertiesLoose(source, excluded);
    var key, i;
    if (Object.getOwnPropertySymbols) {
        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
        for(i = 0; i < sourceSymbolKeys.length; i++){
            key = sourceSymbolKeys[i];
            if (excluded.indexOf(key) >= 0) continue;
            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
            target[key] = source[key];
        }
    }
    return target;
}
function _objectWithoutPropertiesLoose(source, excluded) {
    if (source == null) return {};
    var target = {};
    var sourceKeys = Object.keys(source);
    var key, i;
    for(i = 0; i < sourceKeys.length; i++){
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
    }
    return target;
}
;
;
;
;
;
function createCountryIconComponent(_ref) {
    var flags = _ref.flags, flagUrl = _ref.flagUrl, FlagComponent = _ref.flagComponent, InternationalIcon = _ref.internationalIcon;
    function CountryIcon(_ref2) {
        var country = _ref2.country, label = _ref2.label, aspectRatio = _ref2.aspectRatio, rest = _objectWithoutProperties(_ref2, _excluded);
        // `aspectRatio` is currently a hack for the default "International" icon
        // to render it as a square when Unicode flag icons are used.
        // So `aspectRatio` property is only used with the default "International" icon.
        var _aspectRatio = InternationalIcon === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$InternationalIcon$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"] ? aspectRatio : undefined;
        return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement("div", _extends({}, rest, {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])('PhoneInputCountryIcon', {
                'PhoneInputCountryIcon--square': _aspectRatio === 1,
                'PhoneInputCountryIcon--border': country
            })
        }), country ? /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(FlagComponent, {
            country: country,
            countryName: label,
            flags: flags,
            flagUrl: flagUrl,
            className: "PhoneInputCountryIconImg"
        }) : /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(InternationalIcon, {
            title: label,
            aspectRatio: _aspectRatio,
            className: "PhoneInputCountryIconImg"
        }));
    }
    CountryIcon.propTypes = {
        country: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string,
        label: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string.isRequired,
        aspectRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].number
    };
    return CountryIcon;
}
const __TURBOPACK__default__export__ = createCountryIconComponent({
    // Must be equal to `defaultProps.flagUrl` in `./PhoneInputWithCountry.js`.
    flagUrl: 'https://purecatamphetamine.github.io/country-flag-icons/3x2/{XX}.svg',
    flagComponent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$Flag$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"],
    internationalIcon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$InternationalIcon$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"]
});
 //# sourceMappingURL=CountryIcon.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/useExternalRef.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useExternalRef),
    "setRefsValue": (()=>setRefsValue)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
function _createForOfIteratorHelperLoose(o, allowArrayLike) {
    var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
    if (it) return (it = it.call(o)).next.bind(it);
    if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {
        if (it) o = it;
        var i = 0;
        return function() {
            if (i >= o.length) return {
                done: true
            };
            return {
                done: false,
                value: o[i++]
            };
        };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _unsupportedIterableToArray(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _arrayLikeToArray(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(o);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
}
function _arrayLikeToArray(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
;
function useExternalRef(externalRef) {
    // Create a copy of the original `ref` (which might not exist).
    // Both refs will point to the same value.
    var refCopy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["useRef"])();
    // Updates both `ref`s with the same `value`.
    var refSetter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useExternalRef.useCallback[refSetter]": function(value) {
            setRefsValue([
                externalRef,
                refCopy
            ], value);
        }
    }["useExternalRef.useCallback[refSetter]"], [
        externalRef,
        refCopy
    ]);
    return [
        refCopy,
        refSetter
    ];
}
function setRefsValue(refs, value) {
    for(var _iterator = _createForOfIteratorHelperLoose(refs), _step; !(_step = _iterator()).done;){
        var ref = _step.value;
        if (ref) {
            setRefValue(ref, value);
        }
    }
}
// Sets the value of a `ref`.
// Before React Hooks were introduced, `ref`s used to be functions.
// After React Hooks were introduces, `ref`s became objects with `.current` property.
// This function sets a `ref`'s value regardless of its internal implementation,
// so it supports both types of `ref`s.
function setRefValue(ref, value) {
    if (typeof ref === 'function') {
        ref(value);
    } else {
        ref.current = value;
    }
} //# sourceMappingURL=useExternalRef.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/helpers/getInternationalPhoneNumberPrefix.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>getInternationalPhoneNumberPrefix)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/metadata.js [client] (ecmascript)");
;
var ONLY_DIGITS_REGEXP = /^\d+$/;
function getInternationalPhoneNumberPrefix(country, metadata) {
    // Standard international phone number prefix: "+" and "country calling code".
    var prefix = '+' + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getCountryCallingCode"])(country, metadata);
    // "Leading digits" can't be used to rule out any countries.
    // So the "pre-fill with leading digits on country selection" feature had to be reverted.
    // https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/10#note_1231042367
    // // Get "leading digits" for a phone number of the country.
    // // If there're "leading digits" then they can be part of the prefix too.
    // // https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/10
    // metadata = new Metadata(metadata)
    // metadata.selectNumberingPlan(country)
    // // "Leading digits" patterns are only defined for about 20% of all countries.
    // // By definition, matching "leading digits" is a sufficient but not a necessary
    // // condition for a phone number to belong to a country.
    // // The point of "leading digits" check is that it's the fastest one to get a match.
    // // https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md#leading_digits
    // const leadingDigits = metadata.numberingPlan.leadingDigits()
    // if (leadingDigits && ONLY_DIGITS_REGEXP.test(leadingDigits)) {
    // 	prefix += leadingDigits
    // }
    return prefix;
} //# sourceMappingURL=getInternationalPhoneNumberPrefix.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/helpers/phoneInputHelpers.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "compareStrings": (()=>compareStrings),
    "couldNumberBelongToCountry": (()=>couldNumberBelongToCountry),
    "e164": (()=>e164),
    "generateNationalNumberDigits": (()=>generateNationalNumberDigits),
    "getCountryForPartialE164Number": (()=>getCountryForPartialE164Number),
    "getCountryFromPossiblyIncompleteInternationalPhoneNumber": (()=>getCountryFromPossiblyIncompleteInternationalPhoneNumber),
    "getCountrySelectOptions": (()=>getCountrySelectOptions),
    "getInitialPhoneDigits": (()=>getInitialPhoneDigits),
    "getNationalSignificantNumberDigits": (()=>getNationalSignificantNumberDigits),
    "getPhoneDigitsForNewCountry": (()=>getPhoneDigitsForNewCountry),
    "getPreSelectedCountry": (()=>getPreSelectedCountry),
    "onPhoneDigitsChange": (()=>onPhoneDigitsChange),
    "parsePhoneNumber": (()=>parsePhoneNumber),
    "stripCountryCallingCode": (()=>stripCountryCallingCode),
    "trimNumber": (()=>trimNumber)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$parsePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/parsePhoneNumber.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/metadata.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$AsYouType$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AsYouType$3e$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/AsYouType.js [client] (ecmascript) <export default as AsYouType>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Metadata$3e$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/metadata.js [client] (ecmascript) <export default as Metadata>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$getInternationalPhoneNumberPrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/helpers/getInternationalPhoneNumberPrefix.js [client] (ecmascript)");
;
;
function getPreSelectedCountry(_ref) {
    var value = _ref.value, phoneNumber = _ref.phoneNumber, defaultCountry = _ref.defaultCountry, getAnyCountry = _ref.getAnyCountry, countries = _ref.countries, required = _ref.required, metadata = _ref.metadata;
    var country;
    // If can get country from E.164 phone number
    // then it overrides the `country` passed (or not passed).
    if (phoneNumber && phoneNumber.country) {
        // `country` will be left `undefined` in case of non-detection.
        country = phoneNumber.country;
    } else if (defaultCountry) {
        if (!value || couldNumberBelongToCountry(value, defaultCountry, metadata)) {
            country = defaultCountry;
        }
    }
    // Only pre-select a country if it's in the available `countries` list.
    if (countries && countries.indexOf(country) < 0) {
        country = undefined;
    }
    // If there will be no "International" option
    // then some `country` must be selected.
    // It will still be the wrong country though.
    // But still country `<select/>` can't be left in a broken state.
    if (!country && required && countries && countries.length > 0) {
        country = getAnyCountry();
    // noCountryMatchesTheNumber = true
    }
    return country;
}
function getCountrySelectOptions(_ref2) {
    var countries = _ref2.countries, countryNames = _ref2.countryNames, addInternationalOption = _ref2.addInternationalOption, compareStringsLocales = _ref2.compareStringsLocales, _compareStrings = _ref2.compareStrings;
    // Default country name comparator uses `String.localeCompare()`.
    if (!_compareStrings) {
        _compareStrings = compareStrings;
    }
    // Generates a `<Select/>` option for each country.
    var countrySelectOptions = countries.map(function(country) {
        return {
            value: country,
            // All `locale` country names included in this library
            // include all countries (this is checked at build time).
            // The only case when a country name might be missing
            // is when a developer supplies their own `labels` property.
            // To guard against such cases, a missing country name
            // is substituted by country code.
            label: countryNames[country] || country
        };
    });
    // Sort the list of countries alphabetically.
    countrySelectOptions.sort(function(a, b) {
        return _compareStrings(a.label, b.label, compareStringsLocales);
    });
    // Add the "International" option to the country list (if suitable)
    if (addInternationalOption) {
        countrySelectOptions.unshift({
            label: countryNames.ZZ
        });
    }
    return countrySelectOptions;
}
function parsePhoneNumber(value, metadata) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$parsePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(value || '', metadata);
}
function generateNationalNumberDigits(phoneNumber) {
    return phoneNumber.formatNational().replace(/\D/g, '');
}
function getPhoneDigitsForNewCountry(phoneDigits, _ref3) {
    var prevCountry = _ref3.prevCountry, newCountry = _ref3.newCountry, metadata = _ref3.metadata, useNationalFormat = _ref3.useNationalFormat;
    if (prevCountry === newCountry) {
        return phoneDigits;
    }
    // If `parsed_input` is empty
    // then no need to migrate anything.
    if (!phoneDigits) {
        if (useNationalFormat) {
            return '';
        } else {
            if (newCountry) {
                // If `phoneDigits` is empty then set `phoneDigits` to
                // `+{getCountryCallingCode(newCountry)}`.
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$getInternationalPhoneNumberPrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(newCountry, metadata);
            }
            return '';
        }
    }
    // If switching to some country.
    // (from "International" or another country)
    // If switching from "International" then `phoneDigits` starts with a `+`.
    // Otherwise it may or may not start with a `+`.
    if (newCountry) {
        // If the phone number was entered in international format
        // then migrate it to the newly selected country.
        // The phone number may be incomplete.
        // The phone number entered not necessarily starts with
        // the previously selected country phone prefix.
        if (phoneDigits[0] === '+') {
            // If the international phone number is for the new country
            // then convert it to local if required.
            if (useNationalFormat) {
                // // If a phone number is being input in international form
                // // and the country can already be derived from it,
                // // and if it is the new country, then format as a national number.
                // const derived_country = getCountryFromPossiblyIncompleteInternationalPhoneNumber(phoneDigits, metadata)
                // if (derived_country === newCountry) {
                // 	return stripCountryCallingCode(phoneDigits, derived_country, metadata)
                // }
                // Actually, the two countries don't necessarily need to match:
                // the condition could be looser here, because several countries
                // might share the same international phone number format
                // (for example, "NANPA" countries like US, Canada, etc).
                // The looser condition would be just "same nternational phone number format"
                // which would mean "same country calling code" in the context of `libphonenumber-js`.
                if (phoneDigits.indexOf('+' + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getCountryCallingCode"])(newCountry, metadata)) === 0) {
                    return stripCountryCallingCode(phoneDigits, newCountry, metadata);
                }
                // Simply discard the previously entered international phone number,
                // because otherwise any "smart" transformation like getting the
                // "national (significant) number" part and then prepending the
                // newly selected country's "country calling code" to it
                // would just be confusing for a user without being actually useful.
                return '';
            // // Simply strip the leading `+` character
            // // therefore simply converting all digits into a "local" phone number.
            // // https://github.com/catamphetamine/react-phone-number-input/issues/287
            // return phoneDigits.slice(1)
            }
            if (prevCountry) {
                var newCountryPrefix = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$getInternationalPhoneNumberPrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(newCountry, metadata);
                if (phoneDigits.indexOf(newCountryPrefix) === 0) {
                    return phoneDigits;
                } else {
                    return newCountryPrefix;
                }
            } else {
                var defaultValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$getInternationalPhoneNumberPrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(newCountry, metadata);
                // If `phoneDigits`'s country calling code part is the same
                // as for the new `country`, then leave `phoneDigits` as is.
                if (phoneDigits.indexOf(defaultValue) === 0) {
                    return phoneDigits;
                }
                // If `phoneDigits`'s country calling code part is not the same
                // as for the new `country`, then set `phoneDigits` to
                // `+{getCountryCallingCode(newCountry)}`.
                return defaultValue;
            }
        // // If the international phone number already contains
        // // any country calling code then trim the country calling code part.
        // // (that could also be the newly selected country phone code prefix as well)
        // // `phoneDigits` doesn't neccessarily belong to `prevCountry`.
        // // (e.g. if a user enters an international number
        // //  not belonging to any of the reduced `countries` list).
        // phoneDigits = stripCountryCallingCode(phoneDigits, prevCountry, metadata)
        // // Prepend country calling code prefix
        // // for the newly selected country.
        // return e164(phoneDigits, newCountry, metadata) || `+${getCountryCallingCode(newCountry, metadata)}`
        }
    } else {
        // If the phone number was entered in national format.
        if (phoneDigits[0] !== '+') {
            // Format the national phone number as an international one.
            // The phone number entered not necessarily even starts with
            // the previously selected country phone prefix.
            // Even if the phone number belongs to whole another country
            // it will still be parsed into some national phone number.
            //
            // Ignore the now-uncovered `|| ''` code branch:
            // previously `e164()` function could return an empty string
            // even when `phoneDigits` were not empty.
            // Now it always returns some `value` when there're any `phoneDigits`.
            // Still, didn't remove the `|| ''` code branch just in case
            // that logic changes somehow in some future, so there're no
            // possible bugs related to that.
            //
            // (ignore the `|| ''` code branch)
            /* istanbul ignore next */ return e164(phoneDigits, prevCountry, metadata) || '';
        }
    }
    return phoneDigits;
}
function e164(number, country, metadata) {
    if (!number) {
        return;
    }
    // If the phone number is being input in international format.
    if (number[0] === '+') {
        // If it's just the `+` sign then return nothing.
        if (number === '+') {
            return;
        }
        // Return a E.164 phone number.
        //
        // Could return `number` "as is" here, but there's a possibility
        // that some user might incorrectly input an international number
        // with a "national prefix". Such numbers aren't considered valid,
        // but `libphonenumber-js` is "forgiving" when it comes to parsing
        // user's input, and this input component follows that behavior.
        //
        var asYouType = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$AsYouType$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AsYouType$3e$__["AsYouType"](country, metadata);
        asYouType.input(number);
        // This function would return `undefined` only when `number` is `"+"`,
        // but at this point it is known that `number` is not `"+"`.
        return asYouType.getNumberValue();
    }
    // For non-international phone numbers
    // an accompanying country code is required.
    // The situation when `country` is `undefined`
    // and a non-international phone number is passed
    // to this function shouldn't happen.
    if (!country) {
        return;
    }
    var partial_national_significant_number = getNationalSignificantNumberDigits(number, country, metadata);
    //
    // Even if no "national (significant) number" digits have been input,
    // still return a non-`undefined` value.
    // https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/113
    //
    // For example, if the user has selected country `US` and entered `"1"`
    // then that `"1"` is just a "national prefix" and no "national (significant) number"
    // digits have been input yet. Still, return `"+1"` as `value` in such cases,
    // because otherwise the app would think that the input is empty and mark it as such
    // while in reality it isn't empty, which might be thought of as a "bug", or just
    // a "weird" behavior.
    //
    // if (partial_national_significant_number) {
    return "+".concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getCountryCallingCode"])(country, metadata)).concat(partial_national_significant_number || '');
// }
}
function trimNumber(number, country, metadata) {
    var nationalSignificantNumberPart = getNationalSignificantNumberDigits(number, country, metadata);
    if (nationalSignificantNumberPart) {
        var overflowDigitsCount = nationalSignificantNumberPart.length - getMaxNumberLength(country, metadata);
        if (overflowDigitsCount > 0) {
            return number.slice(0, number.length - overflowDigitsCount);
        }
    }
    return number;
}
function getMaxNumberLength(country, metadata) {
    // Get "possible lengths" for a phone number of the country.
    metadata = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Metadata$3e$__["Metadata"](metadata);
    metadata.selectNumberingPlan(country);
    // Return the last "possible length".
    return metadata.numberingPlan.possibleLengths()[metadata.numberingPlan.possibleLengths().length - 1];
}
function getCountryForPartialE164Number(partialE164Number, _ref4) {
    var country = _ref4.country, countries = _ref4.countries, defaultCountry = _ref4.defaultCountry, latestCountrySelectedByUser = _ref4.latestCountrySelectedByUser, required = _ref4.required, metadata = _ref4.metadata;
    // `partialE164Number` is supposed to be an E.164 phone number.
    // `partialE164Number` is supposed to be non-empty when calling this function
    // so it doesn't check for `if (!partialE164Number)`.
    if (partialE164Number === '+') {
        // Don't change the currently selected country yet.
        return country;
    }
    var derived_country = getCountryFromPossiblyIncompleteInternationalPhoneNumber(partialE164Number, metadata);
    // If a phone number is being input in international form
    // and the country can already be derived from it,
    // then select that country.
    if (derived_country) {
        if (!countries || countries.indexOf(derived_country) >= 0) {
            return derived_country;
        } else {
            return undefined;
        }
    } else if (country) {
        // If the international phone number entered could still correspond to the previously selected country
        // and also to some other country or countries corresponding to the same calling code
        // then it should reset the currently selected country to reflect the ambiguity.
        if (couldNumberBelongToCountry(partialE164Number, country, metadata)) {
            // Reset the country either to the latest one that was manually selected by the user
            // or to the default country or just reset the country selection.
            if (latestCountrySelectedByUser && couldNumberBelongToCountry(partialE164Number, latestCountrySelectedByUser, metadata)) {
                return latestCountrySelectedByUser;
            } else if (defaultCountry && couldNumberBelongToCountry(partialE164Number, defaultCountry, metadata)) {
                return defaultCountry;
            } else {
                if (!required) {
                    // Just reset the currently selected country.
                    return undefined;
                }
            }
        } else {
            // If "International" country option has not been disabled
            // and the international phone number entered doesn't necessarily correspond to
            // the currently selected country and it could not possibly correspond to it
            // then reset the currently selected country.
            if (!required) {
                return undefined;
            }
        }
    }
    // Don't change the currently selected country.
    return country;
}
function onPhoneDigitsChange(phoneDigits, _ref5) {
    var prevPhoneDigits = _ref5.prevPhoneDigits, country = _ref5.country, defaultCountry = _ref5.defaultCountry, latestCountrySelectedByUser = _ref5.latestCountrySelectedByUser, countryRequired = _ref5.countryRequired, getAnyCountry = _ref5.getAnyCountry, countries = _ref5.countries, international = _ref5.international, limitMaxLength = _ref5.limitMaxLength, countryCallingCodeEditable = _ref5.countryCallingCodeEditable, metadata = _ref5.metadata;
    // When the input is in `international` and `countryCallingCodeEditable={false}` mode,
    // the `country` should not change. If the user attempted to overwrite the country callling code part,
    // the component should reset it back to the correct country calling code for the `country`.
    if (international && countryCallingCodeEditable === false) {
        if (country) {
            // For international phone numbers written with non-editable country calling code,
            // the `<input/>` value must always start with that non-editable country calling code.
            var prefix = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$getInternationalPhoneNumberPrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(country, metadata);
            // If the input value doesn't start with the non-editable country calling code,
            // it should be fixed.
            if (phoneDigits.indexOf(prefix) !== 0) {
                var _value;
                // If a phone number input is declared as
                // `international: true` and `countryCallingCodeEditable: false`,
                // then the value of the `<input/>` is gonna be non-empty at all times,
                // even before the user has started to input any digits in the input field,
                // because the country calling code is always there by design.
                //
                // The fact that the input value is always non-empty results in a side effect:
                // whenever a user tabs into such input field, its value gets automatically selected.
                // If at that moment in time the user starts typing in the national digits of the phone number,
                // the selected `<input/>` value gets automatically replaced by those typed-in digits
                // so the value changes from `+xxx` to `y`, because inputting anything while having
                // the `<input/>` value selected results in erasing that `<input/>` value.
                //
                // This component handles such cases by restoring the `<input/>` value to what
                // it should be in such cases: `+xxxy`.
                // https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/43
                //
                var hasStartedTypingInNationalNumberDigitsHavingInputValueSelected = phoneDigits && phoneDigits[0] !== '+';
                if (hasStartedTypingInNationalNumberDigitsHavingInputValueSelected) {
                    // Fix the input value to what it should be: `y` → `+xxxy`.
                    phoneDigits = prefix + phoneDigits;
                    _value = e164(phoneDigits, country, metadata);
                } else {
                    // In other cases, simply reset the `<input/>` value, because there're only two
                    // possible cases:
                    // * The user has selected the `<input/>` value and then hit Delete/Backspace to erase it.
                    // * The user has pasted an international phone number for another country calling code,
                    //   which is considered a non-valid value.
                    phoneDigits = prefix;
                }
                return {
                    phoneDigits: phoneDigits,
                    value: _value,
                    country: country
                };
            }
        }
    }
    // If `international` property is `false`, then it means
    // "enforce national-only format during input",
    // so, if that's the case, then remove all `+` characters,
    // but only if some country is currently selected.
    // (not if "International" country is selected).
    if (international === false && country && phoneDigits && phoneDigits[0] === '+') {
        phoneDigits = convertInternationalPhoneDigitsToNational(phoneDigits, country, metadata);
    }
    // Trim the input to not exceed the maximum possible number length.
    if (phoneDigits && country && limitMaxLength) {
        phoneDigits = trimNumber(phoneDigits, country, metadata);
    }
    // If this `onChange()` event was triggered
    // as a result of selecting "International" country,
    // then force-prepend a `+` sign if the phone number
    // `<input/>` value isn't in international format.
    // Also, force-prepend a `+` sign if international
    // phone number input format is set.
    if (phoneDigits && phoneDigits[0] !== '+' && (!country || international)) {
        phoneDigits = '+' + phoneDigits;
    }
    // If the previously entered phone number
    // has been entered in international format
    // and the user decides to erase it,
    // then also reset the `country`
    // because it was most likely automatically selected
    // while the user was typing in the phone number
    // in international format.
    // This fixes the issue when a user is presented
    // with a phone number input with no country selected
    // and then types in their local phone number
    // then discovers that the input's messed up
    // (a `+` has been prepended at the start of their input
    //  and a random country has been selected),
    // decides to undo it all by erasing everything
    // and then types in their local phone number again
    // resulting in a seemingly correct phone number
    // but in reality that phone number has incorrect country.
    // https://github.com/catamphetamine/react-phone-number-input/issues/273
    if (!phoneDigits && prevPhoneDigits && prevPhoneDigits[0] === '+') {
        if (international) {
            country = undefined;
        } else {
            country = defaultCountry;
        }
    }
    // Also resets such "randomly" selected country
    // as soon as the user erases the number
    // digit-by-digit up to the leading `+` sign.
    if (phoneDigits === '+' && prevPhoneDigits && prevPhoneDigits[0] === '+' && prevPhoneDigits.length > '+'.length) {
        country = undefined;
    }
    // Generate the new `value` property.
    var value;
    if (phoneDigits) {
        if (phoneDigits[0] === '+') {
            if (phoneDigits === '+') {
                value = undefined;
            } else if (country && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$getInternationalPhoneNumberPrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(country, metadata).indexOf(phoneDigits) === 0) {
                // Selected a `country` and started inputting an
                // international phone number for this country
                // but hasn't input any "national (significant) number" digits yet.
                // In that case, assume `value` be `undefined`.
                //
                // For example, if selected `country` `"US"`
                // and started inputting phone number `"+1"`
                // then `value` `undefined` will be returned from this function.
                //
                value = undefined;
            } else {
                value = e164(phoneDigits, country, metadata);
            }
        } else {
            value = e164(phoneDigits, country, metadata);
        }
    }
    // Derive the country from the phone number.
    // (regardless of whether there's any country currently selected,
    //  because there could be several countries corresponding to one country calling code)
    if (value) {
        country = getCountryForPartialE164Number(value, {
            country: country,
            countries: countries,
            defaultCountry: defaultCountry,
            latestCountrySelectedByUser: latestCountrySelectedByUser,
            // `countryRequired` flag is not passed here.
            // Instead, it's explicitly checked a bit later in the code.
            required: false,
            metadata: metadata
        });
        // If `international` property is `false`, then it means
        // "enforce national-only format during input",
        // so, if that's the case, then remove all `+` characters,
        // but only if some country is currently selected.
        // (not if "International" country is selected).
        if (international === false && country && phoneDigits && phoneDigits[0] === '+') {
            phoneDigits = convertInternationalPhoneDigitsToNational(phoneDigits, country, metadata);
            // Re-calculate `value` because `phoneDigits` has changed.
            value = e164(phoneDigits, country, metadata);
        }
    }
    if (!country && countryRequired) {
        country = defaultCountry || getAnyCountry();
    }
    return {
        // `phoneDigits` returned here are a "normalized" version of the original `phoneDigits`.
        // The returned `phoneDigits` shouldn't be used anywhere except for passing it as
        // `prevPhoneDigits` parameter to this same function on next input change event.
        phoneDigits: phoneDigits,
        country: country,
        value: value
    };
}
function convertInternationalPhoneDigitsToNational(input, country, metadata) {
    // Handle the case when a user might have pasted
    // a phone number in international format.
    if (input.indexOf((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$getInternationalPhoneNumberPrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(country, metadata)) === 0) {
        // Create "as you type" formatter.
        var formatter = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$AsYouType$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AsYouType$3e$__["AsYouType"](country, metadata);
        // Input partial national phone number.
        formatter.input(input);
        // Return the parsed partial national phone number.
        var phoneNumber = formatter.getNumber();
        if (phoneNumber) {
            // Transform the number to a national one,
            // and remove all non-digits.
            return phoneNumber.formatNational().replace(/\D/g, '');
        } else {
            return '';
        }
    } else {
        // Just remove the `+` sign.
        return input.replace(/\D/g, '');
    }
}
function getCountryFromPossiblyIncompleteInternationalPhoneNumber(number, metadata) {
    var formatter = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$AsYouType$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AsYouType$3e$__["AsYouType"](null, metadata);
    formatter.input(number);
    // // `001` is a special "non-geograpical entity" code
    // // in Google's `libphonenumber` library.
    // if (formatter.getCountry() === '001') {
    // 	return
    // }
    return formatter.getCountry();
}
function compareStrings(a, b, locales) {
    // Use `String.localeCompare` if it's available.
    // https://developer.mozilla.org/ru/docs/Web/JavaScript/Reference/Global_Objects/String/localeCompare
    // Which means everyone except IE <= 10 and Safari <= 10.
    // `localeCompare()` is available in latest Node.js versions.
    /* istanbul ignore else */ if (String.prototype.localeCompare) {
        return a.localeCompare(b, locales);
    }
    /* istanbul ignore next */ return a < b ? -1 : a > b ? 1 : 0;
}
function stripCountryCallingCode(number, country, metadata) {
    // Just an optimization, so that it
    // doesn't have to iterate through all country calling codes.
    if (country) {
        var countryCallingCodePrefix = '+' + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getCountryCallingCode"])(country, metadata);
        // If `country` fits the actual `number`.
        if (number.length < countryCallingCodePrefix.length) {
            if (countryCallingCodePrefix.indexOf(number) === 0) {
                return '';
            }
        } else {
            if (number.indexOf(countryCallingCodePrefix) === 0) {
                return number.slice(countryCallingCodePrefix.length);
            }
        }
    }
    // If `country` doesn't fit the actual `number`.
    // Try all available country calling codes.
    for(var _i = 0, _Object$keys = Object.keys(metadata.country_calling_codes); _i < _Object$keys.length; _i++){
        var country_calling_code = _Object$keys[_i];
        if (number.indexOf(country_calling_code) === '+'.length) {
            return number.slice('+'.length + country_calling_code.length);
        }
    }
    return '';
}
function getNationalSignificantNumberDigits(number, country, metadata) {
    // Create "as you type" formatter.
    var formatter = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$AsYouType$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AsYouType$3e$__["AsYouType"](country, metadata);
    // Input partial national phone number.
    formatter.input(number);
    // Return the parsed partial national phone number.
    var phoneNumber = formatter.getNumber();
    return phoneNumber && phoneNumber.nationalNumber;
}
function couldNumberBelongToCountry(number, country, metadata) {
    var intlPhoneNumberPrefix = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$getInternationalPhoneNumberPrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(country, metadata);
    var i = 0;
    while(i < number.length && i < intlPhoneNumberPrefix.length){
        if (number[i] !== intlPhoneNumberPrefix[i]) {
            return false;
        }
        i++;
    }
    return true;
}
function getInitialPhoneDigits(_ref6) {
    var value = _ref6.value, phoneNumber = _ref6.phoneNumber, defaultCountry = _ref6.defaultCountry, international = _ref6.international, useNationalFormat = _ref6.useNationalFormat, metadata = _ref6.metadata;
    // If the `value` (E.164 phone number)
    // belongs to the currently selected country
    // and `useNationalFormat` is `true`
    // then convert `value` (E.164 phone number)
    // to a local phone number digits.
    // E.g. '+78005553535' -> '88005553535'.
    if ((international === false || useNationalFormat) && phoneNumber && phoneNumber.country) {
        return generateNationalNumberDigits(phoneNumber);
    }
    // If `international` property is `true`,
    // meaning "enforce international phone number format",
    // then always show country calling code in the input field.
    if (!value && international && defaultCountry) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$getInternationalPhoneNumberPrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(defaultCountry, metadata);
    }
    return value;
} // function doesIncompletePhoneNumberCorrespondToASingleCountry(value, metadata) {
 // 	// Create "as you type" formatter.
 // 	const formatter = new AsYouType(undefined, metadata)
 // 	// Input partial national phone number.
 // 	formatter.input(value)
 // 	// Return the parsed partial national phone number.
 // 	const phoneNumber = formatter.getNumber()
 // 	if (phoneNumber) {
 // 		return phoneNumber.getPossibleCountries().length === 1
 // 	} else {
 // 		return false
 // 	}
 // }
 //# sourceMappingURL=phoneInputHelpers.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/helpers/getPhoneInputWithCountryStateUpdateFromNewProps.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>getPhoneInputWithCountryStateUpdateFromNewProps),
    "valuesAreEqual": (()=>valuesAreEqual)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/helpers/phoneInputHelpers.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$isE164Number$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/helpers/isE164Number.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$getInternationalPhoneNumberPrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/helpers/getInternationalPhoneNumberPrefix.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$countries$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/helpers/countries.js [client] (ecmascript) <locals>");
function _typeof(o) {
    "@babel/helpers - typeof";
    return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o) {
        return typeof o;
    } : function(o) {
        return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;
    }, _typeof(o);
}
function ownKeys(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
function _defineProperty(obj, key, value) {
    key = _toPropertyKey(key);
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _toPropertyKey(t) {
    var i = _toPrimitive(t, "string");
    return "symbol" == _typeof(i) ? i : i + "";
}
function _toPrimitive(t, r) {
    if ("object" != _typeof(t) || !t) return t;
    var e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        var i = e.call(t, r || "default");
        if ("object" != _typeof(i)) return i;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return ("string" === r ? String : Number)(t);
}
;
;
;
;
function getPhoneInputWithCountryStateUpdateFromNewProps(props, prevProps, state) {
    var metadata = props.metadata, countries = props.countries, newDefaultCountry = props.defaultCountry, newValue = props.value, newReset = props.reset, international = props.international, displayInitialValueAsLocalNumber = props.displayInitialValueAsLocalNumber, initialValueFormat = props.initialValueFormat;
    var prevDefaultCountry = prevProps.defaultCountry, prevValue = prevProps.value, prevReset = prevProps.reset;
    var country = state.country, value = state.value, hasUserSelectedACountry = state.hasUserSelectedACountry, latestCountrySelectedByUser = state.latestCountrySelectedByUser;
    var _getInitialPhoneDigits = function _getInitialPhoneDigits(parameters) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getInitialPhoneDigits"])(_objectSpread(_objectSpread({}, parameters), {}, {
            international: international,
            useNationalFormat: displayInitialValueAsLocalNumber || initialValueFormat === 'national',
            metadata: metadata
        }));
    };
    // Some users requested a way to reset the component
    // (both number `<input/>` and country `<select/>`).
    // Whenever `reset` property changes both number `<input/>`
    // and country `<select/>` are reset.
    // It's not implemented as some instance `.reset()` method
    // because `ref` is forwarded to `<input/>`.
    // It's also not replaced with just resetting `country` on
    // external `value` reset, because a user could select a country
    // and then not input any `value`, and so the selected country
    // would be "stuck", if not using this `reset` property.
    // https://github.com/catamphetamine/react-phone-number-input/issues/300
    if (newReset !== prevReset) {
        return {
            phoneDigits: _getInitialPhoneDigits({
                value: undefined,
                defaultCountry: newDefaultCountry
            }),
            value: undefined,
            country: newDefaultCountry,
            latestCountrySelectedByUser: undefined,
            hasUserSelectedACountry: undefined
        };
    }
    // `value` is the value currently shown in the component:
    // it's stored in the component's `state`, and it's not the `value` property.
    // `prevValue` is "previous `value` property".
    // `newValue` is "new `value` property".
    // If the default country changed
    // (e.g. in case of ajax GeoIP detection after page loaded)
    // then select it, but only if the user hasn't already manually
    // selected a country, and no phone number has been manually entered so far.
    // Because if the user has already started inputting a phone number
    // then they're okay with no country being selected at all ("International")
    // and they don't want to be disturbed, don't want their input to be screwed, etc.
    if (newDefaultCountry !== prevDefaultCountry) {
        var isNewDefaultCountrySupported = !newDefaultCountry || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$countries$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isCountrySupportedWithError"])(newDefaultCountry, metadata);
        var noValueHasBeenEnteredByTheUser = // By default, "no value has been entered" means `value` is `undefined`.
        !value || // When `international` is `true`, and some country has been pre-selected,
        // then the `<input/>` contains a pre-filled value of `+${countryCallingCode}${leadingDigits}`,
        // so in case of `international` being `true`, "the user hasn't entered anything" situation
        // doesn't just mean `value` is `undefined`, but could also mean `value` is `+${countryCallingCode}`.
        international && value === _getInitialPhoneDigits({
            value: undefined,
            defaultCountry: prevDefaultCountry
        });
        // Only update the `defaultCountry` property if no phone number
        // has been entered by the user or pre-set by the application.
        var noValueHasBeenEntered = !newValue && noValueHasBeenEnteredByTheUser;
        if (!hasUserSelectedACountry && isNewDefaultCountrySupported && noValueHasBeenEntered) {
            return {
                country: newDefaultCountry,
                // If `phoneDigits` is empty, then automatically select the new `country`
                // and set `phoneDigits` to `+{getCountryCallingCode(newCountry)}`.
                // The code assumes that "no phone number has been entered by the user",
                // and no `value` property has been passed, so the `phoneNumber` parameter
                // of `_getInitialPhoneDigits({ value, phoneNumber, ... })` is `undefined`.
                phoneDigits: _getInitialPhoneDigits({
                    value: undefined,
                    defaultCountry: newDefaultCountry
                }),
                // `value` is `undefined` and it stays so.
                value: undefined
            };
        }
    }
    // If a new `value` is set externally.
    // (e.g. as a result of an ajax API request
    //  to get user's phone after page loaded)
    // The first part — `newValue !== prevValue` —
    // is basically `props.value !== prevProps.value`
    // so it means "if value property was changed externally".
    // The second part — `newValue !== value` —
    // is for ignoring the `getDerivedStateFromProps()` call
    // which happens in `this.onChange()` right after `this.setState()`.
    // If this `getDerivedStateFromProps()` call isn't ignored
    // then the country flag would reset on each input.
    if (!valuesAreEqual(newValue, prevValue) && !valuesAreEqual(newValue, value)) {
        var phoneNumber;
        var parsedCountry;
        if (newValue) {
            // Validate that the newly-supplied `value` is in `E.164` format.
            // Because sometimes people attempt to supply a `value` like "+****************".
            // https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/231#note_2016334796
            if (newValue) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$isE164Number$2e$js__$5b$client$5d$__$28$ecmascript$29$__["validateE164Number"])(newValue);
            }
            phoneNumber = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__["parsePhoneNumber"])(newValue, metadata);
            var supportedCountries = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$countries$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getSupportedCountries"])(countries, metadata);
            if (phoneNumber && phoneNumber.country) {
                // Ignore `else` because all countries are supported in metadata.
                /* istanbul ignore next */ if (!supportedCountries || supportedCountries.indexOf(phoneNumber.country) >= 0) {
                    parsedCountry = phoneNumber.country;
                }
            } else {
                parsedCountry = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getCountryForPartialE164Number"])(newValue, {
                    country: undefined,
                    countries: supportedCountries,
                    metadata: metadata
                });
                // In cases when multiple countries correspond to the same country calling code,
                // the phone number digits of `newValue` have to be matched against country-specific
                // regular expressions in order to determine the exact country.
                // Sometimes, that algorithm can't decide for sure which country does the phone number belong to,
                // for example when the digits of `newValue` don't match any of those regular expressions.
                // and the country of the phone number couldn't be determined.
                // In those cases, people prefer the component to show the flag of the `defaultCountry`
                // if the phone number could potentially belong to that `defaultCountry`.
                // At least that's how the component behaves when a user pastes an international
                // phone number into the input field: for example, when `defaultCountry` is `"US"`
                // and the user pastes value "****** 555 5555" into the input field, it keep showing "US" flag.
                // So when setting new `value` property externally, the component should behave the same way:
                // it should select the `defaultCountry` when the new `value` could potentially belong
                // to that country in cases when the exact country can't be determined.
                // https://github.com/catamphetamine/react-phone-number-input/issues/413#issuecomment-1536219404
                if (!parsedCountry) {
                    if (newDefaultCountry) {
                        if (newValue.indexOf((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$getInternationalPhoneNumberPrefix$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(newDefaultCountry, metadata)) === 0) {
                            parsedCountry = newDefaultCountry;
                        }
                    }
                }
            }
        }
        var userCountrySelectionHistoryStateUpdate;
        if (newValue) {
            // If the latest country that has been manually selected by the user
            // no longer corresponds to the new value then reset it.
            if (latestCountrySelectedByUser) {
                var couldNewValueCorrespondToLatestCountrySelectedByUser = parsedCountry ? latestCountrySelectedByUser === parsedCountry : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__["couldNumberBelongToCountry"])(newValue, latestCountrySelectedByUser, metadata);
                if (couldNewValueCorrespondToLatestCountrySelectedByUser) {
                    if (!parsedCountry) {
                        parsedCountry = latestCountrySelectedByUser;
                    }
                } else {
                    userCountrySelectionHistoryStateUpdate = {
                        latestCountrySelectedByUser: undefined
                    };
                }
            }
        } else {
            // When the `value` property is being reset "externally",
            // reset any tracking of the country that the user has previously selected.
            userCountrySelectionHistoryStateUpdate = {
                latestCountrySelectedByUser: undefined,
                hasUserSelectedACountry: undefined
            };
        }
        return _objectSpread(_objectSpread({}, userCountrySelectionHistoryStateUpdate), {}, {
            phoneDigits: _getInitialPhoneDigits({
                phoneNumber: phoneNumber,
                value: newValue,
                defaultCountry: newDefaultCountry
            }),
            value: newValue,
            country: newValue ? parsedCountry : newDefaultCountry
        });
    }
// `defaultCountry` didn't change.
// `value` didn't change.
// `phoneDigits` didn't change, because `value` didn't change.
//
// So no need to update state.
}
function valuesAreEqual(value1, value2) {
    // If `value` has been set to `null` externally then convert it to `undefined`.
    //
    // For example, `react-hook-form` sets `value` to `null` when the user clears the input.
    // https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/164
    // In that case, without this conversion of `null` to `undefined`, it would reset
    // the selected country to `defaultCountry` because in that case `newValue !== value`
    // because `null !== undefined`.
    //
    // Historically, empty `value` is encoded as `undefined`.
    // Perhaps empty `value` would be better encoded as `null` instead.
    // But because that would be a potentially breaking change for some people,
    // it's left as is for the current "major" version of this library.
    //
    if (value1 === null) {
        value1 = undefined;
    }
    if (value2 === null) {
        value2 = undefined;
    }
    return value1 === value2;
} //# sourceMappingURL=getPhoneInputWithCountryStateUpdateFromNewProps.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/PhoneInputWithCountry.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prop-types/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$InputSmart$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/InputSmart.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$InputBasic$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/InputBasic.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$CountrySelect$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/CountrySelect.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$Flag$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/Flag.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$InternationalIcon$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/InternationalIcon.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$isE164Number$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/helpers/isE164Number.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$countries$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/helpers/countries.js [client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$getCountries$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__getCountries$3e$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/getCountries.js [client] (ecmascript) <export default as getCountries>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$CountryIcon$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/CountryIcon.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$useExternalRef$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/useExternalRef.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$PropTypes$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/PropTypes.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/helpers/phoneInputHelpers.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$getPhoneInputWithCountryStateUpdateFromNewProps$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/helpers/getPhoneInputWithCountryStateUpdateFromNewProps.js [client] (ecmascript)");
var _excluded = [
    "name",
    "disabled",
    "readOnly",
    "autoComplete",
    "style",
    "className",
    "inputRef",
    "inputComponent",
    "numberInputProps",
    "smartCaret",
    "countrySelectComponent",
    "countrySelectProps",
    "containerComponent",
    "containerComponentProps",
    "defaultCountry",
    "countries",
    "countryOptionsOrder",
    "labels",
    "flags",
    "flagComponent",
    "flagUrl",
    "addInternationalOption",
    "internationalIcon",
    "displayInitialValueAsLocalNumber",
    "initialValueFormat",
    "onCountryChange",
    "limitMaxLength",
    "countryCallingCodeEditable",
    "focusInputOnCountrySelection",
    "reset",
    "metadata",
    "international",
    "locales"
];
function _typeof(o) {
    "@babel/helpers - typeof";
    return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o) {
        return typeof o;
    } : function(o) {
        return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;
    }, _typeof(o);
}
function ownKeys(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
function _extends() {
    _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined);
    return _extends.apply(this, arguments);
}
function _objectWithoutProperties(source, excluded) {
    if (source == null) return {};
    var target = _objectWithoutPropertiesLoose(source, excluded);
    var key, i;
    if (Object.getOwnPropertySymbols) {
        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
        for(i = 0; i < sourceSymbolKeys.length; i++){
            key = sourceSymbolKeys[i];
            if (excluded.indexOf(key) >= 0) continue;
            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
            target[key] = source[key];
        }
    }
    return target;
}
function _objectWithoutPropertiesLoose(source, excluded) {
    if (source == null) return {};
    var target = {};
    var sourceKeys = Object.keys(source);
    var key, i;
    for(i = 0; i < sourceKeys.length; i++){
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
    }
    return target;
}
function _classCallCheck(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);
    }
}
function _createClass(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    Object.defineProperty(Constructor, "prototype", {
        writable: false
    });
    return Constructor;
}
function _callSuper(t, o, e) {
    return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));
}
function _possibleConstructorReturn(self, call) {
    if (call && (_typeof(call) === "object" || typeof call === "function")) {
        return call;
    } else if (call !== void 0) {
        throw new TypeError("Derived constructors may only return object or undefined");
    }
    return _assertThisInitialized(self);
}
function _assertThisInitialized(self) {
    if (self === void 0) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    }
    return self;
}
function _isNativeReflectConstruct() {
    try {
        var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
    } catch (t) {}
    return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {
        return !!t;
    })();
}
function _getPrototypeOf(o) {
    _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
    };
    return _getPrototypeOf(o);
}
function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }
    subClass.prototype = Object.create(superClass && superClass.prototype, {
        constructor: {
            value: subClass,
            writable: true,
            configurable: true
        }
    });
    Object.defineProperty(subClass, "prototype", {
        writable: false
    });
    if (superClass) _setPrototypeOf(subClass, superClass);
}
function _setPrototypeOf(o, p) {
    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _setPrototypeOf(o, p);
}
function _defineProperty(obj, key, value) {
    key = _toPropertyKey(key);
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _toPropertyKey(t) {
    var i = _toPrimitive(t, "string");
    return "symbol" == _typeof(i) ? i : i + "";
}
function _toPrimitive(t, r) {
    if ("object" != _typeof(t) || !t) return t;
    var e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        var i = e.call(t, r || "default");
        if ("object" != _typeof(i)) return i;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return ("string" === r ? String : Number)(t);
}
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
var PhoneNumberInput_ = /*#__PURE__*/ function(_React$PureComponent) {
    function PhoneNumberInput_(props) {
        var _this;
        _classCallCheck(this, PhoneNumberInput_);
        _this = _callSuper(this, PhoneNumberInput_, [
            props
        ]);
        // This function mimicks `refSetter` function returned from `useExternalRef()` hook
        // because this class-like React component can't use the `useExternalRef()` hook.
        _defineProperty(_this, "setInputRef", function(instance) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$useExternalRef$2e$js__$5b$client$5d$__$28$ecmascript$29$__["setRefsValue"])([
                _this.props.inputRef,
                _this.inputRef
            ], instance);
        });
        // A shorthand for not passing `metadata` as a second argument.
        _defineProperty(_this, "isCountrySupportedWithError", function(country) {
            var metadata = _this.props.metadata;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$countries$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isCountrySupportedWithError"])(country, metadata);
        });
        // Country `<select/>` `onChange` handler.
        _defineProperty(_this, "onCountryChange", function(newCountry) {
            var _this$props = _this.props, international = _this$props.international, metadata = _this$props.metadata, onChange = _this$props.onChange, focusInputOnCountrySelection = _this$props.focusInputOnCountrySelection;
            var _this$state = _this.state, prevPhoneDigits = _this$state.phoneDigits, prevCountry = _this$state.country;
            // After the new `country` has been selected,
            // if the phone number `<input/>` holds any digits
            // then migrate those digits for the new `country`.
            var newPhoneDigits = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getPhoneDigitsForNewCountry"])(prevPhoneDigits, {
                prevCountry: prevCountry,
                newCountry: newCountry,
                metadata: metadata,
                // Convert the phone number to "national" format
                // when the user changes the selected country by hand.
                useNationalFormat: !international
            });
            var newValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__["e164"])(newPhoneDigits, newCountry, metadata);
            // Focus phone number `<input/>` upon country selection.
            if (focusInputOnCountrySelection) {
                _this.inputRef.current.focus();
            }
            // If the user has already manually selected a country
            // then don't override that already selected country
            // if the `defaultCountry` property changes.
            // That's what `hasUserSelectedACountry` flag is for.
            _this.setState({
                country: newCountry,
                latestCountrySelectedByUser: newCountry,
                hasUserSelectedACountry: true,
                phoneDigits: newPhoneDigits,
                value: newValue
            }, function() {
                // Update the new `value` property.
                // Doing it after the `state` has been updated
                // because `onChange()` will trigger `getDerivedStateFromProps()`
                // with the new `value` which will be compared to `state.value` there.
                onChange(newValue);
            });
        });
        /**
     * `<input/>` `onChange()` handler.
     * Updates `value` property accordingly (so that they are kept in sync).
     * @param {string?} input — Either a parsed phone number or an empty string. Examples: `""`, `"+"`, `"+123"`, `"123"`.
     */ _defineProperty(_this, "onChange", function(_phoneDigits) {
            var _this$props2 = _this.props, defaultCountry = _this$props2.defaultCountry, onChange = _this$props2.onChange, addInternationalOption = _this$props2.addInternationalOption, international = _this$props2.international, limitMaxLength = _this$props2.limitMaxLength, countryCallingCodeEditable = _this$props2.countryCallingCodeEditable, metadata = _this$props2.metadata;
            var _this$state2 = _this.state, countries = _this$state2.countries, prevPhoneDigits = _this$state2.phoneDigits, currentlySelectedCountry = _this$state2.country, latestCountrySelectedByUser = _this$state2.latestCountrySelectedByUser;
            var _onPhoneDigitsChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__["onPhoneDigitsChange"])(_phoneDigits, {
                prevPhoneDigits: prevPhoneDigits,
                country: currentlySelectedCountry,
                countryRequired: !addInternationalOption,
                defaultCountry: defaultCountry,
                latestCountrySelectedByUser: latestCountrySelectedByUser,
                getAnyCountry: function getAnyCountry() {
                    return _this.getFirstSupportedCountry({
                        countries: countries
                    });
                },
                countries: countries,
                international: international,
                limitMaxLength: limitMaxLength,
                countryCallingCodeEditable: countryCallingCodeEditable,
                metadata: metadata
            }), phoneDigits = _onPhoneDigitsChange.phoneDigits, country = _onPhoneDigitsChange.country, value = _onPhoneDigitsChange.value;
            var stateUpdate = {
                phoneDigits: phoneDigits,
                value: value,
                country: country
            };
            // Reset `latestCountrySelectedByUser` if it no longer fits the `value`.
            if (latestCountrySelectedByUser && value && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__["couldNumberBelongToCountry"])(value, latestCountrySelectedByUser, metadata)) {
                stateUpdate.latestCountrySelectedByUser = undefined;
            }
            if (countryCallingCodeEditable === false) {
                // If it simply did `setState({ phoneDigits: intlPrefix })` here,
                // then it would have no effect when erasing an inital international prefix
                // via Backspace, because `phoneDigits` in `state` wouldn't change
                // as a result, because it was `prefix` and it became `prefix`,
                // so the component wouldn't rerender, and the user would be able
                // to erase the country calling code part, and that part is
                // assumed to be non-eraseable. That's why the component is
                // forcefully rerendered here.
                // https://github.com/catamphetamine/react-phone-number-input/issues/367#issuecomment-721703501
                if (!value && phoneDigits === _this.state.phoneDigits) {
                    // Force a re-render of the `<input/>` in order to reset its value.
                    stateUpdate.forceRerender = {};
                }
            }
            _this.setState(stateUpdate, // Update the new `value` property.
            // Doing it after the `state` has been updated
            // because `onChange()` will trigger `getDerivedStateFromProps()`
            // with the new `value` which will be compared to `state.value` there.
            function() {
                return onChange(value);
            });
        });
        // Toggles the `--focus` CSS class.
        _defineProperty(_this, "_onFocus", function() {
            return _this.setState({
                isFocused: true
            });
        });
        // Toggles the `--focus` CSS class.
        _defineProperty(_this, "_onBlur", function() {
            return _this.setState({
                isFocused: false
            });
        });
        _defineProperty(_this, "onFocus", function(event) {
            _this._onFocus();
            var onFocus = _this.props.onFocus;
            if (onFocus) {
                onFocus(event);
            }
        });
        _defineProperty(_this, "onBlur", function(event) {
            var onBlur = _this.props.onBlur;
            _this._onBlur();
            if (onBlur) {
                onBlur(event);
            }
        });
        _defineProperty(_this, "onCountryFocus", function(event) {
            _this._onFocus();
            // this.setState({ countrySelectFocused: true })
            var countrySelectProps = _this.props.countrySelectProps;
            if (countrySelectProps) {
                var onFocus = countrySelectProps.onFocus;
                if (onFocus) {
                    onFocus(event);
                }
            }
        });
        _defineProperty(_this, "onCountryBlur", function(event) {
            _this._onBlur();
            // this.setState({ countrySelectFocused: false })
            var countrySelectProps = _this.props.countrySelectProps;
            if (countrySelectProps) {
                var onBlur = countrySelectProps.onBlur;
                if (onBlur) {
                    onBlur(event);
                }
            }
        });
        _this.inputRef = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createRef();
        var _this$props3 = _this.props, _value = _this$props3.value, labels = _this$props3.labels, _international = _this$props3.international, _addInternationalOption = _this$props3.addInternationalOption, displayInitialValueAsLocalNumber = _this$props3.displayInitialValueAsLocalNumber, initialValueFormat = _this$props3.initialValueFormat, _metadata = _this$props3.metadata;
        var _this$props4 = _this.props, _defaultCountry = _this$props4.defaultCountry, _countries = _this$props4.countries;
        // Validate `defaultCountry`.
        if (_defaultCountry) {
            if (!_this.isCountrySupportedWithError(_defaultCountry)) {
                _defaultCountry = undefined;
            }
        }
        // Validate that the initially-supplied `value` is in `E.164` format.
        // Because sometimes people attempt to supply a `value` like "+****************".
        // https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/231#note_2016334796
        if (_value) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$isE164Number$2e$js__$5b$client$5d$__$28$ecmascript$29$__["validateE164Number"])(_value);
        }
        // Validate `countries`.
        _countries = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$countries$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getSupportedCountries"])(_countries, _metadata);
        var phoneNumber = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__["parsePhoneNumber"])(_value, _metadata);
        _this.CountryIcon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$CountryIcon$2e$js__$5b$client$5d$__$28$ecmascript$29$__["createCountryIconComponent"])(_this.props);
        var preSelectedCountry = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getPreSelectedCountry"])({
            value: _value,
            phoneNumber: phoneNumber,
            defaultCountry: _defaultCountry,
            required: !_addInternationalOption,
            countries: _countries || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$getCountries$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__getCountries$3e$__["getCountries"])(_metadata),
            getAnyCountry: function getAnyCountry() {
                return _this.getFirstSupportedCountry({
                    countries: _countries
                });
            },
            metadata: _metadata
        });
        _this.state = {
            // Workaround for `this.props` inside `getDerivedStateFromProps()`.
            props: _this.props,
            // The country selected.
            country: preSelectedCountry,
            // `countries` are stored in `this.state` because they're filtered.
            // For example, a developer might theoretically pass some unsupported
            // countries as part of the `countries` property, and because of that
            // the component uses `this.state.countries` (which are filtered)
            // instead of `this.props.countries`
            // (which could potentially contain unsupported countries).
            countries: _countries,
            // `phoneDigits` state property holds non-formatted user's input.
            // The reason is that there's no way of finding out
            // in which form should `value` be displayed: international or national.
            // E.g. if `value` is `+78005553535` then it could be input
            // by a user both as `8 (800) 555-35-35` and `****** 555 35 35`.
            // Hence storing just `value` is not sufficient for correct formatting.
            // E.g. if a user entered `8 (800) 555-35-35`
            // then value is `+78005553535` and `phoneDigits` are `88005553535`
            // and if a user entered `****** 555 35 35`
            // then value is `+78005553535` and `phoneDigits` are `+78005553535`.
            phoneDigits: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getInitialPhoneDigits"])({
                value: _value,
                phoneNumber: phoneNumber,
                defaultCountry: _defaultCountry,
                international: _international,
                useNationalFormat: displayInitialValueAsLocalNumber || initialValueFormat === 'national',
                metadata: _metadata
            }),
            // `value` property is duplicated in state.
            // The reason is that `getDerivedStateFromProps()`
            // needs this `value` to compare to the new `value` property
            // to find out if `phoneDigits` needs updating:
            // If the `value` property was changed externally
            // then it won't be equal to `state.value`
            // in which case `phoneDigits` and `country` should be updated.
            value: _value
        };
        return _this;
    }
    _inherits(PhoneNumberInput_, _React$PureComponent);
    return _createClass(PhoneNumberInput_, [
        {
            key: "componentDidMount",
            value: function componentDidMount() {
                var onCountryChange = this.props.onCountryChange;
                var defaultCountry = this.props.defaultCountry;
                var selectedCountry = this.state.country;
                if (onCountryChange) {
                    if (defaultCountry) {
                        if (!this.isCountrySupportedWithError(defaultCountry)) {
                            defaultCountry = undefined;
                        }
                    }
                    if (selectedCountry !== defaultCountry) {
                        onCountryChange(selectedCountry);
                    }
                }
            }
        },
        {
            key: "componentDidUpdate",
            value: function componentDidUpdate(prevProps, prevState) {
                var onCountryChange = this.props.onCountryChange;
                var country = this.state.country;
                // Call `onCountryChange` when user selects another country.
                if (onCountryChange && country !== prevState.country) {
                    onCountryChange(country);
                }
            }
        },
        {
            key: "getCountrySelectOptions",
            value: function getCountrySelectOptions(_ref) {
                var countries = _ref.countries;
                var _this$props5 = this.props, international = _this$props5.international, countryCallingCodeEditable = _this$props5.countryCallingCodeEditable, countryOptionsOrder = _this$props5.countryOptionsOrder, addInternationalOption = _this$props5.addInternationalOption, labels = _this$props5.labels, locales = _this$props5.locales, metadata = _this$props5.metadata;
                return this.useMemoCountrySelectOptions({
                    "PhoneNumberInput_.getCountrySelectOptions.useMemoCountrySelectOptions": function() {
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$countries$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["sortCountryOptions"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$phoneInputHelpers$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getCountrySelectOptions"])({
                            countries: countries || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$getCountries$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__getCountries$3e$__["getCountries"])(metadata),
                            countryNames: labels,
                            addInternationalOption: international && countryCallingCodeEditable === false ? false : addInternationalOption,
                            compareStringsLocales: locales
                        }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$countries$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getSupportedCountryOptions"])(countryOptionsOrder, metadata));
                    }
                }["PhoneNumberInput_.getCountrySelectOptions.useMemoCountrySelectOptions"], [
                    countries,
                    countryOptionsOrder,
                    addInternationalOption,
                    labels,
                    metadata
                ]);
            }
        },
        {
            key: "useMemoCountrySelectOptions",
            value: function useMemoCountrySelectOptions(generator, dependencies) {
                if (!this.countrySelectOptionsMemoDependencies || !areEqualArrays(dependencies, this.countrySelectOptionsMemoDependencies)) {
                    this.countrySelectOptionsMemo = generator();
                    this.countrySelectOptionsMemoDependencies = dependencies;
                }
                return this.countrySelectOptionsMemo;
            }
        },
        {
            key: "getFirstSupportedCountry",
            value: function getFirstSupportedCountry(_ref2) {
                var countries = _ref2.countries;
                var countryOptions = this.getCountrySelectOptions({
                    countries: countries
                });
                return countryOptions[0].value;
            }
        },
        {
            key: "render",
            value: function render() {
                var _this$props6 = this.props, name = _this$props6.name, disabled = _this$props6.disabled, readOnly = _this$props6.readOnly, autoComplete = _this$props6.autoComplete, style = _this$props6.style, className = _this$props6.className, inputRef = _this$props6.inputRef, inputComponent = _this$props6.inputComponent, numberInputProps = _this$props6.numberInputProps, smartCaret = _this$props6.smartCaret, CountrySelectComponent = _this$props6.countrySelectComponent, countrySelectProps = _this$props6.countrySelectProps, ContainerComponent = _this$props6.containerComponent, containerComponentProps = _this$props6.containerComponentProps, defaultCountry = _this$props6.defaultCountry, countriesProperty = _this$props6.countries, countryOptionsOrder = _this$props6.countryOptionsOrder, labels = _this$props6.labels, flags = _this$props6.flags, flagComponent = _this$props6.flagComponent, flagUrl = _this$props6.flagUrl, addInternationalOption = _this$props6.addInternationalOption, internationalIcon = _this$props6.internationalIcon, displayInitialValueAsLocalNumber = _this$props6.displayInitialValueAsLocalNumber, initialValueFormat = _this$props6.initialValueFormat, onCountryChange = _this$props6.onCountryChange, limitMaxLength = _this$props6.limitMaxLength, countryCallingCodeEditable = _this$props6.countryCallingCodeEditable, focusInputOnCountrySelection = _this$props6.focusInputOnCountrySelection, reset = _this$props6.reset, metadata = _this$props6.metadata, international = _this$props6.international, locales = _this$props6.locales, rest = _objectWithoutProperties(_this$props6, _excluded);
                var _this$state3 = this.state, country = _this$state3.country, countries = _this$state3.countries, phoneDigits = _this$state3.phoneDigits, isFocused = _this$state3.isFocused;
                var InputComponent = smartCaret ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$InputSmart$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$InputBasic$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"];
                var countrySelectOptions = this.getCountrySelectOptions({
                    countries: countries
                });
                return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(ContainerComponent, _extends({
                    style: style,
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(className, 'PhoneInput', {
                        'PhoneInput--focus': isFocused,
                        'PhoneInput--disabled': disabled,
                        'PhoneInput--readOnly': readOnly
                    })
                }, containerComponentProps), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(CountrySelectComponent, _extends({
                    name: name ? "".concat(name, "Country") : undefined,
                    "aria-label": labels.country
                }, countrySelectProps, {
                    value: country,
                    options: countrySelectOptions,
                    onChange: this.onCountryChange,
                    onFocus: this.onCountryFocus,
                    onBlur: this.onCountryBlur,
                    disabled: disabled || countrySelectProps && countrySelectProps.disabled,
                    readOnly: readOnly || countrySelectProps && countrySelectProps.readOnly,
                    iconComponent: this.CountryIcon
                })), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(InputComponent, _extends({
                    ref: this.setInputRef,
                    type: "tel",
                    autoComplete: autoComplete
                }, numberInputProps, rest, {
                    inputFormat: international === true ? 'INTERNATIONAL' : international === false ? 'NATIONAL' : 'INTERNATIONAL_OR_NATIONAL',
                    international: international ? true : undefined,
                    withCountryCallingCode: international ? true : undefined,
                    name: name,
                    metadata: metadata,
                    country: country,
                    value: phoneDigits || '',
                    onChange: this.onChange,
                    onFocus: this.onFocus,
                    onBlur: this.onBlur,
                    disabled: disabled,
                    readOnly: readOnly,
                    inputComponent: inputComponent,
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])('PhoneInputInput', numberInputProps && numberInputProps.className, rest.className)
                })));
            }
        }
    ], [
        {
            key: "getDerivedStateFromProps",
            value: // `state` holds previous props as `props`, and also:
            // * `country` — The currently selected country, e.g. `"RU"`.
            // * `value` — The currently entered phone number (E.164), e.g. `+78005553535`.
            // * `phoneDigits` — The parsed `<input/>` value, e.g. `8005553535`.
            // (and a couple of other less significant properties)
            function getDerivedStateFromProps(props, state) {
                return _objectSpread({
                    // Emulate `prevProps` via `state.props`.
                    props: props
                }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$helpers$2f$getPhoneInputWithCountryStateUpdateFromNewProps$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"])(props, state.props, state));
            }
        }
    ]);
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].PureComponent); // This wrapper is only to `.forwardRef()` to the `<input/>`.
var PhoneNumberInput = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].forwardRef(function(props, ref) {
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(PhoneNumberInput_, _extends({}, withDefaultProps(props), {
        inputRef: ref
    }));
});
PhoneNumberInput.propTypes = {
    /**
   * Phone number in `E.164` format.
   *
   * Example:
   *
   * `"+12223333333"`
   *
   * Any "falsy" value like `undefined`, `null` or an empty string `""` is treated like "empty".
   */ value: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string,
    /**
   * A function of `value: string?`.
   *
   * Updates the `value` property as the user inputs a phone number.
   *
   * If the user erases the input value, the argument is `undefined`.
   */ onChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].func.isRequired,
    /**
   * Toggles the `--focus` CSS class.
   * @ignore
   */ onFocus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].func,
    /**
   * `onBlur` is usually passed by `redux-form`.
   * @ignore
   */ onBlur: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].func,
    /**
   * Set to `true` to mark both the phone number `<input/>`
   * and the country `<select/>` as `disabled`.
   */ disabled: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].bool,
    /**
   * Set to `true` to mark both the phone number `<input/>`
   * and the country `<select/>` as `readonly`.
   */ readOnly: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].bool,
    /**
   * Sets `autoComplete` property for phone number `<input/>`.
   *
   * Web browser's "autocomplete" feature
   * remembers the phone number being input
   * and can also autofill the `<input/>`
   * with previously remembered phone numbers.
   *
   * https://developers.google.com
   * /web/updates/2015/06/checkout-faster-with-autofill
   *
   * For example, can be used to turn it off:
   *
   * "So when should you use `autocomplete="off"`?
   *  One example is when you've implemented your own version
   *  of autocomplete for search. Another example is any form field
   *  where users will input and submit different kinds of information
   *  where it would not be useful to have the browser remember
   *  what was submitted previously".
   */ // (is `"tel"` by default)
    autoComplete: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string,
    /**
   * Set to `"national"` to show the initial `value` in
   * "national" format rather than "international".
   *
   * For example, if `initialValueFormat` is `"national"`
   * and the initial `value="+12133734253"` is passed
   * then the `<input/>` value will be `"(*************"`.
   *
   * By default, `initialValueFormat` is `undefined`,
   * meaning that if the initial `value="+12133734253"` is passed
   * then the `<input/>` value will be `"****** 373 4253"`.
   *
   * The reason for such default behaviour is that
   * the newer generation grows up when there are no stationary phones
   * and therefore everyone inputs phone numbers in international format
   * in their smartphones so people gradually get more accustomed to
   * writing phone numbers in international format rather than in local format.
   * Future people won't be using "national" format, only "international".
   */ // (is `undefined` by default)
    initialValueFormat: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].oneOf([
        'national'
    ]),
    // `displayInitialValueAsLocalNumber` property has been
    // superceded by `initialValueFormat` property.
    displayInitialValueAsLocalNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].bool,
    /**
   * The country to be selected by default.
   * For example, can be set after a GeoIP lookup.
   *
   * Example: `"US"`.
   */ // A two-letter country code ("ISO 3166-1 alpha-2").
    defaultCountry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string,
    /**
   * If specified, only these countries will be available for selection.
   *
   * Example:
   *
   * `["RU", "UA", "KZ"]`
   */ countries: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].arrayOf(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string),
    /**
   * Custom country `<select/>` option names.
   * Also some labels like "ext" and country `<select/>` `aria-label`.
   *
   * Example:
   *
   * `{ "ZZ": "Международный", RU: "Россия", US: "США", ... }`
   *
   * See the `locales` directory for examples.
   */ labels: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$PropTypes$2e$js__$5b$client$5d$__$28$ecmascript$29$__["labels"],
    /**
   * Country `<select/>` options are sorted by their labels.
   * The default sorting function uses `a.localeCompare(b, locales)`,
   * and, if that's not available, falls back to simple `a > b` / `a < b`.
   * Some languages, like Chinese, support multiple sorting variants
   * (called "collations"), and the user might prefer one or another.
   * Also, sometimes the Operating System language is not always
   * the preferred language for a person using a website or an application,
   * so there should be a way to specify custom locale.
   * This `locales` property mimicks the `locales` argument of `Intl` constructors,
   * and can be either a Unicode BCP 47 locale identifier or an array of such locale identifiers.
   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl#locales_argument
   */ locales: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].oneOfType([
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].arrayOf(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string)
    ]),
    /*
   * Custom country `<select/>` options sorting function.
   * The default one uses `a.localeCompare(b)`, and,
   * if that's not available, falls back to simple `a > b`/`a < b`.
   * There have been requests to add custom sorter for cases
   * like Chinese language and "pinyin" (non-default) sorting order.
   * https://stackoverflow.com/questions/22907288/chinese-sorting-by-pinyin-in-javascript-with-localecompare
  compareStrings: PropTypes.func,
   */ /**
   * A URL template of a country flag, where
   * "{XX}" is a two-letter country code in upper case,
   * or where "{xx}" is a two-letter country code in lower case.
   * By default it points to `country-flag-icons` gitlab pages website.
   * I imagine someone might want to download those country flag icons
   * and host them on their own servers instead
   * (all flags are available in the `country-flag-icons` library).
   * There's a catch though: new countries may be added in future,
   * so when hosting country flag icons on your own server
   * one should check the `CHANGELOG.md` every time before updating this library,
   * otherwise there's a possibility that some new country flag would be missing.
   */ flagUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string,
    /**
   * Custom country flag icon components.
   * These flags will be used instead of the default ones.
   * The the "Flags" section of the readme for more info.
   *
   * The shape is an object where keys are country codes
   * and values are flag icon components.
   * Flag icon components receive the same properties
   * as `flagComponent` (see below).
   *
   * Example:
   *
   * `{ "RU": (props) => <img src="..."/> }`
   *
   * Example:
   *
   * `import flags from 'country-flag-icons/react/3x2'`
   *
   * `import PhoneInput from 'react-phone-number-input'`
   *
   * `<PhoneInput flags={flags} .../>`
   */ flags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].objectOf(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].elementType),
    /**
   * Country flag icon component.
   *
   * Takes properties:
   *
   * * `country: string` — The country code.
   * * `countryName: string` — The country name.
   * * `flagUrl: string` — The `flagUrl` property (see above).
   * * `flags: object` — The `flags` property (see above).
   */ flagComponent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].elementType,
    /**
   * Set to `false` to remove the "International" option from country `<select/>`.
   */ addInternationalOption: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].bool,
    /**
   * "International" icon component.
   * Should have the same aspect ratio.
   *
   * Receives properties:
   *
   * * `title: string` — "International" country option label.
   */ internationalIcon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].elementType,
    /**
   * Can be used to place some countries on top of the list of country `<select/>` options.
   *
   * * `"XX"` — inserts an option for "XX" country.
   * * `"🌐"` — inserts "International" option.
   * * `"|"` — inserts a separator.
   * * `"..."` — inserts options for the rest of the countries (can be omitted, in which case it will be automatically added at the end).
   *
   * Example:
   *
   * `["US", "CA", "AU", "|", "..."]`
   */ countryOptionsOrder: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].arrayOf(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string),
    /**
   * `<Phone/>` component CSS style object.
   */ style: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].object,
    /**
   * `<Phone/>` component CSS class.
   */ className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].string,
    /**
   * Country `<select/>` component.
   *
   * Receives properties:
   *
   * * `name: string?` — HTML `name` attribute.
   * * `value: string?` — The currently selected country code.
   * * `onChange(value: string?)` — Updates the `value`.
   * * `onFocus()` — Is used to toggle the `--focus` CSS class.
   * * `onBlur()` — Is used to toggle the `--focus` CSS class.
   * * `options: object[]` — The list of all selectable countries (including "International") each being an object of shape `{ value: string?, label: string }`.
   * * `iconComponent: PropTypes.elementType` — React component that renders a country icon: `<Icon country={value}/>`. If `country` is `undefined` then it renders an "International" icon.
   * * `disabled: boolean?` — HTML `disabled` attribute.
   * * `readOnly: boolean?` — HTML `readOnly` attribute.
   * * `tabIndex: (number|string)?` — HTML `tabIndex` attribute.
   * * `className: string` — CSS class name.
   */ countrySelectComponent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].elementType,
    /**
   * Country `<select/>` component props.
   * Along with the usual DOM properties such as `aria-label` and `tabIndex`,
   * some custom properties are supported, such as `arrowComponent` and `unicodeFlags`.
   */ countrySelectProps: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].object,
    /**
   * Phone number `<input/>` component.
   *
   * Receives properties:
   *
   * * `value: string` — The formatted `value`.
   * * `onChange(event: Event)` — Updates the formatted `value` from `event.target.value`.
   * * `onFocus()` — Is used to toggle the `--focus` CSS class.
   * * `onBlur()` — Is used to toggle the `--focus` CSS class.
   * * Other properties like `type="tel"` or `autoComplete="tel"` that should be passed through to the DOM `<input/>`.
   *
   * Must also either use `React.forwardRef()` to "forward" `ref` to the `<input/>` or implement `.focus()` method.
   */ inputComponent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].elementType,
    /**
   * Phone number `<input/>` component props.
   */ numberInputProps: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].object,
    /**
   * Wrapping `<div/>` component.
   *
   * Receives properties:
   *
   * * `style: object` — A component CSS style object.
   * * `className: string` — Classes to attach to the component, typically changes when component focuses or blurs.
   */ containerComponent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].elementType,
    /**
   * Wrapping `<div/>` component props.
   */ containerComponentProps: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].object,
    /**
   * When the user attempts to insert a digit somewhere in the middle of a phone number,
   * the caret position is moved right before the next available digit skipping
   * any punctuation in between. This is called "smart" caret positioning.
   * Another case would be the phone number format changing as a result of
   * the user inserting the digit somewhere in the middle, which would require
   * re-positioning the caret because all digit positions have changed.
   * This "smart" caret positioning feature can be turned off by passing
   * `smartCaret={false}` property: use it in case of any possible issues
   * with caret position during phone number input.
   */ // Is `true` by default.
    smartCaret: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].bool,
    /**
   * Set to `true` to force "international" phone number format.
   * Set to `false` to force "national" phone number format.
   * By default it's `undefined` meaning that it doesn't enforce any phone number format:
   * the user can input their phone number in either "national" or "international" format.
   */ international: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].bool,
    /**
   * If set to `true`, the phone number input will get trimmed
   * if it exceeds the maximum length for the country.
   */ limitMaxLength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].bool,
    /**
   * If set to `false`, and `international` is `true`, then
   * users won't be able to erase the "country calling part"
   * of a phone number in the `<input/>`.
   */ countryCallingCodeEditable: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].bool,
    /**
   * `libphonenumber-js` metadata.
   *
   * Can be used to pass custom `libphonenumber-js` metadata
   * to reduce the overall bundle size for those who compile "custom" metadata.
   */ metadata: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$PropTypes$2e$js__$5b$client$5d$__$28$ecmascript$29$__["metadata"],
    /**
   * Is called every time the selected country changes:
   * either programmatically or when user selects it manually from the list.
   */ // People have been asking for a way to get the selected country.
    // @see  https://github.com/catamphetamine/react-phone-number-input/issues/128
    // For some it's just a "business requirement".
    // I guess it's about gathering as much info on the user as a website can
    // without introducing any addional fields that would complicate the form
    // therefore reducing "conversion" (that's a marketing term).
    // Assuming that the phone number's country is the user's country
    // is not 100% correct but in most cases I guess it's valid.
    onCountryChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].func,
    /**
   * If set to `false`, will not focus the `<input/>` component
   * when the user selects a country from the list of countries.
   * This can be used to conform to the Web Content Accessibility Guidelines (WCAG).
   * Quote:
   * "On input: Changing the setting of any user interface component
   *  does not automatically cause a change of context unless the user
   *  has been advised of the behaviour before using the component."
   */ focusInputOnCountrySelection: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].bool
};
var defaultProps = {
    /**
   * Remember (and autofill) the value as a phone number.
   */ autoComplete: 'tel',
    /**
   * Country `<select/>` component.
   */ countrySelectComponent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$CountrySelect$2e$js__$5b$client$5d$__$28$ecmascript$29$__["CountrySelectWithIcon"],
    /**
   * Flag icon component.
   */ flagComponent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$Flag$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"],
    /**
   * By default, uses icons from `country-flag-icons` gitlab pages website.
   */ // Must be equal to `flagUrl` in `./CountryIcon.js`.
    flagUrl: 'https://purecatamphetamine.github.io/country-flag-icons/3x2/{XX}.svg',
    /**
   * Default "International" country `<select/>` option icon.
   */ internationalIcon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$InternationalIcon$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"],
    /**
   * Phone number `<input/>` component.
   */ inputComponent: 'input',
    /**
   * Wrapping `<div/>` component.
   */ containerComponent: 'div',
    /**
   * Some users requested a way to reset the component:
   * both number `<input/>` and country `<select/>`.
   * Whenever `reset` property changes both number `<input/>`
   * and country `<select/>` are reset.
   * It's not implemented as some instance `.reset()` method
   * because `ref` is forwarded to `<input/>`.
   * It's also not replaced with just resetting `country` on
   * external `value` reset, because a user could select a country
   * and then not input any `value`, and so the selected country
   * would be "stuck", if not using this `reset` property.
   */ // https://github.com/catamphetamine/react-phone-number-input/issues/300
    reset: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].any,
    /**
   *
   */ /**
   * Set to `false` to use "basic" caret instead of the "smart" one.
   */ smartCaret: true,
    /**
   * Whether to add the "International" option
   * to the list of countries.
   */ addInternationalOption: true,
    /**
   * If set to `false`, and `international` is `true`, then
   * users won't be able to erase the "country calling part"
   * of a phone number in the `<input/>`.
   */ countryCallingCodeEditable: true,
    /**
   * If set to `false`, will not focus the `<input/>` component
   * when the user selects a country from the list of countries.
   * This can be used to conform to the Web Content Accessibility Guidelines (WCAG).
   * Quote:
   * "On input: Changing the setting of any user interface component
   *  does not automatically cause a change of context unless the user
   *  has been advised of the behaviour before using the component."
   */ focusInputOnCountrySelection: true
};
function withDefaultProps(props) {
    props = _objectSpread({}, props);
    for(var key in defaultProps){
        if (props[key] === undefined) {
            props[key] = defaultProps[key];
        }
    }
    return props;
}
const __TURBOPACK__default__export__ = PhoneNumberInput;
function areEqualArrays(a, b) {
    if (a.length !== b.length) {
        return false;
    }
    var i = 0;
    while(i < a.length){
        if (a[i] !== b[i]) {
            return false;
        }
        i++;
    }
    return true;
} //# sourceMappingURL=PhoneInputWithCountry.js.map
}}),
"[project]/node_modules/react-phone-number-input/modules/PhoneInputWithCountryDefault.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createPhoneInput": (()=>createPhoneInput),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$locale$2f$en$2e$json$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/locale/en.json.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$PropTypes$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/PropTypes.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$PhoneInputWithCountry$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/PhoneInputWithCountry.js [client] (ecmascript)");
var _excluded = [
    "metadata",
    "labels"
];
function _extends() {
    _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined);
    return _extends.apply(this, arguments);
}
function _objectWithoutProperties(source, excluded) {
    if (source == null) return {};
    var target = _objectWithoutPropertiesLoose(source, excluded);
    var key, i;
    if (Object.getOwnPropertySymbols) {
        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
        for(i = 0; i < sourceSymbolKeys.length; i++){
            key = sourceSymbolKeys[i];
            if (excluded.indexOf(key) >= 0) continue;
            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
            target[key] = source[key];
        }
    }
    return target;
}
function _objectWithoutPropertiesLoose(source, excluded) {
    if (source == null) return {};
    var target = {};
    var sourceKeys = Object.keys(source);
    var key, i;
    for(i = 0; i < sourceKeys.length; i++){
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
    }
    return target;
}
;
;
;
;
;
function createPhoneInput(defaultMetadata) {
    var PhoneInputDefault = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].forwardRef(function(_ref, ref) {
        var _ref$metadata = _ref.metadata, metadata = _ref$metadata === void 0 ? defaultMetadata : _ref$metadata, _ref$labels = _ref.labels, labels = _ref$labels === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$locale$2f$en$2e$json$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"] : _ref$labels, rest = _objectWithoutProperties(_ref, _excluded);
        return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$PhoneInputWithCountry$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"], _extends({}, rest, {
            ref: ref,
            metadata: metadata,
            labels: labels
        }));
    });
    PhoneInputDefault.propTypes = {
        metadata: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$PropTypes$2e$js__$5b$client$5d$__$28$ecmascript$29$__["metadata"],
        labels: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$PropTypes$2e$js__$5b$client$5d$__$28$ecmascript$29$__["labels"]
    };
    return PhoneInputDefault;
}
const __TURBOPACK__default__export__ = createPhoneInput();
 //# sourceMappingURL=PhoneInputWithCountryDefault.js.map
}}),
"[project]/node_modules/react-phone-number-input/min/index.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "formatPhoneNumber": (()=>formatPhoneNumber),
    "formatPhoneNumberIntl": (()=>formatPhoneNumberIntl),
    "getCountries": (()=>getCountries),
    "getCountryCallingCode": (()=>getCountryCallingCode),
    "isPossiblePhoneNumber": (()=>isPossiblePhoneNumber),
    "isSupportedCountry": (()=>isSupportedCountry),
    "isValidPhoneNumber": (()=>isValidPhoneNumber),
    "parsePhoneNumber": (()=>parsePhoneNumber)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$metadata$2e$min$2e$json$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/metadata.min.json.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$parsePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__parsePhoneNumber$3e$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/parsePhoneNumber.js [client] (ecmascript) <export default as parsePhoneNumber>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$libphonenumber$2f$formatPhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__formatPhoneNumber$3e$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/libphonenumber/formatPhoneNumber.js [client] (ecmascript) <export default as formatPhoneNumber>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$libphonenumber$2f$formatPhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/libphonenumber/formatPhoneNumber.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$isValidPhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isValidPhoneNumber$3e$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/isValidPhoneNumber.js [client] (ecmascript) <export default as isValidPhoneNumber>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$isPossiblePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isPossiblePhoneNumber$3e$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/isPossiblePhoneNumber.js [client] (ecmascript) <export default as isPossiblePhoneNumber>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$getCountries$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__getCountries$3e$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/getCountries.js [client] (ecmascript) <export default as getCountries>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/libphonenumber-js/es6/metadata.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$PhoneInputWithCountryDefault$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-phone-number-input/modules/PhoneInputWithCountryDefault.js [client] (ecmascript)");
;
;
;
function call(func, _arguments) {
    var args = Array.prototype.slice.call(_arguments);
    args.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$metadata$2e$min$2e$json$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"]);
    return func.apply(this, args);
}
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$PhoneInputWithCountryDefault$2e$js__$5b$client$5d$__$28$ecmascript$29$__["createPhoneInput"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$metadata$2e$min$2e$json$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"]);
function parsePhoneNumber() {
    return call(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$parsePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__parsePhoneNumber$3e$__["parsePhoneNumber"], arguments);
}
function formatPhoneNumber() {
    return call(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$libphonenumber$2f$formatPhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__formatPhoneNumber$3e$__["formatPhoneNumber"], arguments);
}
function formatPhoneNumberIntl() {
    return call(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$phone$2d$number$2d$input$2f$modules$2f$libphonenumber$2f$formatPhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__["formatPhoneNumberIntl"], arguments);
}
function isValidPhoneNumber() {
    return call(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$isValidPhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isValidPhoneNumber$3e$__["isValidPhoneNumber"], arguments);
}
function isPossiblePhoneNumber() {
    return call(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$isPossiblePhoneNumber$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isPossiblePhoneNumber$3e$__["isPossiblePhoneNumber"], arguments);
}
function getCountries() {
    return call(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$getCountries$2e$js__$5b$client$5d$__$28$ecmascript$29$__$3c$export__default__as__getCountries$3e$__["getCountries"], arguments);
}
function getCountryCallingCode() {
    return call(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__["getCountryCallingCode"], arguments);
}
function isSupportedCountry() {
    return call(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$libphonenumber$2d$js$2f$es6$2f$metadata$2e$js__$5b$client$5d$__$28$ecmascript$29$__["isSupportedCountry"], arguments);
}
}}),
}]);

//# sourceMappingURL=node_modules_react-phone-number-input_adaf3af7._.js.map