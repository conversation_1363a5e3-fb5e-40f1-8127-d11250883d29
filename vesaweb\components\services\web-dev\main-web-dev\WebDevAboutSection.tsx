import React from 'react';
import Image from 'next/image';
import { CheckCircle } from 'lucide-react';

const WebDevAboutSection: React.FC = () => {
  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        {/* What is Web Development Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-24">
          <div>
            <Image
              src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=600&h=400&fit=crop"
              alt="Professional Web Development"
              width={600}
              height={400}
              className="w-full rounded-3xl shadow-xl"
            />
          </div>
          <div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
              Professional Web Development That Drives Business Growth
            </h2>
            <p className="text-xl text-gray-600 mb-6 leading-relaxed">
              Web development is more than just creating a website—it&apos;s about building a powerful digital platform that converts visitors into customers and drives sustainable business growth.
            </p>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              At VESA Solutions, we combine cutting-edge technology with proven conversion strategies to create websites that not only look amazing but deliver measurable results for your business.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              <div className="flex items-center">
                <CheckCircle className="text-green-500 mr-3 flex-shrink-0" size={24} />
                <span className="text-gray-700">Custom Design & Development</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-green-500 mr-3 flex-shrink-0" size={24} />
                <span className="text-gray-700">Mobile-First Responsive Design</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-green-500 mr-3 flex-shrink-0" size={24} />
                <span className="text-gray-700">Lightning-Fast Performance</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-green-500 mr-3 flex-shrink-0" size={24} />
                <span className="text-gray-700">SEO-Optimized Structure</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-green-500 mr-3 flex-shrink-0" size={24} />
                <span className="text-gray-700">Security & Maintenance</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-green-500 mr-3 flex-shrink-0" size={24} />
                <span className="text-gray-700">Analytics & Conversion Tracking</span>
              </div>
            </div>
          </div>
        </div>

        {/* Why Choose VESA Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
              Why Choose VESA for Your Web Development?
            </h2>
            <p className="text-xl text-gray-600 mb-6 leading-relaxed">
              We don&apos;t just build websites—we create digital experiences that drive results. Our proven development process ensures your website performs at the highest level.
            </p>
            
            <div className="space-y-6">
              <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-2xl">
                <h4 className="text-xl font-bold text-gray-800 mb-3">Performance-First Approach</h4>
                <p className="text-gray-600">Every website we build is optimized for speed, with load times under 3 seconds and 98%+ uptime guarantee.</p>
              </div>
              
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-2xl">
                <h4 className="text-xl font-bold text-gray-800 mb-3">Conversion-Focused Design</h4>
                <p className="text-gray-600">We design with your business goals in mind, creating user experiences that guide visitors toward taking action.</p>
              </div>
              
              <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-2xl">
                <h4 className="text-xl font-bold text-gray-800 mb-3">Ongoing Support & Maintenance</h4>
                <p className="text-gray-600">Your website is never &quot;finished.&quot; We provide continuous updates, security monitoring, and performance optimization.</p>
              </div>
            </div>
          </div>
          <div>
            <Image
              src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop"
              alt="Web Development Process"
              width={600}
              height={400}
              className="w-full rounded-3xl shadow-xl"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default WebDevAboutSection;
