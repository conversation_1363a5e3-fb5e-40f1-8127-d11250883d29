// pages/blog.tsx - Blog archive page following homepage design patterns
import React, {useState} from 'react'
import { GetStaticProps } from 'next'
import Head from 'next/head'
import Header from '@/components/global/Header'
import Footer from '@/components/global/Footer'
import { BlogArchiveHero, BlogGrid, BlogFilters, BlogPagination } from '@/components/blog'
import { getBlogPosts, getBlogCategories, getBlogTags } from '@/lib/sanity-blog'
import { BlogListItem, BlogCategory, BlogTag, BlogFilters as BlogFiltersType } from '@/types/blog'

interface BlogPageProps {
  initialPosts: BlogListItem[]
  categories: BlogCategory[]
  tags: BlogTag[]
  totalPosts: number
  hasMore: boolean
}

const BlogPage: React.FC<BlogPageProps> = ({
  initialPosts,
  categories,
  tags,
  totalPosts,
  hasMore: initialHasMore
}) => {
  const [posts, setPosts] = useState<BlogListItem[]>(initialPosts)
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(initialHasMore)
  const [currentPage, setCurrentPage] = useState(1)
  const [filters, setFilters] = useState<BlogFiltersType>({})

  // Handle filter changes
  const handleFilterChange = async (newFilters: BlogFiltersType) => {
    setLoading(true)
    setCurrentPage(1)
    setFilters(newFilters)

    try {
      const response = await fetch('/api/blog?' + new URLSearchParams({
        ...newFilters,
        page: '1',
        limit: '12'
      } as Record<string, string>))
      
      const data = await response.json()
      setPosts(data.posts)
      setHasMore(data.hasMore)
    } catch (error) {
      console.error('Error filtering posts:', error)
    } finally {
      setLoading(false)
    }
  }

  // Handle pagination
  const handlePageChange = async (page: number) => {
    setLoading(true)
    setCurrentPage(page)

    try {
      const response = await fetch('/api/blog?' + new URLSearchParams({
        ...filters,
        page: page.toString(),
        limit: '12'
      } as Record<string, string>))
      
      const data = await response.json()
      setPosts(data.posts)
      setHasMore(data.hasMore)
    } catch (error) {
      console.error('Error loading page:', error)
    } finally {
      setLoading(false)
    }
  }

  // Load more posts (for infinite scroll)
  const loadMorePosts = async () => {
    if (loading || !hasMore) return

    setLoading(true)
    const nextPage = currentPage + 1

    try {
      const response = await fetch('/api/blog?' + new URLSearchParams({
        ...filters,
        page: nextPage.toString(),
        limit: '12'
      } as Record<string, string>))
      
      const data = await response.json()
      setPosts(prev => [...prev, ...data.posts])
      setHasMore(data.hasMore)
      setCurrentPage(nextPage)
    } catch (error) {
      console.error('Error loading more posts:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <Head>
        <title>Blog - Digital Marketing Insights | VESA Solutions</title>
        <meta 
          name="description" 
          content="Stay updated with the latest digital marketing trends, SEO tips, and industry insights from VESA Solutions. Expert advice to grow your business online." 
        />
        <meta name="keywords" content="digital marketing blog, SEO tips, marketing insights, VESA Solutions" />
        <meta property="og:title" content="Blog - Digital Marketing Insights | VESA Solutions" />
        <meta property="og:description" content="Stay updated with the latest digital marketing trends, SEO tips, and industry insights from VESA Solutions." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://vesasolutions.com/blog" />
        <link rel="canonical" href="https://vesasolutions.com/blog" />
      </Head>

      <Header isVisible={true} />
      
      <main>
        {/* Hero Section */}
        <BlogArchiveHero />

        {/* Blog Content */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Filters */}
            <BlogFilters
              categories={categories}
              tags={tags}
              onFilterChange={handleFilterChange}
              loading={loading}
            />

            {/* Blog Grid */}
            <BlogGrid
              posts={posts}
              loading={loading}
              onLoadMore={loadMorePosts}
              hasMore={hasMore}
            />

            {/* Pagination */}
            <BlogPagination
              currentPage={currentPage}
              totalPosts={totalPosts}
              postsPerPage={12}
              onPageChange={handlePageChange}
              loading={loading}
            />
          </div>
        </section>
      </main>

      <Footer />
    </>
  )
}

export const getStaticProps: GetStaticProps = async () => {
  try {
    const [postsData, categories, tags] = await Promise.all([
      getBlogPosts({ page: 1, limit: 12 }),
      getBlogCategories(),
      getBlogTags()
    ])

    return {
      props: {
        initialPosts: postsData.posts,
        categories,
        tags,
        totalPosts: postsData.totalPosts,
        hasMore: postsData.hasMore
      },
      revalidate: 60 // Revalidate every minute
    }
  } catch (error) {
    console.error('Error fetching blog data:', error)
    return {
      props: {
        initialPosts: [],
        categories: [],
        tags: [],
        totalPosts: 0,
        hasMore: false
      },
      revalidate: 60
    }
  }
}

export default BlogPage
