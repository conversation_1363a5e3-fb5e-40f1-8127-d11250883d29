import React from 'react';
import { Star, Award, Users, TrendingUp } from 'lucide-react';

const DigitalMarketingTrustSignalsSection: React.FC = () => {
  return (
    <section className="py-16 bg-white border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 items-center">
          {/* Trust Signal 1 - Reviews */}
          <div className="text-center">
            <div className="flex justify-center items-center mb-4">
              <div className="flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 fill-current" />
                ))}
              </div>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">4.9/5</div>
            <div className="text-gray-600 text-sm">Client Rating</div>
            <div className="text-gray-500 text-xs mt-1">Based on 150+ reviews</div>
          </div>

          {/* Trust Signal 2 - Certification */}
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-purple-100 p-3 rounded-full">
                <Award className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">Certified</div>
            <div className="text-gray-600 text-sm">Google Partner</div>
            <div className="text-gray-500 text-xs mt-1">Premier status</div>
          </div>

          {/* Trust Signal 3 - Clients */}
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-green-100 p-3 rounded-full">
                <Users className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">500+</div>
            <div className="text-gray-600 text-sm">Happy Clients</div>
            <div className="text-gray-500 text-xs mt-1">Across all industries</div>
          </div>

          {/* Trust Signal 4 - Growth */}
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-purple-100 p-3 rounded-full">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">300%</div>
            <div className="text-gray-600 text-sm">Avg ROI Growth</div>
            <div className="text-gray-500 text-xs mt-1">Within 6 months</div>
          </div>
        </div>

        {/* Partner Logos */}
        <div className="mt-12 pt-8 border-t border-gray-100">
          <div className="text-center mb-8">
            <p className="text-gray-500 text-sm uppercase tracking-wide font-medium">
              Trusted by Leading Brands & Certified Partners
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60">
            <div className="flex justify-center">
              <div className="bg-gray-200 rounded-lg px-4 py-2 text-gray-600 font-semibold text-sm">
                Google Partner
              </div>
            </div>
            <div className="flex justify-center">
              <div className="bg-gray-200 rounded-lg px-4 py-2 text-gray-600 font-semibold text-sm">
                Facebook Partner
              </div>
            </div>
            <div className="flex justify-center">
              <div className="bg-gray-200 rounded-lg px-4 py-2 text-gray-600 font-semibold text-sm">
                LinkedIn Partner
              </div>
            </div>
            <div className="flex justify-center">
              <div className="bg-gray-200 rounded-lg px-4 py-2 text-gray-600 font-semibold text-sm">
                Microsoft Partner
              </div>
            </div>
            <div className="flex justify-center">
              <div className="bg-gray-200 rounded-lg px-4 py-2 text-gray-600 font-semibold text-sm">
                Amazon Partner
              </div>
            </div>
            <div className="flex justify-center">
              <div className="bg-gray-200 rounded-lg px-4 py-2 text-gray-600 font-semibold text-sm">
                HubSpot Partner
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DigitalMarketingTrustSignalsSection;
