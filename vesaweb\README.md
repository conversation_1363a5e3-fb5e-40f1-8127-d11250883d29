# VESA Digital Marketing Website

This is the **frontend application** for VESA, a professional digital marketing services website built with [Next.js](https://nextjs.org) and powered by [Sanity CMS](https://sanity.io) for content management.

## 🎯 What This Is

A modern, responsive digital marketing website featuring:
- **Dynamic service pages** with comprehensive content management
- **Contact forms** with email integration (Nodemailer)
- **SEO optimization** for search engine visibility
- **Smooth animations** using Framer Motion and GSAP
- **Mobile-first design** with Tailwind CSS
- **Form validation** with React Hook Form and Zod

## 🏗️ Architecture

### Frontend (vesaweb)
- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS 4
- **Animations**: Framer Motion + GSAP
- **Forms**: React Hook Form + Zod validation
- **Icons**: Lucide React

### Backend (vesasanity)
- **CMS**: Sanity Studio for content management
- **Content**: Dynamic service pages, testimonials, FAQs
- **API**: Real-time content updates via Sanity Client

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ installed
- Access to Sanity project (see `../vesasanity/`)

### Installation & Setup
```bash
# Install dependencies
npm install

# Start the development server
npm run dev --turbopack
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Available Scripts
- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 📁 Project Structure

```
vesaweb/
├── pages/
│   ├── [slug].tsx          # Dynamic service pages
│   ├── index.tsx           # Homepage
│   ├── about.tsx           # About page
│   ├── contact-us.tsx      # Contact page
│   ├── free-estimate.tsx   # Lead generation page
│   └── api/
│       ├── contact.ts      # Contact form handler
│       └── hello.ts        # API example
├── components/             # Reusable UI components
├── lib/                   # Utilities and configurations
├── styles/                # Global styles
└── types/                 # TypeScript type definitions
```

## 🔗 Content Management Integration

### Working with Sanity CMS
This website pulls content from the **vesasanity** Sanity Studio:

1. **Start the CMS**: Navigate to `../vesasanity/` and run `npm run dev`
2. **Access Studio**: Open `http://localhost:3333` to manage content
3. **Create Services**: Add new service pages through the "Sub Service" content type
4. **Live Updates**: Changes in Sanity appear immediately on the website

### Dynamic Content Features
- **Service Pages**: Automatically generated from Sanity content via `[slug].tsx`
- **SEO Optimization**: Meta tags and descriptions managed through CMS
- **Contact Forms**: Integrated with Nodemailer for email delivery
- **Testimonials**: Customer reviews and case studies from CMS

## 🎨 Key Features

### Pages & Functionality
- **Homepage** (`/`) - Main landing page with hero and services overview
- **Dynamic Services** (`/[slug]`) - Individual service pages from CMS
- **About Page** (`/about`) - Company information and team
- **Contact** (`/contact-us`) - Contact form with validation
- **Free Estimate** (`/free-estimate`) - Lead generation form
- **SEO Page** (`/seo-search-engine-optimization`) - Dedicated SEO service page

### Technical Features
- **Server-Side Rendering** for optimal SEO performance
- **Image Optimization** with Next.js Image component
- **Form Validation** with comprehensive error handling
- **Responsive Design** optimized for all devices
- **Performance Optimized** with Turbopack and modern bundling

## 🚀 Deployment

### Vercel (Recommended)
The easiest way to deploy is using [Vercel Platform](https://vercel.com/new):

1. Connect your GitHub repository
2. Configure environment variables for Sanity
3. Deploy with automatic CI/CD

### Environment Variables
```bash
# Add these to your deployment environment
NEXT_PUBLIC_SANITY_PROJECT_ID=your_project_id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your_api_token
```

## 📚 Learn More

### Next.js Resources
- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API
- [Learn Next.js](https://nextjs.org/learn-pages-router) - interactive Next.js tutorial

### Sanity Resources
- [Sanity Documentation](https://www.sanity.io/docs) - CMS documentation
- [Sanity + Next.js Guide](https://www.sanity.io/guides/nextjs) - integration guide

### Project Resources
- [Tailwind CSS](https://tailwindcss.com/docs) - utility-first CSS framework
- [Framer Motion](https://www.framer.com/motion/) - animation library
- [React Hook Form](https://react-hook-form.com/) - form validation
