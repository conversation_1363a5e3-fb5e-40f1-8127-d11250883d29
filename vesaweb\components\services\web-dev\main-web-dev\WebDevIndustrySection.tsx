import React from 'react';
import { Building, ShoppingBag, Heart, GraduationCap, Car, Home, Briefcase, Users } from 'lucide-react';

const WebDevIndustrySection: React.FC = () => {
  const industries = [
    {
      icon: ShoppingBag,
      name: 'E-commerce & Retail',
      description: 'Custom online stores with advanced shopping features, payment integration, and inventory management.',
      specialties: ['Product Catalogs', 'Shopping Carts', 'Payment Gateways', 'Inventory Systems']
    },
    {
      icon: Heart,
      name: 'Healthcare & Medical',
      description: 'HIPAA-compliant websites and patient portals with appointment scheduling and secure communications.',
      specialties: ['Patient Portals', 'Appointment Systems', 'HIPAA Compliance', 'Telemedicine Integration']
    },
    {
      icon: Building,
      name: 'Professional Services',
      description: 'Professional websites for law firms, accounting, consulting, and other service-based businesses.',
      specialties: ['Client Portals', 'Case Management', 'Appointment Booking', 'Document Sharing']
    },
    {
      icon: GraduationCap,
      name: 'Education & Training',
      description: 'Learning management systems, course platforms, and educational websites with interactive features.',
      specialties: ['LMS Development', 'Course Management', 'Student Portals', 'Online Testing']
    },
    {
      icon: Home,
      name: 'Real Estate',
      description: 'Property listing websites with advanced search, virtual tours, and lead generation systems.',
      specialties: ['Property Listings', 'Search Filters', 'Virtual Tours', 'Lead Capture']
    },
    {
      icon: Car,
      name: 'Automotive',
      description: 'Dealership websites with inventory management, financing calculators, and service scheduling.',
      specialties: ['Inventory Management', 'Financing Tools', 'Service Booking', 'Vehicle Comparisons']
    },
    {
      icon: Briefcase,
      name: 'Financial Services',
      description: 'Secure financial websites with client portals, calculators, and compliance-ready features.',
      specialties: ['Client Portals', 'Financial Calculators', 'Compliance Features', 'Secure Transactions']
    },
    {
      icon: Users,
      name: 'Non-Profit Organizations',
      description: 'Mission-driven websites with donation systems, volunteer management, and event coordination.',
      specialties: ['Donation Systems', 'Volunteer Portals', 'Event Management', 'Impact Tracking']
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
            Industry-Specific Web Development Solutions
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            We understand that every industry has unique requirements. Our web development solutions are tailored to meet the specific needs and regulations of your business sector.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {industries.map((industry, index) => {
            const IconComponent = industry.icon;
            return (
              <div key={index} className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="bg-gradient-to-br from-blue-100 to-blue-200 w-16 h-16 rounded-2xl flex items-center justify-center mb-6">
                  <IconComponent className="text-blue-600" size={32} />
                </div>
                
                <h3 className="text-xl font-bold text-gray-800 mb-4">{industry.name}</h3>
                <p className="text-gray-600 mb-6 leading-relaxed">{industry.description}</p>
                
                <div>
                  <h4 className="font-semibold text-gray-800 mb-3">Specialties:</h4>
                  <div className="space-y-2">
                    {industry.specialties.map((specialty, idx) => (
                      <div key={idx} className="text-sm text-gray-600 bg-white px-3 py-1 rounded-full">
                        {specialty}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Stats Section */}
        <div className="mt-20 bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-12 text-white">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold mb-4">
              Proven Results Across All Industries
            </h3>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Our industry-specific approach delivers measurable results for businesses of all sizes and sectors.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl md:text-5xl font-bold text-blue-200 mb-2">200+</div>
              <div className="text-blue-100">Happy Clients</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-blue-200 mb-2">300%</div>
              <div className="text-blue-100">Average ROI</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-blue-200 mb-2">5+</div>
              <div className="text-blue-100">Years Experience</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-blue-200 mb-2">99%</div>
              <div className="text-blue-100">Google Searches</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WebDevIndustrySection;
