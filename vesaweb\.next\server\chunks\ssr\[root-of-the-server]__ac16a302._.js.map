{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { ChevronDown } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\n\r\ninterface HeaderProps {\r\n  isVisible?: boolean;\r\n}\r\n\r\nconst Header: React.FC<HeaderProps> = ({ isVisible = true }) => {\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);\r\n  const [seoDropdownOpen, setSeoDropdownOpen] = useState<boolean>(false);\r\n  const [webDevDropdownOpen, setWebDevDropdownOpen] = useState<boolean>(false);\r\n  const [digitalMarketingDropdownOpen, setDigitalMarketingDropdownOpen] = useState<boolean>(false);\r\n  const [aboutDropdownOpen, setAboutDropdownOpen] = useState<boolean>(false);\r\n\r\n  const [mounted, setMounted] = useState<boolean>(false);\r\n\r\n  // Ensure component is hydrated before rendering state-dependent UI\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Handle body scroll when mobile menu is open\r\n  useEffect(() => {\r\n    if (mobileMenuOpen) {\r\n      document.body.style.overflow = 'hidden';\r\n    } else {\r\n      document.body.style.overflow = 'unset';\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [mobileMenuOpen]);\r\n\r\n  // Close mobile menu on escape key\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (e.key === 'Escape' && mobileMenuOpen) {\r\n        setMobileMenuOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleEscape);\r\n    return () => document.removeEventListener('keydown', handleEscape);\r\n  }, [mobileMenuOpen]);\r\n\r\n  // SEO Sub-services\r\n  const seoSubServices = [\r\n    { name: 'On-Page SEO Optimization', href: '/on-page-seo', description: 'Optimize content and structure' },\r\n    { name: 'Off-Page SEO & Link Building', href: '/off-page-seo', description: 'Build authority and backlinks' },\r\n    { name: 'Technical SEO Services', href: '/technical-seo', description: 'Optimize technical performance' },\r\n    { name: 'Local SEO Marketing', href: '/local-seo', description: 'Dominate local search results' },\r\n    { name: 'Content Writing', href: '/content-writing', description: 'SEO-optimized content creation' },\r\n    { name: 'SEO Analytics', href: '/seo-analytics', description: 'Comprehensive SEO analysis' }\r\n  ];\r\n\r\n  // Web Development Sub-services\r\n  const webDevSubServices = [\r\n    { name: 'Custom Website Development', href: '/custom-website-development', description: 'Unique, tailored websites' },\r\n    { name: 'E-commerce Development', href: '/ecommerce-development', description: 'Online store solutions' },\r\n    { name: 'Mobile App Development', href: '/mobile-app-development', description: 'iOS & Android apps' },\r\n    { name: 'Website Speed Optimization', href: '/website-speed-optimization', description: 'Lightning-fast websites' },\r\n    { name: 'Web Application Development', href: '/web-application-development', description: 'Custom web applications' },\r\n    { name: 'Website Maintenance & Support', href: '/website-maintenance-support', description: '24/7 website care' }\r\n  ];\r\n\r\n  // Digital Marketing Sub-services\r\n  const digitalMarketingSubServices = [\r\n    { name: 'PPC Advertising', href: '/ppc', description: 'Targeted pay-per-click campaigns' },\r\n    { name: 'Email Marketing', href: '/email-marketing', description: 'Automated campaigns that convert' },\r\n    { name: 'Social Media Marketing', href: '/social-media', description: 'Engage your audience effectively' },\r\n    { name: 'Branding Services', href: '/branding-services', description: 'Build powerful brand identity' },\r\n    { name: 'Conversion Optimization', href: '/conversion-optimization', description: 'Turn visitors into customers' },\r\n    { name: 'Reputation Management', href: '/reputation-management', description: 'Protect and enhance your brand' }\r\n  ];\r\n\r\n  // About Sub-services\r\n  const aboutSubServices = [\r\n    { name: 'Case Studies', href: '/case-studies', description: 'View our successful projects' },\r\n    { name: 'Blog', href: '/blog', description: 'Latest insights and industry news' }\r\n  ];\r\n\r\n  const toggleMobileMenu = () => {\r\n    setMobileMenuOpen(!mobileMenuOpen);\r\n  };\r\n\r\n  const closeMobileMenu = () => {\r\n    setMobileMenuOpen(false);\r\n    // Reset all dropdown states when closing mobile menu\r\n    setSeoDropdownOpen(false);\r\n    setWebDevDropdownOpen(false);\r\n    setDigitalMarketingDropdownOpen(false);\r\n    setAboutDropdownOpen(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <header className={`w-full bg-white/95 backdrop-blur-md border-b border-gray-100 z-50 transition-all duration-500 ease-in-out ${\r\n        isVisible \r\n          ? 'sticky top-0 translate-y-0 opacity-100' \r\n          : 'fixed top-0 -translate-y-full opacity-0 pointer-events-none'\r\n      }`}>\r\n        <div className=\"max-w-[1440px] mx-auto px-4 sm:px-6 py-3 sm:py-4\">\r\n          <div className=\"flex justify-between items-center\">\r\n            {/* Logo */}\r\n            <div className=\"flex items-center group cursor-pointer\">\r\n              <div className=\"relative\">\r\n                <Link href=\"/\">\r\n                  <Image \r\n                    src=\"/VesaLogo.svg\" \r\n                    alt=\"VESA Solutions Logo\" \r\n                    width={94}\r\n                    height={40}\r\n                    priority\r\n                    className=\"transition-transform duration-300 group-hover:scale-105 w-20 h-auto sm:w-[94px]\"\r\n                  />\r\n                </Link>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Desktop Navigation */}\r\n            <nav className=\"hidden lg:flex items-center space-x-12\">\r\n              {/* SEO Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setSeoDropdownOpen(true)}\r\n                onMouseLeave={() => setSeoDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/seo-search-engine-optimization\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-green-600 transition-all duration-300 group\">\r\n                  SEO\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-green-500 to-green-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  seoDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">SEO Services</div>\r\n                      {seoSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100/50 transition-all duration-200 ease-out border border-transparent hover:border-green-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-green-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Web Development Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setWebDevDropdownOpen(true)}\r\n                onMouseLeave={() => setWebDevDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/web-development\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group\">\r\n                  WEB DEVELOPMENT\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  webDevDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">Web Development</div>\r\n                      {webDevSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Digital Marketing Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setDigitalMarketingDropdownOpen(true)}\r\n                onMouseLeave={() => setDigitalMarketingDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/digital-marketing\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-purple-600 transition-all duration-300 group\">\r\n                  DIGITAL MARKETING\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-purple-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  digitalMarketingDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">Digital Marketing</div>\r\n                      {digitalMarketingSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50/50 transition-all duration-200 ease-out border border-transparent hover:border-indigo-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-purple-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-purple-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Services */}\r\n              <Link href=\"/services\" className=\"relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300\">\r\n                SERVICES\r\n                <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n              </Link>\r\n\r\n              {/* About Dropdown */}\r\n              <div\r\n                className=\"relative group\"\r\n                onMouseEnter={() => setAboutDropdownOpen(true)}\r\n                onMouseLeave={() => setAboutDropdownOpen(false)}\r\n              >\r\n                <Link href=\"/about\" className=\"flex items-center text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300 group\">\r\n                  ABOUT\r\n                  <ChevronDown size={16} className={`ml-1 transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n                </Link>\r\n\r\n                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 pt-3 transition-all duration-300 ${\r\n                  aboutDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'\r\n                }`}>\r\n                  <div className=\"w-80 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\">\r\n                    <div className=\"p-3\">\r\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3\">About</div>\r\n                      {aboutSubServices.map((service, index) => (\r\n                        <Link\r\n                          key={index}\r\n                          href={service.href}\r\n                          className=\"group block p-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100/50 transition-all duration-200 ease-out border border-transparent hover:border-blue-200/50 hover:shadow-sm\"\r\n                        >\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 text-sm\">\r\n                                {service.name}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 group-hover:text-gray-600 mt-1 transition-colors duration-200\">\r\n                                {service.description}\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                              <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\r\n                            </div>\r\n                          </div>\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Contact */}\r\n              <Link href=\"/contact-us\" className=\"relative group text-gray-700 text-sm font-semibold tracking-wide hover:text-blue-600 transition-all duration-300\">\r\n                CONTACT\r\n                <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 group-hover:w-full\"></span>\r\n              </Link>\r\n            </nav>\r\n\r\n\r\n            {/* CTA Button and Mobile Menu */}\r\n            <div className=\"flex items-center space-x-3 sm:space-x-4\">\r\n              {/* Desktop CTA */}\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                className=\"hidden sm:block group relative bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-sm font-semibold px-6 lg:px-8 py-2.5 lg:py-3 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-blue-500/25\"\r\n              >\r\n                <span className=\"relative z-10\">GET FREE PROPOSAL</span>\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n              </Link>\r\n\r\n              {/* Mobile Menu Button */}\r\n              <button\r\n                className=\"lg:hidden relative p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-all duration-300 group\"\r\n                onClick={toggleMobileMenu}\r\n                aria-label=\"Toggle mobile menu\"\r\n              >\r\n                <div className=\"w-6 h-6 relative\">\r\n                  <span className={`absolute top-1 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${\r\n                    mobileMenuOpen ? 'rotate-45 translate-y-2' : ''\r\n                  }`}></span>\r\n                  <span className={`absolute top-2.5 left-0 w-6 h-0.5 bg-current transition-all duration-300 ${\r\n                    mobileMenuOpen ? 'opacity-0' : ''\r\n                  }`}></span>\r\n                  <span className={`absolute top-4 left-0 w-6 h-0.5 bg-current transition-all duration-300 transform origin-center ${\r\n                    mobileMenuOpen ? '-rotate-45 -translate-y-2' : ''\r\n                  }`}></span>\r\n                </div>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Mobile Menu */}\r\n      {mounted && mobileMenuOpen && (\r\n        <div className=\"lg:hidden fixed inset-0 z-40 bg-white overflow-y-auto\">\r\n          {/* Mobile Header */}\r\n          <div className=\"flex justify-between items-center p-4 border-b border-gray-200\">\r\n            <Link href=\"/\" onClick={closeMobileMenu}>\r\n              <Image\r\n                src=\"/VesaLogo.svg\"\r\n                alt=\"VESA Solutions Logo\"\r\n                width={80}\r\n                height={34}\r\n                priority\r\n                className=\"w-20 h-auto\"\r\n              />\r\n            </Link>\r\n            <button\r\n              onClick={closeMobileMenu}\r\n              className=\"p-2 text-gray-700 hover:text-blue-600 rounded-xl transition-colors\"\r\n              aria-label=\"Close mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 relative\">\r\n                <span className=\"absolute top-2.5 left-0 w-6 h-0.5 bg-current transform rotate-45\"></span>\r\n                <span className=\"absolute top-2.5 left-0 w-6 h-0.5 bg-current transform -rotate-45\"></span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Mobile Navigation */}\r\n          <div className=\"p-4 space-y-6\">\r\n            {/* SEO Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setSeoDropdownOpen(!seoDropdownOpen)}\r\n              >\r\n                <Link href=\"/seo-search-engine-optimization\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-green-600 transition-colors\">\r\n                  SEO\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${seoDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {seoDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {seoSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-green-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Web Development Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setWebDevDropdownOpen(!webDevDropdownOpen)}\r\n              >\r\n                <Link href=\"/web-development\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\r\n                  WEB DEVELOPMENT\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${webDevDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {webDevDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {webDevSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-blue-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Digital Marketing Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setDigitalMarketingDropdownOpen(!digitalMarketingDropdownOpen)}\r\n              >\r\n                <Link href=\"/digital-marketing\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-purple-600 transition-colors\">\r\n                  DIGITAL MARKETING\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${digitalMarketingDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {digitalMarketingDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {digitalMarketingSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-purple-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Services */}\r\n            <div>\r\n              <Link\r\n                href=\"/services\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\"\r\n              >\r\n                SERVICES\r\n              </Link>\r\n            </div>\r\n\r\n            {/* About Section */}\r\n            <div>\r\n              <div\r\n                className=\"flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer\"\r\n                onClick={() => setAboutDropdownOpen(!aboutDropdownOpen)}\r\n              >\r\n                <Link href=\"/about\" onClick={closeMobileMenu} className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\r\n                  ABOUT\r\n                </Link>\r\n                <ChevronDown size={20} className={`transition-transform duration-300 ${aboutDropdownOpen ? 'rotate-180' : ''}`} />\r\n              </div>\r\n              {aboutDropdownOpen && (\r\n                <div className=\"pl-4 mt-2 space-y-2\">\r\n                  {aboutSubServices.map((service, index) => (\r\n                    <Link\r\n                      key={index}\r\n                      href={service.href}\r\n                      onClick={closeMobileMenu}\r\n                      className=\"block py-2 text-gray-600 hover:text-blue-600 transition-colors\"\r\n                    >\r\n                      {service.name}\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Contact */}\r\n            <div>\r\n              <Link\r\n                href=\"/contact-us\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block py-3 border-b border-gray-100 text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors\"\r\n              >\r\n                CONTACT\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Mobile CTA Button */}\r\n            <div className=\"pt-4\">\r\n              <Link\r\n                href=\"/free-estimate\"\r\n                onClick={closeMobileMenu}\r\n                className=\"block w-full text-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-6 py-4 rounded-full transition-all duration-300 transform hover:scale-105\"\r\n              >\r\n                GET FREE PROPOSAL\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWA,MAAM,SAAgC,CAAC,EAAE,YAAY,IAAI,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAC1F,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAEpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,mEAAmE;IACnE,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,qBAAqB;QACrB,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAe;IAEnB,kCAAkC;IAClC,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,YAAY,gBAAgB;gBACxC,kBAAkB;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;KAAe;IAEnB,mBAAmB;IACnB,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAA4B,MAAM;YAAgB,aAAa;QAAiC;QACxG;YAAE,MAAM;YAAgC,MAAM;YAAiB,aAAa;QAAgC;QAC5G;YAAE,MAAM;YAA0B,MAAM;YAAkB,aAAa;QAAiC;QACxG;YAAE,MAAM;YAAuB,MAAM;YAAc,aAAa;QAAgC;QAChG;YAAE,MAAM;YAAmB,MAAM;YAAoB,aAAa;QAAiC;QACnG;YAAE,MAAM;YAAiB,MAAM;YAAkB,aAAa;QAA6B;KAC5F;IAED,+BAA+B;IAC/B,MAAM,oBAAoB;QACxB;YAAE,MAAM;YAA8B,MAAM;YAA+B,aAAa;QAA4B;QACpH;YAAE,MAAM;YAA0B,MAAM;YAA0B,aAAa;QAAyB;QACxG;YAAE,MAAM;YAA0B,MAAM;YAA2B,aAAa;QAAqB;QACrG;YAAE,MAAM;YAA8B,MAAM;YAA+B,aAAa;QAA0B;QAClH;YAAE,MAAM;YAA+B,MAAM;YAAgC,aAAa;QAA0B;QACpH;YAAE,MAAM;YAAiC,MAAM;YAAgC,aAAa;QAAoB;KACjH;IAED,iCAAiC;IACjC,MAAM,8BAA8B;QAClC;YAAE,MAAM;YAAmB,MAAM;YAAQ,aAAa;QAAmC;QACzF;YAAE,MAAM;YAAmB,MAAM;YAAoB,aAAa;QAAmC;QACrG;YAAE,MAAM;YAA0B,MAAM;YAAiB,aAAa;QAAmC;QACzG;YAAE,MAAM;YAAqB,MAAM;YAAsB,aAAa;QAAgC;QACtG;YAAE,MAAM;YAA2B,MAAM;YAA4B,aAAa;QAA+B;QACjH;YAAE,MAAM;YAAyB,MAAM;YAA0B,aAAa;QAAiC;KAChH;IAED,qBAAqB;IACrB,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAgB,MAAM;YAAiB,aAAa;QAA+B;QAC3F;YAAE,MAAM;YAAQ,MAAM;YAAS,aAAa;QAAoC;KACjF;IAED,MAAM,mBAAmB;QACvB,kBAAkB,CAAC;IACrB;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,qDAAqD;QACrD,mBAAmB;QACnB,sBAAsB;QACtB,gCAAgC;QAChC,qBAAqB;IACvB;IAEA,qBACE;;0BACE,qKAAC;gBAAO,WAAW,CAAC,0GAA0G,EAC5H,YACI,2CACA,+DACJ;0BACA,cAAA,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,qKAAC,sHAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;;;;;0CAOlB,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC;wCACC,WAAU;wCACV,cAAc,IAAM,mBAAmB;wCACvC,cAAc,IAAM,mBAAmB;;0DAEvC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,WAAU;;oDAA6H;kEAElL,qKAAC,6MAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,kBAAkB,eAAe,IAAI;;;;;;kEACjH,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,qKAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,kBAAkB,sCAAsC,sCACxD;0DACA,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,qKAAC,qHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAI,WAAU;;kGACb,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,qKAAC;wCACC,WAAU;wCACV,cAAc,IAAM,sBAAsB;wCAC1C,cAAc,IAAM,sBAAsB;;0DAE1C,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;;oDAA4H;kEAElK,qKAAC,6MAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,qBAAqB,eAAe,IAAI;;;;;;kEACpH,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,qKAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,qBAAqB,sCAAsC,sCAC3D;0DACA,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,qKAAC,qHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAI,WAAU;;kGACb,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,qKAAC;wCACC,WAAU;wCACV,cAAc,IAAM,gCAAgC;wCACpD,cAAc,IAAM,gCAAgC;;0DAEpD,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;;oDAA8H;kEAEtK,qKAAC,6MAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,+BAA+B,eAAe,IAAI;;;;;;kEAC9H,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,qKAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,+BAA+B,sCAAsC,sCACrE;0DACA,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,4BAA4B,GAAG,CAAC,CAAC,SAAS,sBACzC,qKAAC,qHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAI,WAAU;;kGACb,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;;4CAAmH;0DAElJ,qKAAC;gDAAK,WAAU;;;;;;;;;;;;kDAIlB,qKAAC;wCACC,WAAU;wCACV,cAAc,IAAM,qBAAqB;wCACzC,cAAc,IAAM,qBAAqB;;0DAEzC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;;oDAA4H;kEAExJ,qKAAC,6MAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,WAAW,CAAC,uCAAuC,EAAE,oBAAoB,eAAe,IAAI;;;;;;kEACnH,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,qKAAC;gDAAI,WAAW,CAAC,uFAAuF,EACtG,oBAAoB,sCAAsC,sCAC1D;0DACA,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;0EAAyE;;;;;;4DACvF,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,qKAAC,qHAAA,CAAA,UAAI;oEAEH,MAAM,QAAQ,IAAI;oEAClB,WAAU;8EAEV,cAAA,qKAAC;wEAAI,WAAU;;0FACb,qKAAC;gFAAI,WAAU;;kGACb,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,IAAI;;;;;;kGAEf,qKAAC;wFAAI,WAAU;kGACZ,QAAQ,WAAW;;;;;;;;;;;;0FAGxB,qKAAC;gFAAI,WAAU;0FACb,cAAA,qKAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;mEAdd;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAyBjB,qKAAC,qHAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;;4CAAmH;0DAEpJ,qKAAC;gDAAK,WAAU;;;;;;;;;;;;;;;;;;0CAMpB,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC,qHAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,qKAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,qKAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,qKAAC;wCACC,WAAU;wCACV,SAAS;wCACT,cAAW;kDAEX,cAAA,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAK,WAAW,CAAC,+FAA+F,EAC/G,iBAAiB,4BAA4B,IAC7C;;;;;;8DACF,qKAAC;oDAAK,WAAW,CAAC,yEAAyE,EACzF,iBAAiB,cAAc,IAC/B;;;;;;8DACF,qKAAC;oDAAK,WAAW,CAAC,+FAA+F,EAC/G,iBAAiB,8BAA8B,IAC/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASb,WAAW,gCACV,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;;0CACb,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,SAAS;0CACtB,cAAA,qKAAC,sHAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAGd,qKAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAK,WAAU;;;;;;sDAChB,qKAAC;4CAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMtB,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;;kDACC,qKAAC;wCACC,WAAU;wCACV,SAAS,IAAM,mBAAmB,CAAC;;0DAEnC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,SAAS;gDAAiB,WAAU;0DAA6E;;;;;;0DAG9J,qKAAC,6MAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,kBAAkB,eAAe,IAAI;;;;;;;;;;;;oCAE7G,iCACC,qKAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,qKAAC,qHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,qKAAC;;kDACC,qKAAC;wCACC,WAAU;wCACV,SAAS,IAAM,sBAAsB,CAAC;;0DAEtC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,SAAS;gDAAiB,WAAU;0DAA4E;;;;;;0DAG9I,qKAAC,6MAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,qBAAqB,eAAe,IAAI;;;;;;;;;;;;oCAEhH,oCACC,qKAAC;wCAAI,WAAU;kDACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,qKAAC,qHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,qKAAC;;kDACC,qKAAC;wCACC,WAAU;wCACV,SAAS,IAAM,gCAAgC,CAAC;;0DAEhD,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,SAAS;gDAAiB,WAAU;0DAA8E;;;;;;0DAGlJ,qKAAC,6MAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,+BAA+B,eAAe,IAAI;;;;;;;;;;;;oCAE1H,8CACC,qKAAC;wCAAI,WAAU;kDACZ,4BAA4B,GAAG,CAAC,CAAC,SAAS,sBACzC,qKAAC,qHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,qKAAC;0CACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;0CAMH,qKAAC;;kDACC,qKAAC;wCACC,WAAU;wCACV,SAAS,IAAM,qBAAqB,CAAC;;0DAErC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,SAAS;gDAAiB,WAAU;0DAA4E;;;;;;0DAGpI,qKAAC,6MAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAW,CAAC,kCAAkC,EAAE,oBAAoB,eAAe,IAAI;;;;;;;;;;;;oCAE/G,mCACC,qKAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,qKAAC,qHAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,SAAS;gDACT,WAAU;0DAET,QAAQ,IAAI;+CALR;;;;;;;;;;;;;;;;0CAaf,qKAAC;0CACC,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;0CAMH,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/components/global/Footer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { Mail, Phone, MapPin, ArrowRight, Facebook, Instagram, Linkedin, Twitter, MessageCircle, Globe } from 'lucide-react';\r\n\r\nconst Footer: React.FC = () => {\r\n  return (\r\n    <footer className=\"relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden min-h-screen flex flex-col justify-center\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0 opacity-5\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/20 to-transparent\"></div>\r\n        <div className=\"absolute top-20 right-0 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute bottom-0 left-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative max-w-7xl mx-auto px-6 py-16\">\r\n        {/* Top Section - CTA */}\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent\">\r\n            Ready to Grow Your Business?\r\n          </h2>\r\n          <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\r\n            Let&apos;s create something amazing together. Get your free consultation today.\r\n          </p>\r\n          <Link href=\"/free-estimate\">\r\n            <button className=\"group inline-flex items-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl shadow-blue-500/25\">\r\n              Get Free Proposal\r\n              <ArrowRight size={20} className=\"ml-2 transition-transform duration-300 group-hover:translate-x-1\" />\r\n            </button>\r\n          </Link>\r\n        </div>\r\n\r\n        {/* Main Content */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12\">\r\n          {/* Company Info */}\r\n          <div className=\"lg:col-span-2\">\r\n            <div className=\"flex items-center mb-6\">\r\n              <Link href=\"/\">\r\n                <Image \r\n                  src=\"/VesaLogo.svg\" \r\n                  alt=\"VESA Solutions Logo\" \r\n                  width={120} \r\n                  height={46}\r\n                  className=\"w-30 h-auto transition-transform duration-300 hover:scale-105\"\r\n                />\r\n              </Link>\r\n            </div>\r\n            <p className=\"text-gray-300 text-lg leading-relaxed mb-8\">\r\n              Full-service digital marketing agency helping businesses attract, impress, and convert more leads online since 2015.\r\n            </p>\r\n            \r\n            {/* Contact Info */}\r\n            <div className=\"space-y-4\">\r\n              <a\r\n                href=\"mailto:<EMAIL>\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Mail size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span><EMAIL></span>\r\n              </a>\r\n              <a\r\n                href=\"tel:+***********\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Phone size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span>****** 628 3793</span>\r\n              </a>\r\n              <a\r\n                href=\"tel:+355694046408\"\r\n                className=\"flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <Phone size={18} className=\"mr-3 text-blue-400 group-hover:scale-110 transition-transform\" />\r\n                <span>+355 69 404 6408</span>\r\n              </a>\r\n              <a \r\n                href=\"https://share.google/T9q3WjqOOmMHrBnJY\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"flex items-start text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group\"\r\n              >\r\n                <MapPin size={18} className=\"mr-3 text-blue-400 mt-0.5 flex-shrink-0 group-hover:scale-110 transition-transform\" />\r\n                <span>Bulevardi Dyrrah, Pallati 394, Kati 4-t<br />2001, Durrës, Albania</span>\r\n              </a>\r\n              <div className=\"flex items-center text-gray-300\">\r\n                <div className=\"w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse\"></div>\r\n                <span className=\"text-green-400 font-medium\">Open 24 Hours</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Services Grid */}\r\n          <div className=\"lg:col-span-3 grid grid-cols-1 md:grid-cols-4 gap-8\">\r\n            {/* SEO Services */}\r\n            <div>\r\n              <Link href=\"/seo-search-engine-optimization\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-green-400 mb-6 relative transition-colors duration-300\">\r\n                  SEO Services\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-green-400 to-green-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/on-page-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  On-Page SEO\r\n                </Link></li>\r\n                <li><Link href=\"/off-page-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Off-Page SEO\r\n                </Link></li>\r\n                <li><Link href=\"/technical-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Technical SEO\r\n                </Link></li>\r\n                <li><Link href=\"/local-seo\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Local SEO\r\n                </Link></li>\r\n                <li><Link href=\"/content-writing\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Content Writing\r\n                </Link></li>\r\n                <li><Link href=\"/seo-analytics\" className=\"text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  SEO Analytics\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Web Development */}\r\n            <div>\r\n              <Link href=\"/web-development\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-blue-400 mb-6 relative transition-colors duration-300\">\r\n                  Web Development\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/custom-website-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Custom Websites\r\n                </Link></li>\r\n                <li><Link href=\"/ecommerce-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  E-commerce Dev\r\n                </Link></li>\r\n                <li><Link href=\"/mobile-app-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Mobile Apps\r\n                </Link></li>\r\n                <li><Link href=\"/website-speed-optimization\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 flex-shrink-0 transition-all duration-300 group-hover:w-2\"></span>\r\n                  <span className=\"whitespace-nowrap\">Speed Optimization</span>\r\n                </Link></li>\r\n                <li><Link href=\"/web-application-development\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Web Applications\r\n                </Link></li>\r\n                <li><Link href=\"/website-maintenance-support\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Maintenance\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Digital Marketing */}\r\n            <div>\r\n              <Link href=\"/digital-marketing\" className=\"group\">\r\n                <h3 className=\"text-lg font-bold text-white hover:text-purple-400 mb-6 relative transition-colors duration-300\">\r\n                  Digital Marketing\r\n                  <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-purple-400 to-purple-600 group-hover:w-full transition-all duration-300\"></span>\r\n                </h3>\r\n              </Link>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/ppc\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  PPC Advertising\r\n                </Link></li>\r\n                <li><Link href=\"/email-marketing\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Email Marketing\r\n                </Link></li>\r\n                <li><Link href=\"/social-media\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Social Media\r\n                </Link></li>\r\n                <li><Link href=\"/branding-services\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Branding Services\r\n                </Link></li>\r\n                <li><Link href=\"/conversion-optimization\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Conversion Optimization\r\n                </Link></li>\r\n                <li><Link href=\"/reputation-management\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Reputation Management\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Company */}\r\n            <div>\r\n              <h3 className=\"text-lg font-bold text-white mb-6 relative\">\r\n                Company\r\n                <span className=\"absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600\"></span>\r\n              </h3>\r\n              <ul className=\"space-y-3\">\r\n                <li><Link href=\"/services\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Services\r\n                </Link></li>\r\n                <li><Link href=\"/case-studies\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Case Studies\r\n                </Link></li>\r\n                <li><Link href=\"/about\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  About Us\r\n                </Link></li>\r\n                <li><Link href=\"/contact-us\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Contact Us\r\n                </Link></li>\r\n                <li><Link href=\"/blog\" className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group\">\r\n                  <span className=\"w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2\"></span>\r\n                  Blog\r\n                </Link></li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Social Media & Bottom Section */}\r\n        <div className=\"flex flex-col lg:flex-row justify-between items-center pt-8 border-t border-gray-700\">\r\n          <div className=\"mb-6 lg:mb-0\">\r\n            <h4 className=\"text-white font-semibold mb-4\">Follow Our Journey</h4>\r\n            <div className=\"flex space-x-4\">\r\n              <a\r\n                href=\"https://m.facebook.com/VesaSolutions/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Facebook\"\r\n              >\r\n                <Facebook size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://www.instagram.com/vesasolutions/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-gradient-to-r hover:from-pink-500 hover:to-purple-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Instagram\"\r\n              >\r\n                <Instagram size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://al.linkedin.com/company/vesasolutions\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-700 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on LinkedIn\"\r\n              >\r\n                <Linkedin size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"#\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-blue-500 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Follow us on Twitter\"\r\n              >\r\n                <Twitter size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://wa.me/***********\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-green-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Chat with us on WhatsApp\"\r\n              >\r\n                <MessageCircle size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n              <a\r\n                href=\"https://vesasolutions.com/\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group w-12 h-12 bg-gray-800 hover:bg-indigo-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110\"\r\n                aria-label=\"Visit our website\"\r\n              >\r\n                <Globe size={20} className=\"text-gray-400 group-hover:text-white transition-colors\" />\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"text-center lg:text-right\">\r\n            <div className=\"text-2xl font-bold text-white mb-2\">Growing Businesses Since 2015</div>\r\n            <div className=\"text-gray-400 text-sm\">Trusted by 200+ companies worldwide</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Copyright */}\r\n        <div className=\"mt-12 pt-8 border-t border-gray-700 text-center\">\r\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\r\n            <p className=\"text-gray-400 text-sm\">\r\n              © 2025 Vesa Solutions Marketing Agency. All rights reserved.\r\n            </p>\r\n            <div className=\"flex flex-wrap justify-center md:justify-end space-x-6 text-sm\">\r\n              <Link href=\"/privacy-policy\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Privacy Policy</Link>\r\n              <Link href=\"/terms-of-service\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Terms of Service</Link>\r\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-blue-400 transition-colors\">Sitemap</Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAOA,MAAM,SAAmB;IACvB,qBACE,qKAAC;QAAO,WAAU;;0BAEhB,qKAAC;gBAAI,WAAU;;kCACb,qKAAC;wBAAI,WAAU;;;;;;kCACf,qKAAC;wBAAI,WAAU;;;;;;kCACf,qKAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAG,WAAU;0CAA4G;;;;;;0CAG1H,qKAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,qKAAC;oCAAO,WAAU;;wCAA2P;sDAE3Q,qKAAC,2MAAA,CAAA,aAAU;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMtC,qKAAC;wBAAI,WAAU;;0CAEb,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,qKAAC,sHAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;kDAIhB,qKAAC;wCAAE,WAAU;kDAA6C;;;;;;kDAK1D,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,qKAAC,2LAAA,CAAA,OAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC1B,qKAAC;kEAAK;;;;;;;;;;;;0DAER,qKAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,qKAAC,6LAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC3B,qKAAC;kEAAK;;;;;;;;;;;;0DAER,qKAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,qKAAC,6LAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC3B,qKAAC;kEAAK;;;;;;;;;;;;0DAER,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,qKAAC,mMAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC5B,qKAAC;;4DAAK;0EAAuC,qKAAC;;;;;4DAAK;;;;;;;;;;;;;0DAErD,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;;;;;;kEACf,qKAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;0CAMnD,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC;;0DACC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkC,WAAU;0DACrD,cAAA,qKAAC;oDAAG,WAAU;;wDAAiG;sEAE7G,qKAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,qKAAC;gDAAG,WAAU;;kEACZ,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAe,WAAU;;8EACtC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EACxC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAa,WAAU;;8EACpC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;;8EAC1C,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;kEAG9G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EACxC,qKAAC;oEAAK,WAAU;;;;;;gEAA4F;;;;;;;;;;;;;;;;;;;;;;;;kDAOlH,qKAAC;;0DACC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;0DACtC,cAAA,qKAAC;oDAAG,WAAU;;wDAAgG;sEAE5G,qKAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,qKAAC;gDAAG,WAAU;;kEACZ,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA8B,WAAU;;8EACrD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAyB,WAAU;;8EAChD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA0B,WAAU;;8EACjD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA8B,WAAU;;8EACrD,qKAAC;oEAAK,WAAU;;;;;;8EAChB,qKAAC;oEAAK,WAAU;8EAAoB;;;;;;;;;;;;;;;;;kEAEtC,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA+B,WAAU;;8EACtD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA+B,WAAU;;8EACtD,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;;;;;;;;;;;;;kDAOjH,qKAAC;;0DACC,qKAAC,qHAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DACxC,cAAA,qKAAC;oDAAG,WAAU;;wDAAkG;sEAE9G,qKAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAGpB,qKAAC;gDAAG,WAAU;;kEACZ,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAO,WAAU;;8EAC9B,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;;8EAC1C,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAqB,WAAU;;8EAC5C,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAA2B,WAAU;;8EAClD,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;kEAG/G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAyB,WAAU;;8EAChD,qKAAC;oEAAK,WAAU;;;;;;gEAA6F;;;;;;;;;;;;;;;;;;;;;;;;kDAOnH,qKAAC;;0DACC,qKAAC;gDAAG,WAAU;;oDAA6C;kEAEzD,qKAAC;wDAAK,WAAU;;;;;;;;;;;;0DAElB,qKAAC;gDAAG,WAAU;;kEACZ,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAY,WAAU;;8EACnC,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACvC,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;;8EAChC,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAc,WAAU;;8EACrC,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;kEAG7G,qKAAC;kEAAG,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DAAC,MAAK;4DAAQ,WAAU;;8EAC/B,qKAAC;oEAAK,WAAU;;;;;;gEAA2F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrH,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,mMAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEhC,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,qMAAA,CAAA,YAAS;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEjC,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,mMAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEhC,qKAAC;gDACC,MAAK;gDACL,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,iMAAA,CAAA,UAAO;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAE/B,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,iNAAA,CAAA,gBAAa;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAErC,qKAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAW;0DAEX,cAAA,qKAAC,6LAAA,CAAA,QAAK;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAKjC,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,qKAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAK3C,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,qKAAC;oCAAI,WAAU;;sDACb,qKAAC,qHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAkB,WAAU;sDAAsD;;;;;;sDAC7F,qKAAC,qHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAoB,WAAU;sDAAsD;;;;;;sDAC/F,qKAAC,qHAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpG;uCAEe", "debugId": null}}, {"offset": {"line": 2493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/vesa/vesaweb/pages/contact-us.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport Head from 'next/head';\r\nimport Header from '@/components/global/Header';\r\nimport Footer from '@/components/global/Footer';\r\nimport { \r\n  Mail, \r\n  Phone, \r\n  MapPin, \r\n  Clock, \r\n  MessageCircle, \r\n  Calendar,\r\n  ArrowRight,\r\n  CheckCircle,\r\n  Users,\r\n  Target,\r\n  TrendingUp,\r\n  Globe\r\n} from 'lucide-react';\r\n\r\n// Reusable Form Component (simplified version based on your existing Form.tsx)\r\nconst ContactForm = () => {\r\n  const [formData, setFormData] = useState({\r\n    businessName: '',\r\n    fullName: '',\r\n    email: '',\r\n    phone: '',\r\n    location: '',\r\n    website: '',\r\n    service: 'General Inquiry',\r\n    message: ''\r\n  });\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [submitStatus, setSubmitStatus] = useState('idle');\r\n\r\n  const services = [\r\n    'General Inquiry',\r\n    'SEO Services',\r\n    'Web Development',\r\n    'PPC Advertising',\r\n    'Social Media Marketing',\r\n    'Content Writing',\r\n    'Email Marketing',\r\n    'Reputation Management'\r\n  ];\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    setIsSubmitting(true);\r\n    \r\n    // Simulate form submission\r\n    setTimeout(() => {\r\n      setSubmitStatus('success');\r\n      setIsSubmitting(false);\r\n      // Reset form after success\r\n      setTimeout(() => {\r\n        setSubmitStatus('idle');\r\n        setFormData({\r\n          businessName: '',\r\n          fullName: '',\r\n          email: '',\r\n          phone: '',\r\n          location: '',\r\n          website: '',\r\n          service: 'General Inquiry',\r\n          message: ''\r\n        });\r\n      }, 3000);\r\n    }, 2000);\r\n  };\r\n\r\n  if (submitStatus === 'success') {\r\n    return (\r\n      <div className=\"bg-white rounded-2xl shadow-xl p-8 text-center\">\r\n        <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n          <CheckCircle className=\"w-8 h-8 text-green-600\" />\r\n        </div>\r\n        <h3 className=\"text-2xl font-bold text-gray-800 mb-2\">Thank You! 🎉</h3>\r\n        <p className=\"text-gray-600 mb-4\">\r\n          Your message has been sent successfully. We&apos;ll get back to you within 24 hours.\r\n        </p>\r\n        <p className=\"text-sm text-gray-500\">\r\n          Check your email for confirmation and next steps.\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-xl p-8\">\r\n      <h3 className=\"text-2xl font-bold text-gray-800 mb-2\">\r\n        Let&apos;s Start Growing Your Business\r\n      </h3>\r\n      <p className=\"text-gray-600 mb-6\">\r\n        Tell us about your project and we&apos;ll create a custom strategy for your success.\r\n      </p>\r\n\r\n      <div className=\"space-y-4\">\r\n        {/* Business Name & Your Name */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n          <input \r\n            name=\"businessName\"\r\n            value={formData.businessName}\r\n            onChange={handleInputChange}\r\n            type=\"text\" \r\n            placeholder=\"Business Name*\" \r\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n          />\r\n          <input \r\n            name=\"fullName\"\r\n            value={formData.fullName}\r\n            onChange={handleInputChange}\r\n            type=\"text\" \r\n            placeholder=\"Your Name*\" \r\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n          />\r\n        </div>\r\n\r\n        {/* Email & Phone */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n          <input \r\n            name=\"email\"\r\n            value={formData.email}\r\n            onChange={handleInputChange}\r\n            type=\"email\" \r\n            placeholder=\"Email Address*\" \r\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n          />\r\n          <input \r\n            name=\"phone\"\r\n            value={formData.phone}\r\n            onChange={handleInputChange}\r\n            type=\"tel\" \r\n            placeholder=\"Phone Number*\" \r\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n          />\r\n        </div>\r\n\r\n        {/* Location & Website */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n          <input \r\n            name=\"location\"\r\n            value={formData.location}\r\n            onChange={handleInputChange}\r\n            type=\"text\" \r\n            placeholder=\"Business Location*\" \r\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n          />\r\n          <input \r\n            name=\"website\"\r\n            value={formData.website}\r\n            onChange={handleInputChange}\r\n            type=\"url\" \r\n            placeholder=\"Website URL (optional)\" \r\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n          />\r\n        </div>\r\n\r\n        {/* Service Selection */}\r\n        <select \r\n          name=\"service\"\r\n          value={formData.service}\r\n          onChange={handleInputChange}\r\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n        >\r\n          {services.map(service => (\r\n            <option key={service} value={service}>{service}</option>\r\n          ))}\r\n        </select>\r\n\r\n        {/* Message */}\r\n        <textarea \r\n          name=\"message\"\r\n          value={formData.message}\r\n          onChange={handleInputChange}\r\n          placeholder=\"Tell us about your business goals and how we can help you achieve them*\"\r\n          rows={4}\r\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-colors\"\r\n        />\r\n\r\n        {/* Submit Button */}\r\n        <button \r\n          onClick={handleSubmit}\r\n          disabled={isSubmitting}\r\n          className=\"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed text-white font-bold py-4 rounded-lg transition-all duration-300 flex items-center justify-center\"\r\n        >\r\n          {isSubmitting ? (\r\n            <>\r\n              <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n              </svg>\r\n              Sending Message...\r\n            </>\r\n          ) : (\r\n            <>\r\n              Send Message\r\n              <ArrowRight className=\"ml-2 w-5 h-5\" />\r\n            </>\r\n          )}\r\n        </button>\r\n      </div>\r\n\r\n      {/* Trust indicator */}\r\n      <p className=\"text-xs text-gray-500 text-center mt-4\">\r\n        🔒 Your information is secure and will never be shared. We&apos;ll contact you within 24 hours.\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ContactPage = () => {\r\n  const contactMethods = [\r\n    {\r\n      icon: Mail,\r\n      title: \"Email Us\",\r\n      description: \"Get a detailed response within 24 hours\",\r\n      value: \"<EMAIL>\",\r\n      action: \"mailto:<EMAIL>\",\r\n      actionText: \"Send Email\"\r\n    },\r\n    {\r\n      icon: Phone,\r\n      title: \"Call Us\",\r\n      description: \"Speak directly with our digital marketing experts\",\r\n      value: \"+355 69 404 6408\",\r\n      action: \"tel:+***********\",\r\n      actionText: \"Call Now\"\r\n    },\r\n    {\r\n      icon: MessageCircle,\r\n      title: \"WhatsApp\",\r\n      description: \"Quick chat for immediate questions\",\r\n      value: \"Message us instantly\",\r\n      action: \"https://wa.me/***********\",\r\n      actionText: \"Start Chat\"\r\n    },\r\n    {\r\n      icon: Calendar,\r\n      title: \"Schedule Meeting\",\r\n      description: \"Book a free 30-minute consultation\",\r\n      value: \"Available 24/7\",\r\n      action: \"#contact-form\",\r\n      actionText: \"Book Call\"\r\n    }\r\n  ];\r\n\r\n  const officeFeatures = [\r\n    {\r\n      icon: Users,\r\n      title: \"Expert Team\",\r\n      description: \"20+ digital marketing specialists\"\r\n    },\r\n    {\r\n      icon: Target,\r\n      title: \"Proven Results\",\r\n      description: \"200+ successful client projects\"\r\n    },\r\n    {\r\n      icon: TrendingUp,\r\n      title: \"Growth Focus\",\r\n      description: \"Average 300% ROI improvement\"\r\n    },\r\n    {\r\n      icon: Globe,\r\n      title: \"Global Reach\",\r\n      description: \"Serving clients worldwide\"\r\n    }\r\n  ];\r\n\r\n  const faqs = [\r\n    {\r\n      question: \"How quickly can you start working on my project?\",\r\n      answer: \"We can typically begin your project within 1-2 business days after our initial consultation and agreement.\"\r\n    },\r\n    {\r\n      question: \"Do you offer free consultations?\",\r\n      answer: \"Yes! We offer complimentary 30-minute consultations to discuss your business goals and how we can help achieve them.\"\r\n    },\r\n    {\r\n      question: \"What's included in your digital marketing services?\",\r\n      answer: \"Our comprehensive services include SEO, PPC advertising, web development, social media marketing, content creation, and reputation management.\"\r\n    },\r\n    {\r\n      question: \"How do you measure success?\",\r\n      answer: \"We track key metrics like organic traffic growth, conversion rates, ROI, and specific KPIs aligned with your business objectives.\"\r\n    }\r\n  ];\r\n\r\n  const scrollToForm = () => {\r\n    const formElement = document.getElementById('contact-form');\r\n    if (formElement) {\r\n      formElement.scrollIntoView({ behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Head>\r\n        <title>Contact Vesa Solutions - Digital Marketing Experts in Albania</title>\r\n        <meta name=\"description\" content=\"Contact Vesa Solutions for expert digital marketing services in Albania. Get free consultation on SEO, web development, PPC, and social media marketing. Call +355 69 404 6408 today!\" />\r\n        \r\n        {/* Open Graph tags for social sharing */}\r\n        <meta property=\"og:title\" content=\"Contact Vesa Solutions - Digital Marketing Experts\" />\r\n        <meta property=\"og:description\" content=\"Get free consultation on digital marketing services in Albania. SEO, web development, PPC, and more.\" />\r\n        <meta property=\"og:type\" content=\"website\" />\r\n        <meta property=\"og:url\" content=\"https://vesasolutions.com/contact\" />\r\n        \r\n        {/* Additional SEO tags */}\r\n        <meta name=\"keywords\" content=\"digital marketing albania, SEO albania, web development durres, contact vesa solutions, digital marketing consultation\" />\r\n        <meta name=\"author\" content=\"Vesa Solutions\" />\r\n        <meta name=\"robots\" content=\"index, follow\" />\r\n        <link rel=\"canonical\" href=\"https://vesasolutions.com/contact\" />\r\n      </Head>\r\n\r\n      <div className=\"min-h-screen bg-gray-50\">\r\n      <Header isVisible={true} />\r\n      \r\n      {/* Hero Section */}\r\n      <section className=\"relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 text-white py-20 overflow-hidden\">\r\n        {/* Background Pattern */}\r\n        <div className=\"absolute inset-0 opacity-10\">\r\n          <div className=\"absolute top-20 left-10 w-32 h-32 border border-white/20 rounded-lg transform rotate-12\"></div>\r\n          <div className=\"absolute top-40 right-20 w-24 h-24 border border-white/20 rounded-lg transform -rotate-12\"></div>\r\n          <div className=\"absolute bottom-32 left-32 w-16 h-16 border border-white/20 rounded-lg transform rotate-45\"></div>\r\n        </div>\r\n\r\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center\">\r\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight\">\r\n              Ready to <span className=\"text-blue-400\">Transform</span><br />\r\n              Your Digital Presence?\r\n            </h1>\r\n            <p className=\"text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto\">\r\n              Get in touch with our digital marketing experts and discover how we can help your business thrive online.\r\n            </p>\r\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n              <button \r\n                onClick={scrollToForm}\r\n                className=\"bg-white text-blue-900 px-8 py-4 rounded-full font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105\"\r\n              >\r\n                Get Free Consultation\r\n              </button>\r\n              <a\r\n                href=\"tel:+***********\"\r\n                className=\"border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-blue-900 transition-all duration-300\"\r\n              >\r\n                Call Us Now\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Methods Grid */}\r\n      <section className=\"py-16 -mt-10 relative z-10\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n            {contactMethods.map((method, index) => (\r\n              <div key={index} className=\"bg-white rounded-2xl shadow-lg p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2\">\r\n                <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                  <method.icon className=\"w-8 h-8 text-blue-600\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-bold text-gray-800 mb-2\">{method.title}</h3>\r\n                <p className=\"text-gray-600 text-sm mb-3\">{method.description}</p>\r\n                <p className=\"text-gray-800 font-semibold mb-4\">{method.value}</p>\r\n                {method.action.startsWith('#') ? (\r\n                  <button \r\n                    onClick={scrollToForm}\r\n                    className=\"inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors\"\r\n                  >\r\n                    {method.actionText}\r\n                    <ArrowRight className=\"ml-1 w-4 h-4\" />\r\n                  </button>\r\n                ) : (\r\n                  <a \r\n                    href={method.action}\r\n                    target={method.action.startsWith('http') ? \"_blank\" : undefined}\r\n                    rel={method.action.startsWith('http') ? \"noopener noreferrer\" : undefined}\r\n                    className=\"inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors\"\r\n                  >\r\n                    {method.actionText}\r\n                    <ArrowRight className=\"ml-1 w-4 h-4\" />\r\n                  </a>\r\n                )}\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Main Contact Section */}\r\n      <section id=\"contact-form\" className=\"py-16\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\">\r\n            {/* Contact Form */}\r\n            <div>\r\n              <ContactForm />\r\n            </div>\r\n\r\n            {/* Contact Information & Office Details */}\r\n            <div className=\"space-y-8\">\r\n              {/* Office Location */}\r\n              <div className=\"bg-white rounded-2xl shadow-lg p-8\">\r\n                <h3 className=\"text-2xl font-bold text-gray-800 mb-6\">Visit Our Office</h3>\r\n                \r\n                <div className=\"space-y-4 mb-6\">\r\n                  <div className=\"flex items-start\">\r\n                    <MapPin className=\"w-6 h-6 text-blue-600 mt-1 mr-3 flex-shrink-0\" />\r\n                    <div>\r\n                      <p className=\"font-semibold text-gray-800\">Headquarters</p>\r\n                      <p className=\"text-gray-600\">\r\n                        Bulevardi Dyrrah, Pallati 394, Kati 4-t<br />\r\n                        2001, Durrës, Albania\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className=\"flex items-center\">\r\n                    <Clock className=\"w-6 h-6 text-blue-600 mr-3\" />\r\n                    <div>\r\n                      <p className=\"font-semibold text-gray-800\">Business Hours</p>\r\n                      <p className=\"text-gray-600\">Available 24/7 for urgent matters</p>\r\n                      <p className=\"text-sm text-gray-500\">Mon-Fri: 9AM-6PM CET</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Google Maps */}\r\n                <div className=\"w-full h-48 rounded-lg overflow-hidden mb-4 border border-gray-200 shadow-sm\">\r\n                  <iframe\r\n                    src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2995.8766!2d19.4458424!3d41.3158368!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x134fda47e5beeea7%3A0x9d60d9bb977e035!2sVesa%20Solutions!5e0!3m2!1sen!2s!4v1647380947890!5m2!1sen!2s\"\r\n                    width=\"100%\"\r\n                    height=\"100%\"\r\n                    style={{ border: 0 }}\r\n                    allowFullScreen\r\n                    loading=\"lazy\"\r\n                    referrerPolicy=\"no-referrer-when-downgrade\"\r\n                    title=\"Vesa Solutions - Pallati 394, Kati 4-t, 4th Floor, Building 394, Durrës, Albania\"\r\n                    className=\"rounded-lg\"\r\n                  ></iframe>\r\n                </div>\r\n\r\n                <a \r\n                  href=\"https://www.google.com/maps/place/Vesa+Solutions/@41.3158368,19.4458424,17z/data=!3m1!4b1!4m6!3m5!1s0x134fda47e5beeea7:0x9d60d9bb977e035!8m2!3d41.3158328!4d19.4484173!16s%2Fg%2F11gcx_qzyj?hl=en&entry=ttu&g_ep=EgoyMDI1MDYyOS4wIKXMDSoASAFQAw%3D%3D\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors\"\r\n                >\r\n                  Get Directions\r\n                  <ArrowRight className=\"ml-1 w-4 h-4\" />\r\n                </a>\r\n              </div>\r\n\r\n              {/* Why Choose Us */}\r\n              <div className=\"bg-white rounded-2xl shadow-lg p-8\">\r\n                <h3 className=\"text-2xl font-bold text-gray-800 mb-6\">Why Choose Vesa Solutions?</h3>\r\n                \r\n                <div className=\"grid grid-cols-2 gap-4\">\r\n                  {officeFeatures.map((feature, index) => (\r\n                    <div key={index} className=\"text-center\">\r\n                      <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3\">\r\n                        <feature.icon className=\"w-6 h-6 text-blue-600\" />\r\n                      </div>\r\n                      <h4 className=\"font-semibold text-gray-800 text-sm mb-1\">{feature.title}</h4>\r\n                      <p className=\"text-xs text-gray-600\">{feature.description}</p>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* FAQ Section */}\r\n      <section className=\"py-16 bg-white\">\r\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center mb-12\">\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-800 mb-4\">\r\n              Frequently Asked Questions\r\n            </h2>\r\n            <p className=\"text-lg text-gray-600\">\r\n              Get quick answers to common questions about our services\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"space-y-6\">\r\n            {faqs.map((faq, index) => (\r\n              <div key={index} className=\"bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-colors\">\r\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">{faq.question}</h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">{faq.answer}</p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"text-center mt-12\">\r\n            <p className=\"text-gray-600 mb-4\">Still have questions?</p>\r\n            <a \r\n              href=\"mailto:<EMAIL>\"\r\n              className=\"inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\r\n            >\r\n              <Mail className=\"mr-2 w-5 h-5\" />\r\n              Email Us Directly\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <Footer />\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ContactPage;"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AAeA,+EAA+E;AAC/E,MAAM,cAAc;IAClB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,cAAc;QACd,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;QACV,SAAS;QACT,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,gBAAgB;QAEhB,2BAA2B;QAC3B,WAAW;YACT,gBAAgB;YAChB,gBAAgB;YAChB,2BAA2B;YAC3B,WAAW;gBACT,gBAAgB;gBAChB,YAAY;oBACV,cAAc;oBACd,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,UAAU;oBACV,SAAS;oBACT,SAAS;oBACT,SAAS;gBACX;YACF,GAAG;QACL,GAAG;IACL;IAEA,IAAI,iBAAiB,WAAW;QAC9B,qBACE,qKAAC;YAAI,WAAU;;8BACb,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;8BAEzB,qKAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,qKAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAGlC,qKAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAK3C;IAEA,qBACE,qKAAC;QAAI,WAAU;;0BACb,qKAAC;gBAAG,WAAU;0BAAwC;;;;;;0BAGtD,qKAAC;gBAAE,WAAU;0BAAqB;;;;;;0BAIlC,qKAAC;gBAAI,WAAU;;kCAEb,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCACC,MAAK;gCACL,OAAO,SAAS,YAAY;gCAC5B,UAAU;gCACV,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;0CAEZ,qKAAC;gCACC,MAAK;gCACL,OAAO,SAAS,QAAQ;gCACxB,UAAU;gCACV,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAKd,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCACC,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;0CAEZ,qKAAC;gCACC,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAKd,qKAAC;wBAAI,WAAU;;0CACb,qKAAC;gCACC,MAAK;gCACL,OAAO,SAAS,QAAQ;gCACxB,UAAU;gCACV,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;0CAEZ,qKAAC;gCACC,MAAK;gCACL,OAAO,SAAS,OAAO;gCACvB,UAAU;gCACV,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAKd,qKAAC;wBACC,MAAK;wBACL,OAAO,SAAS,OAAO;wBACvB,UAAU;wBACV,WAAU;kCAET,SAAS,GAAG,CAAC,CAAA,wBACZ,qKAAC;gCAAqB,OAAO;0CAAU;+BAA1B;;;;;;;;;;kCAKjB,qKAAC;wBACC,MAAK;wBACL,OAAO,SAAS,OAAO;wBACvB,UAAU;wBACV,aAAY;wBACZ,MAAM;wBACN,WAAU;;;;;;kCAIZ,qKAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,6BACC;;8CACE,qKAAC;oCAAI,WAAU;oCAA6C,OAAM;oCAA6B,MAAK;oCAAO,SAAQ;;sDACjH,qKAAC;4CAAO,WAAU;4CAAa,IAAG;4CAAK,IAAG;4CAAK,GAAE;4CAAK,QAAO;4CAAe,aAAY;;;;;;sDACxF,qKAAC;4CAAK,WAAU;4CAAa,MAAK;4CAAe,GAAE;;;;;;;;;;;;gCAC/C;;yDAIR;;gCAAE;8CAEA,qKAAC,2MAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;0BAO9B,qKAAC;gBAAE,WAAU;0BAAyC;;;;;;;;;;;;AAK5D;AAEA,MAAM,cAAc;IAClB,MAAM,iBAAiB;QACrB;YACE,MAAM,2LAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,OAAO;YACP,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM,6LAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;YACP,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM,iNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,aAAa;YACb,OAAO;YACP,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM,mMAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,OAAO;YACP,QAAQ;YACR,YAAY;QACd;KACD;IAED,MAAM,iBAAiB;QACrB;YACE,MAAM,6LAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,+LAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,2MAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,6LAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,OAAO;QACX;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;KACD;IAED,MAAM,eAAe;QACnB,MAAM,cAAc,SAAS,cAAc,CAAC;QAC5C,IAAI,aAAa;YACf,YAAY,cAAc,CAAC;gBAAE,UAAU;YAAS;QAClD;IACF;IAEA,qBACE;;0BACE,qKAAC,qHAAA,CAAA,UAAI;;kCACH,qKAAC;kCAAM;;;;;;kCACP,qKAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCAGjC,qKAAC;wBAAK,UAAS;wBAAW,SAAQ;;;;;;kCAClC,qKAAC;wBAAK,UAAS;wBAAiB,SAAQ;;;;;;kCACxC,qKAAC;wBAAK,UAAS;wBAAU,SAAQ;;;;;;kCACjC,qKAAC;wBAAK,UAAS;wBAAS,SAAQ;;;;;;kCAGhC,qKAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,qKAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;kCAC5B,qKAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;kCAC5B,qKAAC;wBAAK,KAAI;wBAAY,MAAK;;;;;;;;;;;;0BAG7B,qKAAC;gBAAI,WAAU;;kCACf,qKAAC,wHAAA,CAAA,UAAM;wBAAC,WAAW;;;;;;kCAGnB,qKAAC;wBAAQ,WAAU;;0CAEjB,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAI,WAAU;;;;;;kDACf,qKAAC;wCAAI,WAAU;;;;;;kDACf,qKAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAG,WAAU;;gDAAgE;8DACnE,qKAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAAgB,qKAAC;;;;;gDAAK;;;;;;;sDAGjE,qKAAC;4CAAE,WAAU;sDAA2D;;;;;;sDAGxE,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;8DAGD,qKAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAST,qKAAC;wBAAQ,WAAU;kCACjB,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,qKAAC;wCAAgB,WAAU;;0DACzB,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC,OAAO,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEzB,qKAAC;gDAAG,WAAU;0DAAwC,OAAO,KAAK;;;;;;0DAClE,qKAAC;gDAAE,WAAU;0DAA8B,OAAO,WAAW;;;;;;0DAC7D,qKAAC;gDAAE,WAAU;0DAAoC,OAAO,KAAK;;;;;;4CAC5D,OAAO,MAAM,CAAC,UAAU,CAAC,qBACxB,qKAAC;gDACC,SAAS;gDACT,WAAU;;oDAET,OAAO,UAAU;kEAClB,qKAAC,2MAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;qEAGxB,qKAAC;gDACC,MAAM,OAAO,MAAM;gDACnB,QAAQ,OAAO,MAAM,CAAC,UAAU,CAAC,UAAU,WAAW;gDACtD,KAAK,OAAO,MAAM,CAAC,UAAU,CAAC,UAAU,wBAAwB;gDAChE,WAAU;;oDAET,OAAO,UAAU;kEAClB,qKAAC,2MAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;uCAvBlB;;;;;;;;;;;;;;;;;;;;kCAiClB,qKAAC;wBAAQ,IAAG;wBAAe,WAAU;kCACnC,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDAEb,qKAAC;kDACC,cAAA,qKAAC;;;;;;;;;;kDAIH,qKAAC;wCAAI,WAAU;;0DAEb,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAG,WAAU;kEAAwC;;;;;;kEAEtD,qKAAC;wDAAI,WAAU;;0EACb,qKAAC;gEAAI,WAAU;;kFACb,qKAAC,mMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,qKAAC;;0FACC,qKAAC;gFAAE,WAAU;0FAA8B;;;;;;0FAC3C,qKAAC;gFAAE,WAAU;;oFAAgB;kGACY,qKAAC;;;;;oFAAK;;;;;;;;;;;;;;;;;;;0EAMnD,qKAAC;gEAAI,WAAU;;kFACb,qKAAC,6LAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,qKAAC;;0FACC,qKAAC;gFAAE,WAAU;0FAA8B;;;;;;0FAC3C,qKAAC;gFAAE,WAAU;0FAAgB;;;;;;0FAC7B,qKAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;;;;;;;kEAM3C,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC;4DACC,KAAI;4DACJ,OAAM;4DACN,QAAO;4DACP,OAAO;gEAAE,QAAQ;4DAAE;4DACnB,eAAe;4DACf,SAAQ;4DACR,gBAAe;4DACf,OAAM;4DACN,WAAU;;;;;;;;;;;kEAId,qKAAC;wDACC,MAAK;wDACL,QAAO;wDACP,KAAI;wDACJ,WAAU;;4DACX;0EAEC,qKAAC,2MAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;0DAK1B,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAG,WAAU;kEAAwC;;;;;;kEAEtD,qKAAC;wDAAI,WAAU;kEACZ,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,qKAAC;gEAAgB,WAAU;;kFACzB,qKAAC;wEAAI,WAAU;kFACb,cAAA,qKAAC,QAAQ,IAAI;4EAAC,WAAU;;;;;;;;;;;kFAE1B,qKAAC;wEAAG,WAAU;kFAA4C,QAAQ,KAAK;;;;;;kFACvE,qKAAC;wEAAE,WAAU;kFAAyB,QAAQ,WAAW;;;;;;;+DALjD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgBxB,qKAAC;wBAAQ,WAAU;kCACjB,cAAA,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAGlE,qKAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,qKAAC;oCAAI,WAAU;8CACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,qKAAC;4CAAgB,WAAU;;8DACzB,qKAAC;oDAAG,WAAU;8DAA4C,IAAI,QAAQ;;;;;;8DACtE,qKAAC;oDAAE,WAAU;8DAAiC,IAAI,MAAM;;;;;;;2CAFhD;;;;;;;;;;8CAOd,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,qKAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,qKAAC,2LAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAOzC,qKAAC,wHAAA,CAAA,UAAM;;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}]}