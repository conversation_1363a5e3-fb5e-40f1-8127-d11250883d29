import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';

const DigitalMarketingFAQSection: React.FC = () => {
  const [openFaq, setOpenFaq] = useState<number | null>(null);

  const faqs = [
    {
      question: 'What exactly does a digital marketing company do for my business?',
      answer: 'A digital marketing company like VESA Solutions creates and executes comprehensive online marketing strategies including PPC advertising, social media marketing, email campaigns, content marketing, and conversion optimization. We analyze your business, competitors, and target audience to create custom campaigns that drive qualified leads and increase sales.'
    },
    {
      question: 'How long does it take to see results from digital marketing services?',
      answer: 'Digital marketing results can vary by channel. PPC and social media ads can show immediate results within days, while email marketing and content strategies typically show significant improvements within 30-90 days. Our comprehensive approach ensures both quick wins and long-term sustainable growth.'
    },
    {
      question: 'How do I know if digital marketing is working for my business?',
      answer: 'Key indicators include increased website traffic, higher conversion rates, more qualified leads, improved brand awareness, and ultimately increased revenue from digital channels. We provide detailed monthly reports with comprehensive analytics showing campaign performance, ROI metrics, and growth trends.'
    },
    {
      question: 'Can I do digital marketing myself, or should I hire an agency like VESA Solutions?',
      answer: 'While basic digital marketing can be learned, professional campaigns require extensive expertise, specialized tools, and significant time investment. An experienced agency like VESA Solutions brings proven strategies, dedicated resources, industry knowledge, and the ability to manage multiple channels effectively while you focus on running your business.'
    },
    {
      question: 'What makes VESA Solutions different from other digital marketing agencies?',
      answer: 'VESA Solutions stands out through our data-driven approach, transparent reporting, custom strategies for each client, and proven track record of delivering measurable results. We focus on integrated multi-channel campaigns rather than single-channel solutions, and we provide comprehensive analytics and optimization for maximum ROI.'
    },
    {
      question: 'Do I need ongoing digital marketing, or can I stop after achieving initial results?',
      answer: 'Digital marketing requires ongoing management and optimization. Market conditions change, competitors adjust their strategies, and new opportunities emerge constantly. Ongoing digital marketing ensures you maintain and improve your results while adapting to industry changes and scaling successful campaigns.'
    },
    {
      question: 'How much does professional digital marketing cost?',
      answer: 'Digital marketing costs vary based on your industry, target audience, campaign complexity, and business goals. VESA Solutions offers flexible pricing options including monthly retainers and performance-based pricing. We provide custom quotes after analyzing your specific needs and competitive landscape.'
    },
    {
      question: 'Will digital marketing work for my specific industry?',
      answer: 'Yes! VESA Solutions has extensive experience across numerous industries including e-commerce, healthcare, legal services, real estate, professional services, and technology. We adapt our proven digital marketing strategies to your specific industry regulations, target audience, and competitive landscape.'
    },
    {
      question: 'What is the difference between digital marketing and traditional advertising?',
      answer: 'Digital marketing offers precise targeting, real-time analytics, immediate optimization capabilities, and typically better ROI than traditional advertising. Digital campaigns can be adjusted instantly based on performance data, while traditional advertising provides broader reach but limited targeting and measurement capabilities.'
    },
    {
      question: 'How do you measure digital marketing success and ROI?',
      answer: 'We track comprehensive metrics including website traffic, conversion rates, cost per acquisition, customer lifetime value, return on ad spend (ROAS), and overall ROI. Our detailed reporting shows how digital marketing efforts directly impact your business goals and bottom line, with clear attribution across all channels.'
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-4xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
            Frequently Asked Questions About Digital Marketing
          </h2>
          <p className="text-xl text-gray-600">
            Get answers to common questions about digital marketing services, strategies, and what to expect when working with VESA Solutions.
          </p>
        </div>

        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <div key={index} className="bg-white rounded-2xl overflow-hidden shadow-lg">
              <button 
                onClick={() => setOpenFaq(openFaq === index ? null : index)}
                className="w-full p-8 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
              >
                <h3 className="text-xl font-semibold text-gray-800 pr-8">
                  {faq.question}
                </h3>
                <ChevronDown
                  size={24}
                  className={`text-purple-600 transform transition-transform ${openFaq === index ? 'rotate-180' : ''}`}
                />
              </button>
              {openFaq === index && (
                <div className="px-8 pb-8">
                  <p className="text-gray-600 leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default DigitalMarketingFAQSection;
