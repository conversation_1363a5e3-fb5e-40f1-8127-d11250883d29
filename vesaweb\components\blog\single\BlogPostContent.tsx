// components/blog/single/BlogPostContent.tsx - Blog post content with Portable Text
import React from 'react'
import Image from 'next/image'
import { PortableText } from '@portabletext/react'
import { BlogPost } from '@/types/blog'
import { urlForImage } from '@/lib/sanity-blog'

interface BlogPostContentProps {
  post: BlogPost
}

interface ImageValue {
  _type: 'image'
  asset: {
    _ref: string
    _type: 'reference'
  }
  alt?: string
  caption?: string
}



const BlogPostContent: React.FC<BlogPostContentProps> = ({ post }) => {
  const portableTextComponents = {
    types: {
      image: ({ value }: { value: ImageValue }) => (
        <div className="my-8">
          <div className="relative rounded-lg overflow-hidden shadow-lg">
            <Image
              src={urlForImage(value).width(800).height(450).url()}
              alt={value.alt || 'Blog post image'}
              width={800}
              height={450}
              className="w-full h-auto"
            />
          </div>
          {value.caption && (
            <p className="text-sm text-gray-600 text-center mt-2 italic">
              {value.caption}
            </p>
          )}
        </div>
      ),
    },
    block: {
      normal: ({ children }: { children?: React.ReactNode }) => (
        <p className="mb-6 text-gray-700 leading-relaxed text-lg">
          {children}
        </p>
      ),
      h2: ({ children }: { children?: React.ReactNode }) => (
        <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mt-12 mb-6 leading-tight">
          {children}
        </h2>
      ),
      h3: ({ children }: { children?: React.ReactNode }) => (
        <h3 className="text-xl md:text-2xl font-semibold text-gray-900 mt-10 mb-4 leading-tight">
          {children}
        </h3>
      ),
      h4: ({ children }: { children?: React.ReactNode }) => (
        <h4 className="text-lg md:text-xl font-semibold text-gray-900 mt-8 mb-3 leading-tight">
          {children}
        </h4>
      ),
      blockquote: ({ children }: { children?: React.ReactNode }) => (
        <blockquote className="border-l-4 border-blue-500 pl-6 py-4 my-8 bg-blue-50 rounded-r-lg">
          <div className="text-lg text-gray-700 italic leading-relaxed">
            {children}
          </div>
        </blockquote>
      ),
    },
    list: {
      bullet: ({ children }: { children?: React.ReactNode }) => (
        <ul className="mb-6 space-y-2 text-gray-700">
          {children}
        </ul>
      ),
      number: ({ children }: { children?: React.ReactNode }) => (
        <ol className="mb-6 space-y-2 text-gray-700 list-decimal list-inside">
          {children}
        </ol>
      ),
    },
    listItem: {
      bullet: ({ children }: { children?: React.ReactNode }) => (
        <li className="flex items-start">
          <span className="text-blue-500 mr-2 mt-2">•</span>
          <span className="text-lg leading-relaxed">{children}</span>
        </li>
      ),
      number: ({ children }: { children?: React.ReactNode }) => (
        <li className="text-lg leading-relaxed ml-4">
          {children}
        </li>
      ),
    },
    marks: {
      strong: ({ children }: { children?: React.ReactNode }) => (
        <strong className="font-semibold text-gray-900">{children}</strong>
      ),
      em: ({ children }: { children?: React.ReactNode }) => (
        <em className="italic">{children}</em>
      ),
      code: ({ children }: { children?: React.ReactNode }) => (
        <code className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono">
          {children}
        </code>
      ),
      link: ({ children, value }: { children?: React.ReactNode; value?: { href: string } }) => (
        <a
          href={value?.href}
          target={value?.href?.startsWith('http') ? '_blank' : '_self'}
          rel={value?.href?.startsWith('http') ? 'noopener noreferrer' : undefined}
          className="text-blue-600 hover:text-blue-800 underline transition-colors duration-200"
        >
          {children}
        </a>
      ),
    },
  } as const

  return (
    <article className="prose prose-lg max-w-none">
      <div className="blog-content">
        <PortableText
          value={post.content}
          components={portableTextComponents}
        />
      </div>

      {/* Article Footer */}
      <div className="mt-12 pt-8 border-t border-gray-200">
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-gray-600 font-medium">Tags:</span>
          {post.tags && post.tags.length > 0 ? (
            post.tags.map((tag) => (
              <span
                key={tag._id}
                className="inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-gray-200 transition-colors duration-200"
              >
                {tag.title}
              </span>
            ))
          ) : (
            <span className="text-sm text-gray-500">No tags</span>
          )}
        </div>
      </div>

      <style jsx global>{`
        .blog-content h2 {
          scroll-margin-top: 100px;
        }
        .blog-content h3 {
          scroll-margin-top: 100px;
        }
        .blog-content h4 {
          scroll-margin-top: 100px;
        }
        .blog-content pre {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 1rem;
          overflow-x: auto;
          margin: 2rem 0;
        }
        .blog-content code {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        .blog-content table {
          width: 100%;
          border-collapse: collapse;
          margin: 2rem 0;
        }
        .blog-content th,
        .blog-content td {
          border: 1px solid #e9ecef;
          padding: 0.75rem;
          text-align: left;
        }
        .blog-content th {
          background: #f8f9fa;
          font-weight: 600;
        }
      `}</style>
    </article>
  )
}

export default BlogPostContent
