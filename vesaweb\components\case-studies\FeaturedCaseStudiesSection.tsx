import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight, TrendingUp, Users, ShoppingCart, Building } from 'lucide-react';

const FeaturedCaseStudiesSection: React.FC = () => {
  // Mock data - in real implementation, this would come from Sanity CMS
  const featuredCaseStudies = [
    {
      id: 1,
      title: 'E-commerce Fashion Retailer Achieves 450% Revenue Growth',
      slug: 'fashion-retailer-revenue-growth',
      client: {
        name: 'StyleHub Fashion',
        industry: 'E-commerce & Retail',
        logo: 'https://via.placeholder.com/120x60/4285f4/ffffff?text=StyleHub'
      },
      shortDescription: 'Complete digital transformation including SEO, web development, and PPC advertising that resulted in unprecedented growth.',
      serviceType: 'Full Digital Strategy',
      duration: '12 months',
      featuredImage: 'https://images.unsplash.com/photo-1556742049-0cfab40df3f6?w=600&h=400&fit=crop',
      keyMetrics: [
        { label: 'Revenue Growth', value: '450%', icon: TrendingUp },
        { label: 'Organic Traffic', value: '280%', icon: Users },
        { label: 'Conversion Rate', value: '65%', icon: ShoppingCart }
      ],
      testimonial: {
        quote: 'VESA Solutions completely transformed our online presence. The results exceeded our wildest expectations.',
        author: '<PERSON> <PERSON>',
        position: 'CEO, StyleHub Fashion'
      }
    },
    {
      id: 2,
      title: 'SaaS Startup Generates 340% More Qualified Leads',
      slug: 'saas-startup-lead-generation',
      client: {
        name: 'TechFlow Solutions',
        industry: 'Technology',
        logo: 'https://via.placeholder.com/120x60/10b981/ffffff?text=TechFlow'
      },
      shortDescription: 'Strategic SEO and content marketing campaign that positioned this startup as an industry leader.',
      serviceType: 'SEO & Content Marketing',
      duration: '8 months',
      featuredImage: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop',
      keyMetrics: [
        { label: 'Lead Generation', value: '340%', icon: Users },
        { label: 'Organic Rankings', value: '250%', icon: TrendingUp },
        { label: 'Cost Per Lead', value: '-85%', icon: Building }
      ],
      testimonial: {
        quote: 'The ROI from our SEO investment has been incredible. We\'re now the go-to solution in our niche.',
        author: 'Michael Chen',
        position: 'Founder, TechFlow Solutions'
      }
    },
    {
      id: 3,
      title: 'Local Restaurant Chain Doubles Online Orders',
      slug: 'restaurant-chain-online-orders',
      client: {
        name: 'Bella Vista Restaurants',
        industry: 'Food & Beverage',
        logo: 'https://via.placeholder.com/120x60/f59e0b/ffffff?text=Bella+Vista'
      },
      shortDescription: 'Local SEO and web development project that revolutionized their online ordering system.',
      serviceType: 'Local SEO & Web Development',
      duration: '6 months',
      featuredImage: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=600&h=400&fit=crop',
      keyMetrics: [
        { label: 'Online Orders', value: '250%', icon: ShoppingCart },
        { label: 'Local Visibility', value: '180%', icon: Building },
        { label: 'Mobile Traffic', value: '320%', icon: Users }
      ],
      testimonial: {
        quote: 'Our online presence went from virtually non-existent to dominating local search results.',
        author: 'David Rodriguez',
        position: 'Owner, Bella Vista Restaurants'
      }
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Featured Success Stories
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Explore our most impactful client transformations. These case studies showcase the power of strategic digital marketing and the remarkable results we achieve together.
          </p>
        </div>

        <div className="space-y-16">
          {featuredCaseStudies.map((caseStudy, index) => (
            <div key={caseStudy.id} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
              {/* Content */}
              <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                <div className="flex items-center mb-6">
                  <Image
                    src={caseStudy.client.logo}
                    alt={caseStudy.client.name}
                    width={80}
                    height={48}
                    className="h-12 mr-4"
                  />
                  <div>
                    <div className="font-semibold text-gray-800">{caseStudy.client.name}</div>
                    <div className="text-sm text-gray-600">{caseStudy.client.industry}</div>
                  </div>
                </div>

                <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-6">
                  {caseStudy.title}
                </h3>

                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  {caseStudy.shortDescription}
                </p>

                {/* Key Metrics */}
                <div className="grid grid-cols-3 gap-4 mb-8">
                  {caseStudy.keyMetrics.map((metric, idx) => {
                    const IconComponent = metric.icon;
                    return (
                      <div key={idx} className="text-center p-4 bg-white rounded-xl shadow-sm">
                        <IconComponent className="text-blue-600 mx-auto mb-2" size={24} />
                        <div className="text-2xl font-bold text-gray-800">{metric.value}</div>
                        <div className="text-sm text-gray-600">{metric.label}</div>
                      </div>
                    );
                  })}
                </div>

                {/* Testimonial */}
                <div className="bg-blue-50 p-6 rounded-2xl mb-8">
                  <div className="text-4xl text-blue-500 mb-2">&quot;</div>
                  <p className="text-gray-700 mb-4 italic">
                    {caseStudy.testimonial.quote}
                  </p>
                  <div className="font-semibold text-gray-800">{caseStudy.testimonial.author}</div>
                  <div className="text-sm text-gray-600">{caseStudy.testimonial.position}</div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="bg-blue-100 px-4 py-2 rounded-full">
                    <span className="text-blue-600 font-semibold text-sm">{caseStudy.serviceType}</span>
                  </div>
                  <div className="text-gray-600 text-sm">{caseStudy.duration}</div>
                </div>

                <Link 
                  href={`/case-studies/${caseStudy.slug}`}
                  className="inline-flex items-center mt-8 bg-blue-600 text-white font-semibold px-8 py-4 rounded-xl hover:bg-blue-700 transition-all duration-300 transform hover:scale-105"
                >
                  Read Full Case Study
                  <ArrowRight size={20} className="ml-2" />
                </Link>
              </div>

              {/* Image */}
              <div className={index % 2 === 1 ? 'lg:col-start-1' : ''}>
                <Image
                  src={caseStudy.featuredImage}
                  alt={caseStudy.title}
                  width={600}
                  height={400}
                  className="w-full rounded-3xl shadow-2xl"
                />
              </div>
            </div>
          ))}
        </div>

        {/* View All CTA */}
        <div className="text-center mt-16">
          <Link 
            href="/case-studies/all"
            className="inline-flex items-center bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold px-10 py-4 rounded-full hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 shadow-xl"
          >
            View All Case Studies
            <ArrowRight size={24} className="ml-3" />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedCaseStudiesSection;
