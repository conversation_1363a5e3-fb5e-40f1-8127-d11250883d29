(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js [client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@sanity_client_dist__chunks-es_stegaEncodeSourceMap_860d9b99.js",
  "static/chunks/node_modules_@sanity_client_dist__chunks-es_stegaEncodeSourceMap_942bf245.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js [client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@sanity/eventsource/browser.js [client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_4f54be19._.js",
  "static/chunks/node_modules_@sanity_eventsource_browser_942bf245.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@sanity/eventsource/browser.js [client] (ecmascript)");
    });
});
}}),
}]);