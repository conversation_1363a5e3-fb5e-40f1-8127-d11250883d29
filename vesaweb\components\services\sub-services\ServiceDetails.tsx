// ServiceDetails.tsx - Complete implementation with all 4 schema sections
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { PortableText } from '@portabletext/react'
import { urlForImage } from '@/lib/sanity'
import { IconRenderer } from '@/components/global/IconRender'
import { 
  ServiceDetail, 
  WhyServiceMatters, 
  StrategicImplementation, 
  MarketIntelligence 
} from '@/types/subService'

interface ServiceDetailsProps {
  whyMatters?: WhyServiceMatters
  services?: ServiceDetail[]
  strategicImplementation?: StrategicImplementation
  marketIntelligence?: MarketIntelligence
}

export const ServiceDetails: React.FC<ServiceDetailsProps> = ({
  whyMatters,
  services,
  strategicImplementation,
  marketIntelligence
}) => {
  return (
    <>
      {/* Section 1: Why Service Matters */}
      {whyMatters && (
        <section className="py-24 bg-white">
          <div className="max-w-7xl mx-auto px-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div>
                {whyMatters.title && (
                  <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
                    {whyMatters.title}
                  </h2>
                )}
                
                {whyMatters.description && (
                  <div className="text-xl text-gray-600 mb-6 leading-relaxed prose prose-lg max-w-none">
                    <PortableText value={whyMatters.description} />
                  </div>
                )}
                
                {whyMatters.features && whyMatters.features.length > 0 && (
                  <div className="space-y-4 mb-8">
                    {whyMatters.features.map((feature, index) => (
                      <div key={index} className="flex items-center">
                        <div className="bg-blue-100 p-2 rounded-full mr-4">
                          <IconRenderer iconName="CheckCircle" className="text-blue-600" size={20} />
                        </div>
                        <span className="text-gray-700">{feature.text}</span>
                      </div>
                    ))}
                  </div>
                )}


              </div>
              
              {whyMatters.stats && whyMatters.stats.length > 0 && (
                <div className="grid grid-cols-2 gap-6">
                  {whyMatters.stats.map((stat, index) => (
                    <div key={index} className={`bg-gradient-to-br ${stat.color || 'from-blue-50 to-blue-100'} p-6 rounded-2xl text-center`}>
                      <div className="text-4xl font-bold text-blue-600 mb-2">{stat.value}</div>
                      <div className="text-gray-700 text-sm">{stat.label}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </section>
      )}

      {/* Section 2: Complete Service Details */}
      {services && services.length > 0 && (
        <section className="py-24 bg-gray-50">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
                Complete Service Solutions
              </h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Our comprehensive approach combines strategic optimization, technical expertise, and ongoing management to establish your business as the dominant authority in your market.
              </p>
            </div>

            <div className="space-y-20">
              {services.map((service, index) => {
                const isEven = index % 2 === 0
                
                return (
                  <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-16 items-center`}>
                    <div className={`${isEven ? 'lg:order-1' : 'lg:order-2'}`}>
                      <div className="flex items-center mb-6">
                        <div className="bg-blue-600 p-4 rounded-2xl mr-4">
                          <IconRenderer iconName={service.icon} size={32} className="text-white" />
                        </div>
                        <div>
                          <h3 className="text-3xl font-bold text-gray-800">{service.title}</h3>
                          {service.result && (
                            <div className="bg-blue-50 px-4 py-2 rounded-full inline-block mt-2">
                              <span className="text-blue-600 font-semibold">{service.result}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="mb-8">
                        {service.description && (
                          <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                            {service.description}
                          </p>
                        )}
                        {service.fullDescription && (
                          <p className="text-gray-600 mb-6 leading-relaxed">
                            {service.fullDescription}
                          </p>
                        )}
                        {service.benefits && (
                          <p className="text-gray-600 leading-relaxed">
                            {service.benefits}
                          </p>
                        )}
                      </div>

                      <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-2xl">
                        <h4 className="text-lg font-semibold text-blue-800 mb-3">Service Impact</h4>
                        <p className="text-blue-700 leading-relaxed">
                          This specialized approach creates measurable improvements in search visibility, customer engagement, and business growth through systematic optimization and ongoing management.
                        </p>
                      </div>
                    </div>
                    
                    <div className={`${isEven ? 'lg:order-2' : 'lg:order-1'}`}>
                      <div className="bg-white rounded-3xl p-8 shadow-lg">
                        {service.image && (
                          <Image
                            src={urlForImage(service.image).width(600).height(400).url()}
                            alt={`${service.title} optimization`}
                            width={600}
                            height={192}
                            className="w-full h-48 object-cover rounded-2xl mb-6"
                          />
                        )}
                        
                        {service.features && service.features.length > 0 && (
                          <>
                            <h4 className="text-xl font-bold text-gray-800 mb-6">
                              Implementation Elements
                            </h4>
                            <div className="space-y-4">
                              {service.features.map((feature, featureIndex) => (
                                <div key={featureIndex} className="flex items-start">
                                  <div className="bg-blue-100 p-1 rounded-full mr-3 mt-1 flex-shrink-0">
                                    <IconRenderer iconName="CheckCircle" size={14} className="text-blue-600" />
                                  </div>
                                  <span className="text-gray-700">{feature}</span>
                                </div>
                              ))}
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </section>
      )}

      {/* Section 3: Strategic Implementation */}
      {strategicImplementation && (
        <section className="py-24 bg-white">
          <div className="max-w-7xl mx-auto px-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
              <div>
                {strategicImplementation.title && (
                  <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
                    {strategicImplementation.title}
                  </h2>
                )}
                {strategicImplementation.description && (
                  <p className="text-xl text-gray-600 mb-6 leading-relaxed">
                    {strategicImplementation.description}
                  </p>
                )}
                {strategicImplementation.secondaryDescription && (
                  <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                    {strategicImplementation.secondaryDescription}
                  </p>
                )}
                {strategicImplementation.features && strategicImplementation.features.length > 0 && (
                  <div className="space-y-4">
                    {strategicImplementation.features.map((feature, index) => (
                      <div key={index} className="flex items-center">
                        <div className="bg-blue-100 p-2 rounded-full mr-4">
                          <IconRenderer iconName="CheckCircle" className="text-blue-600" size={20} />
                        </div>
                        <span className="text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <div>
                {strategicImplementation.image && (
                  <Image
                    src={urlForImage(strategicImplementation.image).width(600).height(400).url()}
                    alt="Strategic implementation planning"
                    width={600}
                    height={400}
                    className="w-full rounded-3xl shadow-2xl"
                  />
                )}
              </div>
            </div>

            {strategicImplementation.sections && strategicImplementation.sections.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {strategicImplementation.sections.map((section, index) => (
                  <div key={index} className={`bg-gradient-to-br ${section.color || 'from-blue-50 to-blue-100'} rounded-2xl p-8 text-center`}>
                    <div className="bg-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                      <IconRenderer iconName={section.icon} size={32} className="text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 mb-3">{section.title}</h3>
                    <p className="text-gray-600 leading-relaxed">{section.description}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Section 4: Market Intelligence */}
      {marketIntelligence && (
        <section className={`py-24 bg-gradient-to-r ${marketIntelligence.backgroundGradient || 'from-blue-50 to-blue-100'}`}>
          <div className="max-w-7xl mx-auto px-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div>
                {marketIntelligence.image && (
                  <Image
                    src={urlForImage(marketIntelligence.image).width(600).height(400).url()}
                    alt="Market intelligence and insights"
                    width={600}
                    height={400}
                    className="w-full rounded-3xl shadow-xl"
                  />
                )}
              </div>
              <div>
                {marketIntelligence.title && (
                  <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
                    {marketIntelligence.title}
                  </h2>
                )}
                {marketIntelligence.description && (
                  <p className="text-xl text-gray-600 mb-6 leading-relaxed">
                    {marketIntelligence.description}
                  </p>
                )}
                {marketIntelligence.secondaryDescription && (
                  <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                    {marketIntelligence.secondaryDescription}
                  </p>
                )}
                
                {marketIntelligence.stats && marketIntelligence.stats.length > 0 && (
                  <div className="grid grid-cols-2 gap-6 mb-8">
                    {marketIntelligence.stats.map((stat, index) => (
                      <div key={index} className="bg-white p-6 rounded-xl text-center shadow-md">
                        <div className="text-3xl font-bold text-blue-600 mb-2">{stat.value}</div>
                        <div className="text-gray-700 text-sm">{stat.label}</div>
                      </div>
                    ))}
                  </div>
                )}

                {marketIntelligence.ctaButton?.text && (
                  <Link
                    href="/contact-us"
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg inline-block"
                  >
                    {marketIntelligence.ctaButton.text}
                  </Link>
                )}
              </div>
            </div>
          </div>
        </section>
      )}
    </>
  )
}