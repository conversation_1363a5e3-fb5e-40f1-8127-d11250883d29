// pages/api/migrate-newsletter.ts - Migration script to move newsletter subscribers from JSON to Redis
import { NextApiRequest, NextApiResponse } from 'next';
import { Redis } from '@upstash/redis';
import fs from 'fs';
import path from 'path';

interface NewsletterSubscription {
  email: string;
  subscribedAt: string;
  isActive: boolean;
}

// Initialize Redis client
const redis = new Redis({
  url: process.env.KV_REST_API_URL!,
  token: process.env.KV_REST_API_TOKEN!,
});

const NEWSLETTER_SUBSCRIBERS_KEY = 'newsletter:subscribers';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests for security
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Simple authentication - you can add a secret key check here
  const { secret } = req.body;
  if (secret !== 'migrate-newsletter-2024') {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    // Check if data already exists in Redis
    const existingData = await redis.get<NewsletterSubscription[]>(NEWSLETTER_SUBSCRIBERS_KEY);
    if (existingData && existingData.length > 0) {
      return res.status(200).json({ 
        message: 'Migration already completed',
        existingSubscribers: existingData.length,
        success: true 
      });
    }

    // Read from JSON file
    const filePath = path.join(process.cwd(), 'data', 'newsletter-subscribers.json');
    
    if (!fs.existsSync(filePath)) {
      return res.status(200).json({ 
        message: 'No existing subscribers file found',
        migratedCount: 0,
        success: true 
      });
    }

    const fileData = fs.readFileSync(filePath, 'utf8');
    const subscribers: NewsletterSubscription[] = JSON.parse(fileData);

    if (subscribers.length === 0) {
      return res.status(200).json({ 
        message: 'No subscribers to migrate',
        migratedCount: 0,
        success: true 
      });
    }

    // Store in Redis
    await redis.set(NEWSLETTER_SUBSCRIBERS_KEY, subscribers);

    console.log(`Migrated ${subscribers.length} newsletter subscribers to Redis storage`);

    res.status(200).json({ 
      message: 'Newsletter subscribers migrated successfully',
      migratedCount: subscribers.length,
      subscribers: subscribers.map(sub => ({ email: sub.email, subscribedAt: sub.subscribedAt, isActive: sub.isActive })),
      success: true 
    });

  } catch (error) {
    console.error('Migration error:', error);
    res.status(500).json({ 
      message: 'Failed to migrate newsletter subscribers',
      error: error instanceof Error ? error.message : 'Unknown error',
      success: false 
    });
  }
}
