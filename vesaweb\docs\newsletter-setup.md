# Newsletter Subscription & Blog Notification Setup

This document explains how to set up the newsletter subscription system and automatic blog notifications for new Sanity blog posts.

## Features Implemented

### 1. Newsletter Subscription
- **API Endpoint**: `/api/newsletter`
- **Frontend Form**: Integrated in `DigitalStrategyArticlesSection`
- **Storage**: Local JSON file (`data/newsletter-subscribers.json`)
- **Email Confirmation**: Automatic welcome email sent to subscribers

### 2. Blog Notifications
- **API Endpoint**: `/api/blog-notification`
- **Webhook Endpoint**: `/api/webhooks/blog-published`
- **Automatic Notifications**: Sends emails to all subscribers when new blog posts are published

## Setup Instructions

### 1. Environment Variables
Add these to your `.env.local` file:

```env
# Email Configuration (required)
BUSINESS_EMAIL=<EMAIL>
APP_PASS=your-app-password

# Newsletter Email Configuration (optional - defaults to BUSINESS_EMAIL)
NEWSLETTER_FROM_EMAIL=<EMAIL>
NEWSLETTER_FROM_NAME=VESA Solutions

# Vercel KV Configuration (automatically set by Vercel)
KV_REST_API_URL=your-kv-rest-api-url
KV_REST_API_TOKEN=your-kv-rest-api-token

# Optional: Webhook Security
SANITY_WEBHOOK_SECRET=your-webhook-secret
```

### 2. Gmail App Password Setup
1. Enable 2-factor authentication on your Gmail account
2. Go to Google Account settings > Security > App passwords
3. Generate an app password for "Mail"
4. Use this password as `APP_PASS` in your environment variables

### 3. Vercel KV Setup
The newsletter system now uses Vercel KV for storing subscriber data, which works in both local and production environments.

#### For Local Development:
1. Install Vercel CLI: `npm i -g vercel`
2. Link your project: `vercel link`
3. Pull environment variables: `vercel env pull .env.local`

#### For Production (Vercel Dashboard):
1. Go to your Vercel project dashboard
2. Navigate to Storage tab
3. Create a new KV Database
4. The environment variables will be automatically set

#### Migration from File Storage:
If you have existing subscribers in `data/newsletter-subscribers.json`, use the migration endpoint:
```bash
curl -X POST https://your-domain.com/api/migrate-newsletter \
  -H "Content-Type: application/json" \
  -d '{"secret": "migrate-newsletter-2024"}'
```

### 4. Sanity Webhook Configuration (Optional)
To automatically send notifications when blog posts are published:

1. Go to your Sanity Studio dashboard
2. Navigate to API > Webhooks
3. Create a new webhook with:
   - **URL**: `https://your-domain.com/api/webhooks/blog-published`
   - **Dataset**: Your dataset name
   - **Trigger on**: Create and Update
   - **Filter**: `_type == "blogPost"`
   - **HTTP method**: POST
   - **Secret**: (optional) Set the same value as `SANITY_WEBHOOK_SECRET`

## API Endpoints

### Newsletter Subscription
```
POST /api/newsletter
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "message": "Successfully subscribed to newsletter! Check your email for confirmation.",
  "success": true
}
```

### Manual Blog Notification
```
POST /api/blog-notification
Content-Type: application/json

{
  "blogSlug": "your-blog-post-slug"
}
```

**Response:**
```json
{
  "message": "Blog notification sent to 25 subscribers",
  "success": true,
  "details": {
    "sentCount": 25,
    "failedCount": 0,
    "totalSubscribers": 25
  }
}
```

### Webhook (Automatic)
```
POST /api/webhooks/blog-published
Content-Type: application/json
X-Webhook-Secret: your-webhook-secret (optional)

{
  "_type": "blogPost",
  "_id": "post-id",
  "title": "Blog Post Title",
  "slug": {
    "current": "blog-post-slug"
  },
  "excerpt": "Blog post excerpt...",
  "publishedAt": "2024-01-01T00:00:00Z"
}
```

## Frontend Integration

### Newsletter Form
The newsletter subscription form is integrated into the `DigitalStrategyArticlesSection` component with:
- Email validation
- Loading states
- Success/error feedback
- Responsive design

### Usage Example
```tsx
import { DigitalStrategyArticlesSection } from '@/components/home';

// In your page component
<DigitalStrategyArticlesSection blogPosts={blogPosts} />
```

## File Structure
```
vesaweb/
├── pages/api/
│   ├── newsletter.ts              # Newsletter subscription endpoint
│   ├── blog-notification.ts       # Manual blog notification endpoint
│   └── webhooks/
│       └── blog-published.ts      # Sanity webhook handler
├── lib/
│   └── newsletter.ts              # Newsletter utility functions
├── components/home/
│   └── DigitalStrategyArticlesSection.tsx  # Newsletter form component
├── data/
│   └── newsletter-subscribers.json # Subscriber storage (auto-created)
└── docs/
    └── newsletter-setup.md        # This documentation
```

## Testing

### Test Newsletter Subscription
1. Go to your homepage
2. Scroll to the "Stay Updated with Digital Marketing Insights" section
3. Enter an email address and click "Subscribe"
4. Check the email for confirmation

### Test Blog Notification
1. Use the manual API endpoint:
```bash
curl -X POST https://your-domain.com/api/blog-notification \
  -H "Content-Type: application/json" \
  -d '{"blogSlug": "your-blog-post-slug"}'
```

### Test Webhook
1. Publish a new blog post in Sanity Studio
2. Check the webhook logs in Sanity dashboard
3. Verify that subscribers receive notification emails

## Security Considerations

1. **Webhook Security**: Use `SANITY_WEBHOOK_SECRET` to validate webhook requests
2. **Email Rate Limiting**: The system sends emails in batches to avoid overwhelming SMTP servers
3. **Error Handling**: Failed email sends are logged but don't prevent webhook success responses
4. **Data Storage**: Subscriber data is stored locally - consider database storage for production

## Troubleshooting

### Common Issues

1. **Email not sending**: Check Gmail app password and 2FA setup
2. **Webhook not triggering**: Verify webhook URL and Sanity configuration
3. **Subscribers not saving**: Check file permissions for `data/` directory

### Logs
Check your application logs for detailed error messages:
- Newsletter subscription errors
- Email sending failures
- Webhook processing issues

## Future Enhancements

1. **Database Storage**: Replace JSON file with database storage
2. **Unsubscribe Links**: Add proper unsubscribe functionality
3. **Email Templates**: Create more sophisticated email templates
4. **Analytics**: Track open rates and click-through rates
5. **Segmentation**: Allow subscribers to choose content categories
