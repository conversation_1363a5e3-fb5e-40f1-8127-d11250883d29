// pages/api/blog/navigation.ts - API route for blog post navigation
import { NextApiRequest, NextApiResponse } from 'next'
import { client } from '@/lib/sanity-blog'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { slug } = req.query

    if (!slug || typeof slug !== 'string') {
      return res.status(400).json({ message: 'Slug is required' })
    }

    // Get current post's published date
    const currentPost = await client.fetch(
      `*[_type == "blogPost" && slug.current == $slug][0] {
        publishedAt
      }`,
      { slug }
    )

    if (!currentPost) {
      return res.status(404).json({ message: 'Post not found' })
    }

    // Get previous and next posts
    const [prevPost, nextPost] = await Promise.all([
      // Previous post (older)
      client.fetch(
        `*[_type == "blogPost" && publishedAt < $publishedAt] | order(publishedAt desc) [0] {
          title,
          "slug": slug.current
        }`,
        { publishedAt: currentPost.publishedAt }
      ),
      // Next post (newer)
      client.fetch(
        `*[_type == "blogPost" && publishedAt > $publishedAt] | order(publishedAt asc) [0] {
          title,
          "slug": slug.current
        }`,
        { publishedAt: currentPost.publishedAt }
      )
    ])

    res.status(200).json({
      prevPost: prevPost || null,
      nextPost: nextPost || null
    })
  } catch (error) {
    console.error('Error fetching blog navigation:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
}
