# Vesa Solutions Digital Marketing Website

> A professional digital marketing website for Vesa Solutions built with Next.js 15 and Sanity CMS. Features dynamic service pages, content management, contact forms with email integration, and comprehensive SEO optimization for a Canadian-Albanian digital marketing agency.

This is a monorepo containing two main applications:
- **vesaweb**: Next.js frontend with TypeScript, Tailwind CSS 4, and advanced animations
- **vesasanity**: Sanity Studio CMS for content management

Key technologies: Next.js 15, TypeScript, Tailwind CSS 4, Sanity CMS, Framer Motion, GSAP, React Hook Form, Zod validation, Material-UI, Nodemailer, and Upstash Redis.

The website serves three main service categories: SEO Services, Web Development, and Digital Marketing, with dynamic sub-service pages, blog system, case studies, and multiple lead generation forms.

Brand guidelines: Company name is "Vesa Solutions" or "VesaSolutions" (never "VESA Solutions"). Color themes are Green for SEO, Blue for Web Dev, Purple for Digital Marketing. Uses Canadian phone number as primary with Albanian as secondary.

## Frontend Documentation

- [Package Configuration](vesaweb/package.json): Next.js dependencies and scripts
- [Main README](vesaweb/README.md): Comprehensive frontend documentation
- [Pages Directory](vesaweb/pages/): File-based routing structure
- [Components Directory](vesaweb/components/): React components organized by feature

## CMS Documentation

- [Sanity Package](vesasanity/package.json): Sanity Studio configuration
- [Schema Types](vesasanity/schemaTypes/): Content type definitions
- [Migrations](vesasanity/migrations/): Data migration scripts

## Key Components

- [Homepage Components](vesaweb/components/home/<USER>
- [Service Components](vesaweb/components/services/): SEO, web dev, digital marketing layouts
- [Global Components](vesaweb/components/global/): Header, footer, forms, WhatsApp chat
- [Blog Components](vesaweb/components/blog/): Blog archive, filters, pagination
- [Case Studies Components](vesaweb/components/case-studies/): Portfolio and testimonials

## Configuration Files

- [Next.js Config](vesaweb/next.config.ts): Next.js configuration
- [Tailwind Config](vesaweb/postcss.config.mjs): Tailwind CSS setup
- [TypeScript Config](vesaweb/tsconfig.json): TypeScript configuration
- [Sanity Config](vesasanity/sanity.config.ts): Sanity Studio configuration

## Optional

- [API Routes](vesaweb/pages/api/): Contact forms, newsletter, scheduling endpoints
- [Type Definitions](vesaweb/types/): TypeScript interfaces
- [Utilities](vesaweb/lib/): Helper functions and configurations
- [Static Assets](vesaweb/public/): Images and static files
