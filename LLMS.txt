# Vesa Solutions Digital Marketing Website

This codebase contains a professional digital marketing website for Vesa Solutions, built with Next.js and Sanity CMS.

## Project Overview

**Company**: Vesa Solutions (digital marketing agency)
**Architecture**: Monorepo with frontend (vesaweb) and CMS (vesasanity)
**Tech Stack**: Next.js 15, TypeScript, Tailwind CSS 4, Sanity CMS
**Purpose**: Professional digital marketing services website with dynamic content management

## Repository Structure

```
/
├── vesaweb/           # Next.js frontend application
│   ├── pages/         # Next.js pages (file-based routing)
│   ├── components/    # React components organized by feature
│   ├── lib/          # Utilities and configurations
│   ├── styles/       # Global styles and Tailwind config
│   ├── types/        # TypeScript type definitions
│   └── public/       # Static assets
└── vesasanity/       # Sanity CMS backend
    ├── schemaTypes/  # Content schemas
    ├── components/   # Sanity Studio components
    └── migrations/   # Data migration scripts
```

## Key Technologies

### Frontend (vesaweb)
- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS 4
- **Animations**: Framer Motion + GSAP
- **Forms**: React Hook Form + Zod validation
- **Icons**: Lucide React
- **UI Components**: Material-UI (MUI)
- **Date Handling**: date-fns with timezone support
- **Email**: Nodemailer integration
- **State Management**: React hooks + Upstash Redis for caching

### Backend (vesasanity)
- **CMS**: Sanity Studio v3
- **Content Types**: Services, blog posts, case studies, testimonials
- **Real-time**: Live content updates via Sanity Client

## Content Architecture

### Main Service Categories
1. **SEO Services** (`/seo-search-engine-optimization`)
2. **Web Development** (`/web-development`) 
3. **Digital Marketing** (`/digital-marketing`)

### Dynamic Content Types
- **Sub-services**: Individual service pages with dynamic slugs
- **Blog Posts**: Content marketing with categories and tags
- **Case Studies**: Client success stories and portfolios
- **Testimonials**: Client reviews and feedback

### Key Pages
- Homepage (`/`) - Main landing with hero and services overview
- Services Archive (`/services`) - All services organized by category
- About (`/about`) - Company information
- Contact (`/contact-us`) - Contact form with validation
- Free Estimate (`/free-estimate`) - Lead generation
- Blog (`/blog`) - Content marketing hub
- Case Studies (`/case-studies`) - Portfolio showcase

## Component Organization

### Global Components (`/components/global/`)
- Header with dropdown navigation
- Footer with contact information
- Cookie consent banner
- WhatsApp chat integration
- Form components with validation

### Feature-Specific Components
- **Home** (`/components/home/<USER>
- **Services** (`/components/services/`) - Service page layouts
- **Blog** (`/components/blog/`) - Blog functionality
- **Case Studies** (`/components/case-studies/`) - Portfolio displays
- **SEO** (`/components/seo/`) - Schema markup and SEO optimization

## Business Logic

### Contact & Lead Generation
- Multiple contact forms with email integration
- Scheduling system with timezone awareness (Albania base)
- Newsletter subscription functionality
- WhatsApp integration for instant communication

### SEO & Performance
- Server-side rendering for optimal SEO
- Dynamic meta tags from CMS content
- Schema markup for rich snippets
- Image optimization with Next.js Image component

### Content Management
- Real-time content updates from Sanity
- Dynamic service page generation
- Blog system with categories and tags
- Case study portfolio management

## Development Commands

### Frontend (vesaweb)
```bash
npm run dev          # Development server with Turbopack
npm run build        # Production build
npm run start        # Production server
npm run lint         # ESLint
```

### CMS (vesasanity)
```bash
npm run dev          # Sanity Studio development
npm run build        # Build Studio
npm run deploy       # Deploy Studio
```

## Environment Configuration

Required environment variables:
- `NEXT_PUBLIC_SANITY_PROJECT_ID` - Sanity project identifier
- `NEXT_PUBLIC_SANITY_DATASET` - Sanity dataset (production/development)
- `SANITY_API_TOKEN` - API token for content access
- Email configuration for contact forms
- Redis configuration for caching

## Brand Guidelines

- Company name: "Vesa Solutions" or "VesaSolutions" (never "VESA Solutions")
- Color themes: Green for SEO, Blue for Web Dev, Purple for Digital Marketing
- Phone numbers: Canadian (primary) and Albanian (secondary)
- Email: <EMAIL> (general), <EMAIL> (services)

## Key Features

- Responsive design with mobile-first approach
- Smooth animations and transitions
- Form validation with comprehensive error handling
- Multi-language support preparation
- Cookie consent and privacy compliance
- Sitemap and legal pages (terms, privacy policy)
- Performance optimization with caching strategies

## Content Strategy

The website serves as a comprehensive digital marketing platform showcasing:
- Service expertise across SEO, web development, and digital marketing
- Client success stories through case studies
- Educational content via blog
- Lead generation through multiple contact points
- Professional credibility through testimonials and certifications
