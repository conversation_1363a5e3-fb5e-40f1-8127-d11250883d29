import React from 'react';
import <PERSON> from 'next/link';
import { Globe, Smartphone, Zap, ShoppingCart, Code, Database, CheckCircle } from 'lucide-react';

const WebDevServicesSection: React.FC = () => {
  const webDevServices = [
    {
      icon: Globe,
      title: 'Custom Website Development',
      slug: 'custom-website-development',
      description: 'Fully custom websites built from scratch to match your brand and business requirements, optimized for performance and conversions.',
      features: ['Custom Design & Branding', 'Responsive Mobile Design', 'Content Management System', 'SEO-Optimized Structure', 'Performance Optimization', 'Security Implementation'],
      benefits: 'Unique brand presence, better user experience, higher conversion rates, and complete control over functionality.',
      results: '+250% in Conversions'
    },
    {
      icon: ShoppingCart,
      title: 'E-commerce Development',
      slug: 'ecommerce-development',
      description: 'Professional e-commerce solutions that drive sales with secure payment processing, inventory management, and conversion optimization.',
      features: ['Shopping Cart Integration', 'Payment Gateway Setup', 'Inventory Management', 'Order Processing System', 'Customer Account Portal', 'Mobile Commerce Optimization'],
      benefits: 'Increased online sales, streamlined operations, better customer experience, and scalable growth platform.',
      results: '+180% in Online Sales'
    },
    {
      icon: Smartphone,
      title: 'Mobile App Development',
      slug: 'mobile-app-development',
      description: 'Native and cross-platform mobile applications that engage users and extend your business reach across all devices.',
      features: ['iOS & Android Development', 'Cross-Platform Solutions', 'User Interface Design', 'App Store Optimization', 'Push Notifications', 'Analytics Integration'],
      benefits: 'Expanded market reach, improved customer engagement, additional revenue streams, and enhanced brand loyalty.',
      results: '+300% User Engagement'
    },
    {
      icon: Zap,
      title: 'Website Speed Optimization',
      slug: 'website-speed-optimization',
      description: 'Comprehensive performance optimization to ensure your website loads in under 3 seconds and provides exceptional user experience.',
      features: ['Core Web Vitals Optimization', 'Image Compression & Optimization', 'Code Minification', 'CDN Implementation', 'Database Optimization', 'Caching Solutions'],
      benefits: 'Better search rankings, reduced bounce rates, improved user satisfaction, and higher conversion rates.',
      results: '+85% Faster Load Times'
    },
    {
      icon: Code,
      title: 'Web Application Development',
      slug: 'web-application-development',
      description: 'Complex web applications and software solutions that streamline business processes and improve operational efficiency.',
      features: ['Custom Functionality Development', 'Database Design & Integration', 'User Authentication Systems', 'API Development & Integration', 'Cloud Deployment', 'Scalable Architecture'],
      benefits: 'Automated workflows, improved efficiency, better data management, and competitive advantage through technology.',
      results: '+200% Operational Efficiency'
    },
    {
      icon: Database,
      title: 'Website Maintenance & Support',
      slug: 'website-maintenance-support',
      description: 'Ongoing website maintenance, security updates, and technical support to keep your website running smoothly and securely.',
      features: ['Regular Security Updates', 'Performance Monitoring', 'Backup & Recovery', 'Content Updates', 'Bug Fixes & Troubleshooting', '24/7 Technical Support'],
      benefits: 'Peace of mind, consistent performance, enhanced security, and focus on your core business activities.',
      results: '99.9% Uptime Guarantee'
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Comprehensive Web Development Services
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            From custom websites to complex web applications, we provide end-to-end development solutions that drive business growth and deliver exceptional user experiences.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {webDevServices.map((service, index) => {
            const Icon = service.icon;
            return (
              <Link href={`/${service.slug}`} key={index}>
                <div className="bg-white border border-gray-200 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer group h-full flex flex-col">
                  <div className="flex items-start justify-between mb-6">
                    <div className="bg-blue-600 p-4 rounded-2xl group-hover:bg-blue-700 transition-colors">
                      <Icon size={32} className="text-white" />
                    </div>
                    <div className="bg-green-50 px-4 py-2 rounded-full">
                      <span className="text-green-600 font-bold text-sm">{service.results}</span>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-blue-600 transition-colors">
                    {service.title}
                  </h3>

                  <p className="text-gray-600 mb-6 leading-relaxed flex-1">
                    {service.description}
                  </p>

                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-800 mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {service.features.slice(0, 4).map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-gray-700 text-sm">
                          <CheckCircle size={16} className="text-green-500 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                      {service.features.length > 4 && (
                        <li className="text-blue-600 text-sm font-medium">
                          +{service.features.length - 4} more features
                        </li>
                      )}
                    </ul>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-2xl mb-6">
                    <div className="text-gray-600 text-sm mb-2">Expected Results:</div>
                    <div className="text-gray-800 font-medium">{service.benefits}</div>
                  </div>

                  <div className="bg-blue-600 text-white text-center py-3 rounded-xl font-semibold group-hover:bg-blue-700 transition-colors">
                    Learn More About {service.title}
                  </div>
                </div>
              </Link>
            );
          })}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-12 text-white">
            <h3 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Transform Your Online Presence?
            </h3>
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Let&apos;s discuss your web development needs and create a solution that drives real results for your business.
            </p>
            <button className="bg-white text-blue-600 font-bold px-10 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-xl">
              Get Your Free Web Development Consultation
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WebDevServicesSection;
