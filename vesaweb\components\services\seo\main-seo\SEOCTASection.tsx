import React from 'react';
import { Phone, Mail } from 'lucide-react';

const SEOCTASection: React.FC = () => {
  return (
    <section className="py-24 bg-gradient-to-br from-green-50 to-green-100">
      <div className="max-w-4xl mx-auto px-6 text-center">
        <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
          Not Convinced Yet to Start Working with Us?
        </h2>
        <p className="text-xl text-gray-600 mb-12 leading-relaxed">
          We understand that choosing the right SEO partner is a big decision. Let&apos;s have a conversation about your goals and how we can help you achieve them.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
          <a
            href="tel:+14166283793"
            className="group bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-xl shadow-green-500/25 inline-flex items-center justify-center text-lg"
          >
            <Phone className="mr-3" size={24} />
            Give Us a Call
          </a>

          <a
            href="mailto:<EMAIL>"
            className="group bg-white hover:bg-gray-50 text-green-600 font-bold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-xl border-2 border-green-500 hover:border-green-600 inline-flex items-center justify-center text-lg"
          >
            <Mail className="mr-3" size={24} />
            Send Us an Email
          </a>
        </div>
        
        <p className="text-gray-500 mt-8 text-sm">
          Free consultation • No obligation • Quick response guaranteed
        </p>
      </div>
    </section>
  );
};

export default SEOCTASection;
