// scripts/migrate-ecommerce-development.js - Working migration with proper schema-compatible icons and BLUE colors
const { createClient } = require('@sanity/client')
const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

const ecommerceData = {
  _type: 'subService',
  parentService: 'web-development',
  title: 'E-commerce Development Services',
  slug: {
    _type: 'slug',
    current: 'ecommerce-development'
  },
  
  // Hero Section
  hero: {
    badgeText: 'E-commerce Development',
    badgeIcon: 'ShoppingCart',
    title: 'Build Your Profitable Online Store',
    subtitle: 'Create a powerful e-commerce website that drives sales, provides exceptional shopping experiences, and grows your business. Our e-commerce development solutions are designed to convert visitors into customers and maximize your online revenue.',
    stats: [
      { _key: 'stat1', value: '300%', label: 'Average Sales Increase' },
      { _key: 'stat2', value: '99.9%', label: 'Uptime Guarantee' },
      { _key: 'stat3', value: '2.5x', label: 'Faster Checkout Process' },
      { _key: 'stat4', value: '24/7', label: 'Store Monitoring' }
    ],
    backgroundGradient: 'from-blue-600 to-blue-800'
  },

  // Why Service Matters
  whyMatters: {
    title: 'Why Professional E-commerce Development is Critical',
    description: [
      {
        _type: 'block',
        _key: 'desc1',
        children: [
          {
            _type: 'span',
            _key: 'span1',
            text: "E-commerce success requires more than just putting products online—it demands a strategic approach to user experience, conversion optimization, and technical performance. A poorly designed e-commerce site can cost you thousands in lost sales, while a professionally developed store can become your most profitable sales channel."
          }
        ],
        markDefs: [],
        style: 'normal'
      },
      {
        _type: 'block',
        _key: 'desc2',
        children: [
          {
            _type: 'span',
            _key: 'span2',
            text: "VESA Solutions specializes in creating high-converting e-commerce websites that not only look professional but also provide seamless shopping experiences, secure payment processing, and the functionality needed to scale your online business successfully."
          }
        ],
        markDefs: [],
        style: 'normal'
      }
    ],
    features: [
      { _key: 'feature1', text: 'Conversion-optimized shopping experiences' },
      { _key: 'feature2', text: 'Secure payment processing and data protection' },
      { _key: 'feature3', text: 'Scalable architecture for business growth' },
      { _key: 'feature4', text: 'Mobile-optimized for increasing mobile commerce' }
    ],
    stats: [
      {
        _key: 'stat1',
        value: '69%',
        label: 'of shopping carts are abandoned',
        color: 'from-blue-50 to-blue-100'
      },
      {
        _key: 'stat2',
        value: '4x',
        label: 'higher conversion with professional design',
        color: 'from-blue-100 to-blue-200'
      },
      {
        _key: 'stat3',
        value: '53%',
        label: 'of mobile users abandon slow sites',
        color: 'from-blue-200 to-blue-300'
      },
      {
        _key: 'stat4',
        value: '88%',
        label: 'of consumers research online before buying',
        color: 'from-blue-300 to-blue-400'
      }
    ],
    ctaButton: {
      text: 'Discover Our E-commerce Solutions'
    }
  },

  // All Services (with schema-compatible icons)
  services: [
    {
      _key: 'service1',
      icon: 'ShoppingCart',
      title: 'Custom E-commerce Platform Development',
      description: 'Build a fully customized online store tailored to your products, brand, and business requirements with advanced e-commerce functionality.',
      fullDescription: 'Custom e-commerce development provides complete control over your online store\'s functionality, design, and user experience. We create tailored solutions that perfectly match your business model, product catalog, and customer needs.',
      features: [
        'Custom shopping cart and checkout process',
        'Product catalog management system',
        'Inventory tracking and management',
        'Customer account and order management',
        'Multi-payment gateway integration',
        'Shipping and tax calculation systems',
        'Advanced search and filtering capabilities',
        'Custom reporting and analytics dashboards'
      ],
      benefits: 'Custom e-commerce platforms increase conversion rates by 85%, provide better user experiences, and offer unlimited scalability as your business grows.',
      result: '+85% Conversion Rate'
    },
    {
      _key: 'service2',
      icon: 'CreditCard',
      title: 'Secure Payment Processing Integration',
      description: 'Implement secure, reliable payment processing systems that build customer trust and ensure smooth transaction experiences.',
      fullDescription: 'Payment security and reliability are crucial for e-commerce success. We integrate industry-leading payment processors with advanced security measures to protect customer data and ensure smooth transactions.',
      features: [
        'Multiple payment gateway integration (Stripe, PayPal, Square)',
        'Credit card and digital wallet support',
        'PCI DSS compliance implementation',
        'Fraud detection and prevention systems',
        'Recurring billing and subscription management',
        'International payment processing',
        'Mobile payment optimization',
        'Payment analytics and reporting'
      ],
      benefits: 'Secure payment processing increases customer trust by 90%, reduces cart abandonment by 40%, and ensures compliance with industry security standards.',
      result: '+90% Customer Trust'
    },
    {
      _key: 'service3',
      icon: 'Smartphone',
      title: 'Mobile Commerce Optimization',
      description: 'Optimize your e-commerce store for mobile devices to capture the growing mobile commerce market and improve user experience.',
      fullDescription: 'Mobile commerce accounts for over 50% of e-commerce sales and continues growing. We optimize every aspect of your online store for mobile devices to ensure seamless shopping experiences and maximum conversions.',
      features: [
        'Mobile-first responsive design',
        'Touch-optimized navigation and interactions',
        'Mobile payment integration (Apple Pay, Google Pay)',
        'Fast mobile loading and performance',
        'Mobile-specific checkout optimization',
        'Progressive web app (PWA) capabilities',
        'Mobile push notifications',
        'Location-based features and services'
      ],
      benefits: 'Mobile optimization increases mobile conversions by 200%, captures the growing mobile market, and provides competitive advantages in mobile commerce.',
      result: '+200% Mobile Conversions'
    },
    {
      _key: 'service4',
      icon: 'BarChart3',
      title: 'E-commerce Analytics & Reporting',
      description: 'Implement comprehensive analytics and reporting systems to track performance, understand customer behavior, and optimize your store.',
      fullDescription: 'Data-driven decision making is essential for e-commerce success. We implement advanced analytics systems that provide insights into sales performance, customer behavior, and optimization opportunities.',
      features: [
        'Sales and revenue tracking and reporting',
        'Customer behavior and journey analysis',
        'Product performance and inventory insights',
        'Conversion funnel analysis and optimization',
        'Marketing campaign tracking and ROI analysis',
        'Customer lifetime value calculations',
        'Abandoned cart recovery tracking',
        'Custom dashboard and automated reporting'
      ],
      benefits: 'Comprehensive analytics improve decision-making accuracy by 75%, identify optimization opportunities 3x faster, and increase overall store profitability by 45%.',
      result: '+75% Decision Accuracy'
    },
    {
      _key: 'service5',
      icon: 'Users',
      title: 'Customer Experience Optimization',
      description: 'Create exceptional shopping experiences that delight customers, encourage repeat purchases, and build brand loyalty.',
      fullDescription: 'Customer experience is the key differentiator in e-commerce. We design and develop user experiences that make shopping enjoyable, intuitive, and efficient, leading to higher satisfaction and loyalty.',
      features: [
        'User experience (UX) design and optimization',
        'Personalized product recommendations',
        'Advanced search and filtering capabilities',
        'Customer reviews and ratings systems',
        'Wishlist and favorites functionality',
        'Live chat and customer support integration',
        'Email marketing automation integration',
        'Loyalty program and rewards systems'
      ],
      benefits: 'Optimized customer experience increases customer satisfaction by 80%, improves repeat purchase rates by 60%, and builds long-term customer loyalty.',
      result: '+80% Customer Satisfaction'
    },
    {
      _key: 'service6',
      icon: 'Shield',
      title: 'E-commerce Security & Compliance',
      description: 'Implement robust security measures and ensure compliance with industry standards to protect your business and customers.',
      fullDescription: 'E-commerce security is non-negotiable. We implement comprehensive security measures that protect customer data, prevent fraud, and ensure compliance with industry regulations and standards.',
      features: [
        'SSL certificate installation and management',
        'PCI DSS compliance implementation',
        'Data encryption and secure storage',
        'Fraud detection and prevention systems',
        'Regular security audits and updates',
        'GDPR and privacy compliance',
        'Secure backup and disaster recovery',
        'Security monitoring and threat detection'
      ],
      benefits: 'Comprehensive security reduces security incidents by 95%, builds customer trust, and ensures compliance with industry regulations and standards.',
      result: '+95% Security Protection'
    }
  ],

  // Strategic Implementation Section (with schema-compatible icons and colors)
  strategicImplementation: {
    title: 'Comprehensive E-commerce Development Process',
    description: 'Successful e-commerce development requires strategic planning, expert design, and meticulous implementation. Our integrated approach ensures your online store not only looks professional but also converts visitors into customers.',
    secondaryDescription: 'From market analysis to post-launch optimization, we guide you through every step of the e-commerce development process with expertise, transparency, and dedication to your success.',
    features: [
      'Strategic planning and market analysis',
      'Custom design and user experience optimization',
      'Expert development and performance optimization'
    ],
    sections: [
      {
        _key: 'section1',
        icon: 'Target',
        title: 'Strategy & Planning',
        description: 'Comprehensive analysis of your market, competitors, and customer needs to create the perfect e-commerce strategy and development roadmap.',
        color: 'from-blue-50 to-blue-100'
      },
      {
        _key: 'section2',
        icon: 'ShoppingCart',
        title: 'Design & Development',
        description: 'Custom e-commerce design and expert development using proven technologies and best practices for optimal performance and conversions.',
        color: 'from-blue-100 to-blue-200'
      },
      {
        _key: 'section3',
        icon: 'TrendingUp',
        title: 'Launch & Optimization',
        description: 'Successful launch with comprehensive testing, ongoing optimization, and continuous improvement to maximize sales and performance.',
        color: 'from-blue-200 to-blue-300'
      }
    ]
  },

  // Market Intelligence Section (with schema-compatible background)
  marketIntelligence: {
    title: 'Advanced E-commerce Development Intelligence',
    description: 'Our e-commerce development strategies are powered by industry insights, proven methodologies, and cutting-edge technologies that deliver exceptional results for online retailers across all industries.',
    secondaryDescription: 'Through years of experience and hundreds of successful e-commerce projects, we\'ve developed the expertise and processes needed to create online stores that not only look great but also drive significant revenue growth.',
    stats: [
      {
        _key: 'stat1',
        value: '200+',
        label: 'E-commerce Stores Built'
      },
      {
        _key: 'stat2',
        value: '300%',
        label: 'Average Sales Increase'
      },
      {
        _key: 'stat3',
        value: '99.9%',
        label: 'Uptime Guarantee'
      },
      {
        _key: 'stat4',
        value: '24/7',
        label: 'Store Monitoring'
      }
    ],
    ctaButton: {
      text: 'Start Your E-commerce Project'
    },
    backgroundGradient: 'from-blue-50 to-blue-100'
  },

  // Process (with schema-compatible icons)
  process: {
    title: 'Our Proven E-commerce Development Process',
    description: 'VESA Solutions follows a systematic approach that has delivered successful e-commerce stores for hundreds of businesses, ensuring quality, performance, and profitability at every step.',
    steps: [
      {
        _key: 'step1',
        step: 1,
        title: 'Market Research & Strategy',
        description: 'Comprehensive analysis of your market, competitors, and target customers to create the perfect e-commerce strategy.',
        icon: 'Search',
        details: [
          'Market and competitive analysis',
          'Target customer research and personas',
          'Product catalog strategy and organization',
          'Pricing and positioning strategy',
          'Technology platform selection'
        ]
      },
      {
        _key: 'step2',
        step: 2,
        title: 'Design & User Experience',
        description: 'Create conversion-optimized designs and user experiences that guide customers through the buying process.',
        icon: 'Palette',
        details: [
          'User experience (UX) design and wireframing',
          'Custom visual design and branding',
          'Shopping cart and checkout optimization',
          'Mobile-first responsive design',
          'Conversion rate optimization (CRO)'
        ]
      },
      {
        _key: 'step3',
        step: 3,
        title: 'Development & Integration',
        description: 'Expert development with secure payment processing, inventory management, and third-party integrations.',
        icon: 'Code',
        details: [
          'Custom e-commerce development',
          'Payment gateway integration and testing',
          'Inventory and order management systems',
          'Third-party service integrations',
          'Security implementation and testing'
        ]
      },
      {
        _key: 'step4',
        step: 4,
        title: 'Launch & Optimization',
        description: 'Successful launch with comprehensive testing, analytics setup, and ongoing optimization for maximum sales.',
        icon: 'Rocket',
        details: [
          'Pre-launch testing and quality assurance',
          'Analytics and tracking implementation',
          'Search engine optimization setup',
          'Performance monitoring and optimization',
          'Ongoing support and maintenance'
        ]
      }
    ]
  },

  // Case Study (with schema-compatible background)
  caseStudy: {
    title: 'Fashion Retailer Achieves 450% Revenue Growth',
    description: 'Discover how our custom e-commerce development helped StyleHub Fashion transform their online store and achieve unprecedented revenue growth through strategic design and optimization.',
    results: [
      {
        _key: 'result1',
        value: '450%',
        label: 'Revenue Growth'
      },
      {
        _key: 'result2',
        value: '320%',
        label: 'Conversion Rate Increase'
      },
      {
        _key: 'result3',
        value: '65%',
        label: 'Cart Abandonment Reduction'
      },
      {
        _key: 'result4',
        value: '280%',
        label: 'Mobile Sales Growth'
      }
    ],
    ctaButton: {
      text: 'View Complete Case Study'
    },
    backgroundGradient: 'from-blue-600 to-blue-800'
  },

  // CTA
  cta: {
    title: 'Get Your Free E-commerce Development Consultation',
    description: 'Ready to build a profitable online store that drives sales and grows your business? Let\'s discuss your e-commerce goals and create a development strategy that delivers results.',
    benefits: [
      'Complete e-commerce strategy & market analysis consultation',
      'Custom store design mockups & development timeline',
      'Technology recommendations & integration planning',
      'Conversion optimization & sales growth strategy'
    ],
    phoneNumber: '(*************',
    formSettings: {
      ctaText: 'Get My Free E-commerce Consultation',
      messagePlaceholder: 'Tell us about your e-commerce goals and product requirements*'
    }
  },

  // All Testimonials (without image references)
  testimonials: [
    {
      _key: 'testimonial1',
      name: 'Amanda Rodriguez',
      business: 'StyleHub Fashion',
      location: 'Los Angeles, CA',
      quote: 'The e-commerce store VESA built for us exceeded all expectations. Our revenue increased by 450% in the first year, and the mobile experience is incredible. The custom features they developed have given us a huge competitive advantage.',
      result: '+450% Revenue Growth',
      rating: 5
    },
    {
      _key: 'testimonial2',
      name: 'David Kim',
      business: 'TechGear Electronics',
      location: 'Seattle, WA',
      quote: 'Working with VESA transformed our online business. The new e-commerce platform handles our complex inventory perfectly, and our conversion rates have tripled. Customer feedback has been overwhelmingly positive.',
      result: '+300% Conversion Rate',
      rating: 5
    },
    {
      _key: 'testimonial3',
      name: 'Lisa Thompson',
      business: 'Artisan Home Goods',
      location: 'Portland, OR',
      quote: 'The custom e-commerce solution they built captures our brand perfectly and makes shopping a joy for our customers. Our average order value increased by 85% and customer retention has never been better.',
      result: '+85% Average Order Value',
      rating: 5
    }
  ],

  // All FAQs
  faqs: [
    {
      _key: 'faq1',
      question: 'What e-commerce platforms do you work with?',
      answer: 'We work with various e-commerce platforms including custom solutions, Shopify, WooCommerce, and Magento. Platform selection depends on your specific requirements, budget, and business goals. We recommend the best solution for your needs.'
    },
    {
      _key: 'faq2',
      question: 'How long does e-commerce development take?',
      answer: 'E-commerce development typically takes 8-16 weeks depending on complexity, features, and product catalog size. This includes strategy, design, development, testing, and launch phases. We provide detailed timelines during the planning phase.'
    },
    {
      _key: 'faq3',
      question: 'Will my e-commerce store be mobile-optimized?',
      answer: 'Absolutely! All our e-commerce stores are built with mobile-first design principles. With over 50% of e-commerce sales happening on mobile devices, we ensure your store provides exceptional mobile shopping experiences.'
    },
    {
      _key: 'faq4',
      question: 'What payment methods can you integrate?',
      answer: 'We integrate all major payment methods including credit cards, PayPal, Apple Pay, Google Pay, Stripe, Square, and other popular payment processors. We also handle international payments and multiple currencies if needed.'
    },
    {
      _key: 'faq5',
      question: 'Can you integrate with my existing inventory management system?',
      answer: 'Yes, we specialize in integrating e-commerce stores with existing business systems including inventory management, CRM, accounting software, and other third-party services to streamline your operations.'
    },
    {
      _key: 'faq6',
      question: 'Do you provide ongoing support and maintenance?',
      answer: 'Yes, we provide comprehensive ongoing support including security updates, performance monitoring, backup management, technical support, and feature updates to keep your store running smoothly and securely.'
    },
    {
      _key: 'faq7',
      question: 'How do you ensure my e-commerce store is secure?',
      answer: 'We implement comprehensive security measures including SSL certificates, PCI DSS compliance, secure payment processing, data encryption, fraud protection, and regular security audits to protect your business and customers.'
    },
    {
      _key: 'faq8',
      question: 'Can you help with SEO and digital marketing for my store?',
      answer: 'Yes, we build SEO optimization into every e-commerce store and can provide ongoing digital marketing services including SEO, PPC advertising, email marketing, and social media marketing to drive traffic and sales.'
    }
  ],

  // Footer CTA (with schema-compatible background)
  footerCta: {
    title: 'Ready to Build Your Profitable Online Store?',
    description: 'Join hundreds of businesses that trust VESA Solutions to create e-commerce stores that drive sales, delight customers, and deliver exceptional results.',
    primaryButton: {
      text: 'Schedule Free Consultation',
      icon: 'Calendar'
    },
    secondaryButton: {
      text: 'Call (*************',
      icon: 'Phone',
      phoneNumber: '(*************'
    },
    trustSignals: {
      title: 'Trusted by Businesses & Certified Partners',
      ratings: [
        {
          _key: 'rating1',
          rating: '4.9/5 Rating',
          source: 'Google Reviews',
          description: 'Google Reviews'
        },
        {
          _key: 'rating2',
          rating: '4.8/5 Rating',
          source: 'Clutch Reviews',
          description: 'Clutch Reviews'
        },
        {
          _key: 'rating3',
          rating: '200+ Stores',
          source: 'Successfully Built',
          description: 'Successfully Built'
        }
      ]
    },
    backgroundGradient: 'from-blue-700 to-blue-900'
  },

  // SEO (without image reference)
  seo: {
    metaTitle: 'E-commerce Development Services | Online Store Development | VESA Solutions',
    metaDescription: 'Build your profitable online store with professional e-commerce development services. Custom design, secure payments, mobile-optimized, and conversion-focused. Free consultation included.',
    keywords: [
      'ecommerce development',
      'online store development',
      'e-commerce website design',
      'shopping cart development',
      'ecommerce web development'
    ]
  }
}

async function migrateEcommerceData() {
  try {
    console.log('🚀 Starting E-commerce Development migration...')
    console.log('📊 Project ID: zleti5e4')
    console.log('🗃️  Dataset: production')
    console.log('')
    
    // Check if document already exists
    const existing = await client.fetch(`*[_type == "subService" && slug.current == "ecommerce-development"][0]`)
    
    if (existing) {
      console.log('📄 E-commerce Development document already exists:', existing._id)
      console.log('🔄 Updating existing document...')
      
      const result = await client
        .patch(existing._id)
        .set(ecommerceData)
        .commit()
      
      console.log('✅ E-commerce Development page updated successfully!')
      console.log(`📄 Document ID: ${result._id}`)
      console.log(`🔗 Edit in Studio: https://vesasanity.sanity.studio/structure/subService;${result._id}`)
      return result
    } else {
      const result = await client.create(ecommerceData)
      console.log('✅ E-commerce Development page created successfully!')
      console.log(`📄 Document ID: ${result._id}`)
      console.log(`🔗 Edit in Studio: https://vesasanity.sanity.studio/structure/subService;${result._id}`)
      return result
    }
    
  } catch (error) {
    console.error('')
    console.error('❌ Migration failed:')
    console.error(error)
    
    if (error.message && error.message.includes('token')) {
      console.log('')
      console.log('💡 Make sure your token is correctly set in this file')
      console.log('   Get your token from: https://sanity.io/manage/personal/tokens')
    }
    
    if (error.message && error.message.includes('Insufficient permissions')) {
      console.log('')
      console.log('💡 Make sure your token has "Editor" or "Administrator" permissions')
    }
    
    process.exit(1)
  }
}

// Export the function instead of auto-running
module.exports = { migrateEcommerceData }

// Run the migration
migrateEcommerceData()
  .then(() => {
    console.log('')
    console.log('🎉 Your E-commerce Development page is now managed by Sanity!')
    console.log('📝 You can now edit all content in Sanity Studio')
    console.log('🔄 Remember to update your Next.js page to fetch from Sanity')
    console.log('')
    console.log('🎯 The page structure is complete and functional!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Migration failed:', error)
    process.exit(1)
  })
