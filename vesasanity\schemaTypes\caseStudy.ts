// schemas/caseStudy.ts - Complete case study schema for VESA Solutions
import { defineType, defineField } from 'sanity'

export const caseStudy = defineType({
  name: 'caseStudy',
  title: 'Case Study',
  type: 'document',
  groups: [
    {
      name: 'content',
      title: 'Basic Information',
      default: true
    },
    {
      name: 'client',
      title: 'Client Details'
    },
    {
      name: 'project',
      title: 'Project Overview'
    },
    {
      name: 'solution',
      title: 'Solution & Implementation'
    },
    {
      name: 'results',
      title: 'Results & Metrics'
    },
    {
      name: 'testimonial',
      title: 'Client Testimonial'
    },
    {
      name: 'media',
      title: 'Images & Media'
    },
    {
      name: 'seo',
      title: 'SEO & Settings'
    }
  ],
  fields: [
    // Basic Information
    defineField({
      name: 'title',
      title: 'Case Study Title',
      type: 'string',
      group: 'content',
      validation: Rule => Rule.required().max(100)
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      group: 'content',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'shortDescription',
      title: 'Short Description',
      type: 'text',
      group: 'content',
      rows: 3,
      validation: Rule => Rule.required().max(200)
    }),
    defineField({
      name: 'featured',
      title: 'Featured Case Study',
      type: 'boolean',
      group: 'content',
      description: 'Mark as featured to display prominently on the case studies page',
      initialValue: false
    }),
    defineField({
      name: 'publishedAt',
      title: 'Published Date',
      type: 'datetime',
      group: 'content',
      validation: Rule => Rule.required()
    }),

    // Client Details
    defineField({
      name: 'client',
      title: 'Client Information',
      type: 'object',
      group: 'client',
      fields: [
        defineField({
          name: 'name',
          title: 'Client Name',
          type: 'string',
          validation: Rule => Rule.required()
        }),
        defineField({
          name: 'industry',
          title: 'Industry',
          type: 'string',
          options: {
            list: [
              { title: 'E-commerce & Retail', value: 'ecommerce' },
              { title: 'Healthcare & Medical', value: 'healthcare' },
              { title: 'Professional Services', value: 'professional-services' },
              { title: 'Education & Training', value: 'education' },
              { title: 'Real Estate', value: 'real-estate' },
              { title: 'Automotive', value: 'automotive' },
              { title: 'Financial Services', value: 'financial' },
              { title: 'Non-Profit', value: 'non-profit' },
              { title: 'Technology', value: 'technology' },
              { title: 'Manufacturing', value: 'manufacturing' },
              { title: 'Food & Beverage', value: 'food-beverage' },
              { title: 'Other', value: 'other' }
            ]
          },
          validation: Rule => Rule.required()
        }),
        defineField({
          name: 'location',
          title: 'Client Location',
          type: 'string'
        }),
        defineField({
          name: 'website',
          title: 'Client Website',
          type: 'url'
        }),
        defineField({
          name: 'logo',
          title: 'Client Logo',
          type: 'image',
          options: {
            hotspot: true
          }
        }),
        defineField({
          name: 'companySize',
          title: 'Company Size',
          type: 'string',
          options: {
            list: [
              { title: 'Startup (1-10 employees)', value: 'startup' },
              { title: 'Small Business (11-50 employees)', value: 'small' },
              { title: 'Medium Business (51-200 employees)', value: 'medium' },
              { title: 'Large Enterprise (200+ employees)', value: 'large' }
            ]
          }
        })
      ]
    }),

    // Project Overview
    defineField({
      name: 'project',
      title: 'Project Details',
      type: 'object',
      group: 'project',
      fields: [
        defineField({
          name: 'serviceType',
          title: 'Primary Service Type',
          type: 'string',
          options: {
            list: [
              { title: 'SEO Services', value: 'seo' },
              { title: 'Web Development', value: 'web-development' },
              { title: 'PPC Advertising', value: 'ppc' },
              { title: 'Social Media Marketing', value: 'social-media' },
              { title: 'Content Marketing', value: 'content-marketing' },
              { title: 'Email Marketing', value: 'email-marketing' },
              { title: 'Reputation Management', value: 'reputation' },
              { title: 'Full Digital Strategy', value: 'full-strategy' }
            ]
          },
          validation: Rule => Rule.required()
        }),
        defineField({
          name: 'duration',
          title: 'Project Duration',
          type: 'string',
          description: 'e.g., "6 months", "1 year", "Ongoing"'
        }),
        defineField({
          name: 'challenge',
          title: 'Challenge/Problem',
          type: 'array',
          of: [{ type: 'block' }],
          validation: Rule => Rule.required()
        }),
        defineField({
          name: 'goals',
          title: 'Project Goals',
          type: 'array',
          of: [{ type: 'string' }],
          validation: Rule => Rule.required().min(1)
        })
      ]
    }),

    // Solution & Implementation
    defineField({
      name: 'solution',
      title: 'Solution & Implementation',
      type: 'object',
      group: 'solution',
      fields: [
        defineField({
          name: 'approach',
          title: 'Solution Approach',
          type: 'array',
          of: [{ type: 'block' }],
          validation: Rule => Rule.required()
        }),
        defineField({
          name: 'implementation',
          title: 'Implementation Steps',
          type: 'array',
          of: [
            {
              type: 'object',
              name: 'step',
              title: 'Implementation Step',
              fields: [
                defineField({
                  name: 'title',
                  title: 'Step Title',
                  type: 'string'
                }),
                defineField({
                  name: 'description',
                  title: 'Step Description',
                  type: 'text'
                }),
                defineField({
                  name: 'timeline',
                  title: 'Timeline',
                  type: 'string'
                })
              ]
            }
          ]
        }),
        defineField({
          name: 'tools',
          title: 'Tools & Technologies Used',
          type: 'array',
          of: [{ type: 'string' }]
        })
      ]
    }),

    // Results & Metrics
    defineField({
      name: 'results',
      title: 'Results & Metrics',
      type: 'object',
      group: 'results',
      fields: [
        defineField({
          name: 'overview',
          title: 'Results Overview',
          type: 'text',
          validation: Rule => Rule.required()
        }),
        defineField({
          name: 'metrics',
          title: 'Key Metrics',
          type: 'array',
          of: [
            {
              type: 'object',
              name: 'metric',
              title: 'Metric',
              fields: [
                defineField({
                  name: 'label',
                  title: 'Metric Label',
                  type: 'string',
                  validation: Rule => Rule.required()
                }),
                defineField({
                  name: 'beforeValue',
                  title: 'Before Value',
                  type: 'string'
                }),
                defineField({
                  name: 'afterValue',
                  title: 'After Value',
                  type: 'string',
                  validation: Rule => Rule.required()
                }),
                defineField({
                  name: 'improvement',
                  title: 'Improvement Percentage',
                  type: 'string',
                  description: 'e.g., "+250%", "300% increase"'
                }),
                defineField({
                  name: 'icon',
                  title: 'Metric Icon',
                  type: 'string',
                  options: {
                    list: [
                      { title: 'Trending Up', value: 'TrendingUp' },
                      { title: 'Users', value: 'Users' },
                      { title: 'Dollar Sign', value: 'DollarSign' },
                      { title: 'Eye', value: 'Eye' },
                      { title: 'Mouse Pointer Click', value: 'MousePointerClick' },
                      { title: 'Search', value: 'Search' },
                      { title: 'Globe', value: 'Globe' },
                      { title: 'Shopping Cart', value: 'ShoppingCart' },
                      { title: 'Phone', value: 'Phone' },
                      { title: 'Mail', value: 'Mail' }
                    ]
                  }
                })
              ],
              preview: {
                select: {
                  title: 'label',
                  subtitle: 'improvement'
                }
              }
            }
          ],
          validation: Rule => Rule.required().min(3).max(6)
        }),
        defineField({
          name: 'timeline',
          title: 'Results Timeline',
          type: 'string',
          description: 'e.g., "Within 6 months", "After 1 year"'
        })
      ]
    }),

    // Client Testimonial
    defineField({
      name: 'testimonial',
      title: 'Client Testimonial',
      type: 'object',
      group: 'testimonial',
      fields: [
        defineField({
          name: 'quote',
          title: 'Testimonial Quote',
          type: 'text',
          validation: Rule => Rule.required()
        }),
        defineField({
          name: 'author',
          title: 'Author Name',
          type: 'string',
          validation: Rule => Rule.required()
        }),
        defineField({
          name: 'position',
          title: 'Author Position',
          type: 'string',
          validation: Rule => Rule.required()
        }),
        defineField({
          name: 'authorImage',
          title: 'Author Photo',
          type: 'image',
          options: {
            hotspot: true
          }
        }),
        defineField({
          name: 'rating',
          title: 'Star Rating',
          type: 'number',
          validation: Rule => Rule.min(1).max(5),
          initialValue: 5
        })
      ]
    }),

    // Images & Media
    defineField({
      name: 'featuredImage',
      title: 'Featured Image',
      type: 'image',
      group: 'media',
      options: {
        hotspot: true
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'beforeAfterImages',
      title: 'Before/After Images',
      type: 'object',
      group: 'media',
      fields: [
        defineField({
          name: 'before',
          title: 'Before Image',
          type: 'image',
          options: {
            hotspot: true
          }
        }),
        defineField({
          name: 'after',
          title: 'After Image',
          type: 'image',
          options: {
            hotspot: true
          }
        })
      ]
    }),
    defineField({
      name: 'gallery',
      title: 'Additional Images',
      type: 'array',
      group: 'media',
      of: [
        {
          type: 'image',
          options: {
            hotspot: true
          }
        }
      ]
    }),

    // SEO
    defineField({
      name: 'seo',
      title: 'SEO Settings',
      type: 'object',
      group: 'seo',
      fields: [
        defineField({
          name: 'metaTitle',
          title: 'Meta Title',
          type: 'string',
          validation: Rule => Rule.max(60)
        }),
        defineField({
          name: 'metaDescription',
          title: 'Meta Description',
          type: 'text',
          validation: Rule => Rule.max(160)
        }),
        defineField({
          name: 'keywords',
          title: 'Focus Keywords',
          type: 'array',
          of: [{ type: 'string' }]
        }),
        defineField({
          name: 'ogImage',
          title: 'Social Share Image',
          type: 'image',
          options: {
            hotspot: true
          }
        })
      ]
    })
  ],
  preview: {
    select: {
      title: 'title',
      subtitle: 'client.name',
      media: 'featuredImage'
    },
    prepare(selection) {
      const { title, subtitle, media } = selection
      return {
        title: title || 'Untitled Case Study',
        subtitle: subtitle ? `Client: ${subtitle}` : 'No client specified',
        media: media
      }
    }
  }
})
