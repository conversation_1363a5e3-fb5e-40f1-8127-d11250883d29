import React from 'react';
import Head from 'next/head';
import Header from '@/components/global/Header';
import Footer from '@/components/global/Footer';
import { 
  ArrowRight,
  Users,
  Target,
  TrendingUp,
  Zap,
  Award,
  Heart,
  Search,
  Smartphone,
  BarChart3,
  CheckCircle,
  Star,
  Building2,
  Lightbulb
} from 'lucide-react';

const AboutPage = () => {
  const keyFeatures = [
    {
      icon: Building2,
      title: "Modern Digital Company",
      description: "We are a forward-thinking digital agency focused on building growth-oriented business processes for companies of all sizes."
    },
    {
      icon: Target,
      title: "Small & Medium Business Focus",
      description: "Specializing in helping small and medium-sized companies achieve rapid growth with the right digital strategies."
    },
    {
      icon: TrendingUp,
      title: "High Return on Sales",
      description: "Our strategies are designed to maximize your ROI and deliver measurable results that directly impact your bottom line."
    },
    {
      icon: Zap,
      title: "Rapid Growth Acceleration",
      description: "Even small companies can make giant strides quickly with the right approach and expert guidance."
    }
  ];

  const statistics = [
    {
      icon: Search,
      number: "99%",
      title: "Google Searches",
      description: "of users search on Google for products and services"
    },
    {
      icon: Smartphone,
      number: "1 in 3",
      title: "Local Searches",
      description: "users perform local searches looking for nearby businesses"
    },
    {
      icon: Users,
      number: "200+",
      title: "Happy Clients",
      description: "successful projects delivered with excellent results"
    },
    {
      icon: BarChart3,
      number: "300%",
      title: "Average ROI",
      description: "improvement in return on investment for our clients"
    }
  ];

  const values = [
    {
      icon: Heart,
      title: "Passion",
      description: "We work every day with genuine passion for what we do and the results we achieve for our clients."
    },
    {
      icon: Award,
      title: "Commitment",
      description: "Our commitment to excellence ensures we provide companies with maximum opportunities for growth."
    },
    {
      icon: Star,
      title: "Satisfaction",
      description: "We prioritize satisfaction for the people who choose us, building long-term partnerships."
    },
    {
      icon: Lightbulb,
      title: "Expertise",
      description: "Our team of experts helps you choose the right path to digital success and sustainable growth."
    }
  ];

  const scrollToContact = () => {
    window.location.href = '/contact';
  };

  return (
    <>
      <Head>
        <title>About Vesa Solutions - Modern Digital Marketing Company in Albania</title>
        <meta name="description" content="Learn about Vesa Solutions, a modern digital company specializing in growth business processes for small and medium businesses. High ROI, expert guidance, and proven results." />
        
        {/* Open Graph tags for social sharing */}
        <meta property="og:title" content="About Vesa Solutions - Modern Digital Marketing Company" />
        <meta property="og:description" content="Modern digital company helping small and medium businesses achieve rapid growth with high ROI digital strategies." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://vesasolutions.com/about" />
        
        {/* Additional SEO tags */}
        <meta name="keywords" content="digital marketing company albania, modern digital agency, small business growth, ROI marketing, local search optimization" />
        <meta name="author" content="Vesa Solutions" />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://vesasolutions.com/about" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        <Header isVisible={true} />
        
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 text-white py-20 overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 left-10 w-32 h-32 border border-white/20 rounded-lg transform rotate-12"></div>
            <div className="absolute top-40 right-20 w-24 h-24 border border-white/20 rounded-lg transform -rotate-12"></div>
            <div className="absolute bottom-32 left-32 w-16 h-16 border border-white/20 rounded-lg transform rotate-45"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Modern <span className="text-blue-400">Digital Company</span><br />
                Driving Business Growth
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-4xl mx-auto">
                We build growth business processes for small and medium-sized companies, 
                delivering high returns on sales through expert digital strategies.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button 
                  onClick={scrollToContact}
                  className="bg-white text-blue-900 px-8 py-4 rounded-full font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                >
                  Start Your Growth Journey
                </button>
                <a 
                  href="#our-story" 
                  className="border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-blue-900 transition-all duration-300"
                >
                  Learn Our Story
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* Key Features Grid */}
        <section className="py-16 -mt-10 relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {keyFeatures.map((feature, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <feature.icon className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Our Story Section */}
        <section id="our-story" className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Content */}
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-6">
                  Your Success is Our Mission
                </h2>
                <div className="space-y-6 text-gray-600 leading-relaxed">
                  <p className="text-lg">
                    We are a modern digital company that works to build growth business processes 
                    for small and medium-sized companies, with a high return on sales.
                  </p>
                  <p>
                    Even if you are a small company, with the right approach you can quickly make 
                    giant strides. Just rely on experts and choose the right path.
                  </p>
                  <p>
                    Today 99% of users search on Google and, of these, 1 in 3 does a local search 
                    because they are looking, with their smartphone, for an activity like yours, 
                    or your own, nearby.
                  </p>
                  <p>
                    <strong className="text-gray-800">We help you to appear to users really interested in you 
                    and to convert visitors to your site into customers.</strong>
                  </p>
                </div>
                
                <div className="mt-8">
                  <button 
                    onClick={scrollToContact}
                    className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                  >
                    Get Started Today
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Visual Element */}
              <div className="bg-white rounded-2xl shadow-xl p-8">
                <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">
                  Why Choose the Right Digital Partner?
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-start">
                    <CheckCircle className="w-6 h-6 text-green-500 mt-1 mr-3 flex-shrink-0" />
                    <div>
                      <p className="font-semibold text-gray-800">Expert Guidance</p>
                      <p className="text-gray-600 text-sm">Professional strategies tailored to your business needs</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="w-6 h-6 text-green-500 mt-1 mr-3 flex-shrink-0" />
                    <div>
                      <p className="font-semibold text-gray-800">Rapid Growth</p>
                      <p className="text-gray-600 text-sm">Quick results that make a real difference to your business</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="w-6 h-6 text-green-500 mt-1 mr-3 flex-shrink-0" />
                    <div>
                      <p className="font-semibold text-gray-800">Local Visibility</p>
                      <p className="text-gray-600 text-sm">Reach customers actively searching for your services nearby</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="w-6 h-6 text-green-500 mt-1 mr-3 flex-shrink-0" />
                    <div>
                      <p className="font-semibold text-gray-800">Conversion Focus</p>
                      <p className="text-gray-600 text-sm">Transform website visitors into paying customers</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Statistics Section */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                The Numbers Don&apos;t Lie
              </h2>
              <p className="text-lg text-gray-600">
                Understanding today&apos;s digital landscape and our proven track record
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {statistics.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <stat.icon className="w-10 h-10 text-blue-600" />
                  </div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{stat.number}</div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">{stat.title}</h3>
                  <p className="text-gray-600 text-sm">{stat.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                Our Values & Commitment
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                We work every day with passion and commitment to providing companies with 
                the maximum opportunities and satisfaction for the people who choose us.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {values.map((value, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-6 text-center hover:shadow-xl transition-all duration-300">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <value.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-3">{value.title}</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">{value.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Call to Action Section */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Make Giant Strides?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Don&apos;t let your competition get ahead. Partner with us and start your 
              journey to digital success today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                onClick={scrollToContact}
                className="bg-white text-blue-600 px-8 py-4 rounded-full font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                Start Your Growth Journey
              </button>
              <a
                href="tel:+14166283793"
                className="border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300"
              >
                Call Us Today
              </a>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
};

export default AboutPage;