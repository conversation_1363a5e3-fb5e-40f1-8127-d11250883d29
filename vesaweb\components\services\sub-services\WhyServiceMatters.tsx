import React from 'react'
import Link from 'next/link'
import { CheckCircle } from 'lucide-react'
import { PortableText } from '@portabletext/react'
import { WhyServiceMatters as WhyServiceMattersType } from '@/types/subService'

interface WhyServiceMattersProps {
  data?: WhyServiceMattersType
}

export const WhyServiceMatters: React.FC<WhyServiceMattersProps> = ({ data }) => {
  if (!data) return null

  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div>
            {data.title && (
              <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
                {data.title}
              </h2>
            )}
            
            {data.description && (
              <div className="text-xl text-gray-600 mb-6 leading-relaxed prose prose-lg max-w-none">
                <PortableText value={data.description} />
              </div>
            )}
            
            {data.features && data.features.length > 0 && (
              <div className="space-y-4 mb-8">
                {data.features.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <div className="bg-blue-100 p-2 rounded-full mr-4">
                      <CheckCircle className="text-blue-600" size={20} />
                    </div>
                    <span className="text-gray-700">{feature.text}</span>
                  </div>
                ))}
              </div>
            )}

            {data.ctaButton?.text && (
              <Link
                href="/free-estimate"
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg shadow-blue-500/25 inline-block text-center"
              >
                {data.ctaButton.text}
              </Link>
            )}
          </div>
          
          {data.stats && data.stats.length > 0 && (
            <div className="grid grid-cols-2 gap-6">
              {data.stats.map((stat, index) => (
                <div key={index} className={`bg-gradient-to-br ${stat.color || 'from-blue-50 to-blue-100'} p-6 rounded-2xl text-center`}>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{stat.value}</div>
                  <div className="text-gray-700 text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  )
}