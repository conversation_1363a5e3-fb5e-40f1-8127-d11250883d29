// components/home/<USER>
import React from 'react';

interface ContactCTASectionProps {
  className?: string;
}

const ContactCTASection: React.FC<ContactCTASectionProps> = ({ className = '' }) => {
  const handleContactUs = (): void => {
    // Navigate to contact page
    window.location.href = '/contact-us';
  };

  return (
    <>
      <style jsx>{`
        @keyframes scroll {
          from { transform: translateX(0%); }
          to { transform: translateX(-50%); }
        }

        .scroll-container {
          display: flex;
          position: absolute;
          bottom: -65px;
          left: 0;
          align-items: flex-end;
          justify-content: flex-start;
          width: 100%;
          height: calc(100% + 65px);
          white-space: nowrap;
        }

        .scroll-wrapper {
          display: flex;
          animation: scroll 8s linear infinite;
        }

        .scroll-text {
          font-size: clamp(3rem, 12vw, 16rem);
          font-weight: 900;
          color: transparent;
          -webkit-text-stroke: clamp(1px, 0.5vw, 3px) rgba(255, 255, 255, 0.2);
          text-transform: uppercase;
          letter-spacing: 0.05em;
          margin: 0;
        }

        /* Mobile - bigger text and faster animation */
        @media (max-width: 639px) {
          .scroll-wrapper {
            animation: scroll 6s linear infinite;
          }
          .scroll-text {
            font-size: clamp(6rem, 20vw, 12rem);
            -webkit-text-stroke: 2px rgba(255, 255, 255, 0.25);
          }
          .scroll-container {
            bottom: -20px;
            height: calc(100% + 20px);
          }
        }

        /* Contact button hover effect */
        .contact-btn {
          position: relative;
          overflow: hidden;
        }

        .contact-btn:hover {
          background: transparent;
          color: transparent;
          -webkit-text-stroke: 2px white;
          text-stroke: 2px white;
          border: 2px solid white;
          transform: translateY(-2px);
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        /* Tablet */
        @media (min-width: 640px) and (max-width: 767px) {
          .scroll-wrapper {
            animation: scroll 10s linear infinite;
          }
        }

        /* Desktop */
        @media (min-width: 768px) and (max-width: 1023px) {
          .scroll-wrapper {
            animation: scroll 14s linear infinite;
          }
        }

        /* Large desktop */
        @media (min-width: 1024px) and (max-width: 1279px) {
          .scroll-wrapper {
            animation: scroll 18s linear infinite;
          }
        }

        /* Extra large desktop */
        @media (min-width: 1280px) {
          .scroll-wrapper {
            animation: scroll 22s linear infinite;
          }
        }
      `}</style>

      <section className={`relative bg-gradient-to-br from-blue-500 to-blue-700 text-white min-h-[400px] sm:min-h-[500px] overflow-hidden ${className}`}>
        {/* Main Content */}
        <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-8 xl:py-12">
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-6 sm:mb-8 leading-tight">
            Wanna get in touch?
          </h2>

          <p className="text-base sm:text-lg md:text-xl leading-relaxed mb-8 sm:mb-10 max-w-2xl opacity-90">
            We offer exceptional services tailored to a wide range of businesses that want to improve the
            effectiveness of their digital marketing activities with discernible returns on investment.
            We aim to get back to all enquiries rapidly.
          </p>

          <button
            onClick={handleContactUs}
            className="contact-btn bg-white text-blue-600 px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-xs sm:text-sm uppercase tracking-wider focus:outline-none focus:ring-4 focus:ring-white focus:ring-opacity-50 transition-all duration-300"
          >
            Contact Us
          </button>
        </div>

        {/* Infinite Scrolling Text - Positioned at bottom like original */}
        <div className="absolute bottom-0 left-0 w-full h-32 sm:h-40 lg:h-48 overflow-hidden z-0">
          <div className="scroll-container">
            <div className="scroll-wrapper">
              <div className="scroll-text">START A PROJECT&nbsp;</div>
              <div className="scroll-text">START A PROJECT&nbsp;</div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default ContactCTASection;