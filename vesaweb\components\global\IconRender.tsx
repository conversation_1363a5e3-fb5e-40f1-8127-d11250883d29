// IconRenderer.tsx - Complete with all schema icons
import React from 'react'
import {
  // All icons from the schema
  MapPin, Search, Target, TrendingUp, Users, Building, Star, Phone, Mail, Globe,
  Smartphone, Calendar, Award, CheckCircle, Navigation, Zap, Rocket, Lightbulb,
  BarChart3, Shield, Cog, ArrowRight, User, ChevronDown,
  // Missing icons that were causing build warnings
  Share2, DollarSign, ShoppingCart, AlertTriangle, Database, Eye, Settings,
  FileText, Layers, Monitor, Code, Palette, Megaphone, MessageSquare,
  // Additional missing icons from migration files
  Image, Cloud, Heart, Store, CreditCard
} from 'lucide-react'

interface IconProps {
  size?: number
  className?: string
}

export const IconRenderer = ({ iconName, size = 24, className = '' }: { 
  iconName?: string
  size?: number
  className?: string 
}) => {
  if (!iconName) return null
  
  // Complete map matching schema exactly
  const iconMap: { [key: string]: React.ComponentType<IconProps> } = {
    // Hero/Badge icons
    MapPin,
    Search,
    Target,
    TrendingUp,
    Users,
    Building,
    Star,
    Phone,
    Mail,
    Globe,
    Smartphone,
    Calendar,
    Award,
    CheckCircle,
    Navigation,
    Zap,
    Rocket,
    Lightbulb,

    // Service icons
    BarChart3,
    Shield,
    Cog,

    // Button/CTA icons
    ArrowRight,
    User,

    // Missing icons that were causing warnings
    Share2,
    DollarSign,
    ShoppingCart,
    AlertTriangle,
    Database,
    Eye,
    Settings,
    FileText,
    Layers,
    Monitor,
    Code,
    Palette,
    Megaphone,
    MessageSquare,

    // Additional missing icons from migration files
    Image,
    Cloud,
    Heart,
    Store,
    CreditCard,

    // Other
    ChevronDown
  }
  
  const IconComponent = iconMap[iconName]
  
  if (!IconComponent) {
    console.warn(`Icon "${iconName}" not found in iconMap. Available icons:`, Object.keys(iconMap))
    return null
  }
  
  return <IconComponent size={size} className={className} />
}