// migrations/test-blog-system.js - Test blog system functionality
const { createClient } = require('@sanity/client')

const client = createClient({
  projectId: process.env.SANITY_STUDIO_PROJECT_ID || 'zleti5e4',
  dataset: process.env.SANITY_STUDIO_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  useCdn: false,
  apiVersion: '2023-05-03'
})

async function testBlogSystem() {
  console.log('🧪 Testing blog system functionality...')

  try {
    // Test 1: Check if blog schemas exist
    console.log('\n📋 Test 1: Checking blog schemas...')

    const schemas = await client.fetch(`
      *[_type in ["blogPost", "blogCategory", "blogTag"]] {
        _type,
        _id
      }
    `)

    const schemaTypes = [...new Set(schemas.map(s => s._type))]
    console.log(`✅ Found schemas: ${schemaTypes.join(', ')}`)

    if (schemaTypes.length < 3) {
      console.log('⚠️  Warning: Some blog schemas may be missing')
    }

    // Test 2: Check blog categories
    console.log('\n📁 Test 2: Checking blog categories...')
    
    const categories = await client.fetch(`
      *[_type == "blogCategory"] {
        title,
        slug,
        color
      }
    `)
    
    console.log(`✅ Found ${categories.length} categories:`)
    categories.forEach(cat => {
      console.log(`   - ${cat.title} (${cat.color})`)
    })

    // Test 3: Check blog tags
    console.log('\n🏷️  Test 3: Checking blog tags...')
    
    const tags = await client.fetch(`
      *[_type == "blogTag"] {
        title,
        slug
      }
    `)
    
    console.log(`✅ Found ${tags.length} tags:`)
    tags.slice(0, 5).forEach(tag => {
      console.log(`   - ${tag.title}`)
    })
    if (tags.length > 5) {
      console.log(`   ... and ${tags.length - 5} more`)
    }

    // Test 4: Check blog posts
    console.log('\n📝 Test 4: Checking blog posts...')

    const posts = await client.fetch(`
      *[_type == "blogPost"] {
        title,
        slug,
        publishedAt,
        featured,
        categories[]->{title},
        tags[]->{title}
      }
    `)

    console.log(`✅ Found ${posts.length} blog posts:`)
    posts.forEach(post => {
      const categoryNames = post.categories?.map(c => c.title).join(', ') || 'No categories'
      const tagNames = post.tags?.map(t => t.title).slice(0, 3).join(', ') || 'No tags'
      console.log(`   - ${post.title}`)
      console.log(`     Categories: ${categoryNames}`)
      console.log(`     Tags: ${tagNames}`)
      console.log(`     Featured: ${post.featured ? 'Yes' : 'No'}`)
      console.log(`     Published: ${post.publishedAt ? new Date(post.publishedAt).toLocaleDateString() : 'No date'}`)
      console.log('')
    })

    // Test 5: Check relationships
    console.log('\n🔗 Test 5: Checking data relationships...')

    const postsWithRefs = await client.fetch(`
      *[_type == "blogPost"] {
        title,
        "hasCategories": count(categories) > 0,
        "hasTags": count(tags) > 0,
        "hasContent": defined(content)
      }
    `)

    const issues = []
    postsWithRefs.forEach(post => {
      if (!post.hasCategories) issues.push(`${post.title}: Missing categories`)
      if (!post.hasContent) issues.push(`${post.title}: Missing content`)
    })
    
    if (issues.length === 0) {
      console.log('✅ All blog posts have proper relationships')
    } else {
      console.log('⚠️  Found relationship issues:')
      issues.forEach(issue => console.log(`   - ${issue}`))
    }

    // Test 6: Check required fields
    console.log('\n✅ Test 6: Checking required fields...')
    
    const fieldCheck = await client.fetch(`
      *[_type == "blogPost"] {
        title,
        slug,
        excerpt,
        "hasSlug": defined(slug.current),
        "hasExcerpt": defined(excerpt),
        "hasTitle": defined(title)
      }
    `)
    
    const fieldIssues = []
    fieldCheck.forEach(post => {
      if (!post.hasTitle) fieldIssues.push(`${post.title || 'Untitled'}: Missing title`)
      if (!post.hasSlug) fieldIssues.push(`${post.title}: Missing slug`)
      if (!post.hasExcerpt) fieldIssues.push(`${post.title}: Missing excerpt`)
    })
    
    if (fieldIssues.length === 0) {
      console.log('✅ All required fields are present')
    } else {
      console.log('⚠️  Found field issues:')
      fieldIssues.forEach(issue => console.log(`   - ${issue}`))
    }

    // Summary
    console.log('\n📊 Test Summary:')
    console.log(`   Categories: ${categories.length}`)
    console.log(`   Tags: ${tags.length}`)
    console.log(`   Blog Posts: ${posts.length}`)
    console.log(`   Featured Posts: ${posts.filter(p => p.featured).length}`)
    
    if (issues.length === 0 && fieldIssues.length === 0) {
      console.log('\n🎉 Blog system test completed successfully!')
      console.log('✅ All tests passed - the blog system is ready to use!')
    } else {
      console.log('\n⚠️  Blog system test completed with warnings')
      console.log('🔧 Please review the issues above before using the blog system')
    }

  } catch (error) {
    console.error('❌ Error during blog system test:', error)
    throw error
  }
}

// Run test if called directly
if (require.main === module) {
  testBlogSystem()
    .then(() => {
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Blog system test failed:', error)
      process.exit(1)
    })
}

module.exports = { testBlogSystem }
