import React from 'react';
import { CheckCircle } from 'lucide-react';

const SEOIndustrySection: React.FC = () => {
  const industries = [
    {
      name: 'E-commerce & Retail',
      description: 'Drive product visibility and sales with specialized e-commerce SEO strategies.',
      benefits: ['Product page optimization', 'Category page SEO', 'Shopping feed optimization', 'Local inventory visibility']
    },
    {
      name: 'Healthcare & Medical',
      description: 'HIPAA-compliant SEO solutions for medical practices and healthcare providers.',
      benefits: ['Medical content optimization', 'Local practice visibility', 'Patient acquisition', 'Reputation management']
    },
    {
      name: 'Legal Services',
      description: 'Attorney and law firm SEO to attract qualified clients and establish authority.',
      benefits: ['Practice area optimization', 'Local legal directory listings', 'Legal content marketing', 'Client testimonial optimization']
    },
    {
      name: 'Real Estate',
      description: 'Property and realtor SEO to dominate local real estate searches.',
      benefits: ['Property listing optimization', 'Local market authority', 'Lead generation focus', 'MLS integration']
    },
    {
      name: 'Professional Services',
      description: 'B2B SEO strategies for consultants, agencies, and service providers.',
      benefits: ['Service page optimization', 'Thought leadership content', 'LinkedIn integration', 'Lead qualification']
    },
    {
      name: 'Technology & SaaS',
      description: 'Technical SEO and content marketing for software and technology companies.',
      benefits: ['Product feature optimization', 'Technical documentation SEO', 'Integration keywords', 'Demo page optimization']
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Industry-Specific SEO Expertise
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Vesa Solutions has extensive experience optimizing websites across diverse industries, understanding unique challenges and opportunities in each sector.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {industries.map((industry, index) => (
            <div key={index} className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <h3 className="text-xl font-bold text-gray-800 mb-4">{industry.name}</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">{industry.description}</p>
              <ul className="space-y-2">
                {industry.benefits.map((benefit, benefitIndex) => (
                  <li key={benefitIndex} className="flex items-center text-gray-700">
                    <CheckCircle size={16} className="text-green-500 mr-2 flex-shrink-0" />
                    <span className="text-sm">{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SEOIndustrySection;