import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Image from 'next/image';
import styles from './HeroSection.module.css';

interface HeroSectionProps {
  className?: string;
}

const HeroSection: React.FC<HeroSectionProps> = (props) => {
  const className = props.className || '';
  const router = useRouter();
  const [animationsReady, setAnimationsReady] = useState(false);
  const [websiteUrl, setWebsiteUrl] = useState('');

  // Small delay to prevent weird reload positioning
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimationsReady(true);
    }, 100); // Very small delay

    return () => clearTimeout(timer);
  }, []);

  // Handle form submission
  const handleGetProposal = () => {
    // Store the website URL in sessionStorage to pass to free-estimate page
    if (websiteUrl.trim()) {
      sessionStorage.setItem('websiteUrl', websiteUrl.trim());
    }
    // Navigate to free-estimate page
    router.push('/free-estimate');
  };
  
  // Images for the carousel
  const images = React.useMemo(() => [
    '/aic.jpg', '/argon.jpg', '/caffee.jpg', '/elixir.jpg', '/harbour.jpg',
    '/icecream.jpg', '/newhorizon.jpg', '/pink.jpg', '/afterhours.jpg',
    '/disti.jpg', '/liquortogo.jpg'
  ], []);

  return (
    <div className={`relative w-full h-screen flex flex-col overflow-hidden ${className}`}>
      {/* Deep gradient background */}
      <div className="absolute inset-0 bg-indigo-900 bg-opacity-90 z-0"></div>
      
      {/* Scrolling Mockups Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute right-0 top-0 w-full h-full opacity-60">
          <div className={`flex h-full ${styles.columnsContainer}`}>
            {/* Create multiple columns of scrolling content - responsive */}
            {Array.from({ length: 5 }, (_, colIndex) => {
              const isReverse = colIndex % 2 === 0;
              const duration = 70 + (colIndex * 8);
              const offsetX = colIndex * 6;
              const startOffsetY = (colIndex % 3) * 10;
              
              // Add responsive classes
              let columnClass = styles.scrollWrapper;
              if (colIndex === 2) columnClass += ` ${styles.column3}`;
              if (colIndex === 3) columnClass += ` ${styles.column4}`; 
              if (colIndex === 4) columnClass += ` ${styles.column5}`;
              
              return (
                <div 
                  key={`col-${colIndex}`} 
                  className={columnClass}
                  style={{ transform: `translateX(${offsetX}px)` }}
                >
                  <div
                    className={`${styles.smoothScroll} ${animationsReady ? styles.infiniteScrollY : ''}`}
                    style={{ 
                      animationDuration: `${duration}s`,
                      animationDirection: isReverse ? 'reverse' : 'normal',
                      marginTop: `${startOffsetY}%`
                    }}
                  >
                    {/* Generate multiple sets of images */}
                    {Array.from({ length: 3 }, (_, setIndex) => (
                      <div key={`set-${colIndex}-${setIndex}`} className="flex flex-col gap-6">
                        {images.map((image, imgIndex) => {
                          const rotate = ((colIndex + imgIndex) % 3 - 1) * 1;
                          
                          return (
                            <div 
                              key={`mockup-${colIndex}-${setIndex}-${imgIndex}`}
                              className="transform hover:scale-105 transition-transform duration-500"
                              style={{ transform: `rotate(${rotate}deg)` }}
                            >
                              <div className={styles.heroMockup}>
                                <Image
                                  src={image}
                                  alt={`Hero Section ${imgIndex}`}
                                  fill
                                  className="object-cover"
                                  priority
                                />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    ))}
                    
                    {/* Duplicate set for seamless looping */}
                    {Array.from({ length: 3 }, (_, setIndex) => (
                      <div key={`dup-set-${colIndex}-${setIndex}`} className="flex flex-col gap-6" aria-hidden="true">
                        {images.map((image, imgIndex) => {
                          const rotate = ((colIndex + imgIndex) % 3 - 1) * 1;
                          
                          return (
                            <div 
                              key={`mockup-dup-${colIndex}-${setIndex}-${imgIndex}`}
                              className="transform hover:scale-105 transition-transform duration-500"
                              style={{ transform: `rotate(${rotate}deg)` }}
                            >
                              <div className={styles.heroMockup}>
                                <Image
                                  src={image}
                                  alt={`Hero Section ${imgIndex}`}
                                  fill
                                  className="object-cover"
                                  priority
                                />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      
      {/* Gradient overlay for readability */}
      <div className="absolute inset-0 bg-gradient-to-r from-indigo-900 via-indigo-900/75 to-indigo-900/5 z-10"></div>
      
      {/* Hero Content */}
      <div className="relative z-20 flex-1 container mx-auto flex items-center">
        <div className="w-full md:w-3/5 px-6 md:px-12">
          <div className="max-w-2xl">
            {/* Main Heading */}
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight mb-6">
              DIGITAL MARKETING<br />
              THAT <span className="text-blue-400">ACTUALLY</span><br />
              WORKS
            </h1>
            
            {/* Subheading */}
            <p className="text-xl md:text-2xl text-blue-100 mb-8">
              More leads, more conversions, more sales.<br />
              We guarantee it.
            </p>
            
            {/* Form */}
            <div className="flex flex-col md:flex-row gap-4">
              <input
                type="text"
                placeholder="Enter Website Address"
                value={websiteUrl}
                onChange={(e) => setWebsiteUrl(e.target.value)}
                className="border-2 border-blue-300 bg-transparent rounded-full px-6 py-3 flex-1 text-base text-white placeholder-blue-200 focus:border-blue-400 focus:outline-none transition-colors"
              />
              <button
                type="button"
                onClick={handleGetProposal}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-full px-8 py-3 whitespace-nowrap text-base transition-colors"
              >
                GET MY FREE PROPOSAL →
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;