import React from 'react';
import { Monitor, Smartphone, Settings } from 'lucide-react';

const DigitalMarketingTechnologySection: React.FC = () => {
  const marketingTools = [
    {
      name: 'Google Ads Platform',
      description: 'Advanced PPC campaign management and optimization',
      purpose: 'Maximize ROI through data-driven ad targeting and bidding'
    },
    {
      name: 'Facebook Business Manager',
      description: 'Comprehensive social media advertising and analytics',
      purpose: 'Reach targeted audiences across Facebook, Instagram, and WhatsApp'
    },
    {
      name: 'HubSpot Marketing Hub',
      description: 'All-in-one marketing automation and CRM platform',
      purpose: 'Lead nurturing, email marketing, and customer journey tracking'
    },
    {
      name: 'Google Analytics 4',
      description: 'Advanced website analytics and conversion tracking',
      purpose: 'Measure campaign performance and user behavior insights'
    },
    {
      name: 'Mailchimp Enterprise',
      description: 'Professional email marketing and automation',
      purpose: 'Personalized email campaigns and customer segmentation'
    },
    {
      name: 'Hootsuite Professional',
      description: 'Social media management and scheduling platform',
      purpose: 'Streamline social media presence and engagement tracking'
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6">
        {/* Marketing Tools Section */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Professional Digital Marketing Tools & Technology
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Vesa Solutions utilizes industry-leading digital marketing tools and technologies to deliver comprehensive campaign management, tracking, and optimization for maximum results.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {marketingTools.map((tool, index) => (
            <div key={index} className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="bg-purple-100 w-12 h-12 rounded-xl flex items-center justify-center mb-4">
                <Monitor className="text-purple-600" size={24} />
              </div>
              <h4 className="text-lg font-bold text-gray-800 mb-3">{tool.name}</h4>
              <p className="text-gray-600 mb-3">{tool.description}</p>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-700 font-medium">Purpose: {tool.purpose}</p>
              </div>
            </div>
          ))}
        </div>

        {/* AI & Future Technology Section */}
        <div className="border-t border-gray-200 pt-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-8">
              Future-Proof Your Marketing: AI & Automation
            </h3>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              Digital marketing is evolving rapidly with AI and automation. VESA Solutions ensures your business leverages cutting-edge technologies for personalized customer experiences, predictive analytics, and automated campaign optimization.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl">
                <Smartphone className="text-purple-600 mb-4" size={48} />
                <h4 className="text-xl font-bold text-gray-800 mb-4">AI-Powered Personalization</h4>
                <p className="text-gray-600">Deliver personalized content and product recommendations based on user behavior and preferences.</p>
              </div>
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl">
                <Monitor className="text-purple-600 mb-4" size={48} />
                <h4 className="text-xl font-bold text-gray-800 mb-4">Predictive Analytics</h4>
                <p className="text-gray-600">Use machine learning to predict customer behavior and optimize campaign performance in real-time.</p>
              </div>
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl">
                <Settings className="text-purple-600 mb-4" size={48} />
                <h4 className="text-xl font-bold text-gray-800 mb-4">Marketing Automation</h4>
                <p className="text-gray-600">Automate lead nurturing, email sequences, and customer journey optimization for maximum efficiency.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DigitalMarketingTechnologySection;
