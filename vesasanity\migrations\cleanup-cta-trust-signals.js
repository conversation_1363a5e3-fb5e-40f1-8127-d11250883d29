// Migration to remove phoneNumber from CTA and trustSignals from FooterCTA
const { createClient } = require('@sanity/client')

const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

async function cleanupSubServices() {
  try {
    console.log('Starting cleanup of sub-services...')
    
    // Get all sub-services
    const subServices = await client.fetch('*[_type == "subService"]')
    
    console.log(`Found ${subServices.length} sub-services to update`)
    
    for (const subService of subServices) {
      const updates = []
      
      // Remove phoneNumber from CTA if it exists
      if (subService.cta && subService.cta.phoneNumber) {
        updates.push({
          id: subService._id,
          patch: {
            unset: ['cta.phoneNumber']
          }
        })
        console.log(`Removing phoneNumber from CTA for: ${subService.title}`)
      }
      
      // Remove trustSignals from FooterCTA if it exists
      if (subService.footerCta && subService.footerCta.trustSignals) {
        updates.push({
          id: subService._id,
          patch: {
            unset: ['footerCta.trustSignals']
          }
        })
        console.log(`Removing trustSignals from FooterCTA for: ${subService.title}`)
      }
      
      // Apply updates
      for (const update of updates) {
        await client.patch(update.id).unset(update.patch.unset).commit()
      }
    }
    
    console.log('Cleanup completed successfully!')
    
  } catch (error) {
    console.error('Error during cleanup:', error)
  }
}

// Run the cleanup
cleanupSubServices()
