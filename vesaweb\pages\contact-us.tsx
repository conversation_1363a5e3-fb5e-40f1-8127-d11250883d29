import React, { useState } from 'react';
import Head from 'next/head';
import Header from '@/components/global/Header';
import Footer from '@/components/global/Footer';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  MessageCircle, 
  Calendar,
  ArrowRight,
  CheckCircle,
  Users,
  Target,
  TrendingUp,
  Globe
} from 'lucide-react';

// Reusable Form Component (simplified version based on your existing Form.tsx)
const ContactForm = () => {
  const [formData, setFormData] = useState({
    businessName: '',
    fullName: '',
    email: '',
    phone: '',
    location: '',
    website: '',
    service: 'General Inquiry',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState('idle');

  const services = [
    'General Inquiry',
    'SEO Services',
    'Web Development',
    'PPC Advertising',
    'Social Media Marketing',
    'Content Writing',
    'Email Marketing',
    'Reputation Management'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      setSubmitStatus('success');
      setIsSubmitting(false);
      // Reset form after success
      setTimeout(() => {
        setSubmitStatus('idle');
        setFormData({
          businessName: '',
          fullName: '',
          email: '',
          phone: '',
          location: '',
          website: '',
          service: 'General Inquiry',
          message: ''
        });
      }, 3000);
    }, 2000);
  };

  if (submitStatus === 'success') {
    return (
      <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <h3 className="text-2xl font-bold text-gray-800 mb-2">Thank You! 🎉</h3>
        <p className="text-gray-600 mb-4">
          Your message has been sent successfully. We&apos;ll get back to you within 24 hours.
        </p>
        <p className="text-sm text-gray-500">
          Check your email for confirmation and next steps.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl shadow-xl p-8">
      <h3 className="text-2xl font-bold text-gray-800 mb-2">
        Let&apos;s Start Growing Your Business
      </h3>
      <p className="text-gray-600 mb-6">
        Tell us about your project and we&apos;ll create a custom strategy for your success.
      </p>

      <div className="space-y-4">
        {/* Business Name & Your Name */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <input 
            name="businessName"
            value={formData.businessName}
            onChange={handleInputChange}
            type="text" 
            placeholder="Business Name*" 
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
          />
          <input 
            name="fullName"
            value={formData.fullName}
            onChange={handleInputChange}
            type="text" 
            placeholder="Your Name*" 
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
          />
        </div>

        {/* Email & Phone */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <input 
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            type="email" 
            placeholder="Email Address*" 
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
          />
          <input 
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            type="tel" 
            placeholder="Phone Number*" 
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
          />
        </div>

        {/* Location & Website */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <input 
            name="location"
            value={formData.location}
            onChange={handleInputChange}
            type="text" 
            placeholder="Business Location*" 
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
          />
          <input 
            name="website"
            value={formData.website}
            onChange={handleInputChange}
            type="url" 
            placeholder="Website URL (optional)" 
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
          />
        </div>

        {/* Service Selection */}
        <select 
          name="service"
          value={formData.service}
          onChange={handleInputChange}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
        >
          {services.map(service => (
            <option key={service} value={service}>{service}</option>
          ))}
        </select>

        {/* Message */}
        <textarea 
          name="message"
          value={formData.message}
          onChange={handleInputChange}
          placeholder="Tell us about your business goals and how we can help you achieve them*"
          rows={4}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-colors"
        />

        {/* Submit Button */}
        <button 
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed text-white font-bold py-4 rounded-lg transition-all duration-300 flex items-center justify-center"
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Sending Message...
            </>
          ) : (
            <>
              Send Message
              <ArrowRight className="ml-2 w-5 h-5" />
            </>
          )}
        </button>
      </div>

      {/* Trust indicator */}
      <p className="text-xs text-gray-500 text-center mt-4">
        🔒 Your information is secure and will never be shared. We&apos;ll contact you within 24 hours.
      </p>
    </div>
  );
};

const ContactPage = () => {
  const contactMethods = [
    {
      icon: Mail,
      title: "Email Us",
      description: "Get a detailed response within 24 hours",
      value: "<EMAIL>",
      action: "mailto:<EMAIL>",
      actionText: "Send Email"
    },
    {
      icon: Phone,
      title: "Call Us",
      description: "Speak directly with our digital marketing experts",
      value: "+355 69 404 6408",
      action: "tel:+***********",
      actionText: "Call Now"
    },
    {
      icon: MessageCircle,
      title: "WhatsApp",
      description: "Quick chat for immediate questions",
      value: "Message us instantly",
      action: "https://wa.me/***********",
      actionText: "Start Chat"
    },
    {
      icon: Calendar,
      title: "Schedule Meeting",
      description: "Book a free 30-minute consultation",
      value: "Available 24/7",
      action: "#contact-form",
      actionText: "Book Call"
    }
  ];

  const officeFeatures = [
    {
      icon: Users,
      title: "Expert Team",
      description: "20+ digital marketing specialists"
    },
    {
      icon: Target,
      title: "Proven Results",
      description: "200+ successful client projects"
    },
    {
      icon: TrendingUp,
      title: "Growth Focus",
      description: "Average 300% ROI improvement"
    },
    {
      icon: Globe,
      title: "Global Reach",
      description: "Serving clients worldwide"
    }
  ];

  const faqs = [
    {
      question: "How quickly can you start working on my project?",
      answer: "We can typically begin your project within 1-2 business days after our initial consultation and agreement."
    },
    {
      question: "Do you offer free consultations?",
      answer: "Yes! We offer complimentary 30-minute consultations to discuss your business goals and how we can help achieve them."
    },
    {
      question: "What's included in your digital marketing services?",
      answer: "Our comprehensive services include SEO, PPC advertising, web development, social media marketing, content creation, and reputation management."
    },
    {
      question: "How do you measure success?",
      answer: "We track key metrics like organic traffic growth, conversion rates, ROI, and specific KPIs aligned with your business objectives."
    }
  ];

  const scrollToForm = () => {
    const formElement = document.getElementById('contact-form');
    if (formElement) {
      formElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <>
      <Head>
        <title>Contact Vesa Solutions - Digital Marketing Experts in Albania</title>
        <meta name="description" content="Contact Vesa Solutions for expert digital marketing services in Albania. Get free consultation on SEO, web development, PPC, and social media marketing. Call +355 69 404 6408 today!" />
        
        {/* Open Graph tags for social sharing */}
        <meta property="og:title" content="Contact Vesa Solutions - Digital Marketing Experts" />
        <meta property="og:description" content="Get free consultation on digital marketing services in Albania. SEO, web development, PPC, and more." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://vesasolutions.com/contact-us" />

        {/* Additional SEO tags */}
        <meta name="keywords" content="digital marketing albania, SEO albania, web development durres, contact vesa solutions, digital marketing consultation" />
        <meta name="author" content="Vesa Solutions" />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://vesasolutions.com/contact-us" />
      </Head>

      <div className="min-h-screen bg-gray-50">
      <Header isVisible={true} />
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 text-white py-20 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-32 h-32 border border-white/20 rounded-lg transform rotate-12"></div>
          <div className="absolute top-40 right-20 w-24 h-24 border border-white/20 rounded-lg transform -rotate-12"></div>
          <div className="absolute bottom-32 left-32 w-16 h-16 border border-white/20 rounded-lg transform rotate-45"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
              Ready to <span className="text-blue-400">Transform</span><br />
              Your Digital Presence?
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Get in touch with our digital marketing experts and discover how we can help your business thrive online.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                onClick={scrollToForm}
                className="bg-white text-blue-900 px-8 py-4 rounded-full font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                Get Free Consultation
              </button>
              <a
                href="tel:+***********"
                className="border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-blue-900 transition-all duration-300"
              >
                Call Us Now
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Methods Grid */}
      <section className="py-16 -mt-10 relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {contactMethods.map((method, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-lg p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <method.icon className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">{method.title}</h3>
                <p className="text-gray-600 text-sm mb-3">{method.description}</p>
                <p className="text-gray-800 font-semibold mb-4">{method.value}</p>
                {method.action.startsWith('#') ? (
                  <button 
                    onClick={scrollToForm}
                    className="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors"
                  >
                    {method.actionText}
                    <ArrowRight className="ml-1 w-4 h-4" />
                  </button>
                ) : (
                  <a 
                    href={method.action}
                    target={method.action.startsWith('http') ? "_blank" : undefined}
                    rel={method.action.startsWith('http') ? "noopener noreferrer" : undefined}
                    className="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors"
                  >
                    {method.actionText}
                    <ArrowRight className="ml-1 w-4 h-4" />
                  </a>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Main Contact Section */}
      <section id="contact-form" className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* Contact Form */}
            <div>
              <ContactForm />
            </div>

            {/* Contact Information & Office Details */}
            <div className="space-y-8">
              {/* Office Location */}
              <div className="bg-white rounded-2xl shadow-lg p-8">
                <h3 className="text-2xl font-bold text-gray-800 mb-6">Visit Our Office</h3>
                
                <div className="space-y-4 mb-6">
                  <div className="flex items-start">
                    <MapPin className="w-6 h-6 text-blue-600 mt-1 mr-3 flex-shrink-0" />
                    <div>
                      <p className="font-semibold text-gray-800">Headquarters</p>
                      <p className="text-gray-600">
                        Bulevardi Dyrrah, Pallati 394, Kati 4-t<br />
                        2001, Durrës, Albania
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <Clock className="w-6 h-6 text-blue-600 mr-3" />
                    <div>
                      <p className="font-semibold text-gray-800">Business Hours</p>
                      <p className="text-gray-600">Available 24/7 for urgent matters</p>
                      <p className="text-sm text-gray-500">Mon-Fri: 9AM-6PM CET</p>
                    </div>
                  </div>
                </div>

                {/* Google Maps */}
                <div className="w-full h-48 rounded-lg overflow-hidden mb-4 border border-gray-200 shadow-sm">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2995.8766!2d19.4458424!3d41.3158368!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x134fda47e5beeea7%3A0x9d60d9bb977e035!2sVesa%20Solutions!5e0!3m2!1sen!2s!4v1647380947890!5m2!1sen!2s"
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Vesa Solutions - Pallati 394, Kati 4-t, 4th Floor, Building 394, Durrës, Albania"
                    className="rounded-lg"
                  ></iframe>
                </div>

                <a 
                  href="https://www.google.com/maps/place/Vesa+Solutions/@41.3158368,19.4458424,17z/data=!3m1!4b1!4m6!3m5!1s0x134fda47e5beeea7:0x9d60d9bb977e035!8m2!3d41.3158328!4d19.4484173!16s%2Fg%2F11gcx_qzyj?hl=en&entry=ttu&g_ep=EgoyMDI1MDYyOS4wIKXMDSoASAFQAw%3D%3D"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors"
                >
                  Get Directions
                  <ArrowRight className="ml-1 w-4 h-4" />
                </a>
              </div>

              {/* Why Choose Us */}
              <div className="bg-white rounded-2xl shadow-lg p-8">
                <h3 className="text-2xl font-bold text-gray-800 mb-6">Why Choose Vesa Solutions?</h3>
                
                <div className="grid grid-cols-2 gap-4">
                  {officeFeatures.map((feature, index) => (
                    <div key={index} className="text-center">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <feature.icon className="w-6 h-6 text-blue-600" />
                      </div>
                      <h4 className="font-semibold text-gray-800 text-sm mb-1">{feature.title}</h4>
                      <p className="text-xs text-gray-600">{feature.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600">
              Get quick answers to common questions about our services
            </p>
          </div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-colors">
                <h3 className="text-lg font-semibold text-gray-800 mb-3">{faq.question}</h3>
                <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">Still have questions?</p>
            <a 
              href="mailto:<EMAIL>"
              className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              <Mail className="mr-2 w-5 h-5" />
              Email Us Directly
            </a>
          </div>
        </div>
      </section>

      <Footer />
      </div>
    </>
  );
};

export default ContactPage;