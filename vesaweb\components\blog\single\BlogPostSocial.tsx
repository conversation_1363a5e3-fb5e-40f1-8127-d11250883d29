// components/blog/single/BlogPostSocial.tsx - Social sharing component
import React from 'react'
import { Share2, Facebook, Twitter, Linkedin, Link as LinkIcon } from 'lucide-react'
import { BlogPost } from '@/types/blog'

interface BlogPostSocialProps {
  post: BlogPost
}

const BlogPostSocial: React.FC<BlogPostSocialProps> = ({ post }) => {
  const postUrl = `https://vesasolutions.com/blog/${post.slug.current}`
  const postTitle = encodeURIComponent(post.title)
  // const postDescription = encodeURIComponent(post.excerpt)

  const shareLinks = {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(postUrl)}`,
    twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(postUrl)}&text=${postTitle}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(postUrl)}`
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(postUrl)
      // You could add a toast notification here
      alert('Link copied to clipboard!')
    } catch (err) {
      console.error('Failed to copy link:', err)
    }
  }

  const handleShare = (platform: string) => {
    const url = shareLinks[platform as keyof typeof shareLinks]
    if (url) {
      window.open(url, '_blank', 'width=600,height=400')
    }
  }

  return (
    <div className="mt-8 pt-6 border-t border-gray-200">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Share2 size={20} className="text-gray-600" />
          <span className="text-gray-700 font-medium">Share this article:</span>
        </div>
        
        <div className="flex items-center gap-3">
          <button
            onClick={() => handleShare('facebook')}
            className="inline-flex items-center justify-center w-10 h-10 bg-blue-600 hover:bg-blue-700 text-white rounded-full transition-colors duration-200"
            title="Share on Facebook"
            aria-label="Share on Facebook"
          >
            <Facebook size={16} />
          </button>

          <button
            onClick={() => handleShare('twitter')}
            className="inline-flex items-center justify-center w-10 h-10 bg-sky-500 hover:bg-sky-600 text-white rounded-full transition-colors duration-200"
            title="Share on Twitter"
            aria-label="Share on Twitter"
          >
            <Twitter size={16} />
          </button>

          <button
            onClick={() => handleShare('linkedin')}
            className="inline-flex items-center justify-center w-10 h-10 bg-blue-700 hover:bg-blue-800 text-white rounded-full transition-colors duration-200"
            title="Share on LinkedIn"
            aria-label="Share on LinkedIn"
          >
            <Linkedin size={16} />
          </button>

          <button
            onClick={copyToClipboard}
            className="inline-flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-700 text-white rounded-full transition-colors duration-200"
            title="Copy link"
            aria-label="Copy link to clipboard"
          >
            <LinkIcon size={16} />
          </button>
        </div>
      </div>
    </div>
  )
}

export default BlogPostSocial
