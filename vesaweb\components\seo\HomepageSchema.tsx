import React from 'react';
import Head from 'next/head';

const HomepageSchema: React.FC = () => {
  // Complete Organization Schema with all services
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Vesa Solutions",
    "alternateName": ["VesaSolutions", "VESA Solutions"],
    "url": "https://vesasolutions.com",
    "logo": {
      "@type": "ImageObject",
      "url": "https://vesasolutions.com/VesaLogo.svg",
      "width": "200",
      "height": "80"
    },
    "image": {
      "@type": "ImageObject",
      "url": "https://vesasolutions.com/VesaLogo.svg",
      "width": "200",
      "height": "80"
    },
    "description": "Full-service digital marketing agency helping businesses attract, impress, and convert more leads online since 2015. Specializing in SEO, web development, and digital marketing services.",
    "foundingDate": "2015",
    "email": "<EMAIL>",
    "telephone": ["+***********", "+355694046408"],
    "faxNumber": null,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Bulevardi Dyrrah, Pallati 394, Kati 4-t",
      "addressLocality": "Durrës",
      "addressRegion": "Durrës County",
      "postalCode": "2001",
      "addressCountry": "AL"
    },

    "contactPoint": [
      {
        "@type": "ContactPoint",
        "telephone": "+***********",
        "contactType": "customer service",
        "areaServed": "US",
        "availableLanguage": "English"
      },
      {
        "@type": "ContactPoint", 
        "telephone": "+355694046408",
        "contactType": "customer service",
        "areaServed": "AL",
        "availableLanguage": ["English", "Albanian"]
      },
      {
        "@type": "ContactPoint",
        "email": "<EMAIL>",
        "contactType": "customer service",
        "areaServed": "Worldwide"
      }
    ],
    "sameAs": [
      "https://www.facebook.com/vesasolutions",
      "https://www.linkedin.com/company/vesasolutions",
      "https://twitter.com/vesasolutions",
      "https://www.instagram.com/vesasolutions"
    ],
    "serviceArea": {
      "@type": "Place",
      "name": "Worldwide"
    },
    "knowsAbout": [
      "Search Engine Optimization",
      "Web Development", 
      "Digital Marketing",
      "PPC Advertising",
      "Social Media Marketing",
      "Email Marketing",
      "Content Marketing",
      "Local SEO",
      "Technical SEO",
      "E-commerce Development",
      "Website Design",
      "Branding"
    ],
    "makesOffer": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "SEO Services",
          "description": "Comprehensive search engine optimization services to improve organic rankings and traffic."
        },
        "areaServed": "Worldwide"
      },
      {
        "@type": "Offer", 
        "itemOffered": {
          "@type": "Service",
          "name": "Web Development Services",
          "description": "Custom website development, e-commerce solutions, and web applications."
        },
        "areaServed": "Worldwide"
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service", 
          "name": "Digital Marketing Services",
          "description": "Complete digital marketing solutions including PPC, social media, and email marketing."
        },
        "areaServed": "Worldwide"
      }
    ]
  };

  // Local Business Schema
  const localBusinessSchema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Vesa Solutions",
    "image": "https://vesasolutions.com/VesaLogo.svg",
    "url": "https://vesasolutions.com",
    "telephone": "+***********",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Bulevardi Dyrrah, Pallati 394, Kati 4-t",
      "addressLocality": "Durrës",
      "addressRegion": "Durrës County", 
      "postalCode": "2001",
      "addressCountry": "AL"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "41.3317",
      "longitude": "19.4414"
    },
    "openingHoursSpecification": {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
      "opens": "00:00",
      "closes": "23:59"
    },
    "priceRange": "$$",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "127",
      "bestRating": "5",
      "worstRating": "1"
    },
    "review": [
      {
        "@type": "Review",
        "author": {
          "@type": "Person",
          "name": "Elira Kokona"
        },
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5"
        },
        "reviewBody": "Working with Vesa Solutions has been a game-changer for our digital presence. They expertly built our website from the ground up, ensuring it's not only visually appealing but also optimized for performance and SEO. Thanks to their comprehensive approach, we've seen significant improvements in our online visibility and search engine rankings."
      },
      {
        "@type": "Review",
        "author": {
          "@type": "Person",
          "name": "Sonila Mustafa"
        },
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5"
        },
        "reviewBody": "Vesa Solutions completely revolutionized our online presence. Their strategic approach to website design and digital marketing has helped us stand out in a competitive market. The team took time to understand our business needs and delivered solutions that exceeded our expectations."
      },
      {
        "@type": "Review",
        "author": {
          "@type": "Organization",
          "name": "Durana Tech Park"
        },
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5"
        },
        "reviewBody": "As the founder of Durana Tech Park, I knew we needed a digital identity that could match the ambition of Albania's first dedicated technology and innovation ecosystem. Vesa Solutions rose to the occasion. They delivered a sleek, scalable, and informative website that showcases our infrastructure, investor incentives, and global opportunities."
      },
      {
        "@type": "Review",
        "author": {
          "@type": "Person",
          "name": "Florian Duci"
        },
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5"
        },
        "reviewBody": "Vesa Solutions understood the unique challenges of marketing an events business online. They created a website that beautifully showcases our past events while making it easy for potential clients to inquire about our services. Their ongoing SEO and social media strategy has helped us reach new clients and build our brand reputation."
      }
    ]
  };

  // Website Schema
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Vesa Solutions",
    "alternateName": "VesaSolutions",
    "url": "https://vesasolutions.com",
    "description": "Full-service digital marketing agency helping businesses attract, impress, and convert more leads online since 2015.",
    "publisher": {
      "@type": "Organization",
      "name": "Vesa Solutions",
      "logo": {
        "@type": "ImageObject",
        "url": "https://vesasolutions.com/VesaLogo.svg"
      }
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://vesasolutions.com/search?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    },
    "mainEntity": {
      "@type": "Organization",
      "name": "Vesa Solutions"
    }
  };

  // Service Schema (cleaner version without warnings)
  const serviceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Digital Marketing Services",
    "url": "https://vesasolutions.com",
    "description": "Professional digital marketing services including SEO, web development, PPC advertising, social media marketing, and branding solutions.",
    "provider": {
      "@type": "Organization",
      "name": "Vesa Solutions",
      "url": "https://vesasolutions.com"
    },
    "areaServed": {
      "@type": "Place",
      "name": "Worldwide"
    },
    "serviceType": "Digital Marketing"
  };

  return (
    <Head>
      {/* Organization Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />

      {/* Local Business Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(localBusinessSchema)
        }}
      />

      {/* Website Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema)
        }}
      />

      {/* Service Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceSchema)
        }}
      />
    </Head>
  );
};

export default HomepageSchema;
