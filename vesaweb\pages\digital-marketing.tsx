import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Header from '@/components/global/Header';
import Footer from '@/components/global/Footer';

// Import optimized Digital Marketing section components
import DigitalMarketingHeroSection from '@/components/services/digital-marketing/main-digital-marketing/DigitalMarketingHeroSection';
import DigitalMarketingAboutSection from '@/components/services/digital-marketing/main-digital-marketing/DigitalMarketingAboutSection';
import DigitalMarketingServicesSection from '@/components/services/digital-marketing/main-digital-marketing/DigitalMarketingServicesSection';
import DigitalMarketingIndustrySection from '@/components/services/digital-marketing/main-digital-marketing/DigitalMarketingIndustrySection';
import DigitalMarketingProcessSection from '@/components/services/digital-marketing/main-digital-marketing/DigitalMarketingProcessSection';
import DigitalMarketingCTASection from '@/components/services/digital-marketing/main-digital-marketing/DigitalMarketingCTASection';
import DigitalMarketingTechnologySection from '@/components/services/digital-marketing/main-digital-marketing/DigitalMarketingTechnologySection';
// import DigitalMarketingResultsSection from '@/components/services/digital-marketing/main-digital-marketing/DigitalMarketingResultsSection';
import DigitalMarketingFAQSection from '@/components/services/digital-marketing/main-digital-marketing/DigitalMarketingFAQSection';

const DigitalMarketingPage: React.FC = () => {
  return (
    <>
      <Head>
        <title>Expert Digital Marketing Agency That Drives Results | Vesa Solutions</title>
        <meta name="description" content="Grow your business with our data-driven digital marketing strategies. 15+ years of proven results. Get more leads, sales & revenue. Free marketing audit included." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      
      <Header />
      
      {/* Breadcrumbs */}
      <div className="bg-gray-50 py-4">
        <div className="max-w-7xl mx-auto px-6">
          <nav className="text-sm" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              <li>
                <Link href="/" className="text-gray-500 hover:text-purple-600 transition-colors">
                  Home
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li>
                <Link href="/services" className="text-gray-500 hover:text-purple-600 transition-colors">
                  Services
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-gray-900 font-medium" aria-current="page">
                Digital Marketing
              </li>
            </ol>
          </nav>
        </div>
      </div>
      
      <main className="min-h-screen bg-white">
        {/* All Digital Marketing Page Sections */}
        <DigitalMarketingHeroSection />
        <DigitalMarketingAboutSection />
        <DigitalMarketingServicesSection />
        <DigitalMarketingIndustrySection />
        <DigitalMarketingProcessSection />
        <DigitalMarketingCTASection />
        <DigitalMarketingTechnologySection />
        {/* <DigitalMarketingResultsSection /> */}
        <DigitalMarketingFAQSection />
      </main>
      
      <Footer />
    </>
  );
};

export default DigitalMarketingPage;
