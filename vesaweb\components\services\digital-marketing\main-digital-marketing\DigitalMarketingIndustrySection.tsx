import React from 'react';
import Link from 'next/link';
import { Building2, ShoppingCart, Heart, Scale, Home, Laptop } from 'lucide-react';

const DigitalMarketingIndustrySection: React.FC = () => {
  const industries = [
    {
      icon: ShoppingCart,
      title: 'E-commerce & Retail',
      description: 'Drive online sales with targeted PPC campaigns, social media advertising, and email marketing automation.',
      results: '+340% Sales Growth',
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: Heart,
      title: 'Healthcare & Medical',
      description: 'Build trust and attract patients with compliant digital marketing strategies and reputation management.',
      results: '+280% Patient Inquiries',
      color: 'from-purple-600 to-purple-700'
    },
    {
      icon: Scale,
      title: 'Legal Services',
      description: 'Generate qualified leads for law firms through targeted advertising and content marketing strategies.',
      results: '+250% Case Inquiries',
      color: 'from-purple-700 to-purple-800'
    },
    {
      icon: Home,
      title: 'Real Estate',
      description: 'Connect with buyers and sellers through social media marketing, PPC, and lead nurturing campaigns.',
      results: '+320% Lead Generation',
      color: 'from-purple-400 to-purple-500'
    },
    {
      icon: Building2,
      title: 'Professional Services',
      description: 'Establish thought leadership and generate B2B leads through content marketing and LinkedIn advertising.',
      results: '+290% B2B Leads',
      color: 'from-purple-800 to-purple-900'
    },
    {
      icon: Laptop,
      title: 'Technology & SaaS',
      description: 'Scale user acquisition with performance marketing, conversion optimization, and retention campaigns.',
      results: '+380% User Growth',
      color: 'from-purple-300 to-purple-400'
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
            Digital Marketing Expertise Across Industries
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Vesa Solutions has delivered successful digital marketing campaigns for businesses across diverse industries.
            Our proven strategies adapt to your specific market, audience, and business goals.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {industries.map((industry, index) => {
            const IconComponent = industry.icon;
            return (
              <div key={index} className="group bg-white border border-gray-200 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="flex items-start justify-between mb-6">
                  <div className={`bg-gradient-to-r ${industry.color} p-4 rounded-2xl group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="h-8 w-8 text-white" />
                  </div>
                  <div className="bg-green-50 px-3 py-1 rounded-full">
                    <span className="text-green-600 font-bold text-sm">{industry.results}</span>
                  </div>
                </div>
                
                <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                  {industry.title}
                </h3>
                
                <p className="text-gray-600 leading-relaxed">
                  {industry.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Industry Expertise Section */}
        <div className="mt-20 bg-gradient-to-br from-purple-50 to-purple-100 rounded-3xl p-12">
          <div className="text-center">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-6">
              Don&apos;t See Your Industry? We&apos;ve Got You Covered.
            </h3>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Our digital marketing expertise extends beyond these industries. We&apos;ve successfully helped businesses in manufacturing,
              education, hospitality, finance, and many other sectors achieve their growth goals.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">99%</div>
                <div className="text-gray-700">Google Searches</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">1 in 3</div>
                <div className="text-gray-700">Local Searches</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">200+</div>
                <div className="text-gray-700">Happy Clients</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">300%</div>
                <div className="text-gray-700">Average ROI</div>
              </div>
            </div>
            <Link
              href="/contact-us"
              className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-semibold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg shadow-purple-500/25 inline-block"
            >
              Discuss Your Industry Needs
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DigitalMarketingIndustrySection;
