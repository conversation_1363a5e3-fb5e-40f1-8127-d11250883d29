import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Header from '@/components/global/Header';
import Footer from '@/components/global/Footer';
import SchemaMarkup from '@/components/seo/SchemaMarkup';

// Import optimized SEO section components
import SEOHeroSection from '@/components/services/seo/main-seo/SEOHeroSection';
import SEOAboutSection from '@/components/services/seo/main-seo/SEOAboutSection';
import SEOServicesSection from '@/components/services/seo/main-seo/SEOServicesSection';
import SEOIndustrySection from '@/components/services/seo/main-seo/SEOIndustrySection';
import SEOProcessSection from '@/components/services/seo/main-seo/SEOProcessSection';
import SEOCTASection from '@/components/services/seo/main-seo/SEOCTASection';
import SEOTechnologySection from '@/components/services/seo/main-seo/SEOTechnologySection';
// import SEOResultsSection from '@/components/services/seo/main-seo/SEOResultsSection';
import SEOFAQSection from '@/components/services/seo/main-seo/SEOFAQSection';

const SEOPage: React.FC = () => {
  return (
    <>
      <Head>
        <title>Expert SEO Agency That Actually Gets Results | Vesa Solutions</title>
        <meta name="description" content="Dominate Google with our data-driven SEO strategies. 15+ years of proven results. Get more leads, traffic & revenue. Free SEO audit included." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      {/* Schema Markup for SEO Services */}
      <SchemaMarkup
        pageType="service"
        serviceData={{
          name: "Search Engine Optimization (SEO) Services",
          description: "Comprehensive SEO services including on-page optimization, technical SEO, local SEO, and content strategy to improve your website's search engine rankings and organic traffic.",
          category: "SEO Services"
        }}
      />
      
      <Header />
      
      {/* Breadcrumbs */}
      <div className="bg-gray-50 py-4">
        <div className="max-w-7xl mx-auto px-6">
          <nav className="text-sm" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              <li>
                <Link href="/" className="text-gray-500 hover:text-green-600 transition-colors">
                  Home
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li>
                <Link href="/services" className="text-gray-500 hover:text-green-600 transition-colors">
                  Services
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-gray-900 font-medium" aria-current="page">
                SEO Search Engine Optimization
              </li>
            </ol>
          </nav>
        </div>
      </div>
      
      <main className="min-h-screen bg-white">
        {/* All SEO Page Sections */}
        <SEOHeroSection />
        <SEOAboutSection />
        <SEOServicesSection />
        <SEOIndustrySection />
        <SEOProcessSection />
        <SEOCTASection />
        <SEOTechnologySection />
        {/* <SEOResultsSection /> */}
        <SEOFAQSection />
      </main>
      
      <Footer />
    </>
  );
};

export default SEOPage;