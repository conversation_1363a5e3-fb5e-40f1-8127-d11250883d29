import React from 'react';
import Image from 'next/image';

const WebDevResultsSection: React.FC = () => {
  return (
    <>
      {/* Case Study Section */}
      <section className="py-24 bg-blue-600">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-8">
                Real Results: E-commerce Client Achieves 450% Revenue Growth
              </h2>
              <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                See how our custom e-commerce development helped a fashion retailer transform their online presence and achieve remarkable growth in sales and customer engagement.
              </p>
              <div className="grid grid-cols-2 gap-8 mb-8">
                <div className="text-center">
                  <div className="text-4xl font-bold text-blue-300 mb-2">450%</div>
                  <div className="text-blue-100">Revenue Increase</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-blue-300 mb-2">280%</div>
                  <div className="text-blue-100">Conversion Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-blue-300 mb-2">65%</div>
                  <div className="text-blue-100">Faster Load Times</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-blue-300 mb-2">320%</div>
                  <div className="text-blue-100">Mobile Traffic</div>
                </div>
              </div>
              <button className="bg-white text-blue-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
                View Full Case Study
              </button>
            </div>
            <div>
              <Image
                src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop"
                alt="E-commerce Success Story"
                width={600}
                height={400}
                className="w-full rounded-3xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Client Testimonials */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
              What Our Clients Say About Our Web Development
            </h2>
            <p className="text-xl text-gray-600">
              Don&apos;t just take our word for it—hear from businesses that have transformed their online presence with VESA Solutions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-3xl p-8">
              <div className="flex items-center mb-6">
                <Image
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face"
                  alt="Client"
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full mr-4"
                />
                <div>
                  <div className="font-semibold text-gray-800">Michael Chen</div>
                  <div className="text-sm text-gray-600">CEO, TechStart Solutions</div>
                </div>
              </div>
              <p className="text-gray-700 mb-4 italic">
                &quot;Vesa Solutions delivered exactly what we needed—a modern, fast website that converts visitors into customers. Our online sales increased by 300% within the first quarter.&quot;
              </p>
              <div className="text-2xl font-bold text-blue-600">300% Sales Increase</div>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-8">
              <div className="flex items-center mb-6">
                <Image
                  src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face"
                  alt="Client"
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full mr-4"
                />
                <div>
                  <div className="font-semibold text-gray-800">Sarah Johnson</div>
                  <div className="text-sm text-gray-600">Founder, Wellness Studio</div>
                </div>
              </div>
              <p className="text-gray-700 mb-4 italic">
                &quot;The team understood our vision perfectly and created a website that truly represents our brand. The booking system has streamlined our operations completely.&quot;
              </p>
              <div className="text-2xl font-bold text-green-600">95% Time Saved</div>
            </div>

            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-3xl p-8">
              <div className="flex items-center mb-6">
                <Image
                  src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face"
                  alt="Client"
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full mr-4"
                />
                <div>
                  <div className="font-semibold text-gray-800">David Rodriguez</div>
                  <div className="text-sm text-gray-600">Owner, Local Restaurant Chain</div>
                </div>
              </div>
              <p className="text-gray-700 mb-4 italic">
                &quot;Our new website with online ordering has been a game-changer. We&apos;ve seen a 250% increase in online orders and our customers love the user experience.&quot;
              </p>
              <div className="text-2xl font-bold text-blue-600">250% More Orders</div>
            </div>
          </div>
        </div>
      </section>

      {/* Results Summary */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
              Proven Track Record of Success
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              Our web development solutions consistently deliver measurable results for businesses across all industries.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white rounded-2xl p-8 text-center shadow-lg">
              <div className="text-4xl font-bold text-blue-600 mb-2">200+</div>
              <div className="text-gray-800 font-semibold mb-2">Happy Clients</div>
              <div className="text-sm text-gray-600">Successful projects delivered with excellent results</div>
            </div>
            <div className="bg-white rounded-2xl p-8 text-center shadow-lg">
              <div className="text-4xl font-bold text-blue-600 mb-2">300%</div>
              <div className="text-gray-800 font-semibold mb-2">Average ROI</div>
              <div className="text-sm text-gray-600">Improvement in return on investment for our clients</div>
            </div>
            <div className="bg-white rounded-2xl p-8 text-center shadow-lg">
              <div className="text-4xl font-bold text-blue-600 mb-2">5+</div>
              <div className="text-gray-800 font-semibold mb-2">Years Experience</div>
              <div className="text-sm text-gray-600">Professional web development expertise</div>
            </div>
            <div className="bg-white rounded-2xl p-8 text-center shadow-lg">
              <div className="text-4xl font-bold text-blue-600 mb-2">99%</div>
              <div className="text-gray-800 font-semibold mb-2">Google Searches</div>
              <div className="text-sm text-gray-600">Of users search on Google for products and services</div>
            </div>
          </div>

          {/* CTA */}
          <div className="text-center mt-16">
            <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-12 text-white">
              <h3 className="text-3xl md:text-4xl font-bold mb-6">
                Ready to Join Our Success Stories?
              </h3>
              <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
                Let&apos;s discuss how our web development expertise can transform your business and drive the results you&apos;re looking for.
              </p>
              <button className="bg-white text-blue-600 font-bold px-10 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-xl">
                Start Your Web Development Project Today
              </button>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default WebDevResultsSection;
