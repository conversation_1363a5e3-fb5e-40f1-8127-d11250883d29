// scripts/migrate-custom-website-development.js - Working migration with proper schema-compatible icons and BLUE colors
const { createClient } = require('@sanity/client')
const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

const customWebsiteData = {
  _type: 'subService',
  parentService: 'web-development',
  title: 'Custom Website Development Services',
  slug: {
    _type: 'slug',
    current: 'custom-website-development'
  },
  
  // Hero Section
  hero: {
    badgeText: 'Custom Website Development',
    badgeIcon: 'Globe',
    title: 'Build Your Perfect Custom Website',
    subtitle: 'Create a unique online presence with fully custom websites designed and developed to match your brand, engage your audience, and drive business growth. Our custom development ensures your website stands out from the competition.',
    stats: [
      { _key: 'stat1', value: '250%', label: 'Average Conversion Increase' },
      { _key: 'stat2', value: '100%', label: 'Custom Design & Code' },
      { _key: 'stat3', value: '3x', label: 'Faster Loading Speed' },
      { _key: 'stat4', value: '24/7', label: 'Ongoing Support' }
    ],
    backgroundGradient: 'from-blue-600 to-blue-800'
  },

  // Why Service Matters
  whyMatters: {
    title: 'Why Custom Website Development is Essential',
    description: [
      {
        _type: 'block',
        _key: 'desc1',
        children: [
          {
            _type: 'span',
            _key: 'span1',
            text: "In today's competitive digital landscape, template websites simply don't cut it. Your business is unique, and your website should reflect that uniqueness. Custom website development allows you to create a digital experience that perfectly aligns with your brand, meets your specific business requirements, and provides the functionality your customers need."
          }
        ],
        markDefs: [],
        style: 'normal'
      },
      {
        _type: 'block',
        _key: 'desc2',
        children: [
          {
            _type: 'span',
            _key: 'span2',
            text: "VESA Solutions specializes in creating custom websites that not only look stunning but also perform exceptionally. From initial concept to final launch, we build websites that are optimized for search engines, mobile-responsive, fast-loading, and designed to convert visitors into customers."
          }
        ],
        markDefs: [],
        style: 'normal'
      }
    ],
    features: [
      { _key: 'feature1', text: 'Unique design that reflects your brand identity' },
      { _key: 'feature2', text: 'Custom functionality tailored to your business needs' },
      { _key: 'feature3', text: 'Superior performance and loading speeds' },
      { _key: 'feature4', text: 'Complete control over your website\'s features' }
    ],
    stats: [
      {
        _key: 'stat1',
        value: '78%',
        label: 'of users judge credibility by design',
        color: 'from-blue-50 to-blue-100'
      },
      {
        _key: 'stat2',
        value: '3x',
        label: 'higher conversion rates with custom design',
        color: 'from-blue-100 to-blue-200'
      },
      {
        _key: 'stat3',
        value: '94%',
        label: 'of first impressions are design-related',
        color: 'from-blue-200 to-blue-300'
      },
      {
        _key: 'stat4',
        value: '2.5x',
        label: 'better performance than templates',
        color: 'from-blue-300 to-blue-400'
      }
    ],
    ctaButton: {
      text: 'Explore Our Custom Development Process'
    }
  },

  // All Services (with schema-compatible icons)
  services: [
    {
      _key: 'service1',
      icon: 'Palette',
      title: 'Custom Design & Branding',
      description: 'Create a unique visual identity with custom designs that perfectly represent your brand and engage your target audience.',
      fullDescription: 'Custom design goes beyond aesthetics—it\'s about creating a visual experience that communicates your brand values, builds trust, and guides users toward conversion. We design every element from scratch, ensuring your website is truly unique and aligned with your business objectives.',
      features: [
        'Brand-aligned color schemes and typography',
        'Custom graphics, icons, and visual elements',
        'User experience (UX) design optimization',
        'Responsive design for all devices',
        'Accessibility compliance (WCAG guidelines)',
        'Visual hierarchy and conversion optimization',
        'Custom animations and interactive elements',
        'Brand consistency across all pages'
      ],
      benefits: 'Custom design increases brand recognition by 80%, improves user engagement by 65%, and creates a memorable experience that sets you apart from competitors using generic templates.',
      result: '+80% Brand Recognition'
    },
    {
      _key: 'service2',
      icon: 'Code',
      title: 'Custom Functionality Development',
      description: 'Build specific features and functionality that meet your unique business requirements and enhance user experience.',
      fullDescription: 'Every business has unique needs that can\'t be met with off-the-shelf solutions. We develop custom functionality that streamlines your operations, improves user experience, and provides the exact features your business requires to succeed online.',
      features: [
        'Custom contact forms and lead capture systems',
        'User registration and membership portals',
        'Custom calculators and interactive tools',
        'Integration with third-party services and APIs',
        'Custom booking and appointment systems',
        'Advanced search and filtering capabilities',
        'Custom dashboards and admin panels',
        'Automated workflows and business processes'
      ],
      benefits: 'Custom functionality improves operational efficiency by 150%, reduces manual work by 70%, and provides competitive advantages through unique features that competitors can\'t easily replicate.',
      result: '+150% Operational Efficiency'
    },
    {
      _key: 'service3',
      icon: 'Smartphone',
      title: 'Mobile-First Responsive Design',
      description: 'Ensure your website looks and functions perfectly on all devices with mobile-first responsive design principles.',
      fullDescription: 'With over 60% of web traffic coming from mobile devices, mobile-first design isn\'t optional—it\'s essential. We build websites that provide exceptional user experiences across all screen sizes, from smartphones to desktop computers.',
      features: [
        'Mobile-first design approach',
        'Touch-friendly navigation and interactions',
        'Optimized images and media for all devices',
        'Fast loading on mobile networks',
        'Cross-browser compatibility testing',
        'Progressive web app (PWA) capabilities',
        'Adaptive layouts for different screen sizes',
        'Mobile-specific features and functionality'
      ],
      benefits: 'Mobile-first design increases mobile conversions by 160%, improves search rankings through mobile-friendliness, and ensures you don\'t lose potential customers due to poor mobile experience.',
      result: '+160% Mobile Conversions'
    },
    {
      _key: 'service4',
      icon: 'Zap',
      title: 'Performance Optimization',
      description: 'Build lightning-fast websites with advanced performance optimization techniques that improve user experience and search rankings.',
      fullDescription: 'Website speed directly impacts user experience, conversion rates, and search engine rankings. We implement advanced performance optimization techniques to ensure your custom website loads quickly and performs exceptionally.',
      features: [
        'Code optimization and minification',
        'Image optimization and compression',
        'Content delivery network (CDN) integration',
        'Caching strategies and implementation',
        'Database query optimization',
        'Lazy loading for images and content',
        'Core Web Vitals optimization',
        'Performance monitoring and reporting'
      ],
      benefits: 'Performance optimization reduces bounce rates by 45%, improves conversion rates by 35%, and enhances search engine rankings through better Core Web Vitals scores.',
      result: '+35% Conversion Rate'
    },
    {
      _key: 'service5',
      icon: 'Shield',
      title: 'Security & Maintenance',
      description: 'Implement robust security measures and ongoing maintenance to keep your custom website secure, updated, and performing optimally.',
      fullDescription: 'Security isn\'t an afterthought—it\'s built into every aspect of our custom development process. We implement comprehensive security measures and provide ongoing maintenance to protect your website and business.',
      features: [
        'SSL certificate installation and configuration',
        'Regular security updates and patches',
        'Malware scanning and protection',
        'Backup and disaster recovery systems',
        'User authentication and access controls',
        'Data encryption and protection',
        'Security monitoring and alerts',
        'Compliance with privacy regulations'
      ],
      benefits: 'Comprehensive security reduces security incidents by 95%, prevents costly downtime, and builds customer trust through reliable, secure website operations.',
      result: '+95% Security Protection'
    },
    {
      _key: 'service6',
      icon: 'Search',
      title: 'SEO-Optimized Development',
      description: 'Build your website with SEO best practices from the ground up to ensure maximum search engine visibility and organic traffic.',
      fullDescription: 'SEO isn\'t something you add later—it needs to be built into the foundation of your website. We develop custom websites with SEO optimization at every level, from code structure to content organization.',
      features: [
        'Clean, semantic HTML structure',
        'Optimized URL structure and navigation',
        'Meta tags and schema markup implementation',
        'Site speed and Core Web Vitals optimization',
        'XML sitemap generation and submission',
        'Internal linking structure optimization',
        'Image alt tags and accessibility features',
        'Analytics and tracking implementation'
      ],
      benefits: 'SEO-optimized development increases organic traffic by 180%, improves search rankings by 65%, and provides a strong foundation for ongoing SEO success.',
      result: '+180% Organic Traffic'
    }
  ],

  // Strategic Implementation Section (with schema-compatible icons and colors)
  strategicImplementation: {
    title: 'Comprehensive Custom Website Development Process',
    description: 'Successful custom website development requires strategic planning, expert design, and meticulous development. Our integrated approach ensures your website not only looks amazing but also performs exceptionally and drives business results.',
    secondaryDescription: 'From initial consultation to post-launch optimization, we guide you through every step of the custom development process with transparency, expertise, and dedication to your success.',
    features: [
      'Strategic planning and requirements analysis',
      'Custom design and user experience optimization',
      'Expert development and quality assurance'
    ],
    sections: [
      {
        _key: 'section1',
        icon: 'Target',
        title: 'Strategy & Planning',
        description: 'Comprehensive analysis of your business goals, target audience, and technical requirements to create the perfect development roadmap.',
        color: 'from-blue-50 to-blue-100'
      },
      {
        _key: 'section2',
        icon: 'Palette',
        title: 'Design & Development',
        description: 'Custom design creation and expert development using the latest technologies and best practices for optimal performance.',
        color: 'from-blue-100 to-blue-200'
      },
      {
        _key: 'section3',
        icon: 'Rocket',
        title: 'Launch & Optimization',
        description: 'Thorough testing, successful launch, and ongoing optimization to ensure your website continues to perform and improve.',
        color: 'from-blue-200 to-blue-300'
      }
    ]
  },

  // Market Intelligence Section (with schema-compatible background)
  marketIntelligence: {
    title: 'Advanced Custom Website Development Intelligence',
    description: 'Our custom development strategies are powered by industry insights, cutting-edge technologies, and proven methodologies that deliver exceptional results for businesses across all industries.',
    secondaryDescription: 'Through years of experience and hundreds of successful projects, we\'ve developed the expertise and processes needed to create custom websites that not only meet but exceed expectations.',
    stats: [
      {
        _key: 'stat1',
        value: '500+',
        label: 'Custom Websites Built'
      },
      {
        _key: 'stat2',
        value: '250%',
        label: 'Average ROI Increase'
      },
      {
        _key: 'stat3',
        value: '99.9%',
        label: 'Uptime Guarantee'
      },
      {
        _key: 'stat4',
        value: '24/7',
        label: 'Support & Monitoring'
      }
    ],
    ctaButton: {
      text: 'Start Your Custom Website Project'
    },
    backgroundGradient: 'from-blue-50 to-blue-100'
  },

  // Process (with schema-compatible icons)
  process: {
    title: 'Our Proven Custom Website Development Process',
    description: 'VESA Solutions follows a systematic approach that has delivered exceptional custom websites for hundreds of businesses, ensuring quality, performance, and results at every step.',
    steps: [
      {
        _key: 'step1',
        step: 1,
        title: 'Discovery & Strategy',
        description: 'Comprehensive analysis of your business, goals, and requirements to create the perfect development strategy.',
        icon: 'Search',
        details: [
          'Business goals and objectives analysis',
          'Target audience research and personas',
          'Competitive analysis and market research',
          'Technical requirements assessment',
          'Content strategy and information architecture'
        ]
      },
      {
        _key: 'step2',
        step: 2,
        title: 'Design & Prototyping',
        description: 'Create custom designs and interactive prototypes that bring your vision to life.',
        icon: 'Palette',
        details: [
          'Wireframing and user experience design',
          'Custom visual design and branding',
          'Interactive prototype development',
          'Design review and refinement',
          'Mobile and responsive design optimization'
        ]
      },
      {
        _key: 'step3',
        step: 3,
        title: 'Development & Testing',
        description: 'Expert development using cutting-edge technologies with comprehensive testing and quality assurance.',
        icon: 'Code',
        details: [
          'Custom code development and implementation',
          'Content management system integration',
          'Performance optimization and testing',
          'Cross-browser and device compatibility testing',
          'Security implementation and testing'
        ]
      },
      {
        _key: 'step4',
        step: 4,
        title: 'Launch & Optimization',
        description: 'Successful launch with ongoing monitoring, support, and optimization for continued success.',
        icon: 'Rocket',
        details: [
          'Pre-launch testing and final review',
          'Domain setup and hosting configuration',
          'Analytics and tracking implementation',
          'Search engine optimization setup',
          'Ongoing support and maintenance'
        ]
      }
    ]
  },

  // Case Study (with schema-compatible background)
  caseStudy: {
    title: 'Manufacturing Company Achieves 340% Lead Increase',
    description: 'Discover how our custom website development helped TechManufacturing transform their online presence and generate significantly more qualified leads through strategic design and functionality.',
    results: [
      {
        _key: 'result1',
        value: '340%',
        label: 'Lead Generation Increase'
      },
      {
        _key: 'result2',
        value: '250%',
        label: 'Conversion Rate Improvement'
      },
      {
        _key: 'result3',
        value: '85%',
        label: 'Faster Page Load Times'
      },
      {
        _key: 'result4',
        value: '180%',
        label: 'Mobile Traffic Growth'
      }
    ],
    ctaButton: {
      text: 'View Complete Case Study'
    },
    backgroundGradient: 'from-blue-600 to-blue-800'
  },

  // CTA
  cta: {
    title: 'Get Your Free Custom Website Development Consultation',
    description: 'Ready to create a custom website that perfectly represents your brand and drives business growth? Let\'s discuss your project and create a development strategy that delivers results.',
    benefits: [
      'Complete project analysis & strategic planning consultation',
      'Custom design mockups & development timeline',
      'Technology recommendations & cost breakdown',
      'SEO strategy & performance optimization plan'
    ],
    phoneNumber: '(*************',
    formSettings: {
      ctaText: 'Get My Free Custom Website Consultation',
      messagePlaceholder: 'Tell us about your custom website goals and requirements*'
    }
  },

  // All Testimonials (without image references)
  testimonials: [
    {
      _key: 'testimonial1',
      name: 'Jennifer Martinez',
      business: 'TechManufacturing Solutions',
      location: 'Phoenix, AZ',
      quote: 'The custom website VESA built for us completely transformed our online presence. We went from getting 2-3 leads per month to over 15 qualified leads monthly. The design is stunning and the functionality is exactly what we needed.',
      result: '+340% Lead Generation',
      rating: 5
    },
    {
      _key: 'testimonial2',
      name: 'Michael Chen',
      business: 'Coastal Real Estate Group',
      location: 'San Diego, CA',
      quote: 'Working with VESA was incredible. They took our vision and created something even better than we imagined. Our new custom website has helped us close 60% more deals and establish ourselves as the premium real estate firm in our market.',
      result: '+60% Deal Closure Rate',
      rating: 5
    },
    {
      _key: 'testimonial3',
      name: 'Sarah Thompson',
      business: 'Elite Fitness Studios',
      location: 'Denver, CO',
      quote: 'The custom booking system and member portal they built has revolutionized our business operations. We\'ve reduced administrative work by 70% and our member satisfaction scores have never been higher.',
      result: '+70% Operational Efficiency',
      rating: 5
    }
  ],

  // All FAQs
  faqs: [
    {
      _key: 'faq1',
      question: 'What makes custom website development different from using templates?',
      answer: 'Custom website development creates a unique website built specifically for your business, brand, and requirements. Unlike templates, custom websites offer unlimited design possibilities, specific functionality tailored to your needs, better performance, and complete control over every aspect of your site.'
    },
    {
      _key: 'faq2',
      question: 'How long does custom website development take?',
      answer: 'Custom website development typically takes 6-12 weeks depending on complexity and requirements. This includes discovery, design, development, testing, and launch phases. We provide detailed timelines during the planning phase and keep you updated throughout the process.'
    },
    {
      _key: 'faq3',
      question: 'Will my custom website be mobile-friendly and responsive?',
      answer: 'Absolutely! All our custom websites are built with mobile-first responsive design principles. Your website will look and function perfectly on all devices, from smartphones to desktop computers, ensuring optimal user experience across all platforms.'
    },
    {
      _key: 'faq4',
      question: 'Can you integrate my existing systems with the new website?',
      answer: 'Yes, we specialize in integrating custom websites with existing business systems including CRMs, inventory management, payment processors, email marketing platforms, and other third-party services. We ensure seamless data flow and functionality.'
    },
    {
      _key: 'faq5',
      question: 'Will I be able to update content on my custom website?',
      answer: 'Yes, we build custom websites with user-friendly content management systems that allow you to easily update text, images, and other content. We also provide training and documentation to ensure you\'re comfortable managing your website.'
    },
    {
      _key: 'faq6',
      question: 'What ongoing support do you provide after launch?',
      answer: 'We provide comprehensive ongoing support including security updates, performance monitoring, backup management, technical support, and content updates. Our support packages are designed to keep your website secure, fast, and up-to-date.'
    },
    {
      _key: 'faq7',
      question: 'How do you ensure my custom website will rank well in search engines?',
      answer: 'We build SEO optimization into every aspect of custom development including clean code structure, fast loading speeds, mobile optimization, proper meta tags, schema markup, and optimized content architecture. This provides a strong foundation for search engine success.'
    },
    {
      _key: 'faq8',
      question: 'What technologies do you use for custom website development?',
      answer: 'We use modern, proven technologies including React, Next.js, Node.js, WordPress (when appropriate), and other cutting-edge frameworks. Technology selection depends on your specific requirements, and we always choose the best tools for your project\'s success.'
    }
  ],

  // Footer CTA (with schema-compatible background)
  footerCta: {
    title: 'Ready to Build Your Perfect Custom Website?',
    description: 'Join hundreds of businesses that trust VESA Solutions to create custom websites that drive growth, engage customers, and deliver exceptional results.',
    primaryButton: {
      text: 'Schedule Free Consultation',
      icon: 'Calendar'
    },
    secondaryButton: {
      text: 'Call (*************',
      icon: 'Phone',
      phoneNumber: '(*************'
    },
    trustSignals: {
      title: 'Trusted by Businesses & Certified Partners',
      ratings: [
        {
          _key: 'rating1',
          rating: '4.9/5 Rating',
          source: 'Google Reviews',
          description: 'Google Reviews'
        },
        {
          _key: 'rating2',
          rating: '4.8/5 Rating',
          source: 'Clutch Reviews',
          description: 'Clutch Reviews'
        },
        {
          _key: 'rating3',
          rating: '500+ Websites',
          source: 'Successfully Built',
          description: 'Successfully Built'
        }
      ]
    },
    backgroundGradient: 'from-blue-700 to-blue-900'
  },

  // SEO (without image reference)
  seo: {
    metaTitle: 'Custom Website Development Services | Professional Web Design | VESA Solutions',
    metaDescription: 'Build your perfect custom website with professional development services. Unique design, custom functionality, mobile-responsive, and SEO-optimized. Free consultation included.',
    keywords: [
      'custom website development',
      'custom web design',
      'professional website development',
      'custom website design',
      'web development services'
    ]
  }
}

async function migrateCustomWebsiteData() {
  try {
    console.log('🚀 Starting Custom Website Development migration...')
    console.log('📊 Project ID: zleti5e4')
    console.log('🗃️  Dataset: production')
    console.log('')
    
    // Check if document already exists
    const existing = await client.fetch(`*[_type == "subService" && slug.current == "custom-website-development"][0]`)
    
    if (existing) {
      console.log('📄 Custom Website Development document already exists:', existing._id)
      console.log('🔄 Updating existing document...')
      
      const result = await client
        .patch(existing._id)
        .set(customWebsiteData)
        .commit()
      
      console.log('✅ Custom Website Development page updated successfully!')
      console.log(`📄 Document ID: ${result._id}`)
      console.log(`🔗 Edit in Studio: https://vesasanity.sanity.studio/structure/subService;${result._id}`)
      return result
    } else {
      const result = await client.create(customWebsiteData)
      console.log('✅ Custom Website Development page created successfully!')
      console.log(`📄 Document ID: ${result._id}`)
      console.log(`🔗 Edit in Studio: https://vesasanity.sanity.studio/structure/subService;${result._id}`)
      return result
    }
    
  } catch (error) {
    console.error('')
    console.error('❌ Migration failed:')
    console.error(error)
    
    if (error.message && error.message.includes('token')) {
      console.log('')
      console.log('💡 Make sure your token is correctly set in this file')
      console.log('   Get your token from: https://sanity.io/manage/personal/tokens')
    }
    
    if (error.message && error.message.includes('Insufficient permissions')) {
      console.log('')
      console.log('💡 Make sure your token has "Editor" or "Administrator" permissions')
    }
    
    process.exit(1)
  }
}

// Export the function instead of auto-running
module.exports = { migrateCustomWebsiteData }

// Run the migration
migrateCustomWebsiteData()
  .then(() => {
    console.log('')
    console.log('🎉 Your Custom Website Development page is now managed by Sanity!')
    console.log('📝 You can now edit all content in Sanity Studio')
    console.log('🔄 Remember to update your Next.js page to fetch from Sanity')
    console.log('')
    console.log('🎯 The page structure is complete and functional!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Migration failed:', error)
    process.exit(1)
  })
