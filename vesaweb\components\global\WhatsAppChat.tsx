import React, { useState, useEffect } from 'react';
import { MessageCircle } from 'lucide-react';

const WhatsAppChat: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  const handleWhatsAppClick = () => {
    const phoneNumber = '14166283793';
    const message = encodeURIComponent('Hi! I\'m interested in your digital marketing services. Can you help me?');
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Simple WhatsApp Button */}
      <button
        onClick={handleWhatsAppClick}
        className={`bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-2xl transition-all duration-500 transform hover:scale-110 group relative ${
          isVisible
            ? 'opacity-100 translate-y-0 scale-100'
            : 'opacity-0 translate-y-4 scale-75 pointer-events-none'
        }`}
        aria-label="Chat on WhatsApp"
        title="Chat with us on WhatsApp"
      >
        <MessageCircle size={24} className="group-hover:scale-110 transition-transform" />

        {/* Subtle pulse animation - only when visible */}
        <div className={`absolute inset-0 rounded-full bg-green-400 animate-ping transition-opacity duration-300 ${
          isVisible ? 'opacity-30' : 'opacity-0'
        }`}></div>
      </button>
    </div>
  );
};

export default WhatsAppChat;
