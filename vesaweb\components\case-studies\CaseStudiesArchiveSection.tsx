import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { CaseStudy, urlFor } from '@/lib/sanity-case-studies';
import { ArrowRight, Calendar, Building, Star, Filter } from 'lucide-react';

interface CaseStudiesArchiveSectionProps {
  caseStudies: CaseStudy[];
}

const CaseStudiesArchiveSection: React.FC<CaseStudiesArchiveSectionProps> = ({ caseStudies }) => {
  const [selectedIndustry, setSelectedIndustry] = useState<string>('all');
  const [selectedService, setSelectedService] = useState<string>('all');

  // Get unique industries and services for filters
  const industries = ['all', ...Array.from(new Set(caseStudies.map(cs => cs.client.industry)))];
  const services = ['all', ...Array.from(new Set(caseStudies.map(cs => cs.project.serviceType)))];

  // Filter case studies based on selected filters
  const filteredCaseStudies = caseStudies.filter(caseStudy => {
    const industryMatch = selectedIndustry === 'all' || caseStudy.client.industry === selectedIndustry;
    const serviceMatch = selectedService === 'all' || caseStudy.project.serviceType === selectedService;
    return industryMatch && serviceMatch;
  });

  // Format industry name for display
  const formatIndustryName = (industry: string) => {
    if (industry === 'all') return 'All Industries';
    return industry.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  // Format service name for display
  const formatServiceName = (service: string) => {
    if (service === 'all') return 'All Services';
    return service.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  // Get primary metric for display
  const getPrimaryMetric = (caseStudy: CaseStudy) => {
    if (caseStudy.results.metrics && caseStudy.results.metrics.length > 0) {
      const metric = caseStudy.results.metrics[0];
      return metric.improvement || metric.afterValue;
    }
    return null;
  };

  return (
    <>
      {/* Hero Section */}
      <section className="py-24 bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <div className="bg-green-600 text-white text-sm font-bold px-4 py-2 rounded-full inline-block mb-6">
            Real Results, Real Success Stories
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold text-white leading-tight mb-8">
            Client Success Stories
          </h1>
          
          <p className="text-xl text-blue-100 mb-12 leading-relaxed max-w-4xl mx-auto">
            Explore our portfolio of successful digital marketing campaigns and transformations. Each case study represents a real business that achieved remarkable growth through our proven strategies.
          </p>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-300 mb-2">{caseStudies.length}+</div>
              <div className="text-blue-100">Success Stories</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-300 mb-2">300%</div>
              <div className="text-blue-100">Average ROI</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-300 mb-2">15+</div>
              <div className="text-blue-100">Industries</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-300 mb-2">98%</div>
              <div className="text-blue-100">Client Satisfaction</div>
            </div>
          </div>
        </div>
      </section>

      {/* Filters and Archive */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6">
          {/* Filters */}
          <div className="bg-white rounded-2xl p-8 shadow-lg mb-12">
            <div className="flex items-center mb-6">
              <Filter className="text-blue-600 mr-3" size={24} />
              <h2 className="text-2xl font-bold text-gray-800">Filter Case Studies</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Industry Filter */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Filter by Industry
                </label>
                <select
                  value={selectedIndustry}
                  onChange={(e) => setSelectedIndustry(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {industries.map(industry => (
                    <option key={industry} value={industry}>
                      {formatIndustryName(industry)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Service Filter */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Filter by Service
                </label>
                <select
                  value={selectedService}
                  onChange={(e) => setSelectedService(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {services.map(service => (
                    <option key={service} value={service}>
                      {formatServiceName(service)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Results Count */}
            <div className="mt-6 text-center">
              <p className="text-gray-600">
                Showing <span className="font-semibold text-blue-600">{filteredCaseStudies.length}</span> of {caseStudies.length} case studies
              </p>
            </div>
          </div>

          {/* Case Studies Grid */}
          {filteredCaseStudies.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredCaseStudies.map((caseStudy) => (
                <Link 
                  key={caseStudy._id}
                  href={`/case-studies/${caseStudy.slug.current}`}
                  className="group bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
                >
                  {/* Featured Image */}
                  <div className="relative">
                    {caseStudy.featuredImage ? (
                      <Image
                        src={urlFor(caseStudy.featuredImage).width(400).height(250).url()}
                        alt={caseStudy.title}
                        width={400}
                        height={250}
                        className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-64 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
                        <Building className="text-blue-600" size={48} />
                      </div>
                    )}
                    
                    {/* Featured Badge */}
                    {caseStudy.featured && (
                      <div className="absolute top-4 left-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                        Featured
                      </div>
                    )}
                    
                    {/* Primary Metric */}
                    {getPrimaryMetric(caseStudy) && (
                      <div className="absolute top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-full font-bold text-sm">
                        {getPrimaryMetric(caseStudy)}
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <div className="p-8">
                    {/* Client Info */}
                    <div className="flex items-center mb-4">
                      {caseStudy.client.logo ? (
                        <Image
                          src={urlFor(caseStudy.client.logo).height(40).url()}
                          alt={caseStudy.client.name}
                          width={40}
                          height={32}
                          className="h-8 mr-3"
                        />
                      ) : (
                        <div className="w-8 h-8 bg-gray-200 rounded mr-3"></div>
                      )}
                      <div>
                        <div className="font-semibold text-gray-800">{caseStudy.client.name}</div>
                        <div className="text-sm text-gray-600">{formatIndustryName(caseStudy.client.industry)}</div>
                      </div>
                    </div>

                    {/* Title */}
                    <h3 className="text-xl font-bold text-gray-800 mb-4 group-hover:text-blue-600 transition-colors line-clamp-2">
                      {caseStudy.title}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-600 mb-6 leading-relaxed line-clamp-3">
                      {caseStudy.shortDescription}
                    </p>

                    {/* Meta Info */}
                    <div className="flex items-center justify-between mb-6">
                      <div className="bg-blue-100 px-3 py-1 rounded-full">
                        <span className="text-blue-600 font-semibold text-sm">
                          {formatServiceName(caseStudy.project.serviceType)}
                        </span>
                      </div>
                      
                      {caseStudy.project.duration && (
                        <div className="flex items-center text-gray-500 text-sm">
                          <Calendar size={16} className="mr-1" />
                          {caseStudy.project.duration}
                        </div>
                      )}
                    </div>

                    {/* Testimonial Rating */}
                    {caseStudy.testimonial?.rating && (
                      <div className="flex items-center mb-4">
                        <div className="flex mr-2">
                          {[...Array(caseStudy.testimonial.rating)].map((_, i) => (
                            <Star key={i} size={16} className="text-yellow-400 fill-current" />
                          ))}
                        </div>
                        <span className="text-sm text-gray-600">
                          {caseStudy.testimonial.author}
                        </span>
                      </div>
                    )}

                    {/* Read More */}
                    <div className="flex items-center justify-between">
                      <span className="text-blue-600 font-semibold group-hover:text-blue-700">
                        Read Full Case Study
                      </span>
                      <ArrowRight 
                        size={20} 
                        className="text-blue-600 group-hover:translate-x-1 transition-transform duration-300" 
                      />
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <Building className="text-gray-400 mx-auto mb-4" size={64} />
              <h3 className="text-2xl font-bold text-gray-600 mb-2">
                No case studies found
              </h3>
              <p className="text-gray-500 mb-6">
                Try adjusting your filters to see more results.
              </p>
              <button
                onClick={() => {
                  setSelectedIndustry('all');
                  setSelectedService('all');
                }}
                className="bg-blue-600 text-white font-semibold px-6 py-3 rounded-xl hover:bg-blue-700 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          )}
        </div>
      </section>
    </>
  );
};

export default CaseStudiesArchiveSection;
