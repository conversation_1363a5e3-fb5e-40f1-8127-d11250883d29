import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Header from '@/components/global/Header';
import Footer from '@/components/global/Footer';
import { ExternalLink, Home, Search, Code, TrendingUp, FileText, Users, Mail } from 'lucide-react';

const SitemapPage: React.FC = () => {
  const siteStructure = [
    {
      title: 'Main Pages',
      icon: Home,
      pages: [
        { name: 'Home', url: '/', description: 'Main homepage with overview of services' },
        { name: 'About Us', url: '/about', description: 'Learn about VESA Solutions and our team' },
        { name: 'Services', url: '/services', description: 'Complete overview of all our services' },
        { name: 'Contact', url: '/contact-us', description: 'Get in touch with our team' },
        { name: 'Free Estimate', url: '/free-estimate', description: 'Request a free consultation and quote' },
        { name: 'Case Studies', url: '/case-studies', description: 'Success stories and client results' },
      ]
    },
    {
      title: 'SEO Services',
      icon: Search,
      pages: [
        { name: 'SEO Search Engine Optimization', url: '/seo-search-engine-optimization', description: 'Main SEO services page' },
        { name: 'On-Page SEO Optimization', url: '/on-page-seo', description: 'Website optimization services' },
        { name: 'Off-Page SEO & Link Building', url: '/off-page-seo', description: 'External SEO and link building' },
        { name: 'Technical SEO Services', url: '/technical-seo', description: 'Technical website optimization' },
        { name: 'Local SEO Marketing', url: '/local-seo', description: 'Local business optimization' },
        { name: 'Content Writing', url: '/content-writing', description: 'SEO content creation services' },
        { name: 'SEO Analytics', url: '/seo-analytics', description: 'SEO reporting and analytics' },
      ]
    },
    {
      title: 'Web Development',
      icon: Code,
      pages: [
        { name: 'Web Development', url: '/web-development', description: 'Main web development services' },
        { name: 'Custom Website Development', url: '/custom-website-development', description: 'Custom web solutions' },
        { name: 'E-commerce Development', url: '/ecommerce-development', description: 'Online store development' },
        { name: 'Mobile App Development', url: '/mobile-app-development', description: 'Mobile application services' },
        { name: 'Website Speed Optimization', url: '/website-speed-optimization', description: 'Performance optimization' },
        { name: 'Web Application Development', url: '/web-application-development', description: 'Custom web applications' },
        { name: 'Website Maintenance & Support', url: '/website-maintenance-support', description: 'Ongoing website support' },
      ]
    },
    {
      title: 'Digital Marketing',
      icon: TrendingUp,
      pages: [
        { name: 'Digital Marketing', url: '/digital-marketing', description: 'Comprehensive digital marketing services' },
        { name: 'PPC Advertising', url: '/ppc', description: 'Pay-per-click advertising campaigns' },
        { name: 'Email Marketing', url: '/email-marketing', description: 'Email campaign management' },
        { name: 'Social Media Marketing', url: '/social-media', description: 'Social media management and advertising' },
        { name: 'Branding Services', url: '/branding-services', description: 'Brand development and design' },
        { name: 'Conversion Optimization', url: '/conversion-optimization', description: 'Website conversion improvement' },
        { name: 'Reputation Management', url: '/reputation-management', description: 'Online reputation services' },
      ]
    },
    {
      title: 'Resources & Information',
      icon: FileText,
      pages: [
        { name: 'Blog', url: '/blog', description: 'Latest articles and insights' },
        { name: 'Privacy Policy', url: '/privacy-policy', description: 'How we handle your data' },
        { name: 'Terms of Service', url: '/terms-of-service', description: 'Terms and conditions' },
        { name: 'Sitemap', url: '/sitemap', description: 'Complete site navigation' },
      ]
    }
  ];

  return (
    <>
      <Head>
        <title>Sitemap | VESA Solutions</title>
        <meta name="description" content="Complete sitemap of VESA Solutions website. Find all pages including SEO services, web development, digital marketing, and resources." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://vesasolutions.com/sitemap" />
      </Head>
      
      <Header />
      
      {/* Breadcrumbs */}
      <div className="bg-gray-50 py-4">
        <div className="max-w-7xl mx-auto px-6">
          <nav className="text-sm" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              <li>
                <Link href="/" className="text-gray-500 hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-gray-900 font-medium" aria-current="page">
                Sitemap
              </li>
            </ol>
          </nav>
        </div>
      </div>
      
      <main className="min-h-screen bg-white">
        {/* Header Section */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-800">
          <div className="max-w-4xl mx-auto px-6 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Website Sitemap
            </h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Navigate through all pages and sections of our website. Find exactly what you&apos;re looking for.
            </p>
          </div>
        </section>

        {/* Sitemap Content */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-6">
            <div className="grid gap-12">
              {siteStructure.map((section, index) => {
                const IconComponent = section.icon;
                return (
                  <div key={index} className="bg-white rounded-2xl border border-gray-200 overflow-hidden shadow-lg">
                    {/* Section Header */}
                    <div className="bg-gradient-to-r from-gray-50 to-blue-50 px-8 py-6 border-b border-gray-200">
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                          <IconComponent className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
                          <p className="text-gray-600 mt-1">{section.pages.length} pages available</p>
                        </div>
                      </div>
                    </div>

                    {/* Pages List */}
                    <div className="p-8">
                      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {section.pages.map((page, pageIndex) => (
                          <Link
                            key={pageIndex}
                            href={page.url}
                            className="group block p-6 bg-gray-50 hover:bg-blue-50 rounded-xl border border-gray-200 hover:border-blue-300 transition-all duration-200"
                          >
                            <div className="flex items-start justify-between mb-3">
                              <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 leading-tight">
                                {page.name}
                              </h3>
                              <ExternalLink className="h-4 w-4 text-gray-400 group-hover:text-blue-600 transition-colors duration-200 flex-shrink-0 ml-2" />
                            </div>
                            <p className="text-gray-600 text-sm leading-relaxed mb-3">
                              {page.description}
                            </p>
                            <div className="text-xs text-blue-600 font-medium">
                              {page.url}
                            </div>
                          </Link>
                        ))}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Additional Information */}
            <div className="mt-16 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 border border-blue-200">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Need Help Finding Something?</h3>
                <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                  Can&apos;t find what you&apos;re looking for? Our team is here to help you navigate our services and find the perfect solution for your business.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link
                    href="/contact"
                    className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
                  >
                    <Mail className="h-5 w-5 mr-2" />
                    Contact Us
                  </Link>
                  <Link
                    href="/free-estimate"
                    className="inline-flex items-center justify-center px-8 py-4 border-2 border-blue-600 text-blue-600 font-semibold rounded-xl hover:bg-blue-600 hover:text-white transition-all duration-300"
                  >
                    <Users className="h-5 w-5 mr-2" />
                    Get Free Consultation
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </>
  );
};

export default SitemapPage;
