import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';

const WebDevFAQSection: React.FC = () => {
  const [openFaq, setOpenFaq] = useState<number | null>(null);

  const faqs = [
    {
      question: 'How long does it take to develop a custom website?',
      answer: 'Most custom websites take 8-14 weeks to complete, depending on complexity and features. Simple business websites may take 6-8 weeks, while complex e-commerce or web applications can take 12-20 weeks. We provide a detailed timeline during the planning phase and keep you updated throughout the process.'
    },
    {
      question: 'What is the difference between custom development and using templates?',
      answer: 'Custom development means building your website from scratch to match your exact requirements, brand, and business goals. Templates are pre-made designs that limit customization. Custom development offers better performance, unique design, scalability, and SEO optimization, while templates are faster but less flexible.'
    },
    {
      question: 'Do you provide ongoing maintenance and support after launch?',
      answer: 'Yes, we offer comprehensive maintenance packages that include security updates, performance monitoring, content updates, backup services, and technical support. We believe in long-term partnerships and ensure your website continues to perform optimally after launch.'
    },
    {
      question: 'Will my website be mobile-responsive and SEO-friendly?',
      answer: 'Absolutely. Every website we develop is mobile-first, meaning it\'s designed to work perfectly on all devices. We also implement SEO best practices including optimized code structure, fast loading times, proper meta tags, and search engine-friendly URLs to help your site rank well in Google.'
    },
    {
      question: 'What technologies do you use for web development?',
      answer: 'We use modern, proven technologies including React, Next.js, Node.js, WordPress, and various databases like MongoDB and PostgreSQL. We choose the best technology stack based on your specific needs, ensuring optimal performance, security, and scalability.'
    },
    {
      question: 'Can you integrate third-party services and APIs?',
      answer: 'Yes, we specialize in integrating various third-party services including payment gateways, CRM systems, email marketing platforms, social media APIs, analytics tools, and custom business applications. We ensure seamless integration that enhances your website\'s functionality.'
    },
    {
      question: 'How much does custom web development cost?',
      answer: 'Web development costs vary based on complexity, features, and timeline. Simple business websites start around $5,000, while complex e-commerce or web applications can range from $15,000-$50,000+. We provide detailed quotes after understanding your specific requirements during our free consultation.'
    },
    {
      question: 'Do you provide training on how to manage my website?',
      answer: 'Yes, we provide comprehensive training on your content management system, including how to update content, add new pages, manage products (for e-commerce), and basic maintenance tasks. We also provide documentation and ongoing support to ensure you\'re comfortable managing your website.'
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-4xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-8">
            Frequently Asked Questions About Web Development
          </h2>
          <p className="text-xl text-gray-600">
            Get answers to common questions about web development services, processes, and what to expect when working with VESA Solutions.
          </p>
        </div>

        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <div key={index} className="bg-white rounded-2xl overflow-hidden shadow-lg">
              <button 
                onClick={() => setOpenFaq(openFaq === index ? null : index)}
                className="w-full p-8 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
              >
                <h3 className="text-xl font-semibold text-gray-800 pr-8">
                  {faq.question}
                </h3>
                <ChevronDown
                  size={24}
                  className={`text-blue-600 transform transition-transform ${openFaq === index ? 'rotate-180' : ''}`}
                />
              </button>
              {openFaq === index && (
                <div className="px-8 pb-8">
                  <div className="border-t border-gray-200 pt-6">
                    <p className="text-gray-600 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Contact CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-12 text-white">
            <h3 className="text-3xl md:text-4xl font-bold mb-6">
              Still Have Questions?
            </h3>
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Our web development experts are here to help. Get personalized answers to your questions and a custom quote for your project.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
                Schedule Free Consultation
              </button>
              <button className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-all duration-300">
                Call (555) 123-4567
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WebDevFAQSection;
