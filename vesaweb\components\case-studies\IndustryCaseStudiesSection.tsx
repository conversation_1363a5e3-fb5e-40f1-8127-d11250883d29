import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ShoppingBag, Heart, Building, Car, Home, Users, ArrowRight } from 'lucide-react';

const IndustryCaseStudiesSection: React.FC = () => {
  const [activeIndustry, setActiveIndustry] = useState('ecommerce');

  const industries = [
    {
      id: 'ecommerce',
      name: 'E-commerce & Retail',
      icon: ShoppingBag,
      color: 'blue',
      caseStudies: [
        {
          title: 'Fashion Retailer Revenue Growth',
          client: 'StyleHub Fashion',
          result: '450% Revenue Increase',
          image: 'https://images.unsplash.com/photo-**********-0cfab40df3f6?w=400&h=300&fit=crop',
          slug: 'fashion-retailer-revenue-growth'
        },
        {
          title: 'Electronics Store SEO Success',
          client: 'TechMart Electronics',
          result: '320% Organic Traffic',
          image: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=400&h=300&fit=crop',
          slug: 'electronics-store-seo-success'
        }
      ]
    },
    {
      id: 'healthcare',
      name: 'Healthcare & Medical',
      icon: Heart,
      color: 'green',
      caseStudies: [
        {
          title: 'Medical Practice Patient Growth',
          client: 'Wellness Medical Center',
          result: '280% New Patients',
          image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop',
          slug: 'medical-practice-patient-growth'
        },
        {
          title: 'Dental Clinic Local Dominance',
          client: 'Bright Smile Dentistry',
          result: '350% Local Visibility',
          image: 'https://images.unsplash.com/photo-1606811841689-23dfddce3e95?w=400&h=300&fit=crop',
          slug: 'dental-clinic-local-dominance'
        }
      ]
    },
    {
      id: 'professional',
      name: 'Professional Services',
      icon: Building,
      color: 'purple',
      caseStudies: [
        {
          title: 'Law Firm Lead Generation',
          client: 'Johnson & Associates',
          result: '400% Qualified Leads',
          image: 'https://images.unsplash.com/photo-*************-d10d557cf95f?w=400&h=300&fit=crop',
          slug: 'law-firm-lead-generation'
        },
        {
          title: 'Accounting Firm Digital Growth',
          client: 'Premier Accounting',
          result: '250% Client Acquisition',
          image: 'https://images.unsplash.com/photo-**********-6726b3ff858f?w=400&h=300&fit=crop',
          slug: 'accounting-firm-digital-growth'
        }
      ]
    },
    {
      id: 'technology',
      name: 'Technology',
      icon: Users,
      color: 'indigo',
      caseStudies: [
        {
          title: 'SaaS Startup Success Story',
          client: 'TechFlow Solutions',
          result: '340% Lead Generation',
          image: 'https://images.unsplash.com/photo-**********-bebda4e38f71?w=400&h=300&fit=crop',
          slug: 'saas-startup-lead-generation'
        },
        {
          title: 'Software Company Expansion',
          client: 'CodeCraft Systems',
          result: '290% Market Reach',
          image: 'https://images.unsplash.com/photo-*************-47ba0277781c?w=400&h=300&fit=crop',
          slug: 'software-company-expansion'
        }
      ]
    },
    {
      id: 'realestate',
      name: 'Real Estate',
      icon: Home,
      color: 'orange',
      caseStudies: [
        {
          title: 'Real Estate Agency Growth',
          client: 'Prime Properties',
          result: '380% Property Inquiries',
          image: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=400&h=300&fit=crop',
          slug: 'real-estate-agency-growth'
        },
        {
          title: 'Property Developer Success',
          client: 'Urban Development Co.',
          result: '220% Sales Leads',
          image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop',
          slug: 'property-developer-success'
        }
      ]
    },
    {
      id: 'food',
      name: 'Food & Beverage',
      icon: Car,
      color: 'red',
      caseStudies: [
        {
          title: 'Restaurant Chain Online Orders',
          client: 'Bella Vista Restaurants',
          result: '250% Online Orders',
          image: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop',
          slug: 'restaurant-chain-online-orders'
        },
        {
          title: 'Food Delivery Service Growth',
          client: 'Fresh Meals Express',
          result: '310% Customer Base',
          image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
          slug: 'food-delivery-service-growth'
        }
      ]
    }
  ];

  const activeIndustryData = industries.find(industry => industry.id === activeIndustry);

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-600 text-white',
      green: 'bg-green-600 text-white',
      purple: 'bg-purple-600 text-white',
      indigo: 'bg-indigo-600 text-white',
      orange: 'bg-orange-600 text-white',
      red: 'bg-red-600 text-white'
    };
    return colorMap[color as keyof typeof colorMap] || 'bg-blue-600 text-white';
  };

  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Success Stories by Industry
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Every industry has unique challenges and opportunities. Explore how we&apos;ve helped businesses in your sector achieve remarkable growth and success.
          </p>
        </div>

        {/* Industry Tabs */}
        <div className="flex flex-wrap justify-center gap-4 mb-16">
          {industries.map((industry) => {
            const IconComponent = industry.icon;
            const isActive = activeIndustry === industry.id;
            return (
              <button
                key={industry.id}
                onClick={() => setActiveIndustry(industry.id)}
                className={`flex items-center px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                  isActive 
                    ? getColorClasses(industry.color)
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <IconComponent size={20} className="mr-2" />
                {industry.name}
              </button>
            );
          })}
        </div>

        {/* Active Industry Case Studies */}
        {activeIndustryData && (
          <div className="space-y-12">
            <div className="text-center">
              <h3 className="text-3xl font-bold text-gray-800 mb-4">
                {activeIndustryData.name} Success Stories
              </h3>
              <p className="text-lg text-gray-600">
                Discover how we&apos;ve helped {activeIndustryData.name.toLowerCase()} businesses achieve exceptional results
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {activeIndustryData.caseStudies.map((caseStudy, index) => (
                <Link 
                  key={index}
                  href={`/case-studies/${caseStudy.slug}`}
                  className="group bg-white border border-gray-200 rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
                >
                  <div className="relative">
                    <Image
                      src={caseStudy.image}
                      alt={caseStudy.title}
                      width={400}
                      height={256}
                      className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className={`absolute top-4 right-4 px-4 py-2 rounded-full text-white font-bold text-sm ${getColorClasses(activeIndustryData.color)}`}>
                      {caseStudy.result}
                    </div>
                  </div>
                  
                  <div className="p-8">
                    <h4 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-blue-600 transition-colors">
                      {caseStudy.title}
                    </h4>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-semibold text-gray-800">{caseStudy.client}</div>
                        <div className="text-sm text-gray-600">{activeIndustryData.name}</div>
                      </div>
                      
                      <ArrowRight 
                        size={24} 
                        className="text-blue-600 group-hover:translate-x-1 transition-transform duration-300" 
                      />
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* Industry Stats */}
        <div className="mt-20 bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-12 text-white">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold mb-4">
              Cross-Industry Impact
            </h3>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Our proven strategies deliver consistent results across all industries and business sizes.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl md:text-5xl font-bold text-blue-200 mb-2">15+</div>
              <div className="text-blue-100">Industries Served</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-blue-200 mb-2">500+</div>
              <div className="text-blue-100">Successful Projects</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-blue-200 mb-2">300%</div>
              <div className="text-blue-100">Average Growth</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-blue-200 mb-2">98%</div>
              <div className="text-blue-100">Client Retention</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default IndustryCaseStudiesSection;
