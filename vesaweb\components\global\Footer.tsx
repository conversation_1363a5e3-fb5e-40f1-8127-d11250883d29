'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Mail, Phone, MapPin, ArrowRight, Facebook, Instagram, Linkedin, Twitter, MessageCircle, Globe } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden min-h-screen flex flex-col justify-center">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/20 to-transparent"></div>
        <div className="absolute top-20 right-0 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-6 py-16">
        {/* Top Section - CTA */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Ready to Grow Your Business?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Let&apos;s create something amazing together. Get your free consultation today.
          </p>
          <Link href="/free-estimate">
            <button className="group inline-flex items-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-8 py-4 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-xl shadow-blue-500/25">
              Get Free Proposal
              <ArrowRight size={20} className="ml-2 transition-transform duration-300 group-hover:translate-x-1" />
            </button>
          </Link>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center mb-6">
              <Link href="/">
                <Image 
                  src="/VesaLogo.svg" 
                  alt="VESA Solutions Logo" 
                  width={120} 
                  height={46}
                  className="w-30 h-auto transition-transform duration-300 hover:scale-105"
                />
              </Link>
            </div>
            <p className="text-gray-300 text-lg leading-relaxed mb-8">
              Full-service digital marketing agency helping businesses attract, impress, and convert more leads online since 2015.
            </p>
            
            {/* Contact Info */}
            <div className="space-y-4">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group"
              >
                <Mail size={18} className="mr-3 text-blue-400 group-hover:scale-110 transition-transform" />
                <span><EMAIL></span>
              </a>
              <a
                href="tel:+***********"
                className="flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group"
              >
                <Phone size={18} className="mr-3 text-blue-400 group-hover:scale-110 transition-transform" />
                <span>****** 628 3793</span>
              </a>
              <a
                href="tel:+355694046408"
                className="flex items-center text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group"
              >
                <Phone size={18} className="mr-3 text-blue-400 group-hover:scale-110 transition-transform" />
                <span>+355 69 404 6408</span>
              </a>
              <a 
                href="https://share.google/T9q3WjqOOmMHrBnJY"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-start text-gray-300 hover:text-blue-400 transition-colors cursor-pointer group"
              >
                <MapPin size={18} className="mr-3 text-blue-400 mt-0.5 flex-shrink-0 group-hover:scale-110 transition-transform" />
                <span>Bulevardi Dyrrah, Pallati 394, Kati 4-t<br />2001, Durrës, Albania</span>
              </a>
              <div className="flex items-center text-gray-300">
                <div className="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                <span className="text-green-400 font-medium">Open 24 Hours</span>
              </div>
            </div>
          </div>

          {/* Services Grid */}
          <div className="lg:col-span-3 grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* SEO Services */}
            <div>
              <Link href="/seo-search-engine-optimization" className="group">
                <h3 className="text-lg font-bold text-white hover:text-green-400 mb-6 relative transition-colors duration-300">
                  SEO Services
                  <span className="absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-green-400 to-green-600 group-hover:w-full transition-all duration-300"></span>
                </h3>
              </Link>
              <ul className="space-y-3">
                <li><Link href="/on-page-seo" className="text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  On-Page SEO
                </Link></li>
                <li><Link href="/off-page-seo" className="text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Off-Page SEO
                </Link></li>
                <li><Link href="/technical-seo" className="text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Technical SEO
                </Link></li>
                <li><Link href="/local-seo" className="text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Local SEO
                </Link></li>
                <li><Link href="/content-writing" className="text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Content Writing
                </Link></li>
                <li><Link href="/seo-analytics" className="text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-green-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  SEO Analytics
                </Link></li>
              </ul>
            </div>

            {/* Web Development */}
            <div>
              <Link href="/web-development" className="group">
                <h3 className="text-lg font-bold text-white hover:text-blue-400 mb-6 relative transition-colors duration-300">
                  Web Development
                  <span className="absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600 group-hover:w-full transition-all duration-300"></span>
                </h3>
              </Link>
              <ul className="space-y-3">
                <li><Link href="/custom-website-development" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Custom Websites
                </Link></li>
                <li><Link href="/ecommerce-development" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  E-commerce Dev
                </Link></li>
                <li><Link href="/mobile-app-development" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Mobile Apps
                </Link></li>
                <li><Link href="/website-speed-optimization" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 flex-shrink-0 transition-all duration-300 group-hover:w-2"></span>
                  <span className="whitespace-nowrap">Speed Optimization</span>
                </Link></li>
                <li><Link href="/web-application-development" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Web Applications
                </Link></li>
                <li><Link href="/website-maintenance-support" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Maintenance
                </Link></li>
              </ul>
            </div>

            {/* Digital Marketing */}
            <div>
              <Link href="/digital-marketing" className="group">
                <h3 className="text-lg font-bold text-white hover:text-purple-400 mb-6 relative transition-colors duration-300">
                  Digital Marketing
                  <span className="absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-purple-400 to-purple-600 group-hover:w-full transition-all duration-300"></span>
                </h3>
              </Link>
              <ul className="space-y-3">
                <li><Link href="/ppc" className="text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  PPC Advertising
                </Link></li>
                <li><Link href="/email-marketing" className="text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Email Marketing
                </Link></li>
                <li><Link href="/social-media" className="text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Social Media
                </Link></li>
                <li><Link href="/branding-services" className="text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Branding Services
                </Link></li>
                <li><Link href="/conversion-optimization" className="text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Conversion Optimization
                </Link></li>
                <li><Link href="/reputation-management" className="text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-purple-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Reputation Management
                </Link></li>
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="text-lg font-bold text-white mb-6 relative">
                Company
                <span className="absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600"></span>
              </h3>
              <ul className="space-y-3">
                <li><Link href="/services" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Services
                </Link></li>
                <li><Link href="/case-studies" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Case Studies
                </Link></li>
                <li><Link href="/about" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  About Us
                </Link></li>
                <li><Link href="/contact-us" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Contact Us
                </Link></li>
                <li><Link href="/blog" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 transition-all duration-300 group-hover:w-2"></span>
                  Blog
                </Link></li>
              </ul>
            </div>
          </div>
        </div>

        {/* Social Media & Bottom Section */}
        <div className="flex flex-col lg:flex-row justify-between items-center pt-8 border-t border-gray-700">
          <div className="mb-6 lg:mb-0">
            <h4 className="text-white font-semibold mb-4">Follow Our Journey</h4>
            <div className="flex space-x-4">
              <a
                href="https://m.facebook.com/VesaSolutions/"
                target="_blank"
                rel="noopener noreferrer"
                className="group w-12 h-12 bg-gray-800 hover:bg-blue-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110"
                aria-label="Follow us on Facebook"
              >
                <Facebook size={20} className="text-gray-400 group-hover:text-white transition-colors" />
              </a>
              <a
                href="https://www.instagram.com/vesasolutions/"
                target="_blank"
                rel="noopener noreferrer"
                className="group w-12 h-12 bg-gray-800 hover:bg-gradient-to-r hover:from-pink-500 hover:to-purple-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110"
                aria-label="Follow us on Instagram"
              >
                <Instagram size={20} className="text-gray-400 group-hover:text-white transition-colors" />
              </a>
              <a
                href="https://al.linkedin.com/company/vesasolutions"
                target="_blank"
                rel="noopener noreferrer"
                className="group w-12 h-12 bg-gray-800 hover:bg-blue-700 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110"
                aria-label="Follow us on LinkedIn"
              >
                <Linkedin size={20} className="text-gray-400 group-hover:text-white transition-colors" />
              </a>
              <a
                href="#"
                className="group w-12 h-12 bg-gray-800 hover:bg-blue-500 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110"
                aria-label="Follow us on Twitter"
              >
                <Twitter size={20} className="text-gray-400 group-hover:text-white transition-colors" />
              </a>
              <a
                href="https://wa.me/***********"
                target="_blank"
                rel="noopener noreferrer"
                className="group w-12 h-12 bg-gray-800 hover:bg-green-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110"
                aria-label="Chat with us on WhatsApp"
              >
                <MessageCircle size={20} className="text-gray-400 group-hover:text-white transition-colors" />
              </a>
              <a
                href="https://vesasolutions.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="group w-12 h-12 bg-gray-800 hover:bg-indigo-600 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110"
                aria-label="Visit our website"
              >
                <Globe size={20} className="text-gray-400 group-hover:text-white transition-colors" />
              </a>
            </div>
          </div>

          <div className="text-center lg:text-right">
            <div className="text-2xl font-bold text-white mb-2">Growing Businesses Since 2015</div>
            <div className="text-gray-400 text-sm">Trusted by 200+ companies worldwide</div>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-12 pt-8 border-t border-gray-700 text-center">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-gray-400 text-sm">
              © 2025 Vesa Solutions Marketing Agency. All rights reserved.
            </p>
            <div className="flex flex-wrap justify-center md:justify-end space-x-6 text-sm">
              <Link href="/privacy-policy" className="text-gray-400 hover:text-blue-400 transition-colors">Privacy Policy</Link>
              <Link href="/terms-of-service" className="text-gray-400 hover:text-blue-400 transition-colors">Terms of Service</Link>
              <Link href="/sitemap" className="text-gray-400 hover:text-blue-400 transition-colors">Sitemap</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;