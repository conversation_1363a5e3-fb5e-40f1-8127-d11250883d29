// components/blog/single/BlogPostHero.tsx - Hero section for individual blog posts
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, Clock, User, ArrowLeft } from 'lucide-react'
import { BlogPost } from '@/types/blog'
import { urlForImage } from '@/lib/sanity-blog'

interface BlogPostHeroProps {
  post: BlogPost
}

const BlogPostHero: React.FC<BlogPostHeroProps> = ({ post }) => {
  // const getCategoryColor = (color: string) => {
  //   const colors = {
  //     blue: 'bg-blue-100 text-blue-800',
  //     green: 'bg-green-100 text-green-800',
  //     purple: 'bg-purple-100 text-purple-800',
  //     orange: 'bg-orange-100 text-orange-800',
  //     red: 'bg-red-100 text-red-800',
  //     teal: 'bg-teal-100 text-teal-800'
  //   }
  //   return colors[color as keyof typeof colors] || colors.blue
  // }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 text-white py-12 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/20 to-transparent"></div>
        <div className="absolute top-20 right-0 w-72 h-72 bg-blue-500/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-20 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back to Blog */}
        <div className="mb-6">
          <Link
            href="/blog"
            className="inline-flex items-center text-blue-200 hover:text-white transition-colors duration-200"
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Blog
          </Link>
        </div>

        <div className="text-center max-w-3xl mx-auto">
          {/* Categories */}
          <div className="flex flex-wrap justify-center gap-2 mb-4">
            {post.categories.map((category) => (
              <span
                key={category._id}
                className="bg-white/20 backdrop-blur-sm border border-white/30 text-white px-3 py-1 rounded-full text-sm font-medium"
              >
                {category.title}
              </span>
            ))}
            {post.featured && (
              <span className="bg-blue-500/30 backdrop-blur-sm border border-blue-400/30 text-blue-100 px-3 py-1 rounded-full text-sm font-medium">
                Featured
              </span>
            )}
          </div>

          {/* Title */}
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 leading-tight">
            {post.title}
          </h1>

          {/* Excerpt */}
          <p className="text-lg md:text-xl text-blue-100 mb-6 leading-relaxed">
            {post.excerpt}
          </p>

          {/* Meta Information */}
          <div className="flex flex-wrap justify-center items-center gap-4 text-blue-200 text-sm">
            {/* Date */}
            <div className="flex items-center gap-1">
              <Calendar size={14} />
              <span>{formatDate(post.publishedAt)}</span>
            </div>

            {/* Reading Time */}
            {post.readingTime && (
              <div className="flex items-center gap-1">
                <Clock size={14} />
                <span>{post.readingTime} min read</span>
              </div>
            )}

            {/* Author */}
            <div className="flex items-center gap-1">
              <User size={14} />
              <span>VESA Solutions</span>
            </div>
          </div>

          {/* Featured Image */}
          {post.featuredImage && (
            <div className="mt-8">
              <div className="relative h-64 md:h-80 rounded-lg overflow-hidden shadow-lg mx-auto max-w-2xl">
                <Image
                  src={urlForImage(post.featuredImage).width(800).height(400).url()}
                  alt={post.title}
                  fill
                  className="object-cover"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent"></div>
              </div>
            </div>
          )}

        </div>
      </div>

      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 w-full -mb-px">
        <svg
          viewBox="0 0 1440 120"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-full h-auto block"
          preserveAspectRatio="none"
        >
          <path
            d="M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0Z"
            fill="white"
          />
        </svg>
      </div>
    </section>
  )
}

export default BlogPostHero
