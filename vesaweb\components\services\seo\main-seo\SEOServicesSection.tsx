import React from 'react';
import Link from 'next/link';
import { Search, Globe, Zap, Target, PenTool, ClipboardCheck, CheckCircle } from 'lucide-react';

const SEOServicesSection: React.FC = () => {
  const seoServices = [
    {
      icon: Search,
      title: 'On-Page SEO Optimization',
      slug: 'on-page-seo',
      description: 'Comprehensive optimization of your website content, meta tags, headers, and internal structure for maximum search engine visibility and user experience.',
      features: ['Keyword Research & Strategy', 'Title Tags & Meta Descriptions', 'Header Tag Optimization', 'Content Structure & Quality', 'Internal Link Building', 'Image Alt Text Optimization'],
      benefits: 'Improved search rankings, better user engagement, higher click-through rates from search results.',
      results: '+180% in Rankings'
    },
    {
      icon: Globe,
      title: 'Off-Page SEO & Link Building',
      slug: 'off-page-seo',
      description: 'Strategic link building campaigns and off-site optimization to build your domain authority and establish your website as a trusted resource in your industry.',
      features: ['High-Quality Backlink Building', 'Guest Posting Opportunities', 'Digital PR & Outreach', 'Local Citations & NAP Consistency', 'Social Signal Enhancement', 'Brand Mention Monitoring'],
      benefits: 'Increased domain authority, improved search engine trust, expanded online presence.',
      results: '+320% Domain Authority'
    },
    {
      icon: Zap,
      title: 'Technical SEO Services',
      slug: 'technical-seo',
      description: 'Advanced technical optimization to ensure search engines can properly crawl, index, and understand your website architecture and content.',
      features: ['Site Speed Optimization', 'Mobile Responsiveness', 'Schema Markup Implementation', 'XML Sitemap Creation', 'Robots.txt Optimization', 'Core Web Vitals Improvement'],
      benefits: 'Faster loading times, better mobile experience, improved search engine accessibility.',
      results: '+95% Site Performance'
    },
    {
      icon: Target,
      title: 'Local SEO Marketing',
      slug: 'local-seo',
      description: 'Dominate local search results and attract customers in your geographic area with comprehensive local SEO strategies tailored to your business.',
      features: ['Google My Business Optimization', 'Local Keyword Targeting', 'Review Management Strategy', 'Local Directory Submissions', 'Geographic Content Creation', 'Location-Based Landing Pages'],
      benefits: 'Increased local visibility, more foot traffic, higher local conversion rates.',
      results: '+250% Local Visibility'
    },
    {
      icon: PenTool,
      title: 'Content Writing',
      slug: 'content-writing',
      description: 'Professional SEO-optimized content creation that engages your audience and drives action across all channels while improving search rankings.',
      features: ['Blog Writing', 'Website Copy', 'Product Descriptions', 'Landing Page Content', 'Meta Descriptions', 'Technical Writing'],
      benefits: 'Better search rankings, higher engagement, improved conversion rates, and enhanced brand authority.',
      results: '+200% Content Traffic'
    },
    {
      icon: ClipboardCheck,
      title: 'SEO Analytics',
      slug: 'seo-analytics',
      description: 'Comprehensive analysis of your website\'s SEO performance with detailed audit reports and actionable recommendations for improvement.',
      features: ['Technical SEO Analysis', 'Content Gap Assessment', 'Competitor Benchmarking', 'Keyword Opportunity Analysis', 'Site Structure Review', 'Performance Metrics'],
      benefits: 'Clear improvement roadmap, identified optimization opportunities, measurable action plan.',
      results: '+180% SEO Score'
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Comprehensive SEO Services
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Vesa Solutions offers end-to-end SEO services designed to improve your search visibility, drive qualified traffic, and increase conversions across all search channels.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {seoServices.map((service, index) => {
            const Icon = service.icon;
            return (
              <Link href={`/${service.slug}`} key={index}>
                <div className="bg-white border border-gray-200 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer group h-full flex flex-col">
                  <div className="flex items-start justify-between mb-6">
                    <div className="bg-green-500 p-4 rounded-2xl group-hover:bg-green-600 transition-colors">
                      <Icon size={32} className="text-white" />
                    </div>
                    <div className="bg-green-50 px-4 py-2 rounded-full">
                      <span className="text-green-600 font-bold text-sm">{service.results}</span>
                    </div>
                  </div>
                  
                  <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-green-600 transition-colors">
                    {service.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-6 leading-relaxed flex-1">
                    {service.description}
                  </p>
                  
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-800 mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {service.features.slice(0, 4).map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-gray-700 text-sm">
                          <CheckCircle size={16} className="text-green-500 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                      {service.features.length > 4 && (
                        <li className="text-green-600 text-sm font-medium">
                          +{service.features.length - 4} more features
                        </li>
                      )}
                    </ul>
                  </div>
                  
                  <div className="bg-green-50 p-4 rounded-xl mb-6">
                    <h4 className="font-semibold text-green-800 mb-2">Benefits:</h4>
                    <p className="text-green-700 text-sm">{service.benefits}</p>
                  </div>
                  
                  <div className="bg-green-500 hover:bg-green-600 text-white font-semibold px-6 py-3 rounded-lg transition-colors w-full text-center group-hover:bg-green-600 mt-auto">
                    Learn More →
                  </div>
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default SEOServicesSection;