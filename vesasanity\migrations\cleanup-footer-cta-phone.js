// Migration to remove phoneNumber from FooterCTA secondaryButton
const { createClient } = require('@sanity/client')

const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

async function cleanupFooterCTAPhoneNumbers() {
  try {
    console.log('Starting cleanup of FooterCTA phoneNumber fields...')
    
    // Get all sub-services
    const subServices = await client.fetch('*[_type == "subService"]')
    
    console.log(`Found ${subServices.length} sub-services to check`)
    
    let updatedCount = 0
    
    for (const subService of subServices) {
      // Check if footerCta.secondaryButton.phoneNumber exists
      if (subService.footerCta && 
          subService.footerCta.secondaryButton && 
          subService.footerCta.secondaryButton.phoneNumber) {
        
        console.log(`Removing phoneNumber from FooterCTA for: ${subService.title}`)
        
        // Remove the phoneNumber field
        await client
          .patch(subService._id)
          .unset(['footerCta.secondaryButton.phoneNumber'])
          .commit()
        
        updatedCount++
      }
    }
    
    console.log(`Cleanup completed! Updated ${updatedCount} sub-services.`)
    
    // Verify cleanup
    console.log('\nVerifying cleanup...')
    const remainingPhoneNumbers = await client.fetch(`
      *[_type == "subService" && defined(footerCta.secondaryButton.phoneNumber)] {
        _id,
        title,
        "phoneNumber": footerCta.secondaryButton.phoneNumber
      }
    `)
    
    if (remainingPhoneNumbers.length === 0) {
      console.log('✅ All phoneNumber fields successfully removed!')
    } else {
      console.log(`⚠️  ${remainingPhoneNumbers.length} phoneNumber fields still exist:`)
      remainingPhoneNumbers.forEach(doc => {
        console.log(`  - ${doc.title}: ${doc.phoneNumber}`)
      })
    }
    
  } catch (error) {
    console.error('Error during cleanup:', error)
  }
}

// Run the cleanup
cleanupFooterCTAPhoneNumbers()
