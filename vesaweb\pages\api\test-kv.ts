// pages/api/test-redis.ts - Test Redis connectivity
import { NextApiRequest, NextApiResponse } from 'next';
import { Redis } from '@upstash/redis';

// Initialize Redis client
const redis = new Redis({
  url: process.env.KV_REST_API_URL!,
  token: process.env.KV_REST_API_TOKEN!,
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Test basic Redis operations
    const testKey = 'test:connection';
    const testValue = { timestamp: new Date().toISOString(), test: true };

    // Set a test value
    await redis.set(testKey, testValue);

    // Get the test value
    const retrieved = await redis.get(testKey);

    // Clean up
    await redis.del(testKey);

    res.status(200).json({
      message: 'Redis connection successful',
      testValue,
      retrieved,
      success: true
    });

  } catch (error) {
    console.error('Redis test error:', error);
    res.status(500).json({
      message: 'Redis connection failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      success: false
    });
  }
}
