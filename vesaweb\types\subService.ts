// types/subService.ts - Updated with all missing types

export interface SubServiceData {
  _id: string
  title: string
  slug: {
    current: string
  }
  parentService?: string
  hero: SubServiceHero
  whyMatters?: WhyServiceMatters
  services?: ServiceDetail[]
  strategicImplementation?: StrategicImplementation  // ADDED - Missing from schema
  marketIntelligence?: MarketIntelligence            // ADDED - Missing from schema
  process?: ServiceProcess
  caseStudy?: CaseStudy
  cta?: ServiceCTA
  testimonials?: SubServiceTestimonial[]
  faqs?: FAQ[]
  footerCta?: FooterCTA
  seo?: SEOSettings
}

export interface SubServiceHero {
  badgeText?: string
  badgeIcon?: string
  title: string
  subtitle?: string
  stats?: Array<{
    value: string
    label: string
  }>
  backgroundGradient?: string
}

export interface WhyServiceMatters {
  title?: string
  description?: any[]
  features?: Array<{
    text: string
  }>
  stats?: Array<{
    value: string
    label: string
    color?: string
  }>
  ctaButton?: {
    text?: string
  }
}

export interface ServiceDetail {
  icon?: string
  title?: string
  description?: string
  fullDescription?: string
  features?: string[]
  benefits?: string
  result?: string
  image?: any
}

// ADDED - Strategic Implementation types (missing from original)
export interface StrategicImplementation {
  title?: string
  description?: string
  secondaryDescription?: string
  image?: any
  features?: string[]
  sections?: StrategicImplementationSection[]
}

export interface StrategicImplementationSection {
  icon?: string
  title?: string
  description?: string
  color?: string
}

// ADDED - Market Intelligence types (missing from original)
export interface MarketIntelligence {
  title?: string
  description?: string
  secondaryDescription?: string
  image?: any
  stats?: MarketIntelligenceStat[]
  ctaButton?: {
    text?: string
  }
  backgroundGradient?: string
}

export interface MarketIntelligenceStat {
  value?: string
  label?: string
}

export interface ServiceProcess {
  title?: string
  description?: string
  steps?: Array<{
    step: number
    title: string
    description: string
    icon?: string
    details?: string[]
  }>
}

export interface CaseStudy {
  title?: string
  description?: string
  image?: any
  results?: Array<{
    value: string
    label: string
  }>
  ctaButton?: {
    text?: string
  }
  backgroundGradient?: string
}

export interface ServiceCTA {
  title?: string
  description?: string
  benefits?: string[]
  formSettings?: {
    ctaText?: string
    messagePlaceholder?: string
  }
}

export interface SubServiceTestimonial {
  name?: string
  business?: string
  location?: string
  image?: any
  quote?: string
  result?: string
  rating?: number
}

export interface FAQ {
  question?: string
  answer?: string
}

export interface FooterCTA {
  title?: string
  description?: string
  primaryButton?: {
    text?: string
    icon?: string
  }
  secondaryButton?: {
    text?: string
    icon?: string
  }
  backgroundGradient?: string
}

export interface SEOSettings {
  metaTitle?: string
  metaDescription?: string
  keywords?: string[]
  ogImage?: any
}

// API Response types
export interface SubServiceResponse {
  subService: SubServiceData | null
  error?: string
}

export interface SubServiceListResponse {
  subServices: Array<{
    _id: string
    title: string
    slug: { current: string }
    hero: { title: string; subtitle?: string }
    seo?: { metaTitle?: string; metaDescription?: string }
  }>
  error?: string
}