// lib/sanity-blog.ts - Sanity client utilities for blog system
import { createClient } from '@sanity/client'
import imageUrlBuilder from '@sanity/image-url'
import { BlogPost, BlogListItem, BlogCategory, BlogTag, BlogFilters, SanityImageObject } from '@/types/blog'

export const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: '2023-05-03',
  useCdn: false,
})

// Image URL builder
const builder = imageUrlBuilder(client)

export function urlForImage(source: SanityImageObject) {
  return builder.image(source)
}

// GROQ Queries
const blogPostFields = `
  _id,
  title,
  slug,
  excerpt,
  featuredImage,
  content,
  publishedAt,
  categories[]->{
    _id,
    title,
    slug,
    description,
    color
  },
  tags[]->{
    _id,
    title,
    slug,
    description
  },
  featured,
  readingTime,
  seo
`

const blogListFields = `
  _id,
  title,
  slug,
  excerpt,
  featuredImage,
  publishedAt,
  categories[]->{
    _id,
    title,
    slug,
    color
  },
  tags[]->{
    _id,
    title,
    slug
  },
  featured,
  readingTime
`

// Get all blog posts with pagination and filtering
export async function getBlogPosts(filters: BlogFilters = {}): Promise<{
  posts: BlogListItem[];
  totalPosts: number;
  hasMore: boolean;
}> {
  const { category, tag, search, page = 1, limit = 12 } = filters
  const offset = (page - 1) * limit

  let filterQuery = '*[_type == "blogPost"'
  const filterConditions: string[] = []

  if (category) {
    filterConditions.push(`"${category}" in categories[]->slug.current`)
  }
  
  if (tag) {
    filterConditions.push(`"${tag}" in tags[]->slug.current`)
  }
  
  if (search) {
    filterConditions.push(`(title match "${search}*" || excerpt match "${search}*")`)
  }

  if (filterConditions.length > 0) {
    filterQuery += ` && (${filterConditions.join(' && ')})`
  }

  filterQuery += ']'

  const query = `{
    "posts": ${filterQuery} | order(publishedAt desc) [${offset}...${offset + limit}] {
      ${blogListFields}
    },
    "totalPosts": count(${filterQuery})
  }`

  const result = await client.fetch(query)
  
  return {
    posts: result.posts || [],
    totalPosts: result.totalPosts || 0,
    hasMore: result.totalPosts > offset + limit
  }
}

// Get featured blog posts
export async function getFeaturedBlogPosts(limit: number = 3): Promise<BlogListItem[]> {
  const query = `*[_type == "blogPost" && featured == true] | order(publishedAt desc) [0...${limit}] {
    ${blogListFields}
  }`
  
  return await client.fetch(query)
}

// Get single blog post by slug
export async function getBlogPost(slug: string): Promise<BlogPost | null> {
  const query = `*[_type == "blogPost" && slug.current == $slug][0] {
    ${blogPostFields}
  }`
  
  return await client.fetch(query, { slug })
}

// Get related blog posts
export async function getRelatedBlogPosts(postId: string, categories: string[], limit: number = 3): Promise<BlogListItem[]> {
  const query = `*[_type == "blogPost" && _id != $postId && count(categories[]->slug.current[@ in $categories]) > 0] | order(publishedAt desc) [0...${limit}] {
    ${blogListFields}
  }`
  
  return await client.fetch(query, { postId, categories })
}

// Get all blog categories
export async function getBlogCategories(): Promise<BlogCategory[]> {
  const query = `*[_type == "blogCategory"] | order(title asc) {
    _id,
    title,
    slug,
    description,
    color
  }`
  
  return await client.fetch(query)
}

// Get all blog tags
export async function getBlogTags(): Promise<BlogTag[]> {
  const query = `*[_type == "blogTag"] | order(title asc) {
    _id,
    title,
    slug,
    description
  }`
  
  return await client.fetch(query)
}



// Get blog post slugs for static generation
export async function getBlogPostSlugs(): Promise<string[]> {
  const query = `*[_type == "blogPost" && defined(slug.current)].slug.current`
  return await client.fetch(query)
}

// Get recent blog posts for homepage or sidebar
export async function getRecentBlogPosts(limit: number = 5): Promise<BlogListItem[]> {
  const query = `*[_type == "blogPost"] | order(publishedAt desc) [0...${limit}] {
    ${blogListFields}
  }`
  
  return await client.fetch(query)
}
