// migrations/case-studies-migration.js - Demo case studies data migration
const { createClient } = require('@sanity/client')

const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

const caseStudiesData = [
  {
    _type: 'caseStudy',
    title: 'E-commerce Fashion Retailer Achieves 450% Revenue Growth',
    slug: {
      _type: 'slug',
      current: 'fashion-retailer-revenue-growth'
    },
    shortDescription: 'Complete digital transformation including SEO, web development, and PPC advertising that resulted in unprecedented growth for this fashion e-commerce business.',
    featured: true,
    publishedAt: '2024-01-15T10:00:00Z',
    client: {
      name: 'StyleHub Fashion',
      industry: 'ecommerce',
      location: 'Los Angeles, CA',
      website: 'https://stylehubfashion.com',
      companySize: 'medium'
    },
    project: {
      serviceType: 'full-strategy',
      duration: '12 months',
      challenge: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'StyleHub Fashion was struggling with low online visibility and poor conversion rates. Their website was outdated, not mobile-optimized, and they had virtually no organic search presence. Despite having quality products, they were losing customers to competitors with better online experiences.'
            }
          ]
        }
      ],
      goals: [
        'Increase online revenue by 200%',
        'Improve organic search rankings',
        'Enhance user experience and conversion rates',
        'Build brand awareness in target markets'
      ]
    },
    solution: {
      approach: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'We implemented a comprehensive digital strategy including complete website redesign, technical SEO optimization, content marketing, and targeted PPC campaigns. Our approach focused on creating a seamless user experience while building long-term organic visibility.'
            }
          ]
        }
      ],
      tools: [
        'Google Analytics 4',
        'Google Search Console',
        'SEMrush',
        'Shopify Plus',
        'Google Ads',
        'Facebook Ads',
        'Klaviyo',
        'Hotjar'
      ]
    },
    results: {
      overview: 'The comprehensive digital transformation delivered exceptional results, with revenue growth far exceeding initial goals and establishing StyleHub as a market leader.',
      metrics: [
        {
          label: 'Revenue Growth',
          beforeValue: '$50K/month',
          afterValue: '$275K/month',
          improvement: '+450%',
          icon: 'DollarSign'
        },
        {
          label: 'Organic Traffic',
          beforeValue: '2,500/month',
          afterValue: '9,500/month',
          improvement: '+280%',
          icon: 'TrendingUp'
        },
        {
          label: 'Conversion Rate',
          beforeValue: '1.2%',
          afterValue: '3.8%',
          improvement: '+217%',
          icon: 'MousePointerClick'
        },
        {
          label: 'Average Order Value',
          beforeValue: '$45',
          afterValue: '$78',
          improvement: '+73%',
          icon: 'ShoppingCart'
        }
      ],
      timeline: 'Results achieved within 12 months'
    },
    testimonial: {
      quote: 'VESA Solutions completely transformed our online presence. The 450% revenue growth we achieved in just 12 months exceeded our wildest expectations. Their strategic approach and attention to detail is unmatched.',
      author: 'Sarah Johnson',
      position: 'CEO & Founder',
      rating: 5
    },
    seo: {
      metaTitle: 'Fashion E-commerce 450% Revenue Growth Case Study | VESA Solutions',
      metaDescription: 'Discover how VESA Solutions helped StyleHub Fashion achieve 450% revenue growth through comprehensive digital marketing strategy.',
      keywords: ['ecommerce case study', 'fashion marketing', 'revenue growth', 'digital transformation']
    }
  },
  {
    _type: 'caseStudy',
    title: 'SaaS Startup Generates 340% More Qualified Leads',
    slug: {
      _type: 'slug',
      current: 'saas-startup-lead-generation'
    },
    shortDescription: 'Strategic SEO and content marketing campaign that positioned this B2B SaaS startup as an industry leader and dramatically increased qualified lead generation.',
    featured: true,
    publishedAt: '2024-02-10T14:30:00Z',
    client: {
      name: 'TechFlow Solutions',
      industry: 'technology',
      location: 'Austin, TX',
      website: 'https://techflowsolutions.com',
      companySize: 'startup'
    },
    project: {
      serviceType: 'seo',
      duration: '8 months',
      challenge: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'TechFlow Solutions had an innovative B2B software product but struggled to generate qualified leads. Their content was technical but not optimized for search, and they had difficulty reaching decision-makers in their target industries.'
            }
          ]
        }
      ],
      goals: [
        'Increase qualified lead generation',
        'Establish thought leadership',
        'Improve organic search visibility',
        'Reduce customer acquisition costs'
      ]
    },
    solution: {
      approach: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'We developed a content-driven SEO strategy targeting high-intent keywords and decision-maker pain points. Our approach included technical SEO optimization, comprehensive content creation, and strategic link building to establish authority.'
            }
          ]
        }
      ],
      tools: [
        'Ahrefs',
        'Google Search Console',
        'HubSpot',
        'Google Analytics',
        'Screaming Frog',
        'BuzzSumo',
        'LinkedIn Sales Navigator'
      ]
    },
    results: {
      overview: 'The SEO and content strategy transformed TechFlow from an unknown startup to a recognized industry authority, generating high-quality leads consistently.',
      metrics: [
        {
          label: 'Qualified Leads',
          beforeValue: '15/month',
          afterValue: '66/month',
          improvement: '+340%',
          icon: 'Users'
        },
        {
          label: 'Organic Rankings',
          beforeValue: '5 keywords',
          afterValue: '127 keywords',
          improvement: '+2440%',
          icon: 'Search'
        },
        {
          label: 'Cost Per Lead',
          beforeValue: '$450',
          afterValue: '$68',
          improvement: '-85%',
          icon: 'DollarSign'
        },
        {
          label: 'Demo Requests',
          beforeValue: '8/month',
          afterValue: '45/month',
          improvement: '+463%',
          icon: 'MousePointerClick'
        }
      ],
      timeline: 'Significant results within 6 months'
    },
    testimonial: {
      quote: 'The ROI from our SEO investment has been incredible. We went from struggling to get leads to becoming the go-to solution in our niche. VESA\'s expertise in B2B marketing is exceptional.',
      author: 'Michael Chen',
      position: 'Founder & CTO',
      rating: 5
    },
    seo: {
      metaTitle: 'SaaS Lead Generation 340% Growth Case Study | VESA Solutions',
      metaDescription: 'Learn how VESA Solutions helped TechFlow Solutions generate 340% more qualified leads through strategic SEO and content marketing.',
      keywords: ['saas marketing', 'lead generation', 'B2B SEO', 'startup growth']
    }
  },
  {
    _type: 'caseStudy',
    title: 'Local Restaurant Chain Doubles Online Orders',
    slug: {
      _type: 'slug',
      current: 'restaurant-chain-online-orders'
    },
    shortDescription: 'Local SEO and web development project that revolutionized online ordering system and local search presence for this growing restaurant chain.',
    featured: true,
    publishedAt: '2024-03-05T16:45:00Z',
    client: {
      name: 'Bella Vista Restaurants',
      industry: 'food-beverage',
      location: 'Miami, FL',
      website: 'https://bellavistarestaurants.com',
      companySize: 'small'
    },
    project: {
      serviceType: 'web-development',
      duration: '6 months',
      challenge: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'Bella Vista Restaurants had multiple locations but poor online presence. Their website was outdated, online ordering was complicated, and they weren\'t appearing in local search results when customers looked for nearby restaurants.'
            }
          ]
        }
      ],
      goals: [
        'Increase online order volume',
        'Improve local search visibility',
        'Streamline online ordering process',
        'Drive foot traffic to locations'
      ]
    },
    solution: {
      approach: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'We rebuilt their website with a focus on local SEO and user experience, implemented a streamlined online ordering system, and optimized their Google Business Profile listings for all locations.'
            }
          ]
        }
      ],
      tools: [
        'WordPress',
        'WooCommerce',
        'Google My Business',
        'Local SEO tools',
        'Google Analytics',
        'Online ordering system',
        'Review management platform'
      ]
    },
    results: {
      overview: 'The local SEO and web development improvements transformed Bella Vista from a hidden gem to the top choice for local diners.',
      metrics: [
        {
          label: 'Online Orders',
          beforeValue: '45/week',
          afterValue: '158/week',
          improvement: '+251%',
          icon: 'ShoppingCart'
        },
        {
          label: 'Local Visibility',
          beforeValue: '12% share',
          afterValue: '34% share',
          improvement: '+183%',
          icon: 'Eye'
        },
        {
          label: 'Mobile Traffic',
          beforeValue: '1,200/month',
          afterValue: '5,040/month',
          improvement: '+320%',
          icon: 'Smartphone'
        },
        {
          label: 'Average Order Value',
          beforeValue: '$28',
          afterValue: '$42',
          improvement: '+50%',
          icon: 'DollarSign'
        }
      ],
      timeline: 'Results achieved within 4 months'
    },
    testimonial: {
      quote: 'Our online presence went from virtually non-existent to dominating local search results. The 250% increase in online orders has revolutionized our business model.',
      author: 'David Rodriguez',
      position: 'Owner & Head Chef',
      rating: 5
    },
    seo: {
      metaTitle: 'Restaurant Online Orders 250% Growth Case Study | VESA Solutions',
      metaDescription: 'See how VESA Solutions helped Bella Vista Restaurants increase online orders by 250% through local SEO and web development.',
      keywords: ['restaurant marketing', 'local SEO', 'online ordering', 'food delivery']
    }
  }
];

// Additional case studies for variety
const additionalCaseStudies = [
  {
    _type: 'caseStudy',
    title: 'Medical Practice Increases New Patients by 280%',
    slug: {
      _type: 'slug',
      current: 'medical-practice-patient-growth'
    },
    shortDescription: 'Healthcare-focused digital marketing strategy that helped this medical practice become the leading choice for patients in their area.',
    featured: false,
    publishedAt: '2024-01-20T09:15:00Z',
    client: {
      name: 'Wellness Medical Center',
      industry: 'healthcare',
      location: 'Phoenix, AZ',
      companySize: 'small'
    },
    project: {
      serviceType: 'seo',
      duration: '10 months',
      challenge: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'Wellness Medical Center was struggling to attract new patients despite excellent care quality. Their online presence was minimal and they weren\'t appearing in local health searches.'
            }
          ]
        }
      ],
      goals: [
        'Increase new patient appointments',
        'Improve local search rankings',
        'Build trust through online reviews',
        'Educate patients through content'
      ]
    },
    solution: {
      approach: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'We implemented a healthcare-specific SEO strategy focusing on local search optimization, medical content creation, and reputation management while ensuring HIPAA compliance.'
            }
          ]
        }
      ],
      tools: ['Google My Business', 'Healthcare SEO tools', 'Review management', 'Medical content platform']
    },
    results: {
      overview: 'The medical practice became the top choice for patients in their area, with significant increases in appointments and patient satisfaction.',
      metrics: [
        {
          label: 'New Patients',
          beforeValue: '25/month',
          afterValue: '95/month',
          improvement: '+280%',
          icon: 'Users'
        },
        {
          label: 'Local Rankings',
          beforeValue: 'Page 3',
          afterValue: 'Position 1-3',
          improvement: '+850%',
          icon: 'TrendingUp'
        },
        {
          label: 'Online Reviews',
          beforeValue: '12 reviews',
          afterValue: '156 reviews',
          improvement: '+1200%',
          icon: 'Star'
        }
      ]
    },
    testimonial: {
      quote: 'The team\'s understanding of healthcare marketing regulations while delivering outstanding results is impressive. We\'ve seen a 280% increase in new patient inquiries.',
      author: 'Dr. Emily Watson',
      position: 'Medical Director',
      rating: 5
    },
    seo: {
      metaTitle: 'Medical Practice 280% Patient Growth Case Study | VESA Solutions',
      metaDescription: 'Discover how VESA Solutions helped Wellness Medical Center increase new patients by 280% through healthcare-focused digital marketing.',
      keywords: ['medical marketing', 'healthcare SEO', 'patient acquisition', 'medical practice growth']
    }
  },
  {
    _type: 'caseStudy',
    title: 'Law Firm Generates 400% More Qualified Leads',
    slug: {
      _type: 'slug',
      current: 'law-firm-lead-generation'
    },
    shortDescription: 'Legal industry digital marketing campaign that established this law firm as the premier choice for clients in their practice areas.',
    featured: false,
    publishedAt: '2024-02-28T11:20:00Z',
    client: {
      name: 'Johnson & Associates Law',
      industry: 'professional-services',
      location: 'Chicago, IL',
      companySize: 'medium'
    },
    project: {
      serviceType: 'full-strategy',
      duration: '9 months',
      challenge: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'Johnson & Associates was competing with larger firms for high-value cases but lacked the online presence to attract quality clients. Their website was outdated and they had poor search visibility.'
            }
          ]
        }
      ],
      goals: [
        'Increase qualified case inquiries',
        'Establish thought leadership',
        'Improve search rankings for legal terms',
        'Build trust through content and reviews'
      ]
    },
    solution: {
      approach: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'We developed a comprehensive legal marketing strategy including website redesign, content marketing focused on legal expertise, local SEO optimization, and reputation management.'
            }
          ]
        }
      ],
      tools: ['Legal SEO platform', 'Content management system', 'Review monitoring', 'Analytics suite']
    },
    results: {
      overview: 'The law firm became the go-to choice for clients in their practice areas, with significant increases in high-value case inquiries.',
      metrics: [
        {
          label: 'Qualified Leads',
          beforeValue: '8/month',
          afterValue: '40/month',
          improvement: '+400%',
          icon: 'Users'
        },
        {
          label: 'Case Value',
          beforeValue: '$15K average',
          afterValue: '$35K average',
          improvement: '+133%',
          icon: 'DollarSign'
        },
        {
          label: 'Organic Traffic',
          beforeValue: '850/month',
          afterValue: '3,200/month',
          improvement: '+276%',
          icon: 'TrendingUp'
        }
      ]
    },
    testimonial: {
      quote: 'VESA helped us generate 400% more qualified leads while reducing our cost per acquisition by 70%. Their data-driven approach delivers real business value.',
      author: 'Robert Johnson',
      position: 'Managing Partner',
      rating: 5
    },
    seo: {
      metaTitle: 'Law Firm 400% Lead Generation Growth Case Study | VESA Solutions',
      metaDescription: 'Learn how VESA Solutions helped Johnson & Associates Law generate 400% more qualified leads through strategic legal marketing.',
      keywords: ['legal marketing', 'law firm SEO', 'legal lead generation', 'attorney marketing']
    }
  }
];

// Combine all case studies
const allCaseStudies = [...caseStudiesData, ...additionalCaseStudies];

async function createCaseStudies() {
  console.log('Starting case studies migration...');
  
  try {
    // Create case studies one by one
    for (const caseStudy of allCaseStudies) {
      console.log(`Creating case study: ${caseStudy.title}`);
      
      const result = await client.create(caseStudy);
      console.log(`✅ Created case study: ${result._id}`);
    }
    
    console.log(`🎉 Successfully created ${allCaseStudies.length} case studies!`);
    
  } catch (error) {
    console.error('❌ Error creating case studies:', error);
  }
}

// Run the migration
createCaseStudies();
