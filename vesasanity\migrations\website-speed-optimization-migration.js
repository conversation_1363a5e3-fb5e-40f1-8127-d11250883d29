// scripts/migrate-website-speed-optimization.js - Working migration with proper schema-compatible icons and BLUE colors
const { createClient } = require('@sanity/client')
const client = createClient({
  projectId: 'zleti5e4',
  dataset: 'production',
  useCdn: false,
  token: 'sk7Q1WAHlqLZbcxlrya4SJ25tiYoZiLygkROUZvGcCG00oxeAmYQ5H26DORJgwKt4ge0WMN6UNQ7dhJdBFQy9U291UFZPKhm3LUGJTVJ9E0cxs1x2a8bzXuhF4i0McKdyO3fTVLF1dQOmPZW2QEUmZc8qkALkd2epeIwtOEiyOHJCzJjdaDA',
  apiVersion: '2024-01-01'
})

const speedOptimizationData = {
  _type: 'subService',
  parentService: 'web-development',
  title: 'Website Speed Optimization Services',
  slug: {
    _type: 'slug',
    current: 'website-speed-optimization'
  },
  
  // Hero Section
  hero: {
    badgeText: 'Website Speed Optimization',
    badgeIcon: 'Zap',
    title: 'Accelerate Your Website Performance',
    subtitle: 'Transform slow-loading websites into lightning-fast experiences that improve user satisfaction, boost search rankings, and increase conversions. Our speed optimization services deliver measurable performance improvements.',
    stats: [
      { _key: 'stat1', value: '75%', label: 'Average Speed Improvement' },
      { _key: 'stat2', value: '40%', label: 'Bounce Rate Reduction' },
      { _key: 'stat3', value: '25%', label: 'Conversion Rate Increase' },
      { _key: 'stat4', value: '90+', label: 'PageSpeed Score Target' }
    ],
    backgroundGradient: 'from-blue-600 to-blue-800'
  },

  // Why Service Matters
  whyMatters: {
    title: 'Why Website Speed Optimization is Critical for Success',
    description: [
      {
        _type: 'block',
        _key: 'desc1',
        children: [
          {
            _type: 'span',
            _key: 'span1',
            text: "Website speed directly impacts every aspect of your online success—from user experience and conversion rates to search engine rankings and revenue. Studies show that even a 1-second delay in page load time can reduce conversions by 7% and increase bounce rates by 32%."
          }
        ],
        markDefs: [],
        style: 'normal'
      },
      {
        _type: 'block',
        _key: 'desc2',
        children: [
          {
            _type: 'span',
            _key: 'span2',
            text: "VESA Solutions specializes in comprehensive website speed optimization that addresses every performance bottleneck. Our systematic approach delivers measurable improvements in loading times, Core Web Vitals, and overall user experience that drive real business results."
          }
        ],
        markDefs: [],
        style: 'normal'
      }
    ],
    features: [
      { _key: 'feature1', text: 'Comprehensive performance audits and analysis' },
      { _key: 'feature2', text: 'Core Web Vitals optimization for better SEO' },
      { _key: 'feature3', text: 'Advanced caching and compression techniques' },
      { _key: 'feature4', text: 'Mobile performance optimization and testing' }
    ],
    stats: [
      {
        _key: 'stat1',
        value: '53%',
        label: 'of mobile users abandon slow sites',
        color: 'from-blue-50 to-blue-100'
      },
      {
        _key: 'stat2',
        value: '7%',
        label: 'conversion loss per second of delay',
        color: 'from-blue-100 to-blue-200'
      },
      {
        _key: 'stat3',
        value: '32%',
        label: 'bounce rate increase with 3s load time',
        color: 'from-blue-200 to-blue-300'
      },
      {
        _key: 'stat4',
        value: '2.5x',
        label: 'better rankings with fast sites',
        color: 'from-blue-300 to-blue-400'
      }
    ],
    ctaButton: {
      text: 'Analyze Your Website Speed'
    }
  },

  // All Services (with schema-compatible icons)
  services: [
    {
      _key: 'service1',
      icon: 'Search',
      title: 'Comprehensive Performance Audit',
      description: 'Conduct detailed analysis of your website\'s performance bottlenecks, loading issues, and optimization opportunities with actionable recommendations.',
      fullDescription: 'Our comprehensive performance audit identifies every factor affecting your website\'s speed, from server response times to resource optimization opportunities. We provide detailed analysis and prioritized recommendations for maximum impact.',
      features: [
        'Complete website speed analysis and testing',
        'Core Web Vitals assessment and optimization planning',
        'Mobile and desktop performance evaluation',
        'Server response time and hosting analysis',
        'Resource loading and optimization assessment',
        'Third-party script impact analysis',
        'Competitive performance benchmarking',
        'Detailed optimization roadmap and priorities'
      ],
      benefits: 'Comprehensive audits identify 95% of performance issues, provide clear optimization priorities, and create actionable roadmaps that deliver measurable speed improvements.',
      result: '+95% Issue Identification'
    },
    {
      _key: 'service2',
      icon: 'Image',
      title: 'Image & Media Optimization',
      description: 'Optimize images, videos, and media files for faster loading without compromising visual quality through advanced compression and delivery techniques.',
      fullDescription: 'Images often account for 60-80% of page weight. We implement advanced image optimization techniques including compression, format conversion, and modern delivery methods to dramatically reduce load times.',
      features: [
        'Advanced image compression and optimization',
        'Next-gen format conversion (WebP, AVIF)',
        'Responsive image implementation',
        'Lazy loading for images and media',
        'Content delivery network (CDN) setup',
        'Video optimization and compression',
        'Progressive image loading techniques',
        'Automated optimization workflows'
      ],
      benefits: 'Image optimization reduces page size by 70%, improves loading speeds by 60%, and maintains visual quality while dramatically improving performance.',
      result: '+70% Size Reduction'
    },
    {
      _key: 'service3',
      icon: 'Database',
      title: 'Code & Database Optimization',
      description: 'Optimize website code, database queries, and server configurations for maximum performance and efficiency.',
      fullDescription: 'Clean, efficient code and optimized databases are essential for fast websites. We optimize HTML, CSS, JavaScript, and database queries to eliminate performance bottlenecks and improve loading speeds.',
      features: [
        'HTML, CSS, and JavaScript minification',
        'Database query optimization and indexing',
        'Code splitting and lazy loading implementation',
        'Unused code removal and cleanup',
        'Server-side rendering optimization',
        'API response time optimization',
        'Memory usage optimization',
        'Code performance profiling and analysis'
      ],
      benefits: 'Code optimization improves loading speeds by 50%, reduces server load by 40%, and creates more efficient, maintainable websites that perform consistently.',
      result: '+50% Loading Speed'
    },
    {
      _key: 'service4',
      icon: 'Cloud',
      title: 'Caching & CDN Implementation',
      description: 'Implement advanced caching strategies and content delivery networks to dramatically reduce loading times for global audiences.',
      fullDescription: 'Strategic caching and CDN implementation can reduce loading times by 80% or more. We implement comprehensive caching solutions that serve content faster to users worldwide.',
      features: [
        'Browser caching configuration and optimization',
        'Server-side caching implementation',
        'Content delivery network (CDN) setup',
        'Edge caching and global distribution',
        'Cache invalidation and management strategies',
        'Dynamic content caching optimization',
        'Mobile-specific caching strategies',
        'Performance monitoring and cache analytics'
      ],
      benefits: 'Advanced caching reduces loading times by 80%, improves global performance, and reduces server load while providing consistent fast experiences worldwide.',
      result: '+80% Speed Improvement'
    },
    {
      _key: 'service5',
      icon: 'Smartphone',
      title: 'Mobile Performance Optimization',
      description: 'Optimize website performance specifically for mobile devices to provide exceptional mobile user experiences.',
      fullDescription: 'Mobile optimization requires specific techniques due to slower networks and limited processing power. We implement mobile-first optimization strategies that ensure fast loading on all devices.',
      features: [
        'Mobile-first performance optimization',
        'Touch and interaction optimization',
        'Mobile network optimization techniques',
        'Progressive web app (PWA) implementation',
        'Mobile-specific image optimization',
        'Accelerated Mobile Pages (AMP) setup',
        'Mobile Core Web Vitals optimization',
        'Cross-device performance testing'
      ],
      benefits: 'Mobile optimization improves mobile loading speeds by 65%, reduces mobile bounce rates by 45%, and ensures excellent performance across all mobile devices.',
      result: '+65% Mobile Speed'
    },
    {
      _key: 'service6',
      icon: 'BarChart3',
      title: 'Performance Monitoring & Reporting',
      description: 'Implement comprehensive performance monitoring and reporting systems to track improvements and maintain optimal speeds.',
      fullDescription: 'Ongoing performance monitoring ensures your website maintains optimal speeds over time. We implement monitoring systems that track performance metrics and alert you to any issues.',
      features: [
        'Real-time performance monitoring setup',
        'Core Web Vitals tracking and reporting',
        'Page speed analytics and insights',
        'Performance alert systems',
        'Competitive performance benchmarking',
        'User experience metrics tracking',
        'Performance regression detection',
        'Monthly performance reports and recommendations'
      ],
      benefits: 'Performance monitoring prevents 90% of speed regressions, provides ongoing optimization insights, and ensures your website maintains peak performance consistently.',
      result: '+90% Issue Prevention'
    }
  ],

  // Strategic Implementation Section (with schema-compatible icons and colors)
  strategicImplementation: {
    title: 'Comprehensive Website Speed Optimization Process',
    description: 'Successful speed optimization requires systematic analysis, strategic implementation, and ongoing monitoring. Our integrated approach ensures maximum performance improvements that deliver lasting results.',
    secondaryDescription: 'From initial performance audits to ongoing monitoring, we guide you through every step of the optimization process with expertise, transparency, and dedication to achieving the fastest possible website speeds.',
    features: [
      'Comprehensive performance analysis and planning',
      'Strategic optimization implementation',
      'Ongoing monitoring and maintenance'
    ],
    sections: [
      {
        _key: 'section1',
        icon: 'Search',
        title: 'Analysis & Planning',
        description: 'Comprehensive performance audits and strategic planning to identify optimization opportunities and create implementation roadmaps.',
        color: 'from-blue-50 to-blue-100'
      },
      {
        _key: 'section2',
        icon: 'Zap',
        title: 'Optimization Implementation',
        description: 'Expert implementation of performance optimizations using proven techniques and cutting-edge technologies for maximum speed improvements.',
        color: 'from-blue-100 to-blue-200'
      },
      {
        _key: 'section3',
        icon: 'BarChart3',
        title: 'Monitoring & Maintenance',
        description: 'Ongoing performance monitoring, reporting, and optimization to ensure your website maintains peak performance over time.',
        color: 'from-blue-200 to-blue-300'
      }
    ]
  },

  // Market Intelligence Section (with schema-compatible background)
  marketIntelligence: {
    title: 'Advanced Website Speed Optimization Intelligence',
    description: 'Our speed optimization strategies are powered by performance data, industry insights, and proven methodologies that deliver exceptional results for websites across all industries and platforms.',
    secondaryDescription: 'Through hundreds of successful optimization projects, we\'ve developed the expertise and techniques needed to achieve dramatic speed improvements that drive real business results and user satisfaction.',
    stats: [
      {
        _key: 'stat1',
        value: '400+',
        label: 'Websites Optimized'
      },
      {
        _key: 'stat2',
        value: '75%',
        label: 'Average Speed Improvement'
      },
      {
        _key: 'stat3',
        value: '90+',
        label: 'PageSpeed Score Target'
      },
      {
        _key: 'stat4',
        value: '24/7',
        label: 'Performance Monitoring'
      }
    ],
    ctaButton: {
      text: 'Optimize Your Website Speed'
    },
    backgroundGradient: 'from-blue-50 to-blue-100'
  },

  // Process (with schema-compatible icons)
  process: {
    title: 'Our Proven Website Speed Optimization Process',
    description: 'VESA Solutions follows a systematic approach that has delivered exceptional speed improvements for hundreds of websites, ensuring maximum performance gains and lasting results.',
    steps: [
      {
        _key: 'step1',
        step: 1,
        title: 'Performance Audit & Analysis',
        description: 'Comprehensive analysis of your website\'s current performance, bottlenecks, and optimization opportunities.',
        icon: 'Search',
        details: [
          'Complete website speed analysis and testing',
          'Core Web Vitals assessment and evaluation',
          'Mobile and desktop performance benchmarking',
          'Server and hosting performance analysis',
          'Competitive performance comparison'
        ]
      },
      {
        _key: 'step2',
        step: 2,
        title: 'Optimization Strategy & Planning',
        description: 'Develop comprehensive optimization strategy with prioritized recommendations for maximum impact.',
        icon: 'Target',
        details: [
          'Optimization priority matrix development',
          'Technical implementation planning',
          'Resource and timeline estimation',
          'Performance goal setting and benchmarking',
          'Risk assessment and mitigation planning'
        ]
      },
      {
        _key: 'step3',
        step: 3,
        title: 'Implementation & Testing',
        description: 'Expert implementation of optimization techniques with comprehensive testing and validation.',
        icon: 'Zap',
        details: [
          'Code and database optimization implementation',
          'Image and media optimization',
          'Caching and CDN configuration',
          'Performance testing and validation',
          'Cross-device and browser testing'
        ]
      },
      {
        _key: 'step4',
        step: 4,
        title: 'Monitoring & Maintenance',
        description: 'Ongoing performance monitoring and optimization to maintain peak speeds and prevent regressions.',
        icon: 'BarChart3',
        details: [
          'Performance monitoring system setup',
          'Regular performance audits and reports',
          'Optimization maintenance and updates',
          'Performance alert configuration',
          'Continuous improvement recommendations'
        ]
      }
    ]
  },

  // Case Study (with schema-compatible background)
  caseStudy: {
    title: 'E-commerce Site Achieves 85% Speed Improvement',
    description: 'Discover how our comprehensive speed optimization helped TechStore Pro reduce loading times by 85% and increase conversions by 40% through strategic performance improvements.',
    results: [
      {
        _key: 'result1',
        value: '85%',
        label: 'Speed Improvement'
      },
      {
        _key: 'result2',
        value: '40%',
        label: 'Conversion Increase'
      },
      {
        _key: 'result3',
        value: '60%',
        label: 'Bounce Rate Reduction'
      },
      {
        _key: 'result4',
        value: '95',
        label: 'PageSpeed Score'
      }
    ],
    ctaButton: {
      text: 'View Complete Case Study'
    },
    backgroundGradient: 'from-blue-600 to-blue-800'
  },

  // CTA
  cta: {
    title: 'Get Your Free Website Speed Analysis',
    description: 'Ready to accelerate your website performance? Get a comprehensive speed analysis and optimization recommendations that will transform your website\'s performance.',
    benefits: [
      'Complete website speed audit & performance analysis',
      'Core Web Vitals assessment & optimization plan',
      'Prioritized recommendations & implementation roadmap',
      'Performance improvement projections & ROI analysis'
    ],
    phoneNumber: '(*************',
    formSettings: {
      ctaText: 'Get My Free Speed Analysis',
      messagePlaceholder: 'Tell us about your website speed concerns and goals*'
    }
  },

  // All Testimonials (without image references)
  testimonials: [
    {
      _key: 'testimonial1',
      name: 'Michael Rodriguez',
      business: 'TechStore Pro',
      location: 'Miami, FL',
      quote: 'The speed optimization VESA performed on our e-commerce site was incredible. We saw an 85% improvement in loading times and our conversion rates increased by 40%. The difference in user experience is night and day.',
      result: '+85% Speed Improvement',
      rating: 5
    },
    {
      _key: 'testimonial2',
      name: 'Sarah Johnson',
      business: 'Creative Design Agency',
      location: 'Portland, OR',
      quote: 'Our website was painfully slow and hurting our business. VESA\'s optimization work transformed our site performance completely. We now have a 95 PageSpeed score and our bounce rate dropped by 60%.',
      result: '+95 PageSpeed Score',
      rating: 5
    },
    {
      _key: 'testimonial3',
      name: 'David Chen',
      business: 'Local Services Company',
      location: 'San Antonio, TX',
      quote: 'The mobile performance improvements have been game-changing for our business. Our mobile conversions increased by 50% after the optimization, and customer feedback has been overwhelmingly positive.',
      result: '+50% Mobile Conversions',
      rating: 5
    }
  ],

  // All FAQs
  faqs: [
    {
      _key: 'faq1',
      question: 'How much can website speed optimization improve my site?',
      answer: 'Speed improvements vary by website, but we typically achieve 50-85% faster loading times. Factors include current performance, optimization opportunities, and technical constraints. We provide realistic projections during our initial analysis.'
    },
    {
      _key: 'faq2',
      question: 'How long does website speed optimization take?',
      answer: 'Most optimization projects take 2-4 weeks depending on complexity and scope. This includes analysis, implementation, testing, and monitoring setup. We provide detailed timelines during the planning phase.'
    },
    {
      _key: 'faq3',
      question: 'Will speed optimization affect my website\'s appearance or functionality?',
      answer: 'No, our optimization techniques maintain your website\'s visual design and functionality while improving performance. We focus on behind-the-scenes improvements that enhance speed without changing user experience.'
    },
    {
      _key: 'faq4',
      question: 'What is Core Web Vitals and why is it important?',
      answer: 'Core Web Vitals are Google\'s key metrics for measuring user experience, including loading speed, interactivity, and visual stability. These metrics directly impact search rankings and user satisfaction.'
    },
    {
      _key: 'faq5',
      question: 'Do you provide ongoing performance monitoring?',
      answer: 'Yes, we offer comprehensive performance monitoring services that track your website\'s speed, alert you to issues, and provide regular optimization recommendations to maintain peak performance.'
    },
    {
      _key: 'faq6',
      question: 'Can you optimize websites built on any platform?',
      answer: 'Yes, we optimize websites built on all major platforms including WordPress, Shopify, custom HTML/CSS, React, and others. Our techniques are platform-agnostic and focus on universal performance principles.'
    },
    {
      _key: 'faq7',
      question: 'How do you measure and report performance improvements?',
      answer: 'We use industry-standard tools like Google PageSpeed Insights, GTmetrix, and WebPageTest to measure performance. We provide detailed before/after reports showing specific improvements and their business impact.'
    },
    {
      _key: 'faq8',
      question: 'What happens if my website speed regresses after optimization?',
      answer: 'We provide ongoing monitoring and support to prevent speed regressions. If issues occur, we quickly identify and resolve them. Our optimization techniques are designed for long-term performance stability.'
    }
  ],

  // Footer CTA (with schema-compatible background)
  footerCta: {
    title: 'Ready to Accelerate Your Website Performance?',
    description: 'Join hundreds of businesses that trust VESA Solutions to optimize website speed, improve user experience, and drive better business results.',
    primaryButton: {
      text: 'Schedule Free Consultation',
      icon: 'Calendar'
    },
    secondaryButton: {
      text: 'Call (*************',
      icon: 'Phone',
      phoneNumber: '(*************'
    },
    trustSignals: {
      title: 'Trusted by Businesses & Certified Partners',
      ratings: [
        {
          _key: 'rating1',
          rating: '4.9/5 Rating',
          source: 'Google Reviews',
          description: 'Google Reviews'
        },
        {
          _key: 'rating2',
          rating: '4.8/5 Rating',
          source: 'Clutch Reviews',
          description: 'Clutch Reviews'
        },
        {
          _key: 'rating3',
          rating: '400+ Sites',
          source: 'Successfully Optimized',
          description: 'Successfully Optimized'
        }
      ]
    },
    backgroundGradient: 'from-blue-700 to-blue-900'
  },

  // SEO (without image reference)
  seo: {
    metaTitle: 'Website Speed Optimization Services | Page Speed Optimization | VESA Solutions',
    metaDescription: 'Professional website speed optimization services that improve loading times, Core Web Vitals, and user experience. Boost conversions and search rankings. Free analysis included.',
    keywords: [
      'website speed optimization',
      'page speed optimization',
      'website performance optimization',
      'core web vitals optimization',
      'website speed improvement'
    ]
  }
}

async function migrateSpeedOptimizationData() {
  try {
    console.log('🚀 Starting Website Speed Optimization migration...')
    console.log('📊 Project ID: zleti5e4')
    console.log('🗃️  Dataset: production')
    console.log('')
    
    // Check if document already exists
    const existing = await client.fetch(`*[_type == "subService" && slug.current == "website-speed-optimization"][0]`)
    
    if (existing) {
      console.log('📄 Website Speed Optimization document already exists:', existing._id)
      console.log('🔄 Updating existing document...')
      
      const result = await client
        .patch(existing._id)
        .set(speedOptimizationData)
        .commit()
      
      console.log('✅ Website Speed Optimization page updated successfully!')
      console.log(`📄 Document ID: ${result._id}`)
      console.log(`🔗 Edit in Studio: https://vesasanity.sanity.studio/structure/subService;${result._id}`)
      return result
    } else {
      const result = await client.create(speedOptimizationData)
      console.log('✅ Website Speed Optimization page created successfully!')
      console.log(`📄 Document ID: ${result._id}`)
      console.log(`🔗 Edit in Studio: https://vesasanity.sanity.studio/structure/subService;${result._id}`)
      return result
    }
    
  } catch (error) {
    console.error('')
    console.error('❌ Migration failed:')
    console.error(error)
    
    if (error.message && error.message.includes('token')) {
      console.log('')
      console.log('💡 Make sure your token is correctly set in this file')
      console.log('   Get your token from: https://sanity.io/manage/personal/tokens')
    }
    
    if (error.message && error.message.includes('Insufficient permissions')) {
      console.log('')
      console.log('💡 Make sure your token has "Editor" or "Administrator" permissions')
    }
    
    process.exit(1)
  }
}

// Export the function instead of auto-running
module.exports = { migrateSpeedOptimizationData }

// Run the migration
migrateSpeedOptimizationData()
  .then(() => {
    console.log('')
    console.log('🎉 Your Website Speed Optimization page is now managed by Sanity!')
    console.log('📝 You can now edit all content in Sanity Studio')
    console.log('🔄 Remember to update your Next.js page to fetch from Sanity')
    console.log('')
    console.log('🎯 The page structure is complete and functional!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Migration failed:', error)
    process.exit(1)
  })
