// components/home/<USER>
import React, { useState } from 'react';
import Image from 'next/image';
import { Testimonial } from '@/types';

interface ClientTestimonialsSectionProps {
  className?: string;
}

const ClientTestimonialsSection: React.FC<ClientTestimonialsSectionProps> = ({ className = '' }) => {
  const [activeTestimonial, setActiveTestimonial] = useState<number>(0);

  const testimonials: Testimonial[] = [
    {
      id: 1,
      name: "<PERSON><PERSON>",
      title: "Executive Director | AIC",
      image: "/homepage/elira kokona.png",
      heading: "How Vesa Solutions Elevated Our Digital Presence",
      content: "Working with Vesa Solutions has been a game-changer for our digital presence. They expertly built our website from the ground up, ensuring it's not only visually appealing but also optimized for performance and SEO. Thanks to their comprehensive approach, we've seen significant improvements in our online visibility and search engine rankings.",
      additionalContent: "Their team's professionalism, creativity, and dedication have made them a trusted partner in our digital growth. Highly recommended for any business looking to elevate their online presence!",
      stars: 5,
      signature: "<PERSON><PERSON>",
      position: "Executive Director at Albanian Investment Corporation"
    },
    {
      id: 2,
      name: "<PERSON><PERSON>",
      title: "Founder | QellzA",
      image: "/homepage/Sonila-Mustafa.png",
      heading: "Transformed Our Brand's Digital Presence",
      content: "Vesa Solutions completely revolutionized our online presence. Their strategic approach to website design and digital marketing has helped us stand out in a competitive market. The team took time to understand our business needs and delivered solutions that exceeded our expectations.",
      additionalContent: "Our website traffic and conversion rates have increased significantly since working with them. I highly recommend their services to any business looking for digital transformation.",
      stars: 5,
      signature: "Sonila Mustafa",
      position: "Founder at QellzA"
    },
    {
  id: 3,
  name: "Durana Tech Park CEO",
  title: "Founder | Durana Tech Park",
  image: "/homepage/Durana.png",
  heading: "A Strategic Digital Launchpad for Albania’s Leading Tech Hub",
  content: "As the founder of Durana Tech Park, I knew we needed a digital identity that could match the ambition of Albania’s first dedicated technology and innovation ecosystem. Our platform had to clearly communicate our mission: to attract startups, global tech companies, researchers, and investors to a thriving hub between Tirana and Durrës.",
  additionalContent: "Vesa Solutions rose to the occasion. They delivered a sleek, scalable, and informative website that showcases our infrastructure, investor incentives, and global opportunities. We've seen an uptick in interest from local and international tech players, and the site has become a key tool for onboarding and partner outreach. Their continued support has been essential to our digital strategy.",
  stars: 5,
  signature: "Durana Tech Park Management",
  position: "Executive Board"
},

    {
      id: 4,
      name: "Florian Duci",
      title: "Founder | Future's Past Events",
      image: "/homepage/florian duci.png",
      heading: "Strategic Digital Approach for Events Business",
      content: "Vesa Solutions understood the unique challenges of marketing an events business online. They created a website that beautifully showcases our past events while making it easy for potential clients to inquire about our services.",
      additionalContent: "Their ongoing SEO and social media strategy has helped us reach new clients and build our brand reputation. Working with them has been a pleasure from day one.",
      stars: 5,
      signature: "Florian Duci",
      position: "Founder at Future's Past Events"
    }
  ];

  const QuoteIcon: React.FC = () => (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width="36" 
      height="36"
      className="sm:w-12 sm:h-12" 
      viewBox="0 0 24 24"
      fill="rgba(59, 130, 246, 0.2)"
    >
      <path d="M9.983 3v7.391c0 5.704-3.731 9.57-8.983 10.609l-.995-2.151c2.432-.917 3.995-3.638 3.995-5.849h-4v-10h9.983zm14.017 0v7.391c0 5.704-3.748 9.571-9 10.609l-.996-2.151c2.433-.917 3.996-3.638 3.996-5.849h-3.983v-10h9.983z"/>
    </svg>
  );

  const renderStars = (count: number) => {
    return [...Array(count)].map((_, i) => (
      <svg key={i} className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
        <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
      </svg>
    ));
  };

  return (
    <section className={`w-full bg-white pb-12 sm:pb-24 ${className}`}>
      <div className="max-w-[1440px] mx-auto px-4 sm:px-6 py-8 sm:py-12">
        {/* Header */}
        <div className="text-center mb-10 sm:mb-16">
          <h3 className="text-blue-700 font-medium text-base sm:text-lg mb-2">CLIENT SUCCESS STORIES</h3>
          <h2 className="text-gray-800 text-xl sm:text-2xl font-bold">WHAT OUR CLIENTS SAY</h2>
        </div>
        
        {/* Main content */}
        <div className="flex flex-col lg:flex-row lg:gap-24 lg:justify-between">
          {/* Left column with profiles */}
          <div className="w-full lg:w-2/5 relative lg:pl-0 lg:mt-16 mb-8 lg:mb-0">
            {/* Mobile horizontal scroll */}
            <div className="lg:hidden flex overflow-x-auto space-x-4 pb-4 mb-6">
              {testimonials.map((testimonial, index) => (
                <div 
                  key={testimonial.id} 
                  className="flex-shrink-0"
                  onClick={() => setActiveTestimonial(index)}
                >
                  <div 
                    className={`flex items-center shadow-md cursor-pointer rounded-2xl transition-all duration-300 w-64 ${
                      index === activeTestimonial ? 'bg-blue-500 scale-105' : 'bg-white hover:shadow-lg'
                    }`}
                  >
                    <div className="p-3">
                      <div className="w-12 h-12 rounded-full border-2 border-white overflow-hidden">
                        <Image
                          src={testimonial.image}
                          alt={testimonial.name}
                          width={48}
                          height={48}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                    <div className="p-3">
                      <div
                        className={`text-lg font-medium ${
                          index === activeTestimonial ? 'text-white' : 'text-blue-700'
                        }`}
                        style={{ fontFamily: 'cursive' }}
                      >
                        {testimonial.name}
                      </div>
                      <div 
                        className={`text-xs ${
                          index === activeTestimonial ? 'text-white' : 'text-gray-600'
                        }`}
                      >
                        {testimonial.title}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Desktop vertical layout */}
            <div className="hidden lg:block relative">
              {/* Vertical timeline line */}
              <div className="absolute right-0 top-0 bottom-0 w-px bg-gray-200"></div>
              
              {testimonials.map((testimonial, index) => (
                <div 
                  key={testimonial.id} 
                  className="relative mb-14"
                  onClick={() => setActiveTestimonial(index)}
                >
                  {/* Profile card */}
                  <div 
                    className={`flex items-center shadow-md cursor-pointer rounded-2xl transition-all duration-300 ${
                      index === activeTestimonial ? 'bg-blue-500 scale-105' : 'bg-white hover:shadow-lg'
                    }`}
                    style={{ width: '90%' }}
                  >
                    <div className="p-3">
                      <div className="w-16 h-16 rounded-full border-2 border-white overflow-hidden">
                        <Image
                          src={testimonial.image}
                          alt={testimonial.name}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                    <div className="p-3">
                      <div
                        className={`text-xl font-medium ${
                          index === activeTestimonial ? 'text-white' : 'text-blue-700'
                        }`}
                        style={{ fontFamily: 'cursive' }}
                      >
                        {testimonial.name}
                      </div>
                      <div 
                        className={`text-sm ${
                          index === activeTestimonial ? 'text-white' : 'text-gray-600'
                        }`}
                      >
                        {testimonial.title}
                      </div>
                    </div>
                  </div>
                  
                  {/* Horizontal connector line */}
                  <div 
                    className={`absolute top-1/2 h-px transform -translate-y-1/2 transition-colors ${
                      index === activeTestimonial ? 'bg-blue-500' : 'bg-gray-200'
                    }`}
                    style={{ 
                      left: '90%', 
                      width: '10%'
                    }}
                  ></div>
                  
                  {/* Timeline dot */}
                  <div 
                    className={`absolute top-1/2 right-0 w-3 h-3 rounded-full transform translate-x-1.5 -translate-y-1/2 transition-colors ${
                      index === activeTestimonial ? 'bg-blue-500' : 'bg-gray-300'
                    }`}
                  ></div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Right column with testimonial content */}
          <div className="w-full lg:w-3/5 relative">
            
            {/* Testimonial content */}
            {testimonials.map((testimonial, index) => (
              <div 
                key={testimonial.id}
                className={`transition-all duration-300 ${
                  index === activeTestimonial ? 'block opacity-100' : 'hidden opacity-0'
                }`}
              >
                <div className="bg-blue-50 rounded-lg p-6 sm:p-12 pt-8 sm:pt-16 mt-8 sm:mt-20 shadow-md relative">
                  {/* Quote icon */}
                  <div className="absolute top-4 sm:top-6 left-4 sm:left-6">
                    <QuoteIcon />
                  </div>
                  
                  {/* Testimonial content */}
                  <div className="relative z-10 pl-8 sm:pl-16">
                    <h3 className="text-blue-700 text-lg sm:text-xl font-bold mb-4 sm:mb-6">{testimonial.heading}</h3>
                    <p className="text-gray-700 mb-3 sm:mb-4 leading-relaxed text-sm sm:text-base">{testimonial.content}</p>
                    <p className="text-gray-700 mb-4 sm:mb-6 leading-relaxed text-sm sm:text-base">{testimonial.additionalContent}</p>
                    
                    {/* Star rating */}
                    <div className="flex mb-3 sm:mb-4">
                      {renderStars(testimonial.stars)}
                    </div>
                    
                    {/* Signature */}
                    <div className="flex flex-col sm:flex-row sm:items-center border-t border-gray-200 pt-4 gap-2 sm:gap-0">
                      <div className="text-blue-700 text-xl sm:text-2xl sm:mr-2" style={{ fontFamily: 'cursive' }}>
                        {testimonial.signature}
                      </div>
                      <div className="text-gray-600 text-xs sm:text-sm sm:border-l sm:border-gray-300 sm:pl-2">
                        {testimonial.position}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ClientTestimonialsSection;